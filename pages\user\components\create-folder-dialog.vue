<template>
  <su-popup
    :show="visible"
    @close="handleClose"
    type="dialog"
    :round="10"
    :isMaskClick="true"
    :animation="true"
  >
    <view class="create-folder">
      <view class="create-folder__title">新建收藏夹</view>
      <view class="create-folder__input-wrap">
        <input
          type="text"
          v-model="folderName"
          placeholder="请输入收藏夹名称"
          class="create-folder__input"
        />
      </view>
      <view class="create-folder__btns">
        <button class="create-folder__btn create-folder__btn--cancel" @tap="handleClose">
          取消
        </button>
        <button class="create-folder__btn create-folder__btn--confirm" @tap="handleConfirm">
          确认
        </button>
      </view>
    </view>
  </su-popup>
</template>

<script setup>
import { ref } from 'vue'
import sheep from '@/sheep'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

const folderName = ref('')

const handleClose = () => {
  emit('update:visible', false)
  folderName.value = ''
}

const handleConfirm = () => {
  if (!folderName.value.trim()) {
    sheep.$helper.toast('请输入收藏夹名称')
    return
  }
  sheep.$helper.toast('创建成功')
  emit('success', folderName.value)
  handleClose()
}
</script>

<style lang="scss" scoped>
.create-folder {
  width: 600rpx;
  padding: 40rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  position: relative; // 添加相对定位
  
  &__title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  &__input-wrap {
    margin-bottom: 40rpx;
    position: relative; // 添加相对定位
  }

  &__input {
    width: 100%;
    height: 88rpx;
    background: #F5F6F7;
    border-radius: 44rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
    color: #333333;
    box-sizing: border-box;
  }

  &__btns {
    display: flex;
    gap: 20rpx;
  }

  &__btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &--cancel {
      background: #F5F6F7;
      color: #333333;
    }

    &--confirm {
      background: #4184FF;
      color: #FFFFFF;
    }
  }
}
</style>
