<template>
  <view class="question-detail-page">
    <!-- 背景图 -->
    <image class="bg-image" :src="sheep.$url.cdn('/group/blue-back.png')" mode="aspectFill"></image>
    
    <!-- 顶部导航 -->
    <easy-navbar 
      :title="assignmentTitle" 
      :transparent="true" 
    />
    
    <!-- 顶部题目导航 -->
    <scroll-view class="question-nav" scroll-x show-scrollbar="false">
      <view class="question-nav-content">
        <view
          v-for="(item, index) in questions"
          :key="index"
          :id="'question-' + index"
          :class="['question-num-wrapper', currentIndex === index ? 'wrapper-active' : '']"
        >
          <view
            :class="['question-num', currentIndex === index ? 'active' : '', item.isCorrect ? 'correct' : 'wrong']"
            @click="switchQuestion(index)"
          >
            {{ index + 1 }}
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 问题内容卡片 -->
    <view class="question-card">
      <!-- 题目信息 -->
      <view class="question-info">
        <view class="question-title">
          <text class="question-index">{{ currentIndex + 1 }}. </text>
          <text class="type-tag" :style="getQuestionTypeStyle(currentQuestion.questionType)">{{ getQuestionTypeName(currentQuestion.questionType) }}</text>
          <text class="score-tag">{{ formatScore(currentQuestion.score) }}分</text>
          <text class="question-content">{{ currentQuestion.stem || currentQuestion.questionStem }}</text>
        </view>
      </view>
      
      <!-- 选项列表 - 单选题、多选题和判断题 -->
      <view class="options-list" v-if="currentQuestion.questionType <= 2">
        <view 
          v-for="(option, idx) in getQuestionOptions(currentQuestion)" 
          :key="idx"
          :class="['option-item', 
                  isUserAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) && !isCorrectAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) ? 'user-selected' : '',
                  isUserAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) && isCorrectAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) ? 'correct-answer' : '']"
        >
          <text :class="['option-text', 
                         isUserAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) && isCorrectAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) ? 'text-correct' : '',
                         isUserAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) && !isCorrectAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) ? 'text-wrong' : '']">
            <template v-if="currentQuestion.questionType === 0">
              {{ option.content || option.serial }}
            </template>
            <template v-else>
              {{ option.serial }}.{{ option.content || option.serial }}
            </template>
          </text>
          <view class="answer-icon" v-if="isUserAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial)) && !isCorrectAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial))">
            <text class="wrong-icon">✕</text>
          </view>
          <view class="answer-icon" v-if="isCorrectAnswer(currentQuestion.questionType === 2 ? option.serial : (option.content || option.serial))">
            <text class="correct-icon">✓</text>
          </view>
        </view>
      </view>
      
      <!-- 填空题、简答题和互译题 -->
      <view class="text-input-container" v-if="currentQuestion.questionType > 2">
        <view 
          class="text-input readonly"
          :class="{ 
            'user-selected': !currentQuestion.isCorrect, 
            'correct-answer': currentQuestion.isCorrect 
          }"
        >
          {{ currentQuestion.userSelectedAnswer || '未作答' }}
        </view>
      </view>
    </view>
    
    <!-- 教师批阅 -->
    <view class="teacher-comment-container">
      <!-- 标题 -->
      <view class="comment-header">
        <view class="header-line"></view>
        <view class="header-card">
          <text class="dot-left">•</text>
          <text class="header-text">教师批阅</text>
          <text class="dot-right">•</text>
        </view>
      </view>
      
      <!-- 批阅内容 -->
      <view class="comment-content">
        <view class="answer-result">
          <text class="label">正确答案：</text>
          <text class="value correct">{{ formatAnswer(currentQuestion.correctAnswer) }}</text>
          <text class="label" style="margin-left: 40rpx;">我的答案：</text>
          <text class="value" :class="currentQuestion.isCorrect ? 'correct' : 'wrong'">{{ formatAnswer(currentQuestion.userSelectedAnswer) }}</text>
          <text class="label" style="margin-left: 40rpx;">得</text>
          <text class="value" :class="currentQuestion.isCorrect ? 'correct' : 'wrong'">{{ currentQuestion.isCorrect ? formatScore(currentQuestion.score) : 0 }}分</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import AssignmentApi from '@/sheep/api/course/assignment';
import { QUESTION_TYPES, getJudgeOptions } from '@/pages/group/util/constants';

// 参数
const assignmentId = ref('');

const courseId = ref('');
const assignmentTitle = ref('作业详情');
const userId = ref('');
const isTeacher = ref(); // 标识是否为老师

// 当前题目索引
const currentIndex = ref(0);

// 题目列表数据
const questions = ref([]);

// 当前问题
const currentQuestion = computed(() => {
  return questions.value[currentIndex.value] || {};
});

// 格式化分数（四舍五入）
const formatScore = (score) => {
  if (!score) return '0';
  return Math.round(score);
};

// 页面加载
onLoad((options) => {
  if (options.id) {
    assignmentId.value = options.id;
  }
  if (options.courseId) {
    courseId.value = options.courseId;
  }
  if (options.title) {
    assignmentTitle.value = decodeURIComponent(options.title);
  }
  if (options.userId) {userId.value = options.userId}; // 获取传入的学生ID
  if(options.isTeacher){
    isTeacher.value = options.isTeacher;
  }

  // 加载作业报告数据，完成后设置当前题目索引
  getAssignmentReport().then(() => {
    // 如果传入了题目索引，则设置当前题目索引
    if (options.questionIndex !== undefined) {
      const index = parseInt(options.questionIndex);
      if (!isNaN(index) && index >= 0 && index < questions.value.length) {
        currentIndex.value = index;
      }
    }
  });
});

// 获取作业报告数据
const getAssignmentReport = async () => {
  let res;
  if (isTeacher.value && userId.value) {

    // 老师查看指定学生的答题报告
    res = await AssignmentApi.getAssignmentAnswerReportForTeacher(
      assignmentId.value,
      userId.value
    );
  } else {
    // 学生查看自己的答题报告
    res = await AssignmentApi.getAssignmentAnswerReport(assignmentId.value);
  }

  if (res.code === 0 && res.data && res.data.questionList) {
    questions.value = res.data.questionList;
  }
};

// 问题类型
const getQuestionTypeName = (type) => {
  switch (type) {
    case 0:
      return '判断题';
    case 1:
      return '单选题';
    case 2:
      return '多选题';
    case 3:
      return '填空题';
    case 4:
      return '简答题';
    case 5:
      return '互译题';
    default:
      return '单选题';
  }
};

// 获取题型样式
const getQuestionTypeStyle = (type) => {
  const styleMap = {
    0: { background: '#E7E9FF', color: '#9D65DB' }, // 判断题
    1: { background: '#E3F5FF', color: '#3DB3F2' }, // 单选题
    2: { background: '#E7FFF5', color: '#2DCF7D' }, // 多选题
    3: { background: '#FFF0FD', color: '#CF2DA3' }, // 填空题
    4: { background: '#FFFCF0', color: '#EEA931' }, // 简答题
    5: { background: '#FFF5F0', color: '#EE6231' }, // 互译题
  };
  return styleMap[type] || { background: '#E3F5FF', color: '#3DB3F2' }; // 默认样式
};

// 切换题目
const switchQuestion = (index) => {
  currentIndex.value = index;
  scrollToQuestion(index);
};

// 滚动到指定题目的位置
const scrollToQuestion = (index) => {
  uni.createSelectorQuery()
    .select(`#question-${index}`)
    .boundingClientRect(function(rect) {
      if (rect) {
        // 计算滚动位置，使其居中
        uni.pageScrollTo({
          selector: `#question-${index}`,
          duration: 300
        });
      }
    })
    .exec();
};

// 判断是否为用户选择的答案
const isUserAnswer = (value) => {
  const userAnswer = currentQuestion.value.userSelectedAnswer;
  if (!userAnswer) return false;
  
  // 多选题特殊处理 - 优先支持JSON数组格式，兼容管道符分隔
  if (currentQuestion.value.questionType === QUESTION_TYPES.MULTIPLE) {
    let answers = [];
    try {
      answers = JSON.parse(userAnswer);
    } catch (e) {
      // 如果JSON解析失败，尝试管道符分隔格式
      answers = userAnswer.split('|');
    }
    return answers.includes(value);
  }
  
  // 判断题特殊处理 - 需要处理A/B与内容的对应关系
  if (currentQuestion.value.questionType === QUESTION_TYPES.JUDGE) {
    // 如果传入的是选项内容，需要与用户答案（A/B格式）进行对应
    if (value === '对' && userAnswer === 'A') return true;
    if (value === '错' && userAnswer === 'B') return true;
    // 直接比较
    return userAnswer === value;
  }
  
  // 单选题特殊处理 - 需要处理字母与选项内容的对应关系
  if (currentQuestion.value.questionType === QUESTION_TYPES.SINGLE) {
    // 如果传入的是选项内容，需要与用户答案（字母格式）进行对应
    const option = currentQuestion.value.options?.find(opt => opt.content === value);
    if (option && userAnswer === option.serial) return true;
    // 直接比较
    return userAnswer === value;
  }
  
  return userAnswer === value;
};

// 判断是否为正确答案
const isCorrectAnswer = (value) => {
  const correctAnswer = currentQuestion.value.correctAnswer;
  if (!correctAnswer) return false;
  
  // 多选题特殊处理 - 优先支持JSON数组格式，兼容管道符分隔
  if (currentQuestion.value.questionType === QUESTION_TYPES.MULTIPLE) {
    let answers = [];
    try {
      answers = JSON.parse(correctAnswer);
    } catch (e) {
      // 如果JSON解析失败，尝试管道符分隔格式
      answers = correctAnswer.split('|');
    }
    return answers.includes(value);
  }
  
  // 判断题特殊处理 - 需要处理A/B与内容的对应关系
  if (currentQuestion.value.questionType === QUESTION_TYPES.JUDGE) {
    // 如果传入的是选项内容，需要与正确答案（A/B格式）进行对应
    if (value === '对' && correctAnswer === 'A') return true;
    if (value === '错' && correctAnswer === 'B') return true;
    // 直接比较
    return correctAnswer === value;
  }
  
  // 单选题特殊处理 - 需要处理字母与选项内容的对应关系
  if (currentQuestion.value.questionType === QUESTION_TYPES.SINGLE) {
    // 如果传入的是选项内容，需要与正确答案（字母格式）进行对应
    const option = currentQuestion.value.options?.find(opt => opt.content === value);
    if (option && correctAnswer === option.serial) return true;
    // 直接比较
    return correctAnswer === value;
  }
  
  return correctAnswer === value;
};

// 获取题目选项（处理判断题的特殊情况）
const getQuestionOptions = (question) => {
  // 如果是判断题且没有选项
  if (question.questionType === QUESTION_TYPES.JUDGE && (!question.options || question.options.length === 0)) {
    return getJudgeOptions();
  }
  return question.options || [];
};

// 格式化答案显示
const formatAnswer = (answer) => {
  if (!answer) return '未作答';
  
  // 判断题特殊处理
  if (currentQuestion.value.questionType === QUESTION_TYPES.JUDGE) {
    return answer === 'A' ? '对' : (answer === 'B' ? '错' : answer);
  }
  
  // 多选题特殊处理，优先支持JSON数组格式，兼容管道符分隔
  if (currentQuestion.value.questionType === QUESTION_TYPES.MULTIPLE) {
    let answers = [];
    try {
      answers = JSON.parse(answer);
      return answers.join('和');
    } catch (e) {
      // 如果JSON解析失败，尝试管道符分隔格式
      if (answer.includes('|')) {
        return answer.split('|').join('和');
      }
    }
  }
  
  // 检查是否为多选题答案格式（不依赖当前题型）
  if (answer.includes('|')) {
    return answer.split('|').join('和');
  }
  
  return answer;
};
</script>

<style scoped lang="scss">
.question-detail-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  z-index: -1;
}

.question-nav {
  height: 141rpx;
  width: 100%;
  white-space: nowrap;
  padding: 10rpx 20rpx 31rpx 20rpx;
  box-sizing: border-box;
}

.question-nav-content {
  display: inline-flex;
}

.question-num-wrapper {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.question-num {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #A2D5FF;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 40rpx;
  color: #FFFFFF;
  transition: all 0.3s;
}

.question-num.active {
  width: 100rpx;
  height: 100rpx;
}

.question-num.correct {
  background-color: #2ADBAF;
  box-shadow: 0rpx 3rpx 12rpx 0rpx #2ADBAF;
}

.question-num.wrong {
  background-color: #FF6263;
  box-shadow: 0rpx 3rpx 12rpx 0rpx rgba(237,55,55,0.6);
}

.question-card {
  width: 634rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 25rpx;
  padding: 30rpx;
  margin: 0 28rpx 28rpx 28rpx;
}

.question-info {
  margin-bottom: 30rpx;
}

.question-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  font-weight: 500;
  overflow-x: auto;
}

.question-index {
  white-space: nowrap;
  margin-right: 10rpx;
}

.question-content {
  margin-left: 10rpx;
}

.type-tag {
  padding: 0 10rpx;
  font-size: 24rpx;
  margin-right: 10rpx;
  min-width: 80rpx;
  height: 43rpx;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.score-tag {
  color: #FF6263;
  padding: 0 10rpx;
  font-size: 24rpx;
  min-width: 70rpx;
  height: 43rpx;
  background: #FFE7E7;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.options-list {
  margin-top: 30rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #F8F8F8;
  border-radius: 40rpx;
  position: relative;
  border: 1px solid #F8F8F8;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-left: 30rpx;
}

.text-correct {
  color: #41E282;
  font-weight: 500;
}

.text-wrong {
  color: #FF6263;
  font-weight: 500;
}

.answer-icon {
  position: absolute;
  right: 30rpx;
  font-size: 40rpx;
}

.correct-icon {
  color: #41E282;
}

.wrong-icon {
  color: #FF6263;
}

.user-selected {
  background-color: #FFE9E9;
  border: 1px solid #FF6263;
}

.correct-answer {
  background-color: #E9FFF2;
  border: 1px solid #41E282;
}

.teacher-comment-container {
  width: 634rpx;
  padding: 110rpx 30rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 25rpx;
  position: relative;

  margin: 40rpx 28rpx 28rpx 28rpx;
  opacity: 0.8;
}

.comment-header {
  position: absolute;
  top: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-line {
  width: 228rpx;
  height: 5rpx;
  background: #1EC5FF;
}

.header-card {
  width: 219rpx;
  height: 56rpx;
  background: #E4F8FF;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-text {
  font-size: 32rpx;
  color: #36C0F6;
  font-weight: 500;
  white-space: nowrap;
}

.dot-left, .dot-right {
  color: #36C0F6;
  font-size: 32rpx;
  margin: 0 10rpx;
}

.answer-result {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  padding: 20rpx 0;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 30rpx;
  font-weight: 600;
  margin-right: 10rpx;
}

.value.correct {
  color: #41E282;
}

.value.wrong {
  color: #FF6263;
}

.text-input-container {
  margin-top: 40rpx;
  width: 100%;
}

.text-input {
  width: 100%;
  height: 400rpx;
  background-color: #F9F9F9;
  border-radius: 20rpx;
  padding: 20rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  border: 1px solid #EEEEEE;
  overflow-y: auto;
}

.text-input.readonly {
  background-color: #F5F5F5;
  color: #666;
}

.text-input.user-selected {
  background-color: #FFE9E9;
  border: 1px solid #FF6263;
  color: #FF6263;
}

.text-input.correct-answer {
  background-color: #E9FFF2;
  border: 1px solid #41E282;
  color: #41E282;
}
</style>