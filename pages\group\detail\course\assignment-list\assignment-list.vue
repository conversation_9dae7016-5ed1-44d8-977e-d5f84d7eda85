<template>
  <view class="assignment-list">
    <!-- 空数据状态 -->
    <s-empty text="暂无作业" v-if="assignmentList.length === 0" />

    <!-- 作业列表 - 创建者视图 -->
    <view class="list-content" v-else-if="isCreator">
      <view
        v-for="(item, index) in assignmentList"
        :key="index"
        class="teacher-assignment-item"
        @click="navToTeacherAssignmentDetail(item)"
      >
        <image class="assignment-icon" :src="sheep.$url.cdn('/group/techer-assignment.png')" mode="aspectFill" />
        <view class="assignment-content">
          <text class="teacher-assignment-title">{{ item.title }}</text>
        </view>
        <text class="arrow">›</text>
      </view>
    </view>

    <!-- 作业列表 - 学生视图 -->
    <view class="list-content" v-else>
      <view
        v-for="(item, index) in assignmentList"
        :key="index"
        class="assignment-item"
        @click="navToAssignmentDetail(item)"
      >
        <image class="assignment-icon" :src="sheep.$url.cdn('/group/fabuzuoye.png')" mode="aspectFill" />
        <view class="assignment-content">
          <view class="assignment-info">
            <text class="assignment-title">{{ item.title }}</text>
            <text :class="['assignment-status', getStatusClass(item)]">
              {{ getStatusText(item) }}
            </text>
          </view>
        </view>
        <text class="arrow">›</text>
      </view>
    </view>
  </view>

  <!-- 未开始模态框 -->
  <su-popup :show="showNotStartedModal" type="center" :round="20" @close="showNotStartedModal = false">
    <view class="modal-content">
      <view class="modal-title">作业未开始</view>
      <view class="modal-info">
        <text>开始时间：{{ formatTime(currentAssignment.startTime) }}</text>
      </view>
      <view class="modal-button" @click="showNotStartedModal = false">确定</view>
    </view>
  </su-popup>

  <!-- 已结束未提交模态框 -->
  <su-popup :show="showEndedModal" type="center" :round="20" @close="showEndedModal = false">
    <view class="modal-content">
      <view class="modal-title">作业未提交</view>
      <view class="modal-info">
        <text>开始时间：{{ formatTime(currentAssignment.startTime) }}</text>
        <text>结束时间：{{ formatTime(currentAssignment.endTime) }}</text>
      </view>
      <view class="modal-button" @click="showEndedModal = false">确定</view>
    </view>
  </su-popup>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import sheep from '@/sheep';
import AssignmentApi from '@/sheep/api/course/assignment';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  },
  keyword: {
    type: String,
    default: ''
  },
  groupId: {
    type: [String, Number],
    default: ''
  },
  isCreator: {
    type: Boolean,
    default: false
  }
});

// 作业列表数据
const assignmentList = ref([]);
// 分页参数
const pageQuery = ref({
  pageNo: 1,
  pageSize: 20,
  courseId: null,
  title: ''
});

// 模态框控制
const showNotStartedModal = ref(false);
const showEndedModal = ref(false);
const currentAssignment = ref({});

// 获取作业状态文本
const getStatusText = (item) => {
  const now = Date.now();

  // 特殊情况：开始时间和结束时间都为0，视为常规作业
  if (item.startTime === 0 && item.endTime === 0) {
    return item.isSubmit ? '已提交' : '未提交';
  }
  
  // 未开始：当前时间小于开始时间
  if (item.startTime > 0 && now < item.startTime) {
    return '未开始';
  }
  
  // 已结束：当前时间大于结束时间
  if (item.endTime > 0 && now > item.endTime) {
    // 已结束状态统一显示为"已结束"，不区分是否提交
    return '已结束';
  }
  
  // 进行中：当前时间在开始和结束时间之间
  const status = item.isSubmit ? '已提交' : '未提交';
  return status;
};

// 获取状态对应的样式类
const getStatusClass = (item) => {
  const now = Date.now();
  
  // 特殊情况：开始时间和结束时间都为0，视为常规作业
  if (item.startTime === 0 && item.endTime === 0) {
    return item.isSubmit ? 'status-completed' : 'status-not-submitted';
  }
  
  // 未开始
  if (item.startTime > 0 && now < item.startTime) {
    return 'status-not-started';
  }
  
  // 已结束
  if (item.endTime > 0 && now > item.endTime) {
    // 已结束状态统一使用已结束样式，不区分是否提交
    return 'status-ended';
  }
  
  // 进行中
  return item.isSubmit ? 'status-completed' : 'status-not-submitted';
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 获取作业列表
const getAssignmentList = async () => {
  pageQuery.value.courseId = props.courseId;
  pageQuery.value.title = props.keyword || '';

  // 创建一个新的请求参数对象，确保字段名称与接口一致
  const requestParams = {
    page: pageQuery.value.pageNo,
    limit: pageQuery.value.pageSize,
    courseId: pageQuery.value.courseId,
    keyword: pageQuery.value.title,
    groupId: getGroupId() // 添加groupId参数
  };

  // 如果是教师，可以调用不同的API获取所有作业
  const res = await AssignmentApi.getMyAssignmentReleasePage(requestParams);

  if (res.code === 0 && res.data) {
    assignmentList.value = res.data.list.map(item => {
      // 确保时间戳是数值类型
      const startTime = typeof item.startTime === 'number' ? item.startTime : 0;
      const endTime = typeof item.endTime === 'number' ? item.endTime : 0;

      return {
        id: item.id,
        title: item.title || '',
        isSubmit: !!item.isSubmit,
        startTime: startTime,
        endTime: endTime
      };
    });
  } else {
    assignmentList.value = [];
  }
};

// 搜索作业列表
const searchAssignments = async () => {
  if (!props.keyword) return getAssignmentList();
  const requestParams = {
    page: 1,
    limit: 100, // 搜索时获取更多结果
    courseId: props.courseId,
    title: props.keyword,
    groupId: getGroupId() // 添加groupId参数
  };

  const res = await AssignmentApi.getMyAssignmentReleasePage(requestParams);

  if (res.code === 0 && res.data) {
    assignmentList.value = res.data.list.map(item => {
      // 确保时间戳是数值类型
      const startTime = typeof item.startTime === 'number' ? item.startTime : 0;
      const endTime = typeof item.endTime === 'number' ? item.endTime : 0;

      return {
        id: item.id,
        title: item.title || '',
        isSubmit: !!item.isSubmit,
        startTime: startTime,
        endTime: endTime
      };
    });
  } else {
    assignmentList.value = [];
  }
};

// 学生角色 - 导航到作业详情
const navToAssignmentDetail = (assignment) => {
  const now = Date.now();
  
  // 特殊情况：开始时间和结束时间都为0，视为常规作业
  if (assignment.startTime === 0 && assignment.endTime === 0) {
    // 将标题编码后传递，避免特殊字符问题
    const encodedTitle = encodeURIComponent(assignment.title || '作业详情');
    if (assignment.isSubmit) {
      // 已提交，跳转到分数页
      sheep.$router.go(`/pages/group/detail/course/assignment-list/score?id=${assignment.id}&courseId=${props.courseId}&title=${encodedTitle}`);
    } else {
      // 未提交，跳转到答题页
      sheep.$router.go(`/pages/group/detail/course/assignment-list/answer?id=${assignment.id}&courseId=${props.courseId}&title=${encodedTitle}`);
    }
    return;
  }
  
  // 未开始状态，显示模态框
  if (assignment.startTime > 0 && now < assignment.startTime) {
    currentAssignment.value = assignment;
    showNotStartedModal.value = true;
    return;
  }
  
  // 已结束状态
  if (assignment.endTime > 0 && now > assignment.endTime) {
    // 已结束已提交，跳转到分数页
    if (assignment.isSubmit) {
      const encodedTitle = encodeURIComponent(assignment.title || '作业详情');
      sheep.$router.go(`/pages/group/detail/course/assignment-list/score?id=${assignment.id}&courseId=${props.courseId}&title=${encodedTitle}`);
    } else {
      // 已结束未提交，显示模态框
      currentAssignment.value = assignment;
      showEndedModal.value = true;
    }
    return;
  }
  
  // 将标题编码后传递，避免特殊字符问题
  const encodedTitle = encodeURIComponent(assignment.title || '作业详情');
  
  // 进行中状态
  if (assignment.isSubmit) {
    // 已提交，跳转到分数页
    sheep.$router.go(`/pages/group/detail/course/assignment-list/score?id=${assignment.id}&courseId=${props.courseId}&title=${encodedTitle}`);
  } else {
    // 未提交，跳转到答题页
    sheep.$router.go(`/pages/group/detail/course/assignment-list/answer?id=${assignment.id}&courseId=${props.courseId}&title=${encodedTitle}`);
  }
};

// 获取参数
const getGroupId = () => {
  // 优先使用props传入的班级ID
  if (props.groupId) return props.groupId;

  // 其次从localStorage中获取当前班级ID
  return uni.getStorageSync('last_group_id') || '';
};

// 教师角色 - 导航到作业发布详情
const navToTeacherAssignmentDetail = (assignment) => {
  // 将标题编码后传递，避免特殊字符问题
  const encodedTitle = encodeURIComponent(assignment.title || '作业详情');
  // 跳转到作业发布详情页，直接使用props传入的班级ID
  sheep.$router.go(`/pages/group/detail/course/assignment-list/released-detail?id=${assignment.id}&courseId=${props.courseId}&groupId=${getGroupId()}&title=${encodedTitle}`);
};

// 监听关键词变化
watch(() => props.keyword, () => {
  if (props.keyword) {
    searchAssignments();
  } else {
    pageQuery.value.pageNo = 1;
    getAssignmentList();
  }
});

// 监听课程ID变化
watch(() => props.courseId, () => {
  if (props.courseId) {
    pageQuery.value.pageNo = 1;
    getAssignmentList();
  }
});

// 组件加载
onMounted(() => {
  // 如果用户已登录，刷新一下用户信息
  if (sheep.$store('user').isLogin) {
    sheep.$store('user').getInfo();
  }

  if (props.courseId) {
    getAssignmentList();
  }
});
</script>

<style scoped lang="scss">
.assignment-list {
  width: 100%;
}

.list-content {
  padding: 20rpx;
}

.assignment-item {
  height: 155rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 25rpx;
  opacity: 0.9;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.teacher-assignment-item {
  height: 139rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 25rpx;
  opacity: 0.9;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.assignment-icon {
  width: 85rpx;
  height: 84rpx;
  margin-right: 20rpx;
}

.assignment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.assignment-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.assignment-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
}

.teacher-assignment-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
}

.assignment-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
  width: fit-content;
}

.status-not-submitted {
  color: #FF6B6B;
  background-color: rgba(255, 107, 107, 0.1);
}

.status-not-started {
  color: #11A2E5;
  background-color: #DBF9FF;
}

.status-completed {
  color: #0AD176;
  background-color: #D9FFF4;
}

.status-ended {
  color: #808080;
  background-color: #E8E8E8;
}

.arrow {
  font-size: 40rpx;
  color: #CCCCCC;
  position: absolute;
  right: 30rpx;
}

/* 模态框样式 */
.modal-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.modal-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.modal-info text {
  font-size: 28rpx;
  color: #666;
}

.modal-button {
  width: 200rpx;
  height: 80rpx;
  background: #3498DB;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
}
</style>