import request from '@/sheep/request';

// 语音合成服务
const NlsApi = {
  // 文字转语音
  ttsAliyun: (data, custom = {}) => {
    return request({
      url: '/ai/nls/tts',
      method: "POST",
      data: data,
      responseType: 'arraybuffer',
      custom: {
        showError: false,
        showLoading: true,
        loadingMsg: '语音生成中...',
        ...custom,
      },
    });
  },
  // 文字转语音
  ttsOpenAI: (data, custom = {}) => {
    return request({
      url: '/ai/nls/openai/tts',
      method: "POST",
      data: data,
      responseType: 'arraybuffer',
      custom: {
        showLoading: true,
        loadingMsg: '语音生成中...',
        ...custom,
      },
    });
  }
};

export default NlsApi;
