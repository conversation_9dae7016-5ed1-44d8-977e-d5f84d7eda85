import request from '@/sheep/request';

const SetApi = {
  // 查询学习集分页
  getWordSetPage: (params) => {
    return request({
      url: `/learning/word-set/page`,
      method: 'GET',
      params,
      custom: {
        showLoading: false,
      }
    })
  },

  // 获取我创建的学习集分页
  getWordSetPageMy: (params) => {
    return request({
      url: `/learning/word-set/page/created`,
      method: 'GET',
      params,
      custom: {
        showLoading: false,
      }
    })
  },

  // 获取我收藏的学习集分页
  getWordSetPageFavourite: (params) => {
    return request({
      url: `/learning/word-set/page/favourite`,
      method: 'GET',
      params,
      custom: {
        showLoading: false,
      }
    })
  },

  // 获取班级下的学习集分页
  getWordSetPageByClass: (params) => {
    return request({
      url: `/learning/word-set/page/class`,
      method: 'GET',
      params,
      custom: {
        showLoading: false,
      }
    })
  },

  // 更新学习集
  updateWordSet: (data) => {
    return request({
      url: `/learning/word-set/update`,
      method: 'PUT',
      data,
      custom: {
        showLoading: true,
        loadingMsg: '更新中...',
      }
    })
  },

  // 创建学习集
  createWordSet: (data) => {
    return request({
      url: `/learning/word-set/create`,
      method: 'POST',
      data
    })
  },

  // 获取单词卡列表
  getWordCardList: (wordSetId) => {
    return request({
      url: `/learning/word-set/word-card/list-by-word-set-id`,
      method: 'GET',
      params: { wordSetId },
      custom: {
        showLoading: false,
      }
    })
  },

  // 获取学习集详情
  getWordSet: (id, isAddClick = false) => {
    if (isAddClick) {
      void SetApi.click(id);
    }
    return request({
      url: `/learning/word-set/get`,
      method: 'GET',
      params: { id },
      custom: {
        showLoading: false,
      }
    })
  },

  // 获取学习集详情
  getWordSetWithPagedCards: (params, isAddClick = false) => {
    if (isAddClick) {
      void SetApi.click(params.id);
    }
    return request({
      url: `/learning/word-set/get/paged-cards`,
      method: 'GET',
      params,
      custom: {
        showLoading: false,
      }
    })
  },

  // 删除学习集
  deleteWordSet: (id) => {
    return request({
      url: `/learning/word-set/delete`,
      method: 'DELETE',
      params: { id }
    })
  },

  // 获取置顶学习集
  getTopWordSet: () => {
    return request({
      url: `/learning/word-set/top/get`,
      method: 'GET',
      custom: {
        showLoading: false,
      }
    })
  },

  // 设置置顶学习集
  setTopWordSet: (setId) => {
    return request({
      url: `/learning/word-set/top/set`,
      method: 'GET',
      params: {
        setId
      },
      custom: {
        showLoading: false,
      }
    })
  },

  // 重置词书
  resetWordSet: (setId) => {
    return request({
      url: `/learning/word-set/word/reset`,
      method: 'GET',
      params: { setId },
      custom: {
        auth: true,
        showLoading: true,
        loadingMsg: '重置中...',
        showSuccess: true,
        successMsg: '词书重置成功',
      }
    })
  },
  // 设置可见范围
  setVisibility: (data) => {
    return request({
      url: `/learning/word-set/update/visibility`,
      method: 'PUT',
      data
    })
  },

  // 学习集分享到班级
  shareToClass: (setId, classId) => {
    return request({
      url: `/learning/word-set/class/share`,
      method: 'GET',
      params: {
        setId,
        classId
      },
      custom: {
        auth: true,
        showLoading: true,
        loadingMsg: '分享中...',
        showSuccess: true,
        successMsg: '分享成功',
      }
    })
  },

  // 批量分享学习集到班级
  batchShareToClass: (data) => {
    return request({
      url: `/learning/word-set/class/batch-share`,
      method: 'POST',
      data,
    })
  },
  // 批量分享学习集到班级
  batchShareToGroup: (data) => {
    return request({
      url: `/learning/word-set/class/batch-shareToGroup`,
      method: 'POST',
      data,
    })
  },

  // 批量删除班级中的学习集
  batchDeleteFromClass: (data) => {
    return request({
      url: `/learning/word-set/class/batch-delete`,
      method: 'POST',
      data,
    })
  },

  // 获取优质学习集分页
  getQualityWordSets: (params) => {
    return request({
      url: `/learning/quality-word-set/page`,
      method: 'GET',
      params,
    });
  },

  //点击量
  click: (setId) => {
    return request({
      url: `/learning/quality-word-set/view`,
      method: 'POST',
      params: { setId },
      custom: {
        showLoading: false,
      },
    });
  }
};

export default SetApi;
