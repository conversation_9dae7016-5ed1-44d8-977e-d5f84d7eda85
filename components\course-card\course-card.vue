<template>
  <view class="course-card" :class="{ 'selected': isSelected }" @click="$emit('click', course)">
    <view class="course-main">
      <image class="course-icon" :src="iconUrl" mode="aspectFill" />
      <view class="course-content">
        <view class="title-row">
          <view class="course-title">{{ course.title }}</view>
          <view v-if="course.nickname" class="course-nickname">{{ course.nickname }}</view>
        </view>
        <view class="course-subtitle">{{ course.subtitle }}</view>
      </view>
    </view>
    <view v-if="isSelected" class="selected-icon">✓</view>
    <image v-else class="come-icon" :src="comeIconUrl" mode="aspectFill" @click.stop="$emit('click', course)" />
  </view>
</template>

<script setup>
import { computed } from 'vue';
import sheep from '@/sheep';

const props = defineProps({
  course: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      title: '',
      subtitle: '',
      nickname: '',
    }),
  },
  iconUrl: {
    type: String,
    default: () => sheep.$url.cdn('/course/lesson.png'),
  },
  comeIconUrl: {
    type: String,
    default: () => sheep.$url.cdn('/course/come.png'),
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

defineEmits(['click']);
</script>

<style lang="scss" scoped>
.course-card {
  display: flex;
  height: 113rpx;
  flex-direction: column;
  background-color: #fff;
  border-radius: 17rpx;
  margin-bottom: 30rpx;
  padding: 24rpx 32rpx 18rpx 32rpx;
  box-shadow: 0rpx 0rpx 13rpx 0rpx rgba(200,200,200,0.55);
  position: relative;
  transition: box-shadow 0.2s;
  cursor: pointer;
  justify-content: center;

  &:active {
    box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(200,200,200,0.35);
  }
  
  &.selected {
    background-color: #e5f8ff;
    border: 1rpx solid #2CB6F2;
  }
}

.course-main {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: flex-start;
  padding-right: 50rpx;
}

.course-icon {
  width: 85rpx;
  height: 85rpx;
  margin-right: 24rpx;
  background: #f2f6fa;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.course-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0;
  margin-top: 2rpx;
}

.title-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 12rpx;
}

.course-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-nickname {
  font-size: 20rpx;
  background: #E5F8FF;
  border-radius: 4rpx;
  color: #2CB6F2;
  padding: 6rpx 12rpx;
  white-space: nowrap;
  flex-shrink: 0;
  text-align: left;
}

.course-subtitle {
  font-size: 24rpx;
  color: #b0b0b0;
  line-height: 1.4;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: calc(100% - 14rpx);
}

.come-icon {
  width: 14rpx;
  height: 24rpx;
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  flex-shrink: 0;
}

.selected-icon {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #2CB6F2;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-weight: bold;
}
</style>