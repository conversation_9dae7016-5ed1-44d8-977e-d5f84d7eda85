<template>
  <view class="favorites">
    <view class="container">
      <easy-navbar title="我的收藏" />
      <view class="favorite-item-container">
        <favorites-card
          v-for="item in 4"
          :key="item"
          folder-name="默认收藏夹"
          item-count="12"
        />
      </view>
    </view>

    <view class="footer">
      <button class="create-btn" @tap="showCreateDialog">
        <text class="icon">+</text>
        创建收藏夹
      </button>
    </view>

    <!-- 创建收藏夹弹窗 -->
    <create-folder-dialog
      v-model:visible="isDialogVisible"
      @success="onCreateSuccess"
    />
  </view>
</template>

<script setup>
  import { ref } from 'vue';
  import FavoritesCard from '@/pages/user/components/favorites-card.vue';
  import CreateFolderDialog from '@/pages/user/components/create-folder-dialog.vue';

  const isDialogVisible = ref(false);

  const showCreateDialog = () => {
    isDialogVisible.value = true;
  };

  const onCreateSuccess = (folderName) => {
    // TODO: 刷新收藏夹列表
    console.log('创建成功:', folderName);
  };
</script>

<style scoped lang="scss">
  .favorites {
    background-color: #F3F7F9;
    min-height: 100vh;
    width: 100%;

    .container {
      padding: 30rpx;
    }

    .footer {
      width: 100%;
      height: 150rpx;
      position: fixed;
      bottom: 0;
      left: 0;
      background-color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: calc(env(safe-area-inset-bottom) / 5 * 2);
    }

    .create-btn {
      width: 88%;
      height: 88rpx;
      background-color: #FFFFFF;
      border: 2rpx solid #4184FF;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #005AFF;

      .icon {
        margin-right: 8rpx;
        font-size: 32rpx;
        font-weight: 300;
      }
    }
  }

  .popup-content {
    width: 600rpx;
    padding: 40rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
  }

  .popup-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .input-wrapper {
    margin-bottom: 40rpx;
  }

  .folder-input {
    height: 88rpx;
    background: #F5F6F7;
    border-radius: 44rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
    color: #333333;
  }

  .button-group {
    display: flex;
    gap: 20rpx;
  }

  .btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cancel {
    background: #F5F6F7;
    color: #333333;
  }

  .confirm {
    background: #4184FF;
    color: #FFFFFF;
  }
</style>
