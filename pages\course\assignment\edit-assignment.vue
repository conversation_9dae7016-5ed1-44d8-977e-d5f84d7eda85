<template>
  <view class="container">
    <easy-navbar custom-class="title" :title="title" />

    <view class="form-container">
      <view class="form-card">
        <!-- 作业标题 -->
        <view class="form-item">
          <view class="form-row">
            <text class="label">作业标题</text>
            <uni-easyinput class="input" :inputBorder="false" :clearable="true" v-model="assignmentTitle"
                           placeholder="请输入作业标题"></uni-easyinput>
          </view>
        </view>

        <view class="divider"></view>

        <!-- 评分机制 -->
        <view class="form-item">
          <view class="form-row">
            <text class="label">评分机制</text>
            <view class="score-type">
              <view class="type-wrapper">
                <view class="type-item" :class="{ active: scoreType === 'percentage' }"
                      @tap="handleScoreTypeChange('percentage')">
                  百分制
                  <view v-if="scoreType === 'percentage'" class="check-icon">✓</view>
                </view>
                <text class="type-desc">平均分配每道题的分值</text>
              </view>
              <view class="type-wrapper">
                <view class="type-item" :class="{ active: scoreType === 'custom' }"
                      @tap="handleScoreTypeChange('custom')">
                  自定义
                  <view v-if="scoreType === 'custom'" class="check-icon">✓</view>
                </view>
                <text class="type-desc">自行设置每道题的分值</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 题目列表 -->
      <view class="question-section" v-if="questionList.length > 0">
        <view class="question-list">
          <swipe-action 
            v-for="(item, index) in questionList" 
            :key="item.id"
            :buttons="[
              { text: '编辑', width: 120, backgroundColor: '#FFC107', color: '#ffffff' },
              { text: '删除', width: 120, backgroundColor: '#ff4757', color: '#ffffff' }
            ]"
            @button-click="handleSwipeAction(index, $event)"
          >
            <view class="question-item">
              <text class="type-tag" :style="getQuestionTypeStyle(item.questionType)">
                {{ getQuestionTypeName(item.questionType) }}
              </text>
              <view class="item-header">
                <view class="topic-info">
                  <!-- 题目标题区域 -->
                  <view class="question-title">
                    <view class="title-row">
                      <text class="question-index">{{ index + 1 }}. </text>
                      <view class="question-content">{{ item.questionStem }}
                        <view class="score-tag" v-if="scoreType === 'custom'">
                          <input type="number" v-model="item.score" maxlength="3" />
                          <text>分</text>
                        </view>
                        <view class="score-tag" v-else>
                          <text class="score-text">{{ getPercentageScore(index) }}分</text>
                        </view>
                      </view>
                    </view>
                  </view>

                   <!-- 选择题选项 -->
                   <view class="options-list" v-if="item.questionType <= 2 && item.optionList">
                     <view class="option-item" v-for="(option, optIndex) in item.optionList" :key="optIndex">
                       <text class="option-label">{{ option.serial }}.</text>
                       <text class="option-text">{{ option.content }}</text>
                     </view>
                   </view>
                   
                   <!-- 填空题、简答题和互译题 -->
                   <view class="text-input-container" v-if="item.questionType > 2">
                     <view class="text-input-content" v-if="item.answer">
                       {{ item.answer }}
                     </view>
                     <view class="text-input-placeholder" v-else>
                       {{ getQuestionTypePlaceholder(item.questionType) }}
                     </view>
                   </view>
                 </view>
               </view>
             </view>
          </swipe-action>
        </view>
      </view>
      
      <!-- 空状态 -->
      <s-empty v-else text="暂无题目数据" />

      <!-- 底部按钮 -->
      <view class="bottom-actions">
        <button class="select-question-btn" @tap="handleAddQuestion">
          添加题目
        </button>
        <button class="save-btn" @tap="handleSave" :class="{'save-btn-disabled': !assignmentTitle || questionList.length === 0}">
          保存作业
        </button>
      </view>
    </view>

    <!-- 编辑题目模态框 -->
    <su-popup ref="editModal" type="center" :is-mask-click="false" round="10">
      <view class="modal-container">
        <view class="modal-header">
          <text class="modal-title">编辑题目</text>
          <text class="modal-close" @tap="closeEditModal">×</text>
        </view>
        
        <view class="modal-content">
          <!-- 题目类型 -->
          <view class="form-item">
            <text class="form-label">题目类型</text>
            <picker :value="editForm.questionType" :range="questionTypeOptions" range-key="label" @change="onQuestionTypeChange">
              <view class="picker-input">
                {{ getQuestionTypeName(editForm.questionType) }}
              </view>
            </picker>
          </view>

          <!-- 难易度 -->
          <view class="form-item">
            <text class="form-label">难易度</text>
            <picker :value="editForm.difficulty" :range="difficultyOptions" range-key="label" @change="onDifficultyChange">
              <view class="picker-input">
                {{ getDifficultyName(editForm.difficulty) }}
              </view>
            </picker>
          </view>

          <!-- 分数(自定义模式) -->
          <view class="form-item" v-if="scoreType === 'custom'">
            <text class="form-label">分数</text>
            <input class="form-input" type="number" v-model="editForm.score" placeholder="请输入分数" />
          </view>

          <!-- 题干 -->
          <view class="form-item">
            <text class="form-label">题干</text>
            <textarea class="form-textarea" v-model="editForm.questionStem" placeholder="请输入题干" />
          </view>

          <!-- 判断题答案 -->
          <view class="form-item" v-if="editForm.questionType === 0">
            <text class="form-label">答案</text>
            <view class="radio-group">
              <label class="radio-item" @tap="editForm.answer = '1'">
                <radio :checked="editForm.answer === '1'" color="#46adf0" />正确
              </label>
              <label class="radio-item" @tap="editForm.answer = '0'">
                <radio :checked="editForm.answer === '0'" color="#46adf0" />错误
              </label>
            </view>
          </view>

          <!-- 单选题和多选题选项 -->
          <view class="form-item" v-if="editForm.questionType === 1 || editForm.questionType === 2">
            <text class="form-label">答案选项</text>
            <scroll-view scroll-y="true" class="options-scroll-container">
              <view class="options-container">
                <view class="option-edit-item" v-for="(option, index) in editForm.optionList" :key="index">
                  <view class="option-row">
                    <text class="option-prefix">{{ String.fromCharCode(65 + index) }}.</text>
                    <!-- 单选题 -->
                    <radio v-if="editForm.questionType === 1" :checked="editForm.correctOptionIndex === index" 
                           @tap="editForm.correctOptionIndex = index" color="#46adf0" />
                    <!-- 多选题 -->
                    <checkbox v-else :checked="option.isCorrect" @tap="option.isCorrect = !option.isCorrect" color="#46adf0" />
                    <input class="option-input" v-model="option.content" placeholder="请输入选项内容" />
                    <text class="option-delete" @tap="removeOption(index)">删除</text>
                  </view>
                </view>
              </view>
            </scroll-view>
            <button class="add-option-btn" @tap="addOption">+ 添加选项</button>
          </view>

          <!-- 填空题、简答题、互译题答案 -->
          <view class="form-item" v-if="editForm.questionType === 3 || editForm.questionType === 4 || editForm.questionType === 5">
            <text class="form-label">参考答案</text>
            <textarea class="form-textarea" v-model="editForm.answer" placeholder="请输入参考答案" />
          </view>

          <!-- 题目出处 -->
          <view class="form-item">
            <text class="form-label">题目出处</text>
            <input class="form-input" v-model="editForm.source" placeholder="请输入题目出处" />
          </view>

          <!-- 试题解析 -->
          <view class="form-item">
            <text class="form-label">试题解析</text>
            <textarea class="form-textarea" v-model="editForm.analysis" placeholder="请输入试题解析" />
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @tap="closeEditModal">取消</button>
          <button class="modal-btn confirm-btn" @tap="saveEditForm">确定</button>
        </view>
      </view>
    </su-popup>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import sheep from '@/sheep'
import AssignmentApi from '@/sheep/api/course/assignment'
import SwipeAction from '@/components/swipe-action/swipe-action.vue'
// 添加 su-popup 组件导入
import SuPopup from '@/sheep/ui/su-popup/su-popup.vue'

// 页面标题
const title = ref('作业库编辑')

// 页面参数
const assignmentId = ref('')
const courseId = ref('')
const isEdit = ref(false)

// 表单数据
const assignmentTitle = ref('')
const scoreType = ref('percentage') // percentage: 百分制, custom: 自定义
// 保存已存储的评分机制类型，用于添加题目时使用
const savedScoreType = ref('percentage')

// 题目列表
const questionList = ref([])

// 编辑模态框相关
const editModal = ref(null)
const editingIndex = ref(-1)
const editForm = ref({
  id: undefined,
  questionType: 0,
  questionStem: '',
  answer: '',
  source: '',
  analysis: '',
  difficulty: 0,
  score: 0,
  optionList: [],
  correctOptionIndex: undefined
})

// 题目类型选项
const questionTypeOptions = ref([
  { label: '判断题', value: 0 },
  { label: '单选题', value: 1 },
  { label: '多选题', value: 2 },
  { label: '填空题', value: 3 },
  { label: '简答题', value: 4 },
  { label: '互译题', value: 5 }
])

// 难易度选项
const difficultyOptions = ref([
  { label: '简单', value: 0 },
  { label: '中等', value: 1 },
  { label: '困难', value: 2 }
])

// 获取页面参数
const getParams = (options) => {
  if (options) {
    assignmentId.value = options.assignmentId || ''
    courseId.value = options.courseId || ''
    isEdit.value = !!assignmentId.value

    if (isEdit.value) {
      title.value = '编辑作业'
      loadAssignmentDetail()
    }
  }
}

// 加载作业详情
const loadAssignmentDetail = async () => {
  if (!assignmentId.value) return

  // 获取作业基本信息
  const assignmentRes = await AssignmentApi.getAssignment(assignmentId.value)
  if (assignmentRes.code === 0 && assignmentRes.data) {
    assignmentTitle.value = assignmentRes.data.name || assignmentRes.data.title || ''
    // 根据作业设置判断评分机制，将后端的scoringMechanism转换为前端的scoreType
    const scoringMechanism = assignmentRes.data.scoringMechanism
    scoreType.value = scoringMechanism === 1 ? 'percentage' : 'custom'
    // 同时保存原始评分机制类型，用于添加题目时使用
    savedScoreType.value = scoringMechanism === 1 ? 'percentage' : 'custom'
  }

  // 获取作业题目列表
  const questionsRes = await AssignmentApi.getAssignmentQuestionList(assignmentId.value)
  if (questionsRes.code === 0 && questionsRes.data) {
    questionList.value = questionsRes.data.map(item => ({
      id: item.id,
      questionId: item.questionId,
      questionType: item.questionType,
      questionStem: item.questionStem,
      answer: item.answer,
      score: item.score || 0,
      optionList: item.optionList || []
    }))
  }
}

// 获取题目类型名称
const getQuestionTypeName = (type) => {
  const typeNames = {
    0: '判断题',
    1: '单选题', 
    2: '多选题',
    3: '填空题',
    4: '简答题',
    5: '互译题'
  }
  return typeNames[type] || '未知题型'
}

// 获取题目类型样式
const getQuestionTypeStyle = (type) => {
  const styles = {
    0: { background: '#FFF3E0', color: '#FF8F00' }, // 判断题
    1: { background: '#E3F5FF', color: '#3DB3F2' }, // 单选题
    2: { background: '#E7FFF5', color: '#2DCF7D' }, // 多选题
    3: { background: '#F3E5F5', color: '#9C27B0' }, // 填空题
    4: { background: '#E8F5E8', color: '#4CAF50' }, // 简答题
    5: { background: '#FFF8E1', color: '#FFC107' }  // 互译题
  }
  return styles[type] || styles[1]
}

// 计算百分制分数
const getPercentageScore = (index) => {
  const totalQuestions = questionList.value.length
  if (totalQuestions === 0) return 0
  return Math.round(100 / totalQuestions)
}

// 获取题目类型占位符
const getQuestionTypePlaceholder = (type) => {
  switch (type) {
    case 3:
      return '[ 填空题标准答案 ]';
    case 4:
      return '[ 简答题参考答案 ]';
    case 5:
      return '[ 互译题参考答案 ]';
    default:
      return '[答案区]';
  }
}

// 添加题目
const handleAddQuestion = () => {
  // 在编辑模式下，使用作业已保存的评分机制类型
  // 在创建模式下，使用界面上选择的评分机制类型
  let currentScoreType = isEdit.value ? savedScoreType.value : scoreType.value;
  
  // 构建跳转参数
  const baseParams = `scoreType=${currentScoreType}&courseId=${courseId.value}`
  const assignmentParam = assignmentId.value ? `&assignmentId=${assignmentId.value}` : ''
  const editParam = isEdit.value ? '&isEdit=true' : ''
  
  // 根据当前评分机制类型跳转
  if (currentScoreType === 'percentage') {
    sheep.$router.go(`/pages/course/assignment/score-percentage?${baseParams}${assignmentParam}${editParam}`);
  } else {
    sheep.$router.go(`/pages/course/assignment/score-custom?${baseParams}${assignmentParam}${editParam}`);
  }
}

// 删除题目
const handleDeleteQuestion = async (index) => {
  const question = questionList.value[index]
  
  // 如果是编辑模式且题目已保存，需要调用删除接口
  if (isEdit.value && question.id) {
    const res = await AssignmentApi.deleteAssignmentQuestion(question.id)
    if (res.code === 0) {
      questionList.value.splice(index, 1)
      sheep.$helper.toast('删除成功')
    } else {
      sheep.$helper.toast('删除失败')
    }
  } else {
    // 新建模式直接从列表中移除
    questionList.value.splice(index, 1)
  }
}

// 处理左滑操作
const handleSwipeAction = (index, event) => {
  if (event.index === 0) {
    // 编辑按钮
    handleEditQuestion(index)
  } else if (event.index === 1) {
    // 删除按钮
    handleSwipeDelete(index)
  }
}

// 编辑题目
const handleEditQuestion = (index) => {
  editingIndex.value = index
  const question = questionList.value[index]
  
  // 初始化编辑表单
  editForm.value = {
    id: question.id,
    questionType: question.questionType,
    questionStem: question.questionStem,
    answer: question.answer,
    source: question.source || '',
    analysis: question.analysis || '',
    difficulty: question.difficulty || 0,
    score: question.score || 0,
    optionList: [],
    correctOptionIndex: undefined
  }
  
  // 处理选项数据
  if (question.optionList && question.optionList.length > 0) {
    editForm.value.optionList = question.optionList.map(opt => ({
      content: opt.content,
      isCorrect: false
    }))
    
    // 处理正确答案
    if (question.questionType === 0 && question.answer) {
      // 判断题 - A为正确，B为错误
      editForm.value.answer = question.answer === 'A' ? '1' : '0'
    } else if (question.questionType === 1 && question.answer) {
      // 单选题 - 通过字母找到对应选项的索引
      const answerLetter = question.answer
      const correctIndex = answerLetter.charCodeAt(0) - 65 // A=0, B=1, C=2...
      if (correctIndex >= 0 && correctIndex < editForm.value.optionList.length) {
        editForm.value.correctOptionIndex = correctIndex
      }
    } else if (question.questionType === 2 && question.answer) {
      // 多选题 - 解析JSON数组格式的答案
      const correctAnswers = JSON.parse(question.answer)
      if (Array.isArray(correctAnswers)) {
        editForm.value.optionList.forEach((opt, index) => {
          const optionLetter = String.fromCharCode(65 + index)
          opt.isCorrect = correctAnswers.includes(optionLetter)
        })
      }
    }
  }
  
  editModal.value.open()
}

// 关闭编辑模态框
const closeEditModal = () => {
  editModal.value.close()
  editingIndex.value = -1
}

// 保存编辑表单
const saveEditForm = async () => {
  if (!editForm.value.questionStem.trim()) {
    sheep.$helper.toast('请输入题干')
    return
  }
  
  // 处理答案数据
  let answer = ''
  switch (editForm.value.questionType) {
    case 0: // 判断题 - 存储A为正确，B为错误
      answer = editForm.value.answer === '1' ? 'A' : 'B'
      break
    case 1: // 单选题 - 直接存储ABC等字母
      if (editForm.value.correctOptionIndex !== undefined) {
        answer = String.fromCharCode(65 + editForm.value.correctOptionIndex) // A, B, C, D...
      }
      break
    case 2: // 多选题 - 存储数组格式如["A","B","C"]
      const selectedOptions = editForm.value.optionList
        .map((opt, index) => opt.isCorrect ? String.fromCharCode(65 + index) : null)
        .filter(option => option !== null)
      answer = JSON.stringify(selectedOptions)
      break
    case 3:
    case 4:
    case 5: // 填空题、简答题、互译题
      answer = editForm.value.answer
      break
  }
  
  // 更新题目数据
  const updatedQuestion = {
    ...questionList.value[editingIndex.value],
    questionType: editForm.value.questionType,
    questionStem: editForm.value.questionStem,
    answer: answer,
    source: editForm.value.source,
    analysis: editForm.value.analysis,
    difficulty: editForm.value.difficulty,
    score: editForm.value.score,
    optionList: editForm.value.optionList.map((opt, index) => ({
      id: 0,
      questionId: editForm.value.id,
      serial: String.fromCharCode(65 + index), // A, B, C, D...
      content: opt.content
    }))
  }
  
  // 如果是已保存的题目，调用更新接口
  if (isEdit.value && editForm.value.id) {
    try {
      const data = {
        id: editForm.value.id,
        courseId: courseId.value,
        assignmentId: assignmentId.value,
        questionType: editForm.value.questionType,
        questionStem: editForm.value.questionStem,
        answer: answer,
        source: editForm.value.source,
        analysis: editForm.value.analysis,
        difficulty: editForm.value.difficulty,
        optionList: editForm.value.optionList.map((opt, index) => ({
          id: 0,
          questionId: editForm.value.id,
          serial: String.fromCharCode(65 + index), // A, B, C, D...
          content: opt.content
        }))
      }
      
      if (scoreType.value === 'custom') {
        data.score = editForm.value.score
      }
      
      const res = await AssignmentApi.updateAssignmentQuestion(data)
      if (res.code === 0) {
        questionList.value[editingIndex.value] = updatedQuestion
        sheep.$helper.toast('更新成功')
        closeEditModal()
      } else {
        sheep.$helper.toast('更新失败')
      }
    } catch (error) {
      sheep.$helper.toast('更新失败')
    }
  } else {
    // 新建模式直接更新本地数据
    questionList.value[editingIndex.value] = updatedQuestion
    sheep.$helper.toast('修改成功')
    closeEditModal()
  }
}

// 题目类型改变
const onQuestionTypeChange = (e) => {
  editForm.value.questionType = questionTypeOptions.value[e.detail.value].value
  // 清空相关数据
  editForm.value.answer = ''
  editForm.value.optionList = []
  editForm.value.correctOptionIndex = undefined
}

// 难易度改变
const onDifficultyChange = (e) => {
  editForm.value.difficulty = difficultyOptions.value[e.detail.value].value
}

// 获取难易度名称
const getDifficultyName = (difficulty) => {
  const names = { 0: '简单', 1: '中等', 2: '困难' }
  return names[difficulty] || '简单'
}

// 添加选项
const addOption = () => {
  editForm.value.optionList.push({
    content: '',
    isCorrect: false
  })
}

// 删除选项
const removeOption = (index) => {
  editForm.value.optionList.splice(index, 1)
  // 如果删除的是当前选中的正确答案，需要清空正确答案
  if (editForm.value.correctOptionIndex === index) {
    editForm.value.correctOptionIndex = undefined
  }
  // 如果删除的选项后面还有选项，需要更新它们的索引
  if (typeof editForm.value.correctOptionIndex === 'number' && editForm.value.correctOptionIndex > index) {
    editForm.value.correctOptionIndex--
  }
}

// 处理左滑删除
const handleSwipeDelete = (index) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这道题目吗？',
    success: (res) => {
      if (res.confirm) {
        handleDeleteQuestion(index)
      }
    }
  })
}

// 保存作业
const handleSave = async () => {
  if (!assignmentTitle.value.trim()) {
    sheep.$helper.toast('请输入作业标题')
    return
  }
  
  if (questionList.value.length === 0) {
    sheep.$helper.toast('请至少添加一道题目')
    return
  }
  
  // 如果是自定义分数模式，检查所有题目是否都设置了分数
  if (scoreType.value === 'custom') {
    const hasInvalidScore = questionList.value.some(item => !item.score || item.score <= 0)
    if (hasInvalidScore) {
      sheep.$helper.toast('请为所有题目设置分数')
      return
    }
  }

  // 将scoreType转换为后端需要的scoringMechanism整数值
  const scoringMechanism = scoreType.value === 'percentage' ? 1 : 0

  const assignmentData = {
    name: assignmentTitle.value.trim(),
    courseId: courseId.value,
    scoringMechanism: scoringMechanism
  }

  let assignmentRes
  if (isEdit.value) {
    // 更新作业
    assignmentRes = await AssignmentApi.updateAssignment({
      id: assignmentId.value,
      ...assignmentData
    })
  } else {
    // 创建作业
    assignmentRes = await AssignmentApi.createAssignment(assignmentData)
  }

  if (assignmentRes.code === 0) {
    // 如果是自定义分数且有题目，更新题目分数
      if (scoreType.value === 'custom' && questionList.value.length > 0) {
        const questionItems = questionList.value.map(item => ({
          questionId: item.questionId || item.id,
          score: Number(item.score)
        }));

        await AssignmentApi.updateQuestionScore({
          assignmentId: assignmentId.value,
          questionItems
        });
      }

    sheep.$helper.toast(isEdit.value ? '保存成功' : '创建成功')
    // 返回上一页并刷新数据
    setTimeout(() => {
      sheep.$router.back(1, true)
    }, 1500)
  } else {
    sheep.$helper.toast(assignmentRes.msg || '保存失败')
  }
}

// 处理评分机制切换
const handleScoreTypeChange = (type) => {
  // 如果是从百分制切换到自定义
  if (scoreType.value === 'percentage' && type === 'custom') {
    // 检查是否存在有效分数的题目
    const hasValidScores = questionList.value.some(item => item.score && item.score > 0)
    
    if (hasValidScores) {
      // 如果存在有效分数，只为分数为null或0的题目分配分数
      // 计算需要分配分数的题目数量
      const invalidScoreQuestions = questionList.value.filter(item => !item.score || item.score <= 0)
      const invalidCount = invalidScoreQuestions.length
      
      if (invalidCount > 0) {
        // 计算每道无效分数题目应分配的分数
        const scorePerInvalidQuestion = Math.round(100 / invalidCount)
        
        // 只为分数为null或0的题目设置分数
        questionList.value.forEach(item => {
          if (!item.score || item.score <= 0) {
            item.score = scorePerInvalidQuestion
          }
          // 有效分数的题目保持不变
        })
      }
    } else {
      // 如果不存在有效分数，则为所有题目平均分配总分100
      const averageScore = Math.round(100 / questionList.value.length)
      questionList.value.forEach(item => {
        item.score = averageScore
      })
    }
  }
  
  // 更新评分机制
  scoreType.value = type
}

// 页面加载
onLoad((options) => {
  getParams(options)
  
  // 监听刷新事件
  uni.$on('refreshAssignmentData', () => {
    // 当从题目选择页面返回时，刷新数据
    if (isEdit.value && assignmentId.value) {
      loadAssignmentDetail()
    }
  })
})

onMounted(() => {
  // 页面挂载后的其他初始化逻辑
})

// 页面卸载时移除事件监听，避免内存泄漏
onUnmounted(() => {
  uni.$off('refreshAssignmentData')
})
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    background-color: #f7fdff;
    position: relative;
    padding-bottom: 120rpx;
  }

  .title {
    font-size: 18.75pt !important;
    color: #383838 !important;
    font-family: '黑体', 'SimHei', 'Heiti SC', sans-serif !important;
  }

  .form-container {
    padding: 42rpx 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .form-card {
    width: 691rpx;
    background: #ffffff;
    box-shadow: 1rpx 1rpx 9rpx 0rpx rgba(211, 223, 230, 0.27);
    border-radius: 25rpx;
    border: 1px solid rgba(233, 247, 255, 0.9);
    padding: 40rpx;
    box-sizing: border-box;
  }

  .divider {
    width: 632rpx;
    height: 1rpx;
    background: #eeeeee;
    margin: 30rpx auto;
  }

  .form-item {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-row {
    display: flex;
    align-items: center;
    gap: 30rpx;
    position: relative;
  }

  .label {
    font-size: 28rpx;
    color: #333;
    white-space: nowrap;
    min-width: 120rpx;
  }

  .input {
    flex: 1;
    height: 80rpx;
    border: none;
    padding: 0 20rpx;
    font-size: 28rpx;
    background: transparent;
  }

  .score-type {
    flex: 1;
    display: flex;
    gap: 30rpx;
  }

  .type-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10rpx;
  }

  .type-item {
    width: 194rpx;
    height: 69rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #bebebe;
    border-radius: 15rpx;
    font-size: 28rpx;
    color: #666;
    position: relative;
    overflow: hidden;

    &.active {
      border: 2px solid #46adf0;
      background-color: transparent;
      color: #46adf0;
    }
  }

  .check-icon {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 31rpx;
    height: 24rpx;
    background: #46adf0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 17rpx;
    border-bottom-right-radius: 15rpx;
    border-top-left-radius: 50rpx;
  }

  .type-desc {
    width: 207rpx;
    margin-top: 16rpx;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 21rpx;
    color: #d0d0d0;
    text-align: center;
  }

  // 题目列表样式
  .question-section {
    width: 691rpx;
    background-color: #F7FDFF;
    min-height: calc(100vh - 150rpx);
    padding: 22rpx 0 200rpx 0;
  }

  .section-header {
    width: 651rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 20rpx;
    border-bottom: 1rpx solid #e9ecef;
    background: #ffffff;
    border-radius: 25rpx;
    margin: 0 auto 30rpx;
    position: relative;
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,.05);
  }

  .section-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .add-question-btn {
    color: #46adf0;
    font-size: 28rpx;
    padding: 10rpx 20rpx;
    background: #e3f5ff;
    border-radius: 20rpx;
  }

  .question-list {
    margin-top: 0;
    background-color: #F7FDFF;
    min-height: calc(100vh - 150rpx);
    padding: 22rpx 0 200rpx 0;
  }

  .question-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx 26rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
  }

  .type-tag {
    padding: 0 12rpx;
    font-size: 24rpx;
    min-width: 80rpx;
    height: 43rpx;
    border-radius: 0rpx 25rpx 0rpx 25rpx;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
  }

  .item-header {
    display: flex;
    align-items: flex-start;
  }

  .topic-info {
    flex: 1;
  }

  .question-title {
    margin-bottom: 20rpx;
  }

  .title-row {
    display: flex;
    align-items: flex-start;
  }

  .question-index {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-right: 8rpx;
  }

  .question-content {
    flex: 1;
    font-size: 30rpx;
    color: #333;
    line-height: 1.6;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16rpx;
  }

  .question-type {
    padding: 0 12rpx;
    font-size: 24rpx;
    min-width: 80rpx;
    height: 43rpx;
    border-radius: 0rpx 25rpx 0rpx 25rpx;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
  }

  .question-stem {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 25rpx;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: break-all;
    white-space: pre-wrap;
  }

  .score-tag {
    color: #FF6263;
    font-size: 24rpx;
    height: 43rpx;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    flex-shrink: 0;
    
    input {
      width: 60rpx;
      height: 40rpx;
      text-align: center;
      border: 1px solid #FFE7E7;
      background-color: #FFE7E7;
      border-radius: 8rpx;
      margin: 0 6rpx 0 0;
      color: #FF7575;
      font-size: 24rpx;
    }
    
    .score-text {
      background-color: #FFE7E7;
      border-radius: 8rpx;
      padding: 8rpx 12rpx;
      color: #FF7575;
      font-size: 24rpx;
    }
  }



  // 选项列表样式
  .options-list {
    margin-top: 20rpx;
    
    .option-item {
      margin-bottom: 12rpx;
      display: flex;
      font-size: 30rpx;
      color: #333;
      background: #F7FDFF;
      padding: 20rpx;
      border-radius: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .option-label {
        width: 50rpx;
        font-weight: 500;
        color: #3D3D3D;
      }
      
      .option-text {
        flex: 1;
      }
    }
  }

  .question-options {
    margin-top: 20rpx;
  }

  .option-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;
    padding: 16rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
  }

  .option-label {
    font-size: 28rpx;
    color: #666;
    margin-right: 12rpx;
    font-weight: 500;
    flex-shrink: 0;
  }

  .option-text {
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
    flex: 1;
  }

  .text-input-container {
    margin-top: 20rpx;
    width: 100%;
    
    .text-input-content {
      width: 100%;
      min-height: 120rpx;
      background: #F7FDFF;
      border-radius: 20rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #333;
      box-sizing: border-box;
      line-height: 1.5;
      word-break: break-all;
      white-space: pre-wrap;
    }
    
    .text-input-placeholder {
      width: 100%;
      height: 120rpx;
      background: #F7FDFF;
      border-radius: 20rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #999;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }
  }

  // 填空题、简答题和互译题样式
  .text-input-container {
    margin-top: 20rpx;
    width: 100%;
    
    .text-input-content {
      width: 100%;
      min-height: 120rpx;
      background: #F7FDFF;
      border-radius: 20rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #333;
      box-sizing: border-box;
      line-height: 1.5;
      word-break: break-all;
      white-space: pre-wrap;
    }
    
    .text-input-placeholder {
      width: 100%;
      height: 120rpx;
      background: #F7FDFF;
      border-radius: 20rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #999;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }
  }

  // 空状态样式
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 40rpx;
    background: #ffffff;
    border-radius: 20rpx;
    margin: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
    text-align: center;
  }

  .add-first-question-btn {
    background: linear-gradient(135deg, #46adf0, #3a9ae0);
    color: #ffffff;
    border: none;
    border-radius: 25rpx;
    padding: 20rpx 40rpx;
    font-size: 30rpx;
    font-weight: 500;
  }

  // 底部按钮样式
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 30rpx;
    border-top: 1rpx solid #e9ecef;
    z-index: 100;
    display: flex;
    gap: 20rpx;
  }

  .select-question-btn {
    flex: 1;
    background: #ffffff;
    font-size: 32rpx;
    height: 82rpx;
    line-height: 82rpx;
    background: linear-gradient(270deg,  rgba(67, 158, 96, 0.89),rgba(126, 248, 166, 0.89));
    border-radius: 41rpx;
    color: #fff;
    text-align: center ;
  }

  .save-btn {
    flex: 1;
    border: none;
    height: 82rpx;
    line-height: 82rpx;
    background: linear-gradient(270deg,rgba(70,173,240,.89),rgba(0,222,255,.89));
    border-radius: 41rpx;
    color: #fff;
    font-size: 32rpx;
    text-align: center ;

    
  }
  .save-btn-disabled {
    background: #cccccc;
    color: #fff !important;
  }

  // 模态框样式
  .modal-container {
    width: 90%;
    max-width: 600rpx;
    max-height: 90vh;
    background: #fff;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #eee;
    background: #f8f9fa;
  }

  .modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .modal-close {
    font-size: 40rpx;
    color: #999;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 30rpx 40rpx;
    max-height: calc(90vh - 280rpx);
    min-height: 0;
  }

  .form-item {
    margin-bottom: 30rpx;
  }

  .form-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 15rpx;
    font-weight: 500;
  }

  .form-input {
    width: 100%;
    height: 70rpx;
    border: 1rpx solid #ddd;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }

  .form-textarea {
    width: 100%;
    min-height: 120rpx;
    border: 1rpx solid #ddd;
    border-radius: 10rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    resize: none;
  }

  .picker-input {
    height: 70rpx;
    border: 1rpx solid #ddd;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    background: #fff;
  }

  .radio-group {
    display: flex;
    gap: 40rpx;
  }

  .radio-item {
    display: flex;
    align-items: center;
    gap: 10rpx;
    font-size: 28rpx;
    color: #333;
  }

  .options-scroll-container {
    max-height: 350rpx;
    border: 1rpx solid #eee;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
  }

  .options-container {
    padding: 10rpx;
  }

  .option-edit-item {
    margin-bottom: 20rpx;
    padding: 20rpx;
    border: 1rpx solid #eee;
    border-radius: 10rpx;
    background: #f8f9fa;
    
    &:last-child {
      margin-bottom: 10rpx;
    }
  }

  .option-row {
    display: flex;
    align-items: center;
    gap: 15rpx;
  }

  .option-prefix {
    font-weight: bold;
    min-width: 40rpx;
    font-size: 28rpx;
    color: #333;
  }

  .option-input {
    flex: 1;
    height: 60rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 0 15rpx;
    font-size: 26rpx;
  }

  .option-delete {
    color: #ff4757;
    font-size: 24rpx;
    padding: 10rpx 15rpx;
    border-radius: 8rpx;
    background: #ffe7e7;
  }

  .add-option-btn {
    width: 100%;
    height: 60rpx;
    border: 1rpx dashed #46adf0;
    border-radius: 10rpx;
    background: #f0f8ff;
    color: #46adf0;
    font-size: 26rpx;
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx 40rpx;
    border-top: 1rpx solid #eee;
    background: #f8f9fa;
    flex-shrink: 0;
  }

  .modal-btn {
    flex: 1;
    height: 70rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
    border: none;
  }

  .cancel-btn {
    background: #f5f5f5;
    color: #666;
  }

  .confirm-btn {
    background: #46adf0;
    color: #fff;
  }
</style>
