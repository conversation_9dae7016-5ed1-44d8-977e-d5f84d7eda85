@import './tools';
@import './ui';
@import './niu';

/* 泰语字体 */
@font-face {
  font-family: 'Sarabun';
  src: url('https://thai-nnnmkj.oss-cn-shenzhen.aliyuncs.com/Sarabun-Regular.ttf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Sarabun';
  src: url('https://thai-nnnmkj.oss-cn-shenzhen.aliyuncs.com/Sarabun-Bold.ttf');
  font-weight: bold;
  font-style: normal;
}

page {
  -webkit-overflow-scrolling: touch; // 解决ios滑动不流畅
  height: 100%;
  width: 100%;
  font-family: 'Sarabun', sans-serif;
  word-break: break-all; //英文文本不换行
  white-space: normal;
  background-color: $bg-page;
  color: $dark-3;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
