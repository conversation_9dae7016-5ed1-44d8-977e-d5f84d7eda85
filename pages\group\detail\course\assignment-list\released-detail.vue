<template>
  <view class="assignment-detail">
    <!-- 顶部导航栏 -->
    <view
      class="navbar-area"
      :style="{ paddingTop: `${paddingTop}px`, paddingBottom: `${paddingBottom}px` }"
    >
      <!-- 左侧区域：返回按钮 -->
      <view class="left">
        <image
          class="back-icon"
          mode="widthFix"
          :src="sheep.$url.cdn('/common/back.png')"
          @click="goBack"
        />
      </view>

      <!-- 中间标题位置放置标签切换 -->
      <view class="title">
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'submitted' }"
            @tap="switchTab('submitted')"
            >已交{{ submittedCount }}</view
          >
          <view
            class="tab-item"
            :class="{ active: activeTab === 'unsubmitted' }"
            @tap="switchTab('unsubmitted')"
            >未交</view
          >
        </view>
      </view>

      <!-- 右侧区域 -->
      <view class="right">
        <view class="more-icon">
          <text class="iconfont icon-more-dot"></text>
        </view>
      </view>
    </view>

    <!-- 占位元素，防止内容被固定导航栏遮挡 -->
    <view class="navbar-placeholder" :style="{ height: `${navbarHeight + paddingBottom}px` }" />

    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input">
        <image class="search-icon" :src="searchIcon" mode="aspectFit"></image>
        <input
          type="text"
          v-model="searchKeyword"
          placeholder="姓名/学号"
          placeholder-class="placeholder"
          @confirm="handleSearch"
          @blur="handleSearch"
        />
      </view>
      <text class="search-btn" @tap="handleStatistics">统计</text>
    </view>
    
    <!-- 学生列表 -->
    <view class="student-list">
      <!-- 已交学生列表 -->
      <view v-if="activeTab === 'submitted'">
        <view 
          class="student-item" 
          v-for="(item, index) in submittedList" 
          :key="index"
          @click="goToAnswerDetail(item.userId)"
        >
          <!-- 使用字母头像替代真实头像 -->
          <view class="avatar-box">
            <text class="avatar-text">{{ item.nickname ? item.nickname.slice(-2) : '--' }}</text>
          </view>
          <view class="student-info">
            <view class="student-name">{{ item.nickname || '未知学生' }}</view>
            <view class="submit-time">{{ item.submissionTime || '无提交时间' }}</view>
          </view>
          <view class="score-tag">{{ Math.round(item.score) }}分</view>
        </view>
      </view>
      
      <!-- 未交学生列表 -->
      <view v-else>
        <view 
          class="student-item" 
          v-for="(item, index) in unsubmittedList" 
          :key="index"
        >
          <!-- 使用字母头像替代真实头像 -->
          <view class="avatar-box">
            <text class="avatar-text">{{ item.nickname ? item.nickname.slice(-2) : '--' }}</text>
          </view>
          <view class="student-info">
            <view class="student-name">{{ item.nickname || '未知学生' }}</view>
            <view class="student-number" v-if="item.studentNumber">{{ item.studentNumber }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import sheep from '@/sheep';
import AssignmentApi from '@/sheep/api/course/assignment';

// 图标资源
const searchIcon = sheep.$url.cdn('/index/search-icon.png');

// 导航栏相关参数
const paddingTop = ref(0);
const navbarHeight = ref(0);
const paddingBottom = ref(10);

// 当前激活的标签页
const activeTab = ref('submitted');

// 作业ID和班级ID
const assignmentId = ref('');
const groupId = ref('');

// 搜索关键词
const searchKeyword = ref('');

// 学生列表数据
const submittedList = ref([]);
const unsubmittedList = ref([]);

// 原始数据备份（用于搜索过滤）
const originalSubmittedList = ref([]);
const originalUnsubmittedList = ref([]);

// 计算已提交和总人数
const submittedCount = computed(() => submittedList.value.length);

// 获取URL参数
const getParams = () => {
  // @ts-ignore
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.options) {
    assignmentId.value = currentPage.options.id || '';
    groupId.value = currentPage.options.groupId || '';
  }
};

// 获取学生提交列表
const getStudentSubmissions = async () => {
  if (!assignmentId.value || !groupId.value) return;
  const params = {
    assignmentReleaseId: assignmentId.value,
    groupId: groupId.value
  };

  const {code, data} = await AssignmentApi.getAssignmentCompletionStatus(params);

  if (code !== 0) {
    sheep.$router.back();
  }

  // 更新已提交学生列表
  submittedList.value = data.submittedUsers || [];
  originalSubmittedList.value = [...submittedList.value];
  // 更新未提交学生列表
  unsubmittedList.value = data.unsubmittedUsers || [];
  originalUnsubmittedList.value = [...unsubmittedList.value];
};

// 跳转到答题详情页
const goToAnswerDetail = (userId) => {
    sheep.$router.go(`/pages/group/detail/course/assignment-list/answer-detail?id=${assignmentId.value}&userId=${userId}`);
};

// 切换标签页
const switchTab = (tab) => {
  activeTab.value = tab;
};

// 搜索学生
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    // 如果搜索关键字为空，恢复原始列表
    submittedList.value = [...originalSubmittedList.value];
    unsubmittedList.value = [...originalUnsubmittedList.value];
    return;
  }

  const keyword = searchKeyword.value.toLowerCase();

  // 根据当前标签页进行搜索
  if (activeTab.value === 'submitted') {
    // 搜索已提交学生
    submittedList.value = originalSubmittedList.value.filter(item =>
      item.nickname && item.nickname.toLowerCase().includes(keyword)
    );
  } else {
    // 搜索未提交学生
    unsubmittedList.value = originalUnsubmittedList.value.filter(item =>
      item.nickname && item.nickname.toLowerCase().includes(keyword)
    );
  }
};

// 统计功能
const handleStatistics = () => {
  sheep.$router.go(`/pages/group/detail/course/assignment-list/statistics?assignmentReleaseId=${assignmentId.value}&groupId=${groupId.value}`);
};

// 返回上一页
const goBack = () => {
  sheep.$router.back();
};

onMounted(() => {
  // 获取URL参数
  getParams();

  // 获取学生提交列表
  getStudentSubmissions();

  // #ifdef MP-WEIXIN
  // 获取胶囊按钮信息
  try {
    // @ts-ignore
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    // 调整导航栏高度，使其能容纳胶囊按钮下方的标题
    paddingTop.value = menuButtonInfo.top;
    // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
    navbarHeight.value = paddingTop.value + 35;
  } catch (e) {
    // 获取状态栏高度
    const systemInfo = sheep.$helper.sys();
    paddingTop.value = systemInfo.marginTop || 20;
    navbarHeight.value = paddingTop.value + 35;
  }
  // #endif

  // #ifndef MP-WEIXIN
  // 获取状态栏高度
  const systemInfo = sheep.$helper.sys();
  paddingTop.value = systemInfo.marginTop;
  // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
  navbarHeight.value = paddingTop.value + 35;
  // #endif
});
</script>

<style scoped lang="scss">
.assignment-detail {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

.navbar-area {
  display: flex;
  align-items: center;
  height: 35px;
  width: 100%;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  transition: all 0.05s ease;
}

.left {
  position: absolute;
  left: 15px;
  display: flex;
  align-items: center;
  z-index: 101;

  .back-icon {
    width: 19rpx;
    padding: 10rpx;
  }
}

.right {
  position: absolute;
  right: 15px;
  display: flex;
  align-items: center;
  z-index: 101;
}

.title {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tab-container {
  display: flex;
  width: 264rpx;
  height: 65rpx;
  background: #46adf0;
  border-radius: 33rpx;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 33rpx;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 25rpx;
    color: #ffffff;
    border: 2px solid #46adf0;

    &.active {
      background: #ffffff;
      border-radius: 33rpx;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 25rpx;
      color: #46adf0;
    }
  }
}

.more-icon {
  width: 60rpx;
  text-align: right;
  font-size: 40rpx;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 10rpx 30rpx 23rpx 30rpx;
  background-color: #fff;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #f5f7fa;
    border-radius: 40rpx;
    padding: 15rpx 20rpx;
    margin-right: 20rpx;

    .search-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }

    input {
      flex: 1;
      font-size: 28rpx;
    }

    .placeholder {
      color: #999;
    }
  }

  .search-btn {
    font-size: 28rpx;
    color: #46adf0;
  }
}

.student-list {
  padding: 0 30rpx;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  margin-top: 16rpx;
  position: relative;

  height: 110rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 25rpx;
  opacity: 0.9;
}

.avatar-box {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(140deg, rgba(97,246,255,0.89), rgba(18,157,237,0.89));
  box-shadow: 1rpx 1rpx 6rpx 0rpx rgba(42,185,242,0.34);
  border-radius: 17rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.avatar-text {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.submit-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.student-number {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.score-tag {
  width: 107rpx;
  height: 26rpx;
  background: #FFE3E3;
  border-radius: 0rpx 25rpx 0rpx 25rpx;
  position: absolute;
  top: 0;
  right: 0;
  padding: 10rpx 0;
  color: #ff6b6b;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
