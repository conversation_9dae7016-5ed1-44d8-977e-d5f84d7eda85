<template>
  <view class="chapter-container">
    <easy-navbar :title="lessonTitle" />

    <scroll-view 
      scroll-y 
      refresher-enabled 
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      class="scroll-container"
    >
      <s-empty v-if="units.length === 0" text="暂无章节数据" />

      <view v-else class="units-container">
        <view v-for="(unit, unitIndex) in units" :key="unitIndex" class="unit-item">
          <view class="unit-header" @click="toggleUnit(unitIndex)">
            <view class="unit-number">{{ unitIndex + 1 }}</view>
            <view class="unit-title">{{ unit.title }}</view>
            <view class="unit-arrow">
              <view class="arrow" :class="{ 'arrow-down': unit.open, 'arrow-up': !unit.open }"></view>
            </view>
          </view>
          
          <view class="chapter-list" v-if="unit.open">
            <template v-if="unit.chapters && unit.chapters.length > 0">
              <view 
                v-for="(chapter, chapterIndex) in unit.chapters" 
                :key="chapterIndex"
                class="chapter-item"
                @click="goToChapterDetail(chapter)"
              >
                <view class="chapter-indicator">
                  <view class="indicator-dot" :class="{ 'active': chapter.completed }"></view>
                </view>
                <view class="chapter-content">
                  <view class="chapter-number">{{ unitIndex + 1 }}-{{ chapterIndex + 1 }}</view>
                  <view class="chapter-name">{{ chapter.title }}</view>
                </view>
              </view>
            </template>
            <view v-else class="chapter-empty">暂无子章节内容</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import sheep from '@/sheep';
  import LessonChapterApi from '@/sheep/api/course/lessonchapter';
  import LessonApi from '@/sheep/api/course/lesson';

  // 接收课程ID参数
  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  // 页面状态
  const refreshing = ref(false);
  const lessonTitle = ref('');
  const units = ref([]);

  // 获取课程标题
  const loadLessonTitle = async (lessonId) => {
    const res = await LessonApi.getLesson(lessonId);
    if (res.code === 0 && res.data) {
      lessonTitle.value = res.data.title || res.data.name;
    }
  };

  // 加载章节数据
  const loadChapters = async () => {
    const lessonId = props.id || getCurrentPageIdParam();
    // 首先获取课程标题
    await loadLessonTitle(lessonId);

    // 使用新的父章节分页API获取顶级章节
    const res = await LessonChapterApi.getParentLessonChapterPage({
      courseId: lessonId,
      pageSize: 100 // 设置一个较大的值，确保获取所有章节
    });

    if (res.code === 0 && res.data && res.data.list && res.data.list.length > 0) {
      // 保存顶级章节数据
      const parentChapters = res.data.list;

      // 格式化顶级章节为单元
      formatChaptersIntoUnits(parentChapters);
    } else {
      units.value = [];
    }
  };
  
  // 获取当前页面的id参数
  const getCurrentPageIdParam = () => {
    const pagesParams = getCurrentPages();
    const currentPage = pagesParams[pagesParams.length - 1];
    const pageOptions = currentPage.options || {};
    return pageOptions.id || '';
  };

  // 将章节数据格式化为单元
  const formatChaptersIntoUnits = (parentChapters) => {
    const formattedUnits = [];
    
    // 对顶级章节进行排序
    parentChapters.sort((a, b) => (a.sort || 0) - (b.sort || 0));
    
    // 将每个顶级章节作为一个单元，默认为折叠状态
    parentChapters.forEach((parentChapter, index) => {
      formattedUnits.push({
        id: parentChapter.id,
        title: parentChapter.name, // 使用name字段作为标题
        open: false, // 默认为折叠状态
        chapters: [] // 初始化为空数组，后续按需加载
      });
    });

    units.value = formattedUnits;
    
    // 不再默认展开第一个单元
  };
  
  // 加载单元的子章节
  const loadUnitChapters = async (parentId, unitIndex) => {
    // 使用新的子章节列表API获取子章节
    const res = await LessonChapterApi.getChildLessonChapterListByParentId(parentId);
    if (res.code === 0 && res.data) {
      const childChapters = res.data;

      // 对子章节进行排序
      childChapters.sort((a, b) => (a.sort || 0) - (b.sort || 0));

      // 格式化子章节数据
      const formattedChildren = childChapters.map(child => ({
        id: child.id,
        title: child.name, // 使用name字段作为标题
        completed: child.status === 1,
        parentId: child.parentId,
        parentName: child.parentName
      }));

      // 更新对应单元的子章节数据
      if (units.value[unitIndex]) {
        units.value[unitIndex].chapters = formattedChildren;
      }
    }
  };

  // 切换单元的展开/折叠状态
  const toggleUnit = (index) => {
    // 切换单元的展开状态
    units.value[index].open = !units.value[index].open;
    
    // 如果是展开状态且该单元的子章节为空，则加载子章节
    if (units.value[index].open && (!units.value[index].chapters || units.value[index].chapters.length === 0)) {
      loadUnitChapters(units.value[index].id, index);
    }
  };

  // 跳转到章节详情
  const goToChapterDetail = (chapter) => {
    uni.navigateTo({
      url: `/pages/course/chapter-detail?id=${chapter.id}`
    });
  };

  // 下拉刷新
  const onRefresh = async () => {
    refreshing.value = true;
    await loadChapters();
    refreshing.value = false;
  };

  // 监听课程ID变化
  watch(() => props.id, (newVal) => {
    if (newVal) {
      loadChapters();
    }
  });

  // 组件挂载时加载数据
  onMounted(() => {
    const lessonId = getCurrentPageIdParam();
    
    if (lessonId) {
      // 加载章节数据
      loadChapters();
    }
  });
</script>

<style lang="scss" scoped>
  .chapter-container {
    min-height: 100vh;
    background-color: #F8FCFF;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .scroll-container {
    flex: 1;
    height: calc(100vh - 90rpx);
  }

  .units-container {
    padding: 20rpx;
  }

  .unit-item {
    margin-bottom: 15rpx;
    overflow: hidden;
  }

  .unit-header {
    display: flex;
    align-items: center;
    padding: 25rpx 30rpx;
  }

  .unit-number {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
    margin-right: 15rpx;
  }

  .unit-title {
    flex: 1;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
  }

  .unit-arrow {
    width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .arrow {
    width: 24rpx;
    height: 24rpx;
    border-left: 2px solid #ccc;
    border-bottom: 2px solid #ccc;
    transform: rotate(-45deg);
    transition: transform 0.3s;
  }
  
  .arrow-up {
    transform: rotate(-45deg);
  }
  
  .arrow-down {
    transform: rotate(135deg);
  }

  .chapter-list {
    padding: 0;
  }
  
  .chapter-empty {
    text-align: center;
    padding: 20rpx 0;
    color: #999;
    font-size: 28rpx;
  }

  .chapter-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    margin-left: 70rpx;
    border-left: 1px solid #eee;
    position: relative;
  }

  .chapter-indicator {
    position: absolute;
    left: -6rpx;
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .indicator-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #e0e0e0;
    
    &.active {
      background-color: #4CD964;
    }
  }

  .chapter-content {
    flex: 1;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
  }

  .chapter-number {
    font-size: 24rpx;
    color: #999;
    margin-right: 10rpx;
    min-width: 40rpx;
  }

  .chapter-name {
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
  }
  
  .loading-box {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    min-height: 300rpx;
  }
</style> 