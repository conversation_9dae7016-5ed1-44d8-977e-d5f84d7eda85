<template>
	<view class="container">
		<!-- Header -->
		<view class="header">
			<text class="title">เรียนภาษาไทย</text>
			<view class="search-wrapper">
				<view class="custom-search-bar">
					<image class="search-icon" :src="sheep.$url.cdn('/index/search-icon.png')" mode="widthFix" />
					<input
						type="text"
						placeholder="搜索学习集、课程..."
						@confirm="handleSearch"
						@blur="handleSearch"
						v-model="keyword"
					/>
				</view>
				<view class="icons">
					<view class="coconut" @click="sheep.$helper.inDevMsg()">
						<image :src="sheep.$url.cdn('/index/Coconut-%403x.png')" mode="widthFix" />
						<text>0</text>
					</view>
					<view class="integral" @click="sheep.$helper.inDevMsg()">
						<image :src="sheep.$url.cdn('/index/Integral-%403x.png.png')" mode="widthFix" />
						<text>0</text>
					</view>
				</view>
			</view>
		</view>

		<view class="main-content">
			<!-- 移除原有的Search Bar -->
			<!-- <search-bar /> -->
		</view>
		<!-- Learning Section -->
		<view class="flex justify-center">
			<view @click="goToSetDetail(currentLearningSet.id)" class="learning-section">
				<view class="learning-section-scarf">
					<view class="progress-text">
						<view class="title-row">
							<view class="set-title">
								<image :src="sheep.$url.cdn('/index/Books-%403x.png')" mode="widthFix" />
								<text>{{ currentLearningSet.name || '暂无学习集' }}</text>
							</view>
							<view class="change-book" @click.stop="handleChangeBook">
								<text>换书</text>
								<image :src="sheep.$url.cdn('/index/come.png')" mode="widthFix" />
							</view>
						</view>
						<view class="word-count">
							<view class="count-wrapper">
								<text
									class="count">{{ Math.min(currentLearningSet.masteredCount, currentLearningSet.totalCount) || 0 }}</text>
								<text class="total">/{{ currentLearningSet.totalCount || 0 }}词</text>
							</view>
							<view class="reset-book" @click.stop="handleResetWordSet">
								<text>重置词书</text>
							</view>
						</view>
					</view>
					<easy-progress mode="single" :percentage="currentLearningSet.progressPercentage"
						:showElephant="true" />
					<view class="buttons" :class="{ 'alternate-mode': !isDefaultMode }">
						<template v-if="isDefaultMode">
							<view class="button continue" @click.stop="continueLearn">继续新学</view>
							<view class="button review" @click.stop="reviewLearn">复习</view>
						</template>
						<template v-else>
							<view class="button pair" @click.stop="goToMatch">配对</view>
							<view class="button test" @click.stop="goToTest">测试</view>
						</template>
					</view>
					<view class="compare" @click.stop="toggleMode">
						<image class="toggle-mode" :src="sheep.$url.cdn('/index/Toggle_face%2024%403x.png')"
							mode="widthFix" />
						<text>{{ isDefaultMode ? '切换模式' : '返回词卡' }}</text>
					</view>
				</view>

			</view>

		</view>
		<!-- Cards Section -->
		<view class="main-content">
			<view class="cards-section">
				<view class="card-container">
					<view class="card-wrapper">
						<image class="card-icon-course-yellow" :src="sheep.$url.cdn('/index/course-yellow.png')"
							mode="widthFix" />
						<view class="card" @click="handleCommunityClick">
							<view class="title">
								<view class="top-wrapper">
									<text class="top">班级</text>
									<image class="title-icon" :src="sheep.$url.cdn('/index/come-yellow.png')"
										mode="widthFix" />
								</view>
								<text class="bottom">挖掘更多学习资源</text>
							</view>
						</view>
					</view>
				</view>
				<view class="card-container">
					<view class="card-wrapper">
						<image class="card-icon-note" :src="sheep.$url.cdn('/index/note.png')" mode="widthFix" />
						<view class="card" @click="handleSetClick">
							<view class="title">
								<view class="top-wrapper">
									<text class="top">学习集</text>
									<image class="title-icon" :src="sheep.$url.cdn('/index/come-orgin.png')"
										mode="widthFix" />
								</view>
								<text class="bottom">收藏·创建学习集</text>
							</view>
						</view>
					</view>
				</view>
			</view>

      <!-- Banner Section -->
      <view class="banner-section">
        <view class="banner" @click="handleBannerClick">
          <image class="banner-image"
            :src="sheep.$url.cdn('/index/Platinum-Lord-Tour-Coconut-Tree-%403x.png')"
            mode="widthFix"
          />
          <view class="title">
            <text class="top">泰语闯关</text>
            <text class="bottom">趣味学习闯关模式</text>
            <image class="button-top-icon" :src="sheep.$url.cdn('/index/come_green.png')"></image>
          </view>
        </view>
      </view>

    </view>

    <!-- Bottom Navigation -->
    <!-- <bottom-nav :currentTab="0" /> -->
  </view>
</template>

<script setup>
	import {
		ref,
		reactive,
		computed,
		onMounted
	} from 'vue';
	import {
		onShow
	} from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import SetApi from '@/sheep/api/set';
	import { SearchApi } from '@/sheep/api/set/search';
  import LessonApi from '@/sheep/api/course/lesson';
  import GroupApi from '@/sheep/api/group';

	// 搜索相关
	const keyword = ref('');

	const handleSearch = async () => {
		if (!keyword.value.trim()) {
			return;
		}

		// 搜索学习集
		const learningResult = await SearchApi.learning({
			keyword: keyword.value,
			pageNo: 1,
			pageSize: 8,
		});

		// 搜索课程（type=5为综合搜索）
		const courseResult = await LessonApi.searchLessons({
			type: 5,
			keyword: keyword.value,
			pageNo: 1,
			pageSize: 10,
		});

		// 检查搜索结果
		const hasLearningResults = learningResult.code === 0 && learningResult.data?.list?.length > 0;
		const hasCourseResults = courseResult.code === 0 && courseResult.data?.list?.length > 0;

		if (!hasLearningResults && !hasCourseResults) {
			sheep.$helper.toast('无搜索结果');
			return;
		}

		// 存储搜索结果到本地storage
		const searchResults = {
			learning: hasLearningResults ? learningResult.data : { list: [], total: 0 },
			course: hasCourseResults ? courseResult.data : { list: [], total: 0 },
			keyword: keyword.value
		};
		uni.setStorageSync('search_comprehensive_result', searchResults);

		sheep.$router.go(`/pages/index/search/list?keyword=${keyword.value}`);
	};

	const isDefaultMode = ref(true);

	const isLogin = computed(() => sheep.$store('user').isLogin)

	const goToSetDetail = async (id) => {
		if (id === '' || !id) {
			console.log('跳转的学习集id不正确')
			return;
		}
		// 正常跳转到学习集详情页
		sheep.$router.go(`/pages/set/word-card?id=${id}`);
	};

	// 当前学习集数据
	const currentLearningSet = reactive({
		id: '',
		name: '',
		masteredCount: 0,
		totalCount: 0,
		progressPercentage: 0
	});

	// 重置学习集数据
	const resetLearningSetData = () => {
		currentLearningSet.id = '';
		currentLearningSet.name = '';
		currentLearningSet.masteredCount = 0;
		currentLearningSet.totalCount = 0;
		currentLearningSet.progressPercentage = 0;
	};

	// 获取最新学习集和学习进度
	const fetchLearningSetData = async () => {
		if (!isLogin.value) {
			resetLearningSetData();
			return;
		}

    // 获取已掌握单词数量
    const getMasteredCount = (setData) => {
      // 直接返回的已掌握数量
      if (typeof setData.masteredWordCardCount === 'number') {
        return setData.masteredWordCardCount;
      }

			// 从卡片记录中计算
			if (setData.cardRecords && Array.isArray(setData.cardRecords)) {
				return setData.cardRecords.filter(record => record.status === 1).length;
			}

			// 从学习进度对象中获取
			if (setData.learningProgress && typeof setData.learningProgress.masteredCount === 'number') {
				return setData.learningProgress.masteredCount;
			}

			return 0;
		}

    // 获取总卡片数
    const getTotalCount = (setData) => {
      // 直接返回的总卡片数
      if (typeof setData.allWordCardCount === 'number') {
        return setData.allWordCardCount;
      }

      // 从卡片数组中计算
      if (setData.wordCards && Array.isArray(setData.wordCards)) {
        return setData.wordCards.length;
      }

      return 0;
    }

    // 更新学习集信息
    const updateSetInfo = (setData) => {
      if (!setData) {
        return;
      }

      // 更新基本信息
      currentLearningSet.id = setData.id;
      currentLearningSet.name = setData.title || setData.name || '学习集';

      // 更新数量信息
      currentLearningSet.masteredCount = getMasteredCount(setData);
      currentLearningSet.totalCount = getTotalCount(setData);

      // 计算进度百分比
      if (currentLearningSet.totalCount > 0) {
        currentLearningSet.progressPercentage = Math.min(
          Math.round((currentLearningSet.masteredCount / currentLearningSet.totalCount) * 100),
          100
        );
      } else {
        currentLearningSet.progressPercentage = 0;
      }
    }

    // 策略一：获取置顶学习集
    const topSetResult = await SetApi.getTopWordSet();
    if (topSetResult.code === 0 && topSetResult.data) {
      updateSetInfo(topSetResult.data);
      return;
    }

		// 策略二：尝试获取优质学习集列表的第一个
		const listResult = await SetApi.getWordSetPage({
			pageNum: 1,
			pageSize: 10
		});
		if (listResult.code !== 0 || !listResult.data?.list?.length) {
			return; // 列表为空，直接返回
		}

    // 获取第一个学习集的基本信息
    const firstSet = listResult.data.list[0];
    currentLearningSet.id = firstSet.id;
    currentLearningSet.name = firstSet.title || firstSet.name || '学习集';

    // 获取该学习集的详细信息
    const detailResult = await SetApi.getWordSet(currentLearningSet.id);
    if (detailResult.code !== 0 || !detailResult.data) {
      return;
    }

    // 确定详细数据来源
    let setData = detailResult.data;
    if (detailResult.data.list && detailResult.data.list.length > 0) {
      setData = detailResult.data.list[0];
    }

		// 更新学习集信息
		updateSetInfo(setData);
	};

	// 继续学习
	const continueLearn = () => {
		if (currentLearningSet.id) {
			// 跳转到学习页面，设置reset=false以保留单词掌握状态
			sheep.$router.go('/pages/set/car/car', {
				setId: currentLearningSet.id,
				currentReset: false,
				startMode: 'continue' // 显式指定为继续学习模式
			});
		} else {
			sheep.$helper.toast('请先选择学习集');
		}
	};

	// 复习
	const reviewLearn = () => {
		if (currentLearningSet.id) {
			sheep.$router.go(`/pages/set/car/car?setId=${currentLearningSet.id}&startMode=review`);
		} else {
			sheep.$helper.toast('暂无可复习的学习集');
		}
	};

	// 配对
	const goToMatch = () => {
		if (currentLearningSet.id) {
			sheep.$router.go(`/pages/set/match/ready?id=${currentLearningSet.id}`);
		} else {
			sheep.$helper.toast('暂无可配对的学习集');
		}
	};

	// 配对
	const goToTest = () => {
		if (currentLearningSet.id) {
			sheep.$router.go(`/pages/set/test/test?setId=${currentLearningSet.id}`);
		} else {
			sheep.$helper.toast('暂无可测试的学习集');
		}
	};

	const toggleMode = () => {
		isDefaultMode.value = !isDefaultMode.value;
	};

	// 换书功能
	const handleChangeBook = () => {
		sheep.$router.go('/pages/set/list?from=home&action=changeBook');
	};

	// 点击学习集
	const handleSetClick = () => {
		sheep.$router.go('/pages/set/list');
	};


	// 点击学习社区
  const handleCommunityClick = async () => {
    // 先检查本地存储中是否有最后访问的班级ID
    const lastGroupId = uni.getStorageSync('last_group_id');
    if (lastGroupId) {
      // 验证班级是否仍然有效
      const result = await GroupApi.getGroup(lastGroupId);
      if (result.code === 0) {
        // 班级有效，直接跳转
        sheep.$router.go(`/pages/group/index?id=${lastGroupId}`);
        return;
      } else {
        // 班级无效，清除缓存
        uni.removeStorageSync('last_group_id');
      }
    }

    // 如果没有有效的缓存班级，则获取班级列表
    // 先获取我加入的班级列表
    const joinedRes = await GroupApi.getMyJoinedGroups();

    // 如果有加入的班级，跳转到第一个班级
    if (joinedRes.data && joinedRes.data.length > 0) {
      const firstClass = joinedRes.data[0];
      uni.setStorageSync('last_group_id', firstClass.id);
      sheep.$router.go('/pages/group/index?id=' + firstClass.id);
      return;
    }

    // 如果没有加入的班级，获取我创建的班级列表
    const createdRes = await GroupApi.getMyCreatedGroups();

    // 如果有创建的班级，跳转到第一个班级
    if (createdRes.data && createdRes.data.length > 0) {
      const firstClass = createdRes.data[0];
      uni.setStorageSync('last_group_id', firstClass.id);
      sheep.$router.go(`/pages/group/index?id=` + firstClass.id);
      return;
    }

    // 如果既没有加入的班级也没有创建的班级，显示弹出框询问是否创建班级
    uni.showModal({
      title: '暂无班级',
      content: '您还没有加入或创建任何班级，是否前去创建班级？',
      confirmText: '创建班级',
      cancelText: '取消创建',
      success: ({ confirm }) => {
        if (confirm) {
          sheep.$router.go('/pages/group/switch');
        }
      }
    });
  };

	const handleBannerClick = () => {
		sheep.$helper.inDevMsg()
	};

	const handleResetWordSet = () => {
		// 检查是否有有效的学习集
		if (!currentLearningSet.id) {
			sheep.$helper.toast('暂无可重置的学习集');
			return;
		}

		// 显示确认弹窗
		uni.showModal({
			title: '重置确认',
			content: '重置后将清空当前所有学习记录，确定要重置吗？',
			confirmText: '确认重置',
			confirmColor: '#FF6B67',
			cancelText: '取消',
			success: async (res) => {
				if (!res.confirm) {
					return;
				}

				// 调用重置API
				const {
					code
				} = await SetApi.resetWordSet(currentLearningSet.id);
				if (code !== 0) {
					return;
				}
				// 重置本地状态
				currentLearningSet.masteredCount = 0;
				currentLearningSet.progressPercentage = 0;
				await fetchLearningSetData();
			}
		});
	};

	// 监听学习集更新事件
	onMounted(() => {
		uni.$on('refreshLearningSet', () => {
			fetchLearningSetData();
		});
	});

  // 处理页面滚动到底部的事件
  function handleReachBottom() {
    console.log('首页触发了滚动到底部事件');
    // 首页的滚动到底部逻辑，可以根据需要添加
  }

  // 在页面显示时更新底部导航状态和数据
  onShow(() => {
    // 每次页面显示都刷新数据，确保从其他模式返回时数据是最新的
    fetchLearningSetData();
  });

  // #ifdef MP-WEIXIN
  // 启用微信小程序分享功能
  // 启用微信小程序分享功能
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline'],
  });
  // #endif

  // 暴露给父组件的方法
  defineExpose({
    // 标签页激活时调用
    onTabActivated(isFirstActivation) {
      // 每次激活都获取数据
      fetchLearningSetData();
    },

    // 标签页停用时调用
    onTabDeactivated() {
    //   console.log('首页被停用');
    },

    // 页面显示时调用
    onPageShow() {
    //   console.log('首页所在的页面显示');
      // 页面重新显示时可能需要刷新数据，根据实际情况决定
    },

    // 页面隐藏时调用
    onPageHide() {
    //   console.log('首页所在的页面隐藏');
    },

    // 处理页面滚动到底部的事件
    handleReachBottom
  });
</script>

<style scoped lang="scss">
	.container {
		width: 100%;
		margin: 0 auto;
		min-height: 100%;
		position: relative;

		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			width: 457rpx;
			height: 395rpx;
			background: url('#{$baseImgUrl}/index/badge-logo.png') no-repeat;
			background-size: 457rpx auto;
			background-position: left -50rpx;
			overflow: hidden;
		}

		background: linear-gradient(to bottom, rgb(38, 158, 235) 0%, rgb(38, 158, 235) 200rpx, rgba(38, 158, 235, 0.9) 250rpx, rgba(38, 158, 235, 0.7) 300rpx, rgba(38, 158, 235, 0.5) 350rpx, rgba(38, 158, 235, 0.3) 400rpx, rgba(38, 158, 235, 0.1) 430rpx, white 454rpx, #f6f6f6 100%);
	}

	.main-content {
		width: 100vw;
		padding: 0 5vw;
		box-sizing: border-box;
	}

	.toggle-mode {
		margin-right: 10rpx;
	}

	/* Header Section */
	.header {
		padding: 0 52rpx 33rpx 31rpx;
		color: #fff;
		display: flex;
		flex-direction: column;
		position: relative;
		box-sizing: border-box;
	}

	.header .title {
		text-indent: 2em;
		font-size: 36rpx;
		font-weight: bold;
		margin: 120rpx 0 50rpx 0;
	}

	.header .search-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.header .custom-search-bar {
		display: flex;
		align-items: center;
		background-color: #FFFFFF;
		border-radius: 50rpx;
		padding: 14rpx 24rpx;
		flex: 1;
		margin-right: 20rpx;
	}

	.header .search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 12rpx;
		opacity: 0.8;
	}

	.header .custom-search-bar input {
		flex: 1;
		background: none;
		border: none;
		font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22rpx;
    color: #000000;
	}

	.header .custom-search-bar :deep(input::placeholder) {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22rpx;
    color: #B8B8B8;
	}

	.header .icons {
		display: flex;
		align-items: center;
	}

	.header .icons image {
		width: 48rpx;
		height: 48rpx;
	}

	.header .icons .coconut {
		display: flex;
		align-items: center;
		font-size: 28rpx;
    margin-right: 53rpx;
	}

  .header .icons .integral {
    display: flex;
    align-items: center;
    font-size: 28rpx;
  }

	.header .icons .coconut text,
	.header .icons .integral text {
		margin-left: 8rpx;
	}

	/* Learning Section */
	.learning-section {
		width: 100%;
		aspect-ratio: 1157/701;
		background: url('#{$baseImgUrl}/index/setback.png') no-repeat;
		background-size: calc(100vw - 10vw) 100%;
		background-position-x: center;
		position: relative;
		box-sizing: border-box;
		color: #fff;
	}

	.learning-section .learning-section-scarf {
		padding: 0vw 9vw;
		padding-top: 4vw;
		box-sizing: border-box;
		height: 100%;
		width: 100%;
	}

	.learning-section .progress-text {
		display: flex;
		flex-direction: column;
		// margin-top: 10rpx;
	}

	.learning-section .progress-text .title-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		// margin-top: 10rpx;
	}

	.learning-section .progress-text .title-row .set-title {
		display: flex;
		align-items: center;
		flex: 1;
		min-width: 0;
	}

	.learning-section .progress-text .title-row .set-title text {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		padding-right: 20rpx;
		flex: 1;
	}

	.learning-section .progress-text .title-row .set-title image {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}

	.learning-section .progress-text .title-row>text {
		font-size: 32rpx;
	}

	.learning-section .progress-text .title-row .change-book {
		color: #fff;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		gap: 4.5vw;
		width: 20%;
		color: #60B4E8;
		font-weight: bold;
	}

	.learning-section .progress-text .title-row .change-book image {
		width: 19rpx;
		height: 25rpx;
	}

	.learning-section .progress-text .word-count {
		padding: 7vw 0 5vw 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.learning-section .progress-text .word-count .count-wrapper {
		display: flex;
		align-items: center;
	}

	.learning-section .progress-text .word-count .count {
		font-size: 36rpx;
		font-weight: bold;
	}

	.learning-section .progress-text .word-count .total {
		font-size: 32rpx;
		opacity: 0.8;
	}

	.learning-section .progress-text .word-count .reset-book {
		color: #fff;
		font-size: 24rpx;
		padding: 8rpx 24rpx;
		background-color: rgba(152, 215, 255, 0.9);
		border-radius: 40rpx;
	}

	.learning-section .buttons {
		display: flex;
		justify-content: space-between;
		margin-top: 5vw;
	}

	.learning-section .buttons .button {
		width: 40vw;
		height: 12vw;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		position: relative;
		overflow: hidden;
		transition: transform 0.2s ease, filter 0.2s ease;
	}

	.learning-section .buttons .continue {
		background: url('#{$baseImgUrl}/index/The-front-page%20rectangle-is%20slanted-to-the-right.png') no-repeat center center / cover;
	}

	.learning-section .buttons .review {
		background: url('#{$baseImgUrl}/index/The-front-page%20rectangle-is%20slanted-to-the-left.png') no-repeat center center / cover;
	}

	.learning-section .buttons .pair {
		background: url('#{$baseImgUrl}/index/<EMAIL>') no-repeat center center / cover;
	}

	.learning-section .buttons .test {
		background: url('#{$baseImgUrl}/index/<EMAIL>') no-repeat center center / cover;
	}

	.learning-section .compare {
		color: #fff;
		padding: 16rpx;
		text-align: center;
		font-size: 26rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 2rpx auto 0;
		width: fit-content;
	}

	.learning-section .compare image {
		width: 34rpx;
	}

	/* Cards Section */
	.cards-section {
		width: 90vw;
		display: flex;
		justify-content: space-between;
	}

	.cards-section .card-container {
		width: 42vw;
		height: 300rpx;
		/* 设置固定高度以便定位 */
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.cards-section .card-wrapper {
		position: relative;
		width: 100%;
		height: 100%;
	}

	.cards-section .card {
		background-repeat: no-repeat;
		background-size: 100% 100%;
		background-position: center;
		width: 100%;
		height: 180rpx;
		/* 调整高度 */
		position: absolute;
		top: 60rpx;
		/* 从上方留出空间让图标穿出 */
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-end;
		/* 将内容推到底部 */
		padding-bottom: 20rpx;
		border-radius: 28rpx;
		/* 圆角 */
	}

	.cards-section .card-container:first-child .card {
		height: 200rpx;
		background-image: url('#{$baseImgUrl}/index/yello-rectangular.png');
	}

	.cards-section .card-container:last-child .card {
		height: 200rpx;
		background-image: url('#{$baseImgUrl}/index/pink-rectangular.png');
	}

	.cards-section .card-icon {
		width: 130rpx;
		height: 130rpx;
		position: absolute;
		top: 10rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 3;
		border-radius: 50%;
		padding: 15rpx;
	}

	.card-icon-course-yellow {
		width: 160rpx;
		height: 139rpx;
		position: absolute;
		top: 10rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 3;
		border-radius: 50%;
	}

	.card-icon-note {
		width: 160rpx;
		height: 141rpx;
		position: absolute;
		top: 10rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 3;
		border-radius: 50%;
	}

	.cards-section .card .title {
		display: flex;
		flex-direction: column;
		width: 100%;
		position: relative;
		z-index: 2;
	}

	.cards-section .card .title .top-wrapper {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.cards-section .card .title .top {
		color: #333;
		font-size: 34rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		padding-left: 30rpx;
	}

	.cards-section .card .title .title-icon {
		width: 21rpx;
		height: 21rpx;
	}

	.cards-section .card .title .bottom {
		color: #999;
		font-size: 26rpx;
		padding-left: 30rpx;
	}

	.banner-section {
		margin-top: 45rpx;
	}

	/* Banner Section */
	.banner-section .banner {
		background: url('#{$baseImgUrl}/index/new-rectangular.png') no-repeat center center / cover;
		border-radius: 28rpx;
		width: 90vw;
		height: 27.5vw;
		display: flex;
		align-items: center;
		color: #fff;
		/* 增强阴影效果 */
	}

	/* 测试代码入口样式 */
	.banner-section .test-code-banner {
		background: #ffc9c9;
		margin-top: 30rpx;
	}

	.banner-section .banner .title {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		margin-left: 3vw;
		position: relative;
		flex: 1;
	}

	.button-top-icon {
		width: 40rpx;
		height: 40rpx;
		position: absolute;
		right: 5vw;
		top: 50%;
		transform: translateY(-50%);
	}

	.top-icon {
		width: 21rpx;
		height: 21rpx;
		position: absolute;
		right: 5vw;
		top: 50%;
		transform: translateY(-50%);
	}

	.banner-section .banner .banner-image {
		width: 220rpx;
		position: relative;
		right: 2vw;
		bottom: -2vw;
	}

	.banner-section .banner .title .top {
		color: #000;
		font-size: 40rpx;
		font-weight: bold;
	}

	.banner-section .banner .title .bottom {
		color: #b5b5b5;
		font-size: 24rpx;
	}

	@keyframes elephantWalk {
		0% {
			transform: rotate(-10deg) translateY(0);
		}

		50% {
			transform: rotate(10deg) translateY(-2rpx);
		}

		100% {
			transform: rotate(-10deg) translateY(0);
		}
	}
</style>
