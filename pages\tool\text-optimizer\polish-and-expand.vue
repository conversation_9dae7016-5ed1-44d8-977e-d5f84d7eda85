<template>
  <view class="polish-and-expand">
    <easy-navbar :title="(activeTab === 0 ? '泰语润色' : '泰语扩写')" :transparent="true" />

    <!-- 标签页切换 -->
    <view class="tab-box">
      <view :class="{ active: activeTab === 0 }" class="tab-item" @click="switchTab(0)">
        泰语润色
        <image class="pen-icon" :style="{ opacity: activeTab === 0 ? 1 : 0 }"
          :src="sheep.$url.cdn('/text-optimizer/icon.png')"></image>
      </view>
      <!--      分割线-->
      <view class="divider"></view>

      <view :class="{ active: activeTab === 1 }" class="tab-item" @click="switchTab(1)">
        泰语扩写
        <image class="pen-icon" :style="{ opacity: activeTab === 1 ? 1 : 0 }"
          :src="sheep.$url.cdn('/text-optimizer/icon.png')"></image>
      </view>
    </view>

    <!-- 输入框区域 -->
    <view class="content-area">
      <view class="content-box">
        <view v-if="!showResult" class="input-container">
          <textarea v-model="content" class="input-area" maxlength="2000"
            :placeholder="activeTab === 0 ? '请输入将被润色的作文，我能根据你修改词句表达，优化语言表达，提升作文文采。' : '请输入待修改的作文，我和你的创作一定会更加精彩的!'"
            placeholder-style="color: #999; font-size: 30rpx;" @input="onInput"></textarea>

          <!-- 底部控制区 -->
          <view class="controls-bar">
            <!-- 历史记录按钮 -->
            <view class="history-btn" @click="showHistory">
              <image class="history-icon" :src="sheep.$url.cdn('/text-optimizer/history.png')"></image>
            </view>

            <!-- 字数统计 -->
            <view class="word-count">
              <text>{{ contentLength }}/2000</text>
            </view>
          </view>
        </view>
        <!-- 润色/扩写结果展示 -->
        <view v-if="showResult" class="result-display">
          <!-- 原文展示 -->
          <view class="text-section">
            <view class="section-title">原文:</view>
            <view class="section-content">
              {{ typingText.original }}<span v-if="typingText.original.length > 0 && (isTyping.original || showCursor.original)" class="cursor"></span>
            </view>
            <view class="audio-control-area">
              <!--          <view class="text-audio-btn" @tap="playAudio(originalText, 'original')">
                <image class="audio-icon" :src="getPlayIconSource('original')" mode="heightFix"></image>
              </view> -->
              <view></view>
              <view class="text-edit-btn" @tap="reEdit">
                <image class="edit-icon" :src="sheep.$url.cdn('/text-optimizer/edit.png')"></image>
              </view>
            </view>
          </view>

          <!-- 分隔线 -->
          <view class="dashed-divider"></view>

          <!-- 润色后文本 -->
          <view class="text-section">
            <view class="result-title">{{ activeTab === 0 ? '润色' : '扩写' }}后:</view>
            <view class="section-content">
              {{ typingText.result }}<span v-if="typingText.result.length > 0 && (isTyping.result || showCursor.result)" class="cursor"></span>
            </view>
            <view class="audio-control-area">
              <!-- <view class="text-audio-btn" @tap="playAudio(activeTab === 0 ? polishResult : expandResult, 'result')">
                <image class="audio-icon" :src="getPlayIconSource('result')" mode="heightFix"></image>
              </view> -->
              <view></view>
              <view class="text-copy-btn" @tap="copyResult(activeTab === 0 ? polishResult : expandResult)">
                <image class="copy-icon" :src="sheep.$url.cdn('/text-optimizer/copy_w.png')"></image>
              </view>
            </view>
          </view>

          <!-- 虚线分隔 -->
          <view class="dashed-divider"></view>

          <!-- 润色说明 -->
          <view class="text-section" v-if="suggestionExplanation">
            <view class="suggestion-title">{{ activeTab === 0 ? '润色' : '扩写' }}说明:</view>
            <view class="section-content-suggestion">
              <view class="suggestion-text">{{ typingText.suggestion }}<span v-if="typingText.suggestion.length > 0 && (isTyping.suggestion || showCursor.suggestion)" class="cursor"></span></view>
            </view>
          </view>

          <!-- 底部控制区 -->
          <view class="controls-bar">
            <!-- 历史记录按钮 -->
            <view class="history-btn" @click="showHistory">
              <image class="history-icon" :src="sheep.$url.cdn('/text-optimizer/history.png')"></image>
            </view>

            <!-- 字数统计 -->
            <view class="word-count">
              <text>{{ content.length }}/2000</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view :style="{ paddingBottom: safeAreaBottom + 'px' }" class="btn-box">
        <button v-if="!showResult" class="polish-btn" @tap="submitPolish">{{ activeTab === 0 ? '一键润色' :
          '一键扩写' }}</button>
      </view>
    </view>
    <su-popup ref="historyPopup" type="right">
      <view class="history-header">
        <text class="history-title">{{ activeTab === 0 ? '润色' : '扩写' }}记录</text>
        <view class="clear-history" @tap="clearHistory">
          <text>清空记录</text>
        </view>
      </view>
      <view class="history-popup">
        <scroll-view class="history-list" scroll-y :show-scrollbar="true" :enhanced="true" :bounces="true"
          :refresher-enabled="true" :refresher-triggered="refreshing" @refresherrefresh="onRefresh"
          @scrolltolower="onHistoryScrollToLower">
          <!-- 空状态显示 -->
          <s-empty class="empty-history" v-if="displayHistoryList.length === 0"
            :text="'暂无' + (activeTab === 0 ? '润色' : '扩写') + '记录'" />

          <swipe-action v-else v-for="(item, index) in displayHistoryList" :key="index"
            :buttons="[{ text: '删除', backgroundColor: '#ff5252', width: 150 }]" @button-click="deleteHistoryItem(item)">
            <view class="history-item" @tap="selectHistoryItem(item)">
              <view class="history-item-content">
                <text class="history-text">{{ activeTab === 0 ? item.originalText : item.original }}</text>
              </view>
              <text class="history-time">{{ formatTime(item.createTime) }}</text>
            </view>
          </swipe-action>
          <!-- 加载更多 -->
          <uni-load-more v-if="displayHistoryList.length > 0" :status="loadStatus" :content-text="{
            contentdown: '上拉加载更多',
          }" @clickLoadMore="loadMore" />
        </scroll-view>
      </view>
    </su-popup>
  </view>
</template>


<script setup>
import { ref, getCurrentInstance, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import polishTextApi from '@/sheep/api/text-optimizer/polishText';
import ConversationApi from '@/sheep/api/text-optimizer/conversation';
import extendTextApi from '@/sheep/api/text-optimizer/expandText';
import sheep from '@/sheep';
import { AiModelEnum } from '@/sheep/util/const';
import { getTtsConfig, TtsTypeEnum } from '@/sheep/util/language-detector';
import NlsApi from '@/sheep/api/voice/nls';
import SwipeAction from '@/components/swipe-action/swipe-action.vue';
import { createStreamHandler } from '@/sheep/util/stream-parser';

const content = ref('');
const contentLength = ref(0);
const activeTab = ref(0);
const safeAreaBottom = ref(0);
const screenWidth = ref(375);
const fontScale = ref(1);
const polishResult = ref(''); // 润色后的文本
const expandResult = ref(''); // 扩写后的文本
const originalText = ref(''); // 原始文本
const suggestionExplanation = ref(''); // 润色建议
const polishConversationId = ref(''); // 润色会话ID，初始为空
const expandConversationId = ref(''); // 扩写会话ID，初始为空
const showResult = ref(false); // 是否显示结果
const isProcessing = ref(false); // 是否正在处理中
let audioContext = null; // 音频上下文
const currentPlayingText = ref(null); // 当前正在播放的文本
const isPlaying = ref(false); // 是否正在播放
const playingType = ref(null); // 正在播放的类型：'original' 或 'result'
const historyList = ref([]); // 历史记录列表
const displayHistoryList = ref([]); // 当前显示的历史记录
const historyHasMore = ref(true); // 是否还有更多历史记录
const instance = getCurrentInstance(); // 获取组件实例
const loading = ref(false);
const refreshing = ref(false);
const loadStatus = ref('more'); // 加载状态：more-加载前，loading-加载中，noMore-没有更多了

//历史记录请求体
const historyRequest = ref({
  pageNo: 1,
  pageSize: 7,
  userId: "",
  type: "",
  modelId: "",
  createTime: []
});

// 定义标记常量
const MARKERS = {
  O: { START: '[O]', END: '[/O]' },
  R: { START: '[R]', END: '[/R]' },
  E: { START: '[E]', END: '[/E]' }
};

// 创建改进的标记解析器
const createImprovedMarkerParser = (markers) => {
  // 存储处理过的内容
  let processedContent = ''; // 已处理并可以丢弃的内容
  let activeContent = '';    // 活动内容（当前需要保持的内容）
  const maxBufferSize = 50000; // 最大缓冲区大小，可以根据实际情况调整
  
  // 存储解析结果
  const parsedContent = {
    O: { value: '', complete: false, partialValue: '' },
    R: { value: '', complete: false, partialValue: '' },
    E: { value: '', complete: false, partialValue: '' }
  };

  // 存储标记状态
  const markerState = {
    O: { inProgress: false, startIndex: -1, absoluteStartIndex: -1 },
    R: { inProgress: false, startIndex: -1, absoluteStartIndex: -1 },
    E: { inProgress: false, startIndex: -1, absoluteStartIndex: -1 }
  };
  
  // 跟踪绝对位置（考虑已处理的内容）
  let totalProcessedLength = 0;
  
  // 压缩缓冲区，释放内存
  const compressBuffer = () => {
    // 找出所有正在进行中的标记的最小开始位置
    let minActivePosition = activeContent.length;
    
    for (const type in markerState) {
      if (markerState[type].inProgress && markerState[type].startIndex !== -1) {
        minActivePosition = Math.min(minActivePosition, markerState[type].startIndex);
      }
    }
    
    // 如果没有活动的标记，或者缓冲区小于阈值，不做任何处理
    if (minActivePosition === activeContent.length || activeContent.length <= maxBufferSize) {
      return;
    }
    
    // 保留足够的上下文，防止标记被截断
    const safeMinPosition = Math.max(0, minActivePosition - 100);
    
    // 移动内容
    const contentToMove = activeContent.substring(0, safeMinPosition);
    processedContent += contentToMove;
    activeContent = activeContent.substring(safeMinPosition);
    
    // 调整所有标记的位置
    for (const type in markerState) {
      if (markerState[type].inProgress && markerState[type].startIndex !== -1) {
        markerState[type].startIndex -= safeMinPosition;
        markerState[type].startIndex = Math.max(0, markerState[type].startIndex);
      }
    }
    
    // 更新总处理长度
    totalProcessedLength += safeMinPosition;
  };
  
  // 解析内容
  const parseContent = (chunk) => {
    // 累积内容到活动缓冲区
    activeContent += chunk;
    let updatedTypes = [];
    
    // 如果活动缓冲区过大，压缩它
    if (activeContent.length > maxBufferSize) {
      compressBuffer();
    }
    
    // 处理新增内容
    const processChunk = () => {
      // 尝试解析所有标记类型
      for (const type in markers) {
        if (parsedContent[type].complete) continue;
        
        const startMarker = markers[type].START;
        const endMarker = markers[type].END;
        
        // 如果当前没有正在处理的标记区块，尝试找开始标记
        if (!markerState[type].inProgress) {
          const startIndex = activeContent.indexOf(startMarker);
          if (startIndex === -1) continue;
          
          // 找到开始标记，设置状态
          markerState[type].inProgress = true;
          markerState[type].startIndex = startIndex;
          markerState[type].absoluteStartIndex = totalProcessedLength + startIndex;
          
          // 记录已更新类型（开始新标记）
          updatedTypes.push(type);
        }
        
        // 如果正在处理标记区块，尝试找结束标记
        if (markerState[type].inProgress) {
          const contentStartIndex = markerState[type].startIndex + startMarker.length;
          const endIndex = activeContent.indexOf(endMarker, contentStartIndex);
          
          // 如果找到结束标记，完成该区块解析
          if (endIndex !== -1) {
            // 提取标记之间的内容
            const content = activeContent.substring(contentStartIndex, endIndex);
            
            // 更新解析结果
            parsedContent[type].value = content;
            parsedContent[type].complete = true;
            parsedContent[type].partialValue = '';
            
            // 重置状态
            markerState[type].inProgress = false;
            markerState[type].startIndex = -1;
            markerState[type].absoluteStartIndex = -1;
            
            // 记录已更新类型
            updatedTypes.push(type);
          } else if (markerState[type].inProgress) {
            // 如果没找到结束标记，更新部分值以供流式显示
            const partialContent = activeContent.substring(contentStartIndex);
            
            // 只有当部分内容有变化时才更新
            if (partialContent !== parsedContent[type].partialValue) {
              parsedContent[type].partialValue = partialContent;
              // 记录已更新类型（部分更新）
              updatedTypes.push(type);
            }
          }
        }
      }
    };
    
    // 处理新增内容
    processChunk();
    
    return {
      // 返回当前解析状态
      isComplete: Object.values(parsedContent).every(item => item.complete),
      parsedContent,
      updatedTypes // 返回已更新的类型
    };
  };
  
  // 重置解析器
  const reset = () => {
    processedContent = '';
    activeContent = '';
    totalProcessedLength = 0;
    
    for (const type in parsedContent) {
      parsedContent[type].value = '';
      parsedContent[type].partialValue = '';
      parsedContent[type].complete = false;
    }
    
    for (const type in markerState) {
      markerState[type].inProgress = false;
      markerState[type].startIndex = -1;
      markerState[type].absoluteStartIndex = -1;
    }
  };
  
  // 强制完成解析（用于流结束时处理不完整的内容）
  const forceComplete = () => {
    let updatedTypes = [];
    
    for (const type in markers) {
      if (!parsedContent[type].complete && markerState[type].inProgress) {
        // 使用部分值作为最终值
        if (parsedContent[type].partialValue) {
          parsedContent[type].value = parsedContent[type].partialValue;
          parsedContent[type].complete = true;
          updatedTypes.push(type);
          
          // 重置状态
          markerState[type].inProgress = false;
          markerState[type].startIndex = -1;
          markerState[type].absoluteStartIndex = -1;
        }
      }
    }
    
    return updatedTypes;
  };
  
  // 获取当前显示值（包括完成值和部分值）
  const getDisplayValue = (type) => {
    // 如果有完整值，返回完整值
    if (parsedContent[type].complete) {
      return parsedContent[type].value;
    }
    // 如果有部分值，返回部分值
    if (parsedContent[type].partialValue) {
      return parsedContent[type].partialValue;
    }
    // 默认返回空字符串
    return '';
  };
  
  // 获取当前解析状态的诊断信息
  const getDiagnostics = () => {
    return {
      processedContentLength: processedContent.length,
      activeContentLength: activeContent.length,
      totalProcessedLength: totalProcessedLength,
      markerState: JSON.parse(JSON.stringify(markerState)),
      completionStatus: Object.fromEntries(
        Object.keys(parsedContent).map(type => [
          type, 
          { 
            complete: parsedContent[type].complete,
            valueLength: parsedContent[type].value.length,
            partialValueLength: parsedContent[type].partialValue.length
          }
        ])
      )
    };
  };
  
  return {
    parseContent,
    parsedContent,
    reset,
    forceComplete,
    getDisplayValue,
    getDiagnostics,
    // 提供访问累积内容的方法，用于调试
    getAccumulatedContent: () => processedContent + activeContent,
    // 提供原始文本的引用，保持兼容性
    originalText: { get value() { return getDisplayValue('O'); } }
  };
};

// 使用改进的标记解析器
const markerParser = createImprovedMarkerParser(MARKERS);
// 监听解析结果变化并更新视图变量
const updateViewVariables = (updatedTypes) => {
  // 如果指定了更新类型，只更新这些类型
  const typesToUpdate = updatedTypes || ['O', 'R', 'E'];
  
  // 更新视图变量
  if (typesToUpdate.includes('O')) {
    const oText = markerParser.getDisplayValue('O');
    originalText.value = oText;
    startTypingEffect('original', oText);
  }
  
  if (typesToUpdate.includes('R')) {
    const resultText = markerParser.getDisplayValue('R');
    if (activeTab.value === 0) {
      polishResult.value = resultText;
      startTypingEffect('result', resultText);
    } else {
      expandResult.value = resultText;
      startTypingEffect('result', resultText);
    }
  }
  
  if (typesToUpdate.includes('E')) {
    const eText = markerParser.getDisplayValue('E');
    suggestionExplanation.value = eText;
    startTypingEffect('suggestion', eText);
  }
};

// 三段打字机相关变量
const typingText = ref({ original: '', result: '', suggestion: '' }); // 当前已显示文本
const fullText = ref({ original: '', result: '', suggestion: '' });   // 完整流式文本
const typingTimer = ref({ original: null, result: null, suggestion: null });
const isTyping = ref({ original: false, result: false, suggestion: false });
const showCursor = ref({ original: true, result: true, suggestion: true });
let cursorBlinkTimer = null;
let hasHideLoading = false;

function startTypingEffect(section, newText) {
  // 如果内容没变，直接返回
  if (fullText.value[section] === newText) return;

  // 如果新内容比已打印内容短，说明是回退或重置，清空并重打
  if (newText.length < typingText.value[section].length) {
    clearTypingEffect(section);
    fullText.value[section] = newText;
    typingText.value[section] = '';
    isTyping.value[section] = true;
    showCursor.value[section] = true;
    typeNextChar(section);
    return;
  }

  // 如果新内容比已打印内容长，继续追加打字
  if (newText.length > typingText.value[section].length) {
    fullText.value[section] = newText;
    if (!isTyping.value[section]) {
      isTyping.value[section] = true;
      showCursor.value[section] = true;
      typeNextChar(section);
    }
  }
}

function typeNextChar(section) {
  const remain = fullText.value[section].length - typingText.value[section].length;
  if (remain > 0) {
    // 每次加2个字，减少页面重绘
    typingText.value[section] += fullText.value[section].slice(typingText.value[section].length, typingText.value[section].length + 2);
    // 首字渲染时关闭 loading
    if (
      !hasHideLoading &&
      typingText.value[section].length > 0
    ) {
      uni.hideLoading();
      hasHideLoading = true;
    }
    let baseSpeed = 40;
    let speed = Math.max(20, baseSpeed - Math.floor(fullText.value[section].length / 20));
    typingTimer.value[section] = setTimeout(() => typeNextChar(section), speed);
  } else {
    isTyping.value[section] = false;
    typingTimer.value[section] = null;
    showCursor.value[section] = false;
  }
}

// 优化清理打字机定时器和内容，防止残影
function clearTypingEffect(section) {
  if (section) {
    if (typingTimer.value[section]) {
      clearTimeout(typingTimer.value[section]);
      typingTimer.value[section] = null;
    }
    isTyping.value[section] = false;
    typingText.value[section] = '';
    fullText.value[section] = '';
  } else {
    Object.keys(typingTimer.value).forEach(key => {
      if (typingTimer.value[key]) {
        clearTimeout(typingTimer.value[key]);
        typingTimer.value[key] = null;
      }
      isTyping.value[key] = false;
      typingText.value[key] = '';
      fullText.value[key] = '';
    });
  }
}

// 生命周期钩子
onLoad((options) => {
  // 获取状态栏高度
  const systemInfo = sheep.$helper.sys();
  safeAreaBottom.value = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom || 0 : 0;
  screenWidth.value = systemInfo.screenWidth || 375;

  // 动态计算字体大小基准
  fontScale.value = screenWidth.value / 375;
  // 根据传入的tabIndex设置当前激活的标签页
  if (options.tabIndex !== undefined) {
    activeTab.value = Number(options.tabIndex);
  }
});

onMounted(() => {
  cursorBlinkTimer = setInterval(() => {
    Object.keys(showCursor.value).forEach(key => {
      if (isTyping.value[key]) showCursor.value[key] = !showCursor.value[key];
    });
  }, 500);
});
onUnmounted(() => {
  clearInterval(cursorBlinkTimer);
  clearTypingEffect();
});

// 切换tab/重新编辑时彻底清理打字机内容，防止残影
const switchTab = (index) => {
  clearTypingEffect();
  activeTab.value = index;
  reEdit();
  checkAndCreateConversation();
  historyList.value = [];
};

const onInput = (e) => {
  contentLength.value = e.detail.value.length;
};

// 显示历史记录弹窗
const showHistory = async () => {
  try {
    // 重置分页参数
    historyRequest.value.pageNo = 1;
    historyRequest.value.pageSize = 7;
    loadStatus.value = 'loading'; // 设置加载状态为加载中

    // 根据当前激活的标签页选择不同的API
    const api = activeTab.value === 0 ? polishTextApi : extendTextApi;
    const { data } = await api.getResultPage(historyRequest.value);

    if (data?.list) {
      historyList.value = data.list;
      displayHistoryList.value = data.list;
      historyHasMore.value = data.total > data.list.length;
      loadStatus.value = historyHasMore.value ? 'more' : 'noMore'; // 根据是否有更多数据设置状态
      loading.value = false;
    } else {
      historyList.value = [];
      displayHistoryList.value = [];
      historyHasMore.value = false;
      loadStatus.value = 'noMore'; // 没有更多数据
    }

    // 打开历史记录弹窗
    const popup = instance.refs.historyPopup;
    if (popup) {
      popup.open();
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    sheep.$helper.toast('获取历史记录失败');
    loadStatus.value = 'more'; // 加载失败时重置状态
  }
};

// 加载更多历史记录
const loadMoreHistory = async () => {
  if (!historyHasMore.value || loading.value) return;

  try {
    loading.value = true;
    loadStatus.value = 'loading'; // 设置加载状态为加载中
    historyRequest.value.pageNo++;

    // 根据当前激活的标签页选择不同的API
    const api = activeTab.value === 0 ? polishTextApi : extendTextApi;
    const { data } = await api.getResultPage(historyRequest.value);

    if (data?.list?.length) {
      displayHistoryList.value = [...displayHistoryList.value, ...data.list];
      // 当返回的数据小于等于 pageSize 时,说明没有更多数据了
      historyHasMore.value = data.list.length >= historyRequest.value.pageSize;
      loadStatus.value = historyHasMore.value ? 'more' : 'noMore'; // 根据是否有更多数据设置状态
    } else {
      historyHasMore.value = false;
      loadStatus.value = 'noMore'; // 没有更多数据
    }
  } catch (error) {
    console.error('加载更多历史记录失败:', error);
    sheep.$helper.toast('加载更多失败');
    loadStatus.value = 'more'; // 加载失败时重置状态
  } finally {
    loading.value = false;
  }
};

// 点击加载更多按钮触发
const loadMore = () => {
  if (historyHasMore.value && !loading.value) {
    loadMoreHistory();
  }
};

// 滚动到底部时触发加载更多
const onHistoryScrollToLower = () => {
  if (historyHasMore.value && !loading.value) {
    loadStatus.value = 'loading';
    loadMoreHistory();
  }
};

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true;
  historyRequest.value.pageNo = 1;

  try {
    // 根据当前激活的标签页选择不同的API
    const api = activeTab.value === 0 ? polishTextApi : extendTextApi;
    const { data } = await api.getResultPage(historyRequest.value);

    if (data?.list) {
      historyList.value = data.list;
      displayHistoryList.value = data.list;
      historyHasMore.value = data.total > data.list.length;
      loadStatus.value = historyHasMore.value ? 'more' : 'noMore';
    } else {
      historyList.value = [];
      displayHistoryList.value = [];
      historyHasMore.value = false;
      loadStatus.value = 'noMore';
    }
  } catch (error) {
    console.error('刷新历史记录失败:', error);
    sheep.$helper.toast('刷新失败');
    loadStatus.value = 'more';
  } finally {
    refreshing.value = false;
  }
};


// 关闭历史记录弹窗
const closeHistory = () => {
  const popup = instance.refs.historyPopup;
  if (popup) {
    popup.close();
  }
};

// 清空历史记录
const clearHistory = async () => {
  try {
    // 确认对话框
    uni.showModal({
      title: '提示',
      content: '确定要清空所有历史记录吗？',
      success: async (res) => {
        if (res.confirm) {
          // 根据当前激活的标签页选择不同的API
          const api = activeTab.value === 0 ? polishTextApi : extendTextApi;
          const { code } = await api.clearHistory();

          if (code === 0) {
            // 清空本地历史记录列表
            historyList.value = [];
            displayHistoryList.value = [];
            historyHasMore.value = false;
            // 重置分页参数
            historyRequest.value.pageNo = 1;
            sheep.$helper.toast('历史记录已清空');
            // 关闭弹窗
            closeHistory();
          }
        }
      }
    });
  } catch (error) {
    console.error('清空历史记录失败:', error);
    sheep.$helper.toast('清空历史记录失败');
  }
};

// 删除单条历史记录
const deleteHistoryItem = async (item) => {
  try {
    // 调用删除API
    const { code } = await ConversationApi.deleteHistory(item.messageId);

    if (code === 0) {
      // 从本地列表中移除该项
      displayHistoryList.value = displayHistoryList.value.filter(record => record.messageId !== item.messageId);

      // 如果当前显示的列表为空，且还有更多数据，则加载更多
      if (displayHistoryList.value.length === 0 && historyHasMore.value) {
        await loadMoreHistory();
      }

      // 如果没有更多数据且当前列表为空，显示空状态
      if (displayHistoryList.value.length === 0) {
        historyHasMore.value = false;
        loadStatus.value = 'noMore';
      }
    }
  } catch (error) {
    console.error('删除历史记录失败:', error);
    sheep.$helper.toast('删除失败');
  }
};

// 选择历史记录项
const selectHistoryItem = (item) => {
  // 关闭历史记录弹窗
  closeHistory();

  // 根据当前激活的标签页设置不同的数据
  if (activeTab.value === 0) {
    // 润色历史
    originalText.value = item.originalText || '';
    polishResult.value = item.revisedText || '';
    suggestionExplanation.value = item.suggestionExplanation || '';
  } else {
    // 扩写历史
    originalText.value = item.original || '';
    expandResult.value = item.expanded || '';
    suggestionExplanation.value = item.expansionNotes || '';
  }

  // 显示结果
  showResult.value = true;

  // 设置内容为原文，方便重新编辑
  content.value = activeTab.value === 0 ? item.originalText : item.original;
  contentLength.value = content.value.length;
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  const date = new Date(timeStr);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 检查并创建会话（如果需要）
const checkAndCreateConversation = async () => {
  // 根据当前选项卡选择不同的会话ID
  const currentConversationId = activeTab.value === 0 ? polishConversationId.value : expandConversationId.value;

  // 如果已有对应类型的会话ID，直接返回
  if (currentConversationId) {
    return;
  }

  // 根据当前选项卡选择不同的模型名称
  const modelId = activeTab.value === 0 ? AiModelEnum.INTELLIGENT_POLISH.id : AiModelEnum.CONTENT_EXPANSION.id;

  // 创建新会话
  const { code, data } = await ConversationApi.createConversation(modelId);

  if (code !== 0) {
    return;
  }

  // 根据当前选项卡设置对应的会话ID
  const targetConversationId = activeTab.value === 0 ? polishConversationId : expandConversationId;
  if (!targetConversationId.value) {
    targetConversationId.value = data;
  }
};

const submitPolish = async () => {
  if (!content.value.trim()) {
    sheep.$helper.toast('请输入需要' + (activeTab.value === 0 ? '润色' : '扩写') + '的内容');
    return;
  }

  hasHideLoading = false;
  // 先重置所有状态
  markerParser.reset();
  originalText.value = '';
  polishResult.value = '';
  expandResult.value = '';
  suggestionExplanation.value = '';
  showResult.value = true;
  isProcessing.value = true; // 设置处理状态为true
  clearTypingEffect();
  typingText.value = { original: '', result: '', suggestion: '' };
  fullText.value = { original: '', result: '', suggestion: '' };
  isTyping.value = { original: false, result: false, suggestion: false };
  showCursor.value = { original: true, result: true, suggestion: true };

  // 显示加载提示
  uni.showLoading({
    title: "加载中",
    mask: true
  });

  // 用于标记是否已收到第一个有效数据块
  let firstValidChunkReceived = false;

  try {
    // 先检查并创建会话
    await checkAndCreateConversation();

    // 确保会话ID是数字类型
    const currentConversationId = activeTab.value === 0 ? polishConversationId.value : expandConversationId.value;
    const numericConversationId = Number(currentConversationId);
    if (isNaN(numericConversationId)) {
      reject(new Error('会话ID格式错误'))
    }

    // 根据当前标签页选择不同的API
    const api = activeTab.value === 0 ? polishTextApi : extendTextApi;
    const apiFunction = activeTab.value === 0 ? 'polishText' : 'expandText';

    // 调用API
    api[apiFunction]({
      conversationId: numericConversationId,
      originalText: content.value,
    }, {
      enableChunked: true,
      onChunkReceived: createStreamHandler((content, jsonData) => {
        // 使用标记解析器处理内容
        const { updatedTypes } = markerParser.parseContent(content);

        // 更新视图变量，仅更新已更改的部分
        updateViewVariables(updatedTypes);

        // 只有当收到有效文本内容时才隐藏加载提示
        // 检查是否有实质性的文本内容（长度大于3个字符）
        const hasValidContent = (
            (originalText.value && originalText.value.trim().length > 3) ||
            (polishResult.value && polishResult.value.trim().length > 3) ||
            (expandResult.value && expandResult.value.trim().length > 3) ||
            (suggestionExplanation.value && suggestionExplanation.value.trim().length > 3)
        );

        if (!firstValidChunkReceived && hasValidContent) {
          firstValidChunkReceived = true;
        }

        if (jsonData?.data?.done) {
          // 流结束时，强制完成解析并获取更新的类型
          const forcedUpdatedTypes = markerParser.forceComplete();
          // 再次更新视图以确保显示所有内容，只更新强制完成的类型
          updateViewVariables(forcedUpdatedTypes);
          
          isProcessing.value = false;
          uni.hideLoading();
          // 检查最终结果
          if (activeTab.value === 0) {
            if (!polishResult.value && !suggestionExplanation.value) {
              sheep.$helper.toast('润色失败，请重试');
              showResult.value = false;
            }
          } else {
            if (!expandResult.value && !suggestionExplanation.value) {
              sheep.$helper.toast('扩写失败，请重试');
              showResult.value = false;
            }
          }
        }
      }, {
        silent: false,
        contentPath: 'data.receive.content',
        successCode: 0
      })
    }).catch(error => {
      console.error('处理失败:', error);
      sheep.$helper.toast('处理失败，请重试');
      showResult.value = false;
      isProcessing.value = false; // 无论成功失败，都重置处理状态
      uni.hideLoading(); // 确保隐藏加载提示
    });

  } catch (error) {
    console.error('处理失败:', error);
    sheep.$helper.toast('处理失败，请重试');
    showResult.value = false;
    isProcessing.value = false; // 无论成功失败，都重置处理状态
    uni.hideLoading(); // 确保隐藏加载提示
  }
};

// 复制结果
const copyResult = (text) => {
  if (!text) return;
  sheep.$helper.copyText(text);
};

// 重新编辑
const reEdit = () => {
  if (isProcessing.value) {
    sheep.$helper.toast('正在处理中，请稍候');
    return;
  }
  clearTypingEffect();
  showResult.value = false;
};

// 获取播放图标路径
const getPlayIconSource = (type) => {
  const basePath = '/translate/';
  return sheep.$url.cdn(
    basePath + (isPlaying.value && playingType.value === type ? 'green_voice.gif' : 'green_voice.png'),
  );
};

// 播放音频
const playAudio = async (text, type) => {
  if (!text) return;

  // 如果正在播放同一类型，停止播放
  if (isPlaying.value && playingType.value === type) {
    stopAudio();
    return;
  }

  // 如果正在播放其他类型，先停止
  if (isPlaying.value) {
    stopAudio();
  }

  isPlaying.value = true; // 设置播放状态
  playingType.value = type; // 设置播放类型
  currentPlayingText.value = text; // 设置当前播放的文本
  await commonPlayText(text); // 播放文本
};

// 停止音频播放
const stopAudio = () => {
  if (audioContext) {
    audioContext.stop();
    audioContext.destroy();
    audioContext = null;
  }
  currentPlayingText.value = null; // 重置当前播放的文本
  isPlaying.value = false; // 重置播放状态
  playingType.value = null; // 重置播放类型
};

// 通用播放文本函数
const commonPlayText = async (text) => {
  try {
    let ttsConfig = getTtsConfig(text, TtsTypeEnum.ALIYUN);
    if (!ttsConfig) {
      stopAudio();
      return;
    }

    const params = {
      text,
      speaker: ttsConfig.speaker,
      speechRate: ttsConfig.speechRate,
      pitchRate: ttsConfig.pitchRate,
      displayCaptions: false,
    };

    const res = await NlsApi.ttsAliyun(params); // 文本转语音

    if (res?.msg) {
      stopAudio();
      return;
    }

    const manager = uni.getFileSystemManager();
    const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;
    await new Promise((resolve, reject) => {
      manager.writeFile({
        filePath: tempFilePath,
        data: res,
        encoding: 'binary',
        success: resolve,
        fail: reject,
      });
    });
    await playFromUrl(tempFilePath); // 播放音频文件
  } catch (error) {
    stopAudio();
    console.error("播放音频失败:", error);
  }
};

// 从URL播放音频
const playFromUrl = (audioUrl) => {
  return new Promise((resolve, reject) => {
    try {
      audioContext = uni.createInnerAudioContext();

      if (!audioContext) {
        reject(new Error('无法创建音频上下文'));
        return;
      }

      audioContext.src = audioUrl;

      audioContext.onEnded(() => {
        stopAudio(); // 播放结束
        resolve();
      });

      audioContext.onError(() => {
        stopAudio(); // 播放错误
        reject(new Error('音频播放失败'));
      });

      // 设置超时处理，防止播放图标一直显示
      setTimeout(() => {
        if (isPlaying.value) {
          stopAudio();
        }
      }, 10000); // 10秒超时

      audioContext.play(); // 开始播放
    } catch (error) {
      stopAudio();
      reject(error);
    }
  });
};

</script>

<style scoped lang="scss">
.polish-and-expand {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F4FBFF;

  /* 标签样式 */
  .tab-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 98rpx;
    position: relative;
    margin: 20rpx 30rpx;
    background: #FFFFFF;
    border-radius: 12rpx;

    // 添加竖向分隔线
    .divider {
      width: 0.41pt;
      height: 50rpx; // 可以根据需要调整高度
      background: #CECECE;
      margin: 0 10rpx;
    }

    .tab-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 35rpx;
      font-family: 'SourceHanSansCN-Regular', sans-serif;
      color: #7C7C7C;
      position: relative;
      transition: all 0.3s;

      &:not(.active) {
        color: #7C7C7C;
      }

      &.active {
        color: #131313;
        font-family: 'SourceHanSansCN-Medium', sans-serif;
        background: #FFFFFF;
        //加租字体
        font-weight: 500;
        //border-radius: 12rpx;
        //box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }

      .pen-icon {
        width: 50rpx;
        height: 45rpx;
        margin-left: 8rpx;
      }
    }
  }

  .content-area {
    flex: 1;
    padding: 2vh 4%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .content-box {
    flex: 1;
    min-height: 55vh;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    border: 2rpx solid transparent; // 保持边框透明
    background: linear-gradient(white, white) padding-box,
      linear-gradient(to bottom, #2871FA 0%, #6717CD 100%) border-box;
    padding: 1.5rpx; // 添加内边距使边框可见
    margin-bottom: 20rpx;

    .input-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 50vh;
      /* 添加最小高度 */
    }

    .input-area {
      flex: 1;
      min-height: 45vh;
      /* 修复最小高度值 */
      padding: 30rpx;
      font-size: 28rpx;
      line-height: 1.8;
      color: #333;
      box-sizing: border-box;
      background: #ffffff;
      width: 100%;
    }

    // 结果展示样式
    .result-display {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow-y: auto;
      padding: 30rpx;
      background: #ffffff; // 添加这行，与输入框背景色保持一致

      .text-section {
        margin-bottom: 20rpx;
        position: relative; // 添加相对定位

        .section-title {
          font-family: 'SourceHanSansCN-Regular', sans-serif;
          /* 设置字体 */
          font-weight: normal;
          /* 设置字体粗细为 Regular （Normal） */
          text-align: left;
          /* 设置左对齐 */
          font-size: 30rpx;
          color: #B2B2B2;
          margin-bottom: 10rpx;
        }

        .result-title {
          font-family: 'SourceHanSansCN-Regular', sans-serif;
          /* 设置字体 */
          font-weight: normal;
          /* 设置字体粗细为 Regular （Normal） */
          text-align: left;
          /* 设置左对齐 */
          font-size: 28rpx;
          color: #B2B2B2;
          margin-bottom: 10rpx;
        }

        .suggestion-title {
          font-family: 'SourceHanSansCN-Regular', sans-serif;
          /* 设置字体 */
          font-weight: normal;
          text-align: left;
          font-size: 28rpx;
          color: #B2B2B2;
          margin-bottom: 10rpx;
        }

        .section-content {
          font-family: 'AdobeThai-Regular', sans-serif;
          /* 设置字体 */
          font-weight: normal;
          /* 设置字体粗细为 Regular （Normal） */
          text-align: left;
          /* 设置左对齐 */
          font-size: 27rpx;
          //line-height: 1.8;
          color: #2C2C2C;
          //white-space: pre-wrap;
          margin-bottom: 20rpx;
        }

        .section-content-suggestion {
          background-color: #F4F5F7;
          border-radius: 12rpx;
          padding: 20rpx;


          .suggestion-text {
            font-family: 'SourceHanSansCN-Regular', sans-serif;
            /* 设置字体 */
            font-weight: normal;
            /* 设置字体粗细为 Regular （Normal） */
            text-align: left;
            /* 设置左对齐 */
            font-size: 27rpx;
            //line-height: 1.8;
            color: #2B2B2B;
          }
        }

        .audio-control-area {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .text-audio-btn,
          .text-copy-btn,
          .text-edit-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            // width: 40rpx;
            // height: 40rpx;

            .audio-icon,
            .copy-icon,
            .edit-icon {
              width: 30rpx;
              height: 30rpx;
            }
          }
        }
      }


      /* 虚线分隔线 */
      .dashed-divider {
        height: 1rpx;
        border-top: 1rpx dashed #D2D2D2;
        width: 100%;
        margin: 20rpx 0;
      }

      .star-divider {
        font-family: "SourceHanSansCN-Regular", sans-serif;
        /* 字体 */
        font-weight: normal;
        /* 字重 */
        text-align: left;
        /* 对齐 */
        color: #ABABAB;
        /* 颜色 */
        font-size: 30rpx;
        /* 字号 */
        line-height: 30rpx;
        /* 行高 */
        letter-spacing: 0;
        /* 字间距 */
        margin: 20rpx 0;
      }

      .explanation-title {
        font-family: "SourceHanSansCN-Regular", sans-serif;
        /* 字体 */
        font-weight: normal;
        /* 字重 */
        text-align: left;
        /* 对齐 */
        color: #ABABAB;
        /* 颜色 */
        font-size: 30rpx;
        /* 字号 */
        line-height: 30rpx;
        /* 行高 */
        letter-spacing: 0;
        /* 字间距 */
      }

      .explanation-content {
        font-family: "SourceHanSansCN-Regular", sans-serif;
        /* 字体 */
        font-weight: normal;
        /* 字重 */
        text-align: left;
        /* 对齐 */
        color: #ABABAB;
        /* 颜色 */
        font-size: 30rpx;
        /* 字号 */
        letter-spacing: 0;
        /* 字间距 */
      }


      .controls-bar {
        margin-top: auto;
        padding: 20rpx 0 0;
        border-top: 1px solid #f0f0f0;
      }
    }

    .controls-bar {
      height: 80rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 30rpx;
      background-color: #ffffff;
      border-top: 1px solid #f0f0f0;

      .history-btn {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .history-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }

      .word-count {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .btn-box {
    padding: 3vh 4vw;
    margin-bottom: 20rpx;
    /* 添加下边距 */

    .polish-btn {
      height: calc(80rpx + 1vh);
      line-height: calc(80rpx + 1vh);
      border-radius: calc(40rpx + 0.5vh);
      background: #46ADF0;
      color: #ffffff;
      font-size: calc(28rpx + 0.4vw);
      letter-spacing: 2rpx;
      box-shadow: 0 4rpx 12rpx rgba(66, 148, 255, 0.2);
      margin-bottom: 15rpx;
      /* 为按钮添加下边距 */
    }


  }
}
  // 打字机
  .cursor {
    display: inline-block;
    width: 6rpx;
    height: 36rpx;
    background: skyblue;
    vertical-align: bottom;
    margin-left: 2px;
    animation: blink 1.3s infinite;
  }



@keyframes blink {

  0%,
  100% {
    background: skyblue;
  }

  50% {
    background: transparent;
    opacity: 0;
  }
}

/* 结果模态框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.result-modal {
  width: 80vw;
  max-height: 70vh;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;

  .result-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    // margin-bottom: 20rpx;
    text-align: center;
  }

  .action-audio-btn .copy-icon {
    width: 25rpx;
    height: 25rpx;
  }

  .result-content {
    flex: 1;
    max-height: 50vh;
    padding: 20rpx;
    background-color: #f9f9f9;
    border-radius: 10rpx;
    font-size: 28rpx;
    line-height: 1.5;
    color: #333;
    margin-bottom: 10rpx;
  }

  .result-actions {
    display: flex;
    justify-content: space-between;

    button {
      flex: 1;
      margin: 0 10rpx;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 28rpx;
      border-radius: 40rpx;

      &.copy-btn {
        background: linear-gradient(to right, #48b1fd, #4294ff);
        color: #fff;
      }

      &.close-btn {
        background: #f0f0f0;
        color: #666;
      }
    }
  }
}

.result-modal {
  .result-content {
    .text-block {
      margin-bottom: 20rpx;

      .block-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      text {
        font-size: 28rpx;
        line-height: 1.6;
        color: #666;
      }

      &.original {
        .block-title {
          color: #999;
        }
      }

      &.revised {
        .block-title {
          color: #2196f3;
        }
      }

      &.suggestion {
        margin-top: 20rpx;
        padding-top: 20rpx;
        border-top: 1px solid #eee;

        .block-title {
          color: #ff9800;
        }
      }
    }
  }
}

/* 小屏幕适配 */
@media screen and (max-width: 320px) {
  .polish-and-expand {
    .tab-box {
      height: 40px;

      .tab-item {
        font-size: 28rpx;
      }
    }

    .content-box {
      .input-area {
        font-size: 26rpx;
      }
    }

    .btn-box {
      .polish-btn {
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
      }
    }
  }
}

/* 大屏幕适配 */
@media screen and (min-width: 768px) {
  .polish-and-expand {
    .tab-box {
      height: 60px;

      .tab-item {
        font-size: 36rpx;
      }
    }

    .content-box {
      .input-area {
        font-size: 32rpx;
      }
    }

    .btn-box {
      .polish-btn {
        height: 100rpx;
        line-height: 100rpx;
        font-size: 36rpx;
      }
    }
  }
}

// 底部按钮样式更新
.result-actions {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 4vw;
  margin-top: 20rpx;

  button {
    flex: 1;
    margin: 0 10rpx;
    height: calc(80rpx + 1vh);
    line-height: calc(80rpx + 1vh);
    border-radius: calc(40rpx + 0.5vh);
    font-size: calc(28rpx + 0.4vw);
    letter-spacing: 2rpx;

    &.edit-btn {
      background: #fff;
      color: #474747;
      border: none;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }

    &.copy-btn {
      background: #46ADF0;
      color: #fff;
      box-shadow: 0 4rpx 12rpx rgba(66, 148, 255, 0.2);
      border: none;
    }
  }
}

/* 历史记录弹窗样式 */

.history-header {
  margin-top: 10vh;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #EBEDF0;

  .history-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }

  .clear-history {
    font-size: 28rpx;
    color: #999999;
  }
}

.history-popup {
  width: 80vw;
  height: 80vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;

  .history-list {
    height: 100%;
    padding: 20rpx;
  }

  .history-item {
    padding: 20rpx;
    border-bottom: 1px solid #eee;

    &:active {
      background-color: #f5f5f5;
    }
  }

  .history-item-content {
    margin-bottom: 10rpx;
  }

  .history-text {
    font-size: 28rpx;
    color: #333;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    line-height: 1.6;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .history-time {
    font-size: 24rpx;
    color: #999;
  }

  .load-more {
    padding: 20rpx 0;
    text-align: center;
    font-size: 24rpx;
    color: #999;
  }

  .loading-status,
  .no-more-status {
    height: 60rpx;
    line-height: 60rpx;
  }
}
</style>
