<template>
  <view class="folder-item" @click="navigateToDetails">
    <view class="card-left">
      <image class="folder-icon" :src="sheep.$url.static('/uniapp/my/favorites.png')" mode="widthFix" />
      <text class="folder-name">{{ folderName }}</text>
    </view>
    <view class="card-right">
      <text class="item-count">{{ itemCount }}个项目</text>
      <image class="enter-icon" :src="sheep.$url.static('/uniapp/my/enter.png')" mode="widthFix" />
    </view>
  </view>
</template>

<script setup>
  import sheep from '@/sheep';

  const props = defineProps({
    folderName: String,
    itemCount: [Number, String],
  });

  function navigateToDetails() {
    // sheep.$router.go();
    sheep.$helper.inDevMsg();
  }
</script>

<style scoped>
  .folder-item {
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background-color: #fff;
    border-radius: 15rpx 26rpx 26rpx 26rpx;
    box-shadow: 0 0 8rpx 0 rgba(216,216,216,0.72);
    margin-bottom: 26rpx;
  }

  .folder-name {
    flex-grow: 1;
    margin-left: 4px;
    font-size: 16px;
    color: #222222;
  }

  .item-count {
    font-size: 14px;
    color: #999;
    margin-right: 8px;
  }

  .card-left, .card-right {
    display: flex;
    align-items: center;
  }

  .folder-icon {
    width: 44rpx;
    height: 30rpx;
  }

  .enter-icon {
    width: 14rpx;
    height: 20rpx;
  }
</style>
