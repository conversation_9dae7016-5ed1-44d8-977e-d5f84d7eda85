import request from '@/sheep/request';

// 智能润色 API
const polishTextApi = {
  polishText: (data, config) => {
    return request({
      url: `/ai/text-optimizer/polish-text-stream`,
      method: 'POST',
      data: data,
      custom: {
        showLoading: true
      },
      ...config
    });
  },

  
    // 获取历史记录列表
  getResultPage: (data) => {
    return request({
      url: '/ai/text-optimizer/polish-stream-history',
      method: 'POST',
      data,
      custom: {
        showLoading: false,
      },
    });
  },

    // 清空视频提炼历史记录
    clearHistory: () => {
      return request({
        url: '/ai/text-optimizer/clear-polish-history',
        method: 'DELETE',
        custom: {
          successMsg: '历史记录已清空',
        },
      });
    },
};

export default polishTextApi;
