<template>
  <view class="container">
    <!-- 上半部分：蓝色背景 -->
    <easy-navbar :transparent="true" />
    <view class="top-section">
      
      <!-- 中央图片 -->
      <image :src="sheep.$url.cdn('/set/homework.png')" class="center-image" mode="widthFix" />
      
      <!-- 标题和祝贺语 -->
      <text class="title-text">太棒啦！你做的很好！</text>
    </view>
    
    <!-- 下半部分：白色背景 -->
    <view class="bottom-section">
      <!-- 结果卡片 -->
      <view class="result-card">
        <view class="card-title">您的结果</view>
        
        <!-- 成绩展示区 -->
        <view class="result-content">
          <!-- 左侧环形进度 -->
          <view class="progress-circle-wrapper">
            <view class="circle-progress">
              <view class="circle-bg"></view>
              <view class="circle-right-wrap" :style="circleRightStyle">
                <view class="circle-right"></view>
              </view>
              <view class="circle-left-wrap" :style="circleLeftStyle">
                <view class="circle-left"></view>
              </view>
              <view class="inner-circle">
                <text class="progress-percent">{{ correctPercentage }}%</text>
              </view>
            </view>
          </view>
          
          <!-- 右侧结果统计 -->
          <view class="result-stats">
            <view class="stat-row">
              <text class="stat-label correct">正确</text>
              <view class="stat-badge correct-badge">{{ result.correctCards }}</view>
            </view>
            <view class="stat-row">
              <text class="stat-label incorrect">不正确</text>
              <view class="stat-badge incorrect-badge">{{ result.incorrectCards }}</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 建议标题 -->
      <text class="suggestion-title">建议您：</text>

      <!-- 按钮区域 -->
      <view class="action-buttons">
        <view class="action-button" @click="handleStudyMode">
          <text class="button-text">复习错题</text>
        </view>
        <view class="action-button" @click="handleRestart">
          <text class="button-text">重新测试</text>
        </view>
      </view>

      <!-- 答案部分 -->
      <text class="answers-title">您的答案</text>
      
      <!-- 答案列表 - 修改为与图片一致的布局 -->
      <view class="answer-list">
        <view 
          v-for="(answer, index) in result.answers" 
          :key="index" 
          class="answer-item"
        >
          <!-- 第一行：题目 -->
          <text class="question-text">{{ index + 1 }}.{{ answer.content }}</text>
          
          <!-- 第二行：用户答案和正误图标 -->
          <view class="answer-content">
            <text :class="answer.isCorrect ? 'check-icon' : 'wrong-icon'">{{ answer.isCorrect ? '✓' : '✕' }}</text>
            <text class="answer-text">{{ answer.userAnswer }}</text>
          </view>
          
          <!-- 第三行：结果信息，正确或错误带正确答案 -->
          <view class="result-bar" :class="answer.isCorrect ? 'result-correct' : 'result-wrong'">
            <text v-if="answer.isCorrect" class="result-text">回答正确</text>
            <text v-else class="result-text">正确答案：<text class="correct-answer">{{ answer.correctAnswer }}</text></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onBeforeUnmount } from 'vue';
// @ts-ignore
import { onLoad, onUnload } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import EasyNavbar from '@/components/easy-navbar/easy-navbar.vue';
import SessionApi from '@/sheep/api/set/session';

// 声明uni对象
declare const uni: any;

// 默认假数据，当没有传递数据时使用
const mockResult = {
  totalCards: 10,
  correctCards: 8,
  incorrectCards: 2,
  testTime: 90000, // 1分30秒
  setId: 'mock-set-id',
  answers: [
    {
      questionId: '1',
      content: '请选择 "รถไฟ" 一词在泰语中的正确含义。',
      userAnswer: '火车',
      correctAnswer: '火车',
      isCorrect: true
    },
    {
      questionId: '2',
      content: '请选择 "รถยนต์" 一词在泰语中的正确含义。',
      userAnswer: '摩托车',
      correctAnswer: '汽车',
      isCorrect: false
    }
  ]
};

interface Answer {
  questionId: string;
  content: string;
  userAnswer: string;
  correctAnswer: string;
  isCorrect: boolean;
}

interface TestResult {
  totalCards: number;
  correctCards: number;
  incorrectCards: number;
  testTime: number;
  setId?: string;
  answers?: Answer[];
}

const result = ref<TestResult>({
  totalCards: 0,
  correctCards: 0,
  incorrectCards: 0,
  testTime: 0,
  setId: '',
  answers: []
});

// 会话相关状态
const sessionId = ref<string>('');
const isReviewMode = ref<boolean>(false);

// 提取setId为单独的计算属性，以便在模板中使用
const setId = computed(() => result.value.setId || '');

const correctPercentage = computed(() => {
  if (result.value.totalCards === 0) return 0;
  return Math.round((result.value.correctCards / result.value.totalCards) * 100);
});

// 环形进度图样式计算
const circleRightStyle = computed(() => {
  const percent = correctPercentage.value;
  // 右半圆：当正确率>50%时，右半圆全部显示蓝色；当正确率<=50%时，右半圆根据正确率部分显示蓝色
  if (percent >= 50) {
    return { transform: 'rotate(0deg)' }; // 右半圆完全显示蓝色
  } else {
    return { transform: `rotate(${180 - percent * 3.6}deg)` }; // 右半圆部分显示蓝色
  }
});

const circleLeftStyle = computed(() => {
  const percent = correctPercentage.value;
  // 左半圆：当正确率>50%时，左半圆根据超出50%的部分显示蓝色；当正确率<=50%时，左半圆完全显示橙色
  if (percent <= 50) {
    return { transform: 'rotate(180deg)' }; // 左半圆完全显示橙色
  } else {
    return { transform: `rotate(${180 - (percent - 50) * 3.6}deg)` }; // 左半圆部分显示蓝色
  }
});

// 验证并修正结果数据
const validateAndFixResult = (data: TestResult): TestResult => {
  // 确保answers数组存在
  if (!data.answers || !Array.isArray(data.answers)) {
    data.answers = [];
  }
  
  // 根据answers数组重新计算正确和错误题目数
  if (data.answers.length > 0) {
    const correctAnswers = data.answers.filter(a => a.isCorrect).length;
    const incorrectAnswers = data.answers.filter(a => !a.isCorrect).length;
    
    // 如果answers中的数量与传入的不一致，以answers为准
    data.correctCards = correctAnswers;
    data.incorrectCards = incorrectAnswers;
    
    // 确保totalCards是正确的总和
    if (data.totalCards !== correctAnswers + incorrectAnswers) {
      console.warn(`总题目数不一致: 传入${data.totalCards}, 计算${correctAnswers + incorrectAnswers}`);
      data.totalCards = correctAnswers + incorrectAnswers;
    }
  } else {
    // 如果没有answers数组但有计数
    if (data.correctCards >= 0 && data.incorrectCards >= 0) {
      // 确保总数与各部分总和一致
      const calculatedTotal = data.correctCards + data.incorrectCards;
      if (data.totalCards !== calculatedTotal) {
        console.warn(`总题目数不一致: 传入${data.totalCards}, 计算${calculatedTotal}`);
        data.totalCards = calculatedTotal;
      }
    }
  }
  
  // 如果总数是0但有分数，这是个数据错误，设置为10题
  if (data.totalCards === 0 && (data.correctCards > 0 || data.incorrectCards > 0)) {
    console.warn('总题目数为0但有得分，设置为10题');
    data.totalCards = 10;
  }
  
  // 确保百分比计算不会出错
  if (data.totalCards === 0) {
    data.correctCards = 0;
    data.incorrectCards = 0;
  }
  
  return data;
};

onLoad((options) => {
  if (options?.data) {
    try {
      const data = JSON.parse(decodeURIComponent(options.data));
      
      // 获取会话ID和模式信息
      sessionId.value = data.sessionId || '';
      isReviewMode.value = data.mode === 'review';
      
      // 复习模式下，以实际答案数量作为总题数
      if (isReviewMode.value && data.answers && Array.isArray(data.answers)) {
        data.totalCards = data.answers.length;
      }
      
      // 验证并修正结果数据
      result.value = validateAndFixResult(data);
    } catch (e) {
      result.value = mockResult;
      sheep.$helper.toast('数据加载失败，显示模拟数据');
    }
  } else {
    result.value = mockResult;
  }
});

const handleRestart = async () => {
  // 标记为按钮跳转
  isNavigatingByButton.value = true;
  
  // 结束当前会话（如果存在且不是复习模式）
  await completeSessionOnce();
  
  // 跳转到测试页面重新测试
  sheep.$router.go('/pages/set/test/test', { setId: result.value.setId }, { redirect: true });
};

const handleStudyMode = async () => {
  // 标记为按钮跳转
  isNavigatingByButton.value = true;
  
  // 复习错题不结束会话，直接跳转
  // 准备错题数据
  const incorrectAnswers = result.value.answers ? result.value.answers.filter(answer => !answer.isCorrect) : [];
  
  // 检查是否有错题需要复习
  if (incorrectAnswers.length === 0) {
    // 没有错题，提示用户并跳转到重新测试
    sheep.$helper.toast('恭喜！已全部正确，将重新开始测试');
    await completeSessionOnce();
    setTimeout(() => {
      sheep.$router.go('/pages/set/test/test', { setId: result.value.setId }, { redirect: true });
    }, 1500);
    return;
  }
  
  // 跳转到测试页面，传递复习模式参数和错题数据
  sheep.$router.go('/pages/set/test/test', { 
    setId: result.value.setId, 
    mode: 'review',
    incorrectData: encodeURIComponent(JSON.stringify(incorrectAnswers))
  }, { redirect: true });
};

// 添加标记避免重复调用
const sessionCompleted = ref(false);
// 标记是否是通过按钮跳转（避免生命周期钩子重复调用）
const isNavigatingByButton = ref(false);

// 结束会话的统一方法
const completeSessionOnce = async () => {
  if (sessionId.value && !isReviewMode.value && !sessionCompleted.value) {
    sessionCompleted.value = true;
    await SessionApi.completeSession(sessionId.value);
  }
};

// 页面卸载时结束会话（仅非复习模式且非按钮跳转）
onUnload(async () => {
  if (!isNavigatingByButton.value) {
    await completeSessionOnce();
  }
});

// 组件销毁时结束会话（仅非复习模式且非按钮跳转）
onBeforeUnmount(async () => {
  if (!isNavigatingByButton.value) {
    await completeSessionOnce();
  }
});

</script>

<style>
.container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(180deg, #59c0fb 0%, #5dc7fb 50%, #46adf5 100%);
}

/* 上半部分样式 */
.top-section {
  height: 40vh; /* 减小上半部分高度 */
  min-height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

/* 下半部分样式 */
.bottom-section {
  flex: 1;
  background-color: #fff;
  padding: 30rpx 32rpx 0;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  position: relative;
  z-index: 5;
}

.center-image {
  width: 550rpx;
  margin-top: 20rpx;
  margin-bottom: 16rpx;
}

.title-text {
  font-size: 44rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 20rpx;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-card {
  width: 90%;
  max-width: 640rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin: -120rpx auto 30rpx; /* 减小负边距，让卡片位置更高 */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.card-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
  padding-left: 20rpx;
  text-align: left;
}

.result-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
}

/* 新的环形进度条样式 */
.progress-circle-wrapper {
  width: 150rpx;
  height: 150rpx;
  position: relative;
}

.circle-progress {
  width: 150rpx;
  height: 150rpx;
  position: relative;
}

.circle-bg {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  position: absolute;
  background-color: #FF9600; /* 橙色背景代表错误 */
}

.circle-right-wrap {
  width: 75rpx;
  height: 150rpx;
  position: absolute;
  top: 0;
  right: 0;
  overflow: hidden;
  transform-origin: left center;
}

.circle-right {
  width: 75rpx;
  height: 150rpx;
  position: absolute;
  top: 0;
  right: 0;
  background-color: #4169e2; /* 蓝色填充代表正确 */
  border-radius: 0 75rpx 75rpx 0;
}

.circle-left-wrap {
  width: 75rpx;
  height: 150rpx;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  transform-origin: right center;
}

.circle-left {
  width: 75rpx;
  height: 150rpx;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #4169e2; /* 蓝色填充代表正确 */
  border-radius: 75rpx 0 0 75rpx;
}

.inner-circle {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-percent {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 结果统计样式 */
.result-stats {
  flex: 1;
  margin-left: 40rpx;
}

.stat-row {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐 */
  margin-bottom: 20rpx;
}

.stat-label {
  font-size: 30rpx;
  margin-right: 20rpx;
}

.correct {
  color: #4169e2; /* 蓝色，与图片一致 */
}

.incorrect {
  color: #ff4d4f; /* 红色 */
}

.stat-badge {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  min-width: 60rpx;
  text-align: center;
}

.correct-badge {
  background-color: #e6f0fa; /* 浅蓝色背景 */
  color: #4169e2; /* 蓝色文字 */
  border: none;
}

.incorrect-badge {
  background-color: #fff1f0; /* 浅红色背景 */
  color: #ff4d4f; /* 红色文字 */
  border: none;
}

/* 建议部分 */
.suggestion-title {
  font-size: 34rpx;
  font-weight: 500;
  margin: 20rpx 0 20rpx; /* 减小上边距 */
  color: #333;
}

/* 修改按钮容器为垂直排列 */
.action-buttons {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx; /* 减小下边距 */
  margin-top: 30rpx; /* 减小下边距 */
}

/* 修改按钮样式为绿色渐变 */
.action-button {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(to right, #00DD5F, #97EA44);
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx; /* 按钮之间的间距 */
}

.button-text {
  color: #fff;
  font-size: 32rpx;
}

/* 答案部分 */
.answers-title {
  font-size: 34rpx;
  font-weight: 500;
  margin: 20rpx 0 20rpx; /* 减小上下边距 */
  color: #333;
}

.answer-list {
  padding-bottom: 30rpx;
}

/* 答案项样式 - 修改为与图片一致 */
.answer-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx 0 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 题目文本 */
.question-text {
  font-size: 30rpx;
  color: #333;
  margin: 0 24rpx 16rpx;
  line-height: 1.4;
}

/* 用户答案行 */
.answer-content {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-bottom: 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.answer-text {
  font-size: 30rpx;
  color: #333;
}

/* 结果条 - 用于显示正确或错误信息 */
.result-bar {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  border-radius: 0;
  color: #FFFFFF; /* 字体颜色改为白色 */
}

.result-text {
  font-size: 28rpx;
  line-height: 1.4;
}

.correct-answer {
  color: #FFFFFF; /* 正确答案文字也改为白色 */
  font-weight: 500;
}

.result-correct {
  background-color: #52c41a; /* 绿色背景表示正确 */
}

.result-wrong {
  background-color: #ff4d4f; /* 红色背景表示错误 */
}

/* 添加图标样式 */
.check-icon {
  color: #52c41a;
  font-size: 32rpx;
  margin-right: 16rpx;
}

.wrong-icon {
  color: #ff4d4f;
  font-size: 32rpx;
  margin-right: 16rpx;
}
</style>