<template>
  <view class="card" @click="$emit('handleClick')" @longpress="$emit('handleLongPress')">
    <view
      class="cover"
      :style="
        coverUrl ? `background-image: url(${coverUrl}); background-size: cover;` : ''
      "
    >
      <text v-if="!coverUrl" class="cover-text">{{ defaultText }}</text>
      <view class="star-icon" v-if="isStore">
        <uni-icons type="star-filled" size="22" color="#FFC107" />
      </view>
      <view class="selected-icon" v-if="isSelected">
        <uni-icons type="checkmarkempty" size="22" color="#FFFFFF" />
      </view>
    </view>
    <view class="info">
      <text class="title">{{ title }}</text>
      <template v-if="mode === 'user'">
        <view class="user-info">
          <image
            class="avatar"
            :src="avatar"
            mode="aspectFit"
          />
          <text class="info-text">{{ nickname }}</text>
        </view>
      </template>
      <template v-else-if="mode === 'course'">
        <view class="word-info">
          <view class="user-info">
            <image
              class="avatar"
              :src="avatar"
              mode="aspectFit"
            />
            <text class="info-text">{{ nickname }}</text>
          </view>
          <text class="info-text" v-if="courseCount">共{{ courseCount }}课</text>
        </view>
      </template>
      <template v-else-if="mode === 'word'">
        <view class="word-info">
          <view class="user-info">
            <image
              class="avatar"
              :src="avatar"
              mode="aspectFit"
            />
            <text class="info-text">{{ nickname }}</text>
          </view>
          <text class="info-text" v-if="wordCardCount">{{ wordCardCount }}个词语</text>
        </view>
      </template>
    </view>
  </view>
</template>


<script setup>
  import { computed } from 'vue';
  import sheep from '@/sheep';
  import UniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue';

  const props = defineProps({
    // 封面图片URL
    coverUrl: {
      type: String,
      required: true,
    },
    // 标题
    title: {
      type: String,
      required: true,
    },
    // 用户昵称
    nickname: {
      type: String,
      required: false,
    },
    // 用户头像
    avatar: {
      type: String,
      required: false,
    },
    // 是否收藏
    isStore: {
      type: Number,
      required: false,
    },
    // 是否选中
    isSelected: {
      type: Boolean,
      default: false,
    },
    // 默认封面文字
    defaultText: {
      type: String,
      default: '封面',
    },
    // 展示模式: user-用户模式 course-课程模式 word-单词模式
    mode: {
      type: String,
      default: 'user',
      validator: (value) => ['user', 'course', 'word'].includes(value),
    },
    // 课程数量（course模式使用）
    courseCount: {
      type: Number,
      required: false,
    },
    // 单词卡数量（word模式使用）
    wordCardCount: {
      type: Number,
      required: false,
    },
  });

  // TODO 默认头像，暂不使用
  const defaultAvatar = computed(() => {
    return sheep.$url.static('/uniapp/my/avatar.png');
  });

  // 定义事件
  defineEmits(['handleClick', 'handleLongPress', 'click', 'longpress']);
</script>

<style scoped lang="scss">
  .card {
    width: 44vw;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 32rpx;

    .cover {
      width: 100%;
      height: 350rpx;
      border-radius: 16rpx;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      background: #239eed no-repeat center;
      background-size: cover;
	  box-shadow: 0px 0px 6px 0px #ddd;
      overflow: hidden;

      .cover-text {
        color: white;
        font-size: 28rpx;
        opacity: 0.7;
        font-weight: 500;
        text-align: center;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      }

      .star-icon {
        position: absolute;
        top: 16rpx;
        right: 16rpx;
        z-index: 1;
      }
      
      .selected-icon {
        position: absolute;
        top: 0;
        right: 0;
        width: 60rpx;
        height: 60rpx;
        background-color: #239eed;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom-left-radius: 16rpx;
        z-index: 2;
      }
    }

    .info {
      .title {
        font-size: 28rpx;
        width: 100%;
        margin: 14rpx 0;
        color: #2D2D2D;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%; /* 确保最大宽度为容器宽度 */
        display: block; /* 确保独占一行 */
      }

      .user-info {
        display: flex;
        align-items: center;
        flex-shrink: 1; // 允许收缩

        .avatar {
          width: 44rpx;
          height: 44rpx;
          border-radius: 50%;
          background: #f1f1f1;
          margin-right: 10rpx;
        }
      }
    }

    .info-text {
      font-size: 22rpx;
      color: #555555;
    }

    .word-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .user-info {
        flex-shrink: 1;
      }
    }
  }
</style>

