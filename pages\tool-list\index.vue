<template>
	<view class="container">
		<!-- 底部盒子 -->
		<view class="top-box">
			<text class="top-title">实用工具</text>
		</view>
		<view class="scarf">
			<!-- 第一组工具（红框） -->
			<view class="tool-group">

				<!-- 左侧大卡片 -->
				<view class="card-column">

					<view class="tool-card-scarf large grammar-bg">
						<view class="tool-card" @tap="goToCorrectGrammar">
							<text class="title">语法纠错</text>
							<text class="description">流畅表达观点</text>
							<view class="action-btn-scarf">
								<view style="color: #5261F8;" class="action-btn">立即使用</view>
							</view>
						</view>
					</view>

				</view>

				<!-- 右侧小卡片组 -->
				<view class="card-column">

					<!-- 智能润色卡片 -->
					<view class="tool-card-scarf small polish-bg">
						<view class="tool-card" @tap="goToPolishAndExpand(0)">
							<text class="title">智能润色</text>
							<text class="description">提升你的文字光泽</text>
							<view class="action-btn-scarf">
								<view style="color: #EA721B;" class="action-btn">立即使用</view>
							</view>
						</view>
					</view>

					<!-- 智能扩写卡片 -->
					<view class="tool-card-scarf small expand-bg">
						<view class="tool-card" @tap="goToPolishAndExpand(1)">
							<text class="title">智能扩写</text>
							<text class="description">丰富文章内涵</text>
							<view class="action-btn-scarf">
								<view style="color: #0FE7D3;" class="action-btn">立即使用</view>
							</view>
						</view>
					</view>

				</view>
			</view>


			<view class="tool-group">
				<!-- 左侧小卡片组 -->
				<view class="card-column">
					<!-- 重点提炼卡片 -->
					<view class="tool-card-scarf small extract-bg">
						<view class="tool-card" @tap="goToVideoRefinement">
							<text class="title">重点提炼</text>
							<text class="description">一键解析重点</text>
							<view class="action-btn-scarf">
								<view style="color: #11BD15;" class="action-btn">立即使用</view>
							</view>
						</view>
					</view>

          <!-- 拍照翻译卡片 -->
          <view class="tool-card-scarf small picture-bg">
            <view class="tool-card" @tap="goToPhotoTranslate">
              <text class="title">拍照翻译</text>
              <text class="description">精准翻译，拍一拍</text>
              <view class="action-btn-scarf">
                <view style="color: #DCC82C;" class="action-btn">立即使用</view>
              </view>
            </view>
          </view>

          <!-- 文本翻译卡片 -->
          <view class="tool-card-scarf small text-bg">
            <view class="tool-card" @tap="goToTextTranslate">
              <text class="title">文本翻译</text>
              <text class="description">输入单词或句子翻译</text>
              <view class="action-btn-scarf">
                <view style="color: #A357DF;" class="action-btn">立即使用</view>
              </view>
            </view>
          </view>

				</view>

				<!-- 右侧卡片组 -->
				<view class="card-column">

          <!-- PDF翻译卡片 -->
          <view class="tool-card-scarf large pdf-bg">
            <view class="tool-card" @tap="goToPdfTranslate">
              <text class="title">PDF翻译</text>
              <text class="description">解析和翻译PDF文件</text>
              <view class="action-btn-scarf">
                <view style="color: #FF5D59;" class="action-btn">立即使用</view>
              </view>
            </view>
          </view>

          <!-- 同生传译卡片 -->
          <view class="tool-card-scarf small samelife-bg">
            <view class="tool-card" @tap="goToSameLifeTranslate">
              <text class="title">同声传译</text>
              <text class="description">多语言实时互译</text>
              <view class="action-btn-scarf">
                <view style="color: #2BB3FF;" class="action-btn">立即使用</view>
              </view>
            </view>
          </view>

				</view>
			</view>
		</view>
		<!-- 底部导航 -->
		<!-- <bottom-nav :currentTab="2" /> -->
	</view>
</template>

<script setup>
import { computed, ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import OcrApi from '@/sheep/api/ocr/index';
import sheep from '@/sheep';

// 计算登录状态
const isLogin = computed(() => sheep.$store('user').isLogin);

// 处理页面滚动到底部的事件
function handleReachBottom() {
  console.log('工具页面触发了滚动到底部事件');
  // 工具页面的滚动到底部逻辑，可以根据需要添加
}

// 在页面显示时更新底部导航状态
onShow(() => {
  // 检查是否是直接导航到此页面（非标签页切换）
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];

  // 如果当前页面路径是直接访问tool页面，而不是通过home页面的标签切换
  if (currentPage.route === 'pages/tool/index') {
    // 工具页面可能需要的初始化逻辑
  }
});

// 跳转到智能润色页面
const goToPolishAndExpand = async (tabIndex) => {
  // 跳转到文字优化页面并传递会话ID
  sheep.$router.go(`/pages/tool/text-optimizer/polish-and-expand?tabIndex=${tabIndex}`);
};

// 跳转到拍照翻译页面前先处理图片
const goToPhotoTranslate = () => {
  // 显示操作菜单，让用户选择拍照或从相册选择
  uni.showActionSheet({
    itemList: ['拍照', '从相册选择'],
    success: (res) => {
      const sourceType = res.tapIndex === 0 ? ['camera'] : ['album'];

      // 调用选择图片API
      uni.chooseImage({
        count: 1,
        sourceType: sourceType,
        success: (imageRes) => {
          const tempFilePath = imageRes.tempFilePaths[0];

          // 读取图片内容为base64
          uni.getFileSystemManager().readFile({
            filePath: tempFilePath,
            encoding: 'base64',
            success: async (fileRes) => {
              // 调用OCR API
              const res = await OcrApi.recognize({
                fileBase64: fileRes.data,
                languageType: 'mix',
                isPdf: false,
                isWords: false,
              });

              // 处理结果
              if (res.code !== 0) {
                return;
              }

              if (res.data.length === 0) {
                sheep.$helper.toast('未识别到文字');
                return;
              }

              // 缓存识别结果
              uni.setStorageSync(
                'ocr_cache',
                JSON.stringify({
                  timestamp: Date.now(),
                  imageUrl: tempFilePath,
                  OCRResponse: res.data,
                }),
              );

              // 跳转到拍照翻译页
              sheep.$router.go('/pages/tool/translator/photo');
            },
            fail: () => {
              sheep.$helper.toast('读取图片失败');
            },
          });
        },
      });
    },
  });
};

const goToCorrectGrammar = () => {
  sheep.$router.go('/pages/tool/text-optimizer/correct-grammar');
};
const goToVideoRefinement = () => {
  sheep.$router.go('/pages/tool/video-refinement/index');
};
const goToTextTranslate = () => {
  sheep.$router.go('/pages/tool/translator/text');
};
const goToSameLifeTranslate = () => {
  sheep.$router.go('/pages/tool/interpreter/test');
  // 检查是否已选择语言
  // const savedLanguage = uni.getStorageSync('interpreter_selected_language');
  //
  // if (savedLanguage) {
  //   // 如果已选择语言，直接跳转到index页面
  //   sheep.$router.go('/pages/tool/interpreter/index');
  // } else {
  //   // 如果未选择语言，先跳转到语言选择页面
  //   sheep.$router.go('/pages/tool/interpreter/language');
  // }
};

const goToPdfTranslate = () => {
  // 从微信聊天记录中选择PDF文件
  uni.chooseMessageFile({
    count: 1,
    type: 'file',
    extension: ['pdf'],
    success: (fileRes) => {
      const tempFilePath = fileRes.tempFiles[0].path;

      // 读取PDF文件内容为base64
      uni.getFileSystemManager().readFile({
        filePath: tempFilePath,
        encoding: 'base64',
        success: async (fileRes) => {
          // 校验base64大小
          const base64Data = fileRes.data;
          const sizeInBytes = base64Data.length; // base64字符串每个字符占1字节
          const sizeInMB = sizeInBytes / (1024 * 1024);

          if (sizeInMB > 10) {
            sheep.$helper.toast('文件大小不能超过7MB');
            return;
          }

          // 调用OCR API
          const res = await OcrApi.pdfRecognize({
            fileBase64: fileRes.data,
            languageType: 'mix',
            isPdf: true,
            pdfPageNumber: 1, // 默认从第一页开始
            isWords: false,
          });

          // 处理结果
          if (res.code !== 0) {
            sheep.$helper.toast('PDF识别失败');
            return;
          }

          if (res.data.length === 0) {
            sheep.$helper.toast('未识别到文字');
            return;
          }

          // 缓存PDF文件和识别结果，使用pageResults存储按页码分类的识别结果
          uni.setStorageSync(
            'pdf_cache',
            JSON.stringify({
              timestamp: Date.now(),
              pdfPath: tempFilePath,
              pdfBase64: fileRes.data,
              currentPage: 1,
              totalPages: res.data.totalPages || 1,
              // 使用pageResults按页码存储识别结果
              pageResults: {
                1: res.data // 第一页的结果
              }
            }),
          );

          // 跳转到PDF翻译页
          sheep.$router.go('/pages/tool/translator/pdf');
        },
        fail: () => {
          sheep.$helper.toast('读取PDF文件失败');
        },
      });
    },
    fail: () => {
      sheep.$helper.toast('选择PDF文件失败');
    }
  });
};

const topBoxBg = sheep.$url.css('/uniapp/live/live-bg.png');
const grammarBg = sheep.$url.css('/uniapp/tool/grammar.png');
const expandBg = sheep.$url.css('/uniapp/tool/expand.png');
const polishBg = sheep.$url.css('/uniapp/tool/polish.png');
const extractBg = sheep.$url.css('/uniapp/tool/extract.png');
const samelifeBg = sheep.$url.css('/uniapp/tool/samelife.png');
const pdfBg = sheep.$url.css('/uniapp/tool/pdf.png');
const textBg = sheep.$url.css('/uniapp/tool/texttranslation.png');
const pictureBg = sheep.$url.css('/uniapp/tool/picture.png');

// 暴露给父组件的方法
defineExpose({
  // 标签页激活时调用
  onTabActivated(isFirstActivation) {
    // 工具页面可能需要在每次激活时执行一些操作
    // 例如：更新用户权限、刷新工具状态等
  },

  // 标签页停用时调用
  onTabDeactivated() {
    // console.log('工具页面被停用');
  },

  // 页面显示时调用
  onPageShow() {
    // console.log('工具页面所在的页面显示');
  },

  // 页面隐藏时调用
  onPageHide() {
    // console.log('工具页面所在的页面隐藏');
  },

  // 处理页面滚动到底部的事件
  handleReachBottom
});
</script>

<style scoped lang="scss">
	.container {
		width: 100%;
		position: relative;
		background-color: #f6f6f6;
	}

	.top-box {
		width: 100%;
		height: 350rpx;
		background: v-bind(topBoxBg);
		background-size: cover;
		background-repeat: no-repeat;
		position: relative;
	}

	.top-title {
		top: 127rpx;
		left: 39rpx;
		font-family: TBMCYXT;
		font-weight: bold;
		font-size: 67rpx;
		color: #2d2d2d;
		position: absolute;
	}

	.scarf {
		padding: 20rpx;
		position: relative;
		top: -80rpx;
	}

	.tool-group {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	.tool-card {
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		width: 100%;
		height: 100%;
		position: relative;
		overflow: hidden;
	}

	.card-column {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		width: 50%;
	}

	.large {
		width: 357rpx;
		height: 442rpx;
		background-size: cover;

		.tool-card {
			padding: 74rpx 50rpx;
		}

		.title {
			font-size: 50rpx;
			margin-bottom: 18rpx;
		}

		.description {
			font-size: 25rpx;
			margin-bottom: 124rpx;
		}

		.action-btn {
			font-size: 25rpx;
			background: #ffffff;
			border-radius: 23rpx;
			padding: 6rpx 16rpx;
			display: inline-block;
		}
	}

	.small {
		width: 357rpx;
		height: 222rpx;
		background-size: cover;


		.tool-card {
			padding: 44rpx;
		}

		.title {
			font-size: 38rpx;
		}

		.description {
			margin-bottom: 12rpx;
			font-size: 21rpx;
		}

		.action-btn {
			font-size: 18rpx;
			background: #ffffff;
			border-radius: 23rpx;
			padding: 6rpx 16rpx;
			display: inline-block;
		}
	}




	.grammar-bg {
		background-image: v-bind(grammarBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.expand-bg {
		background-image: v-bind(expandBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.polish-bg {
		background-image: v-bind(polishBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.extract-bg {
		background-image: v-bind(extractBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.samelife-bg {
		background-image: v-bind(samelifeBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.pdf-bg {
		background-image: v-bind(pdfBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.text-bg {
		background-image: v-bind(textBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.picture-bg {
		background-image: v-bind(pictureBg);
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
	}

	.title {
		font-family: PingFang SC;
		font-weight: bold;
		font-size: 50rpx;
		color: #ffffff;
	}

	.description {
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 25rpx;
		color: #ffffff;
	}



	.icon-container {
		position: absolute;
		bottom: 20rpx;
		right: 20rpx;
	}

	.tool-icon {
		width: 80rpx;
		height: 80rpx;
		opacity: 0.8;
	}
</style>
