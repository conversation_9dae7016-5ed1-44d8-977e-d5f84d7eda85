<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<easy-navbar title="班级详情"/>

		<!-- 表单区域 -->
		<view class="form-container">
			<!-- 班级名称 -->
			<view class="form-item">
				<view class="label">班级名称</view>
				<view class="value">
					<text v-if="!isCreator" class="text-right">{{ className }}</text>
					<input
						v-else
						class="input-field"
						type="text"
						v-model="className"
						placeholder="班级名称"
						placeholder-class="placeholder"
						focus
						@blur="handleNameBlur"
						@confirm="handleNameConfirm"
					/>
				</view>
			</view>

			<!-- 教师改为创建者 -->
			<view class="form-item">
				<view class="label">创建者</view>
				<view class="value">
					<text class="text-right">{{ creatorNickname }}</text>
				</view>
			</view>

			<!-- 群说明 -->
			<view class="form-item">
				<view class="label">群说明</view>
				<view class="value">
					<text v-if="!isCreator" class="text-right">{{ classDescription }}</text>
					<input
						v-else
						class="input-field"
						type="text"
						v-model="classDescription"
						placeholder="群说明"
						placeholder-class="placeholder"
						@blur="handleDescriptionBlur"
						@confirm="handleDescriptionConfirm"
					/>
				</view>
			</view>
		</view>

		<!-- 保存按钮 - 仅创建者可见 -->
		<!-- 移除保存按钮，改为失去焦点自动保存 -->

		<!-- 功能按钮区域 -->
		<view class="function-buttons">
			<!-- 邀请好友 - 仅创建者可见 -->
			<view v-if="isCreator" class="function-item" @click="inviteFriends">
				<view class="function-left">
					<image class="function-icon" :src="sheep.$url.cdn('/course/member.png')" mode="aspectFit"/>
					<text class="function-text">邀请好友</text>
				</view>
				<view class="function-right">
					<text class="arrow-icon">›</text>
				</view>
			</view>

			<!-- 添加学习集 - 仅创建者可见 -->
			<view v-if="isCreator" class="function-item" @click="addStudySet">
				<view class="function-left">
					<image class="function-icon" :src="sheep.$url.cdn('/group/class-set.png')" mode="aspectFit"/>
					<text class="function-text">学习集管理</text>
				</view>
				<view class="function-right">
					<text class="arrow-icon">›</text>
				</view>
			</view>

			<!-- 课程管理 - 仅创建者可见 -->
			<view v-if="isCreator" class="function-item" @click="manageCourses">
				<view class="function-left">
					<image class="function-icon" :src="sheep.$url.cdn('/group/class-course.png')" mode="aspectFit"/>
					<text class="function-text">课程管理</text>
				</view>
				<view class="function-right">
					<text class="arrow-icon">›</text>
				</view>
			</view>

			<!-- 创建者显示删除班级，非创建者显示退出班级 -->
			<view class="function-item danger" @click="isCreator ? deleteClass() : exitClass()">
				<view class="function-left">
					<image class="function-icon" :src="sheep.$url.cdn('/course/layout.png')" mode="aspectFit"/>
					<text class="function-text">{{ isCreator ? '删除班级' : '退出班级' }}</text>
				</view>
				<text class="arrow-icon">›</text>
			</view>
		</view>

		<!-- 成员列表区域 -->
		<view class="member-section" v-if="showMembers">
			<view class="section-header">
				<text class="section-title">成员 ({{ members.length }})</text>
			</view>

			<view class="member-list">
				<view v-if="members.length === 0" class="empty-state">
					<text>暂无成员</text>
				</view>
				<block v-else>
					<swipe-action
						v-for="(member, index) in members"
						:key="index"
						:buttons="getSwipeButtons(member)"
						@button-click="(e) => handleMemberAction(member, e)"
						:disabled="!isCreator || isCreatorMember(member)"
						:autoClose="true"
					>
						<view class="member-item">
							<view class="member-avatar">
								<image v-if="member.avatar" :src="member.avatar" mode="aspectFill" />
								<view v-else class="default-avatar" />
							</view>
							<view class="member-info">
								<text class="member-name">{{ member.nickname }}</text>
								<text v-if="isCreatorMember(member)" class="creator-tag">创建者</text>
							</view>
						</view>
					</swipe-action>
				</block>
			</view>
		</view>

		<!-- 邀请链接弹窗 -->
		<su-popup ref="invitePopupRef" type="center">
			<view class="invite-modal">
				<view class="invite-title">邀请加入班级</view>
				<view class="invite-class-name">{{ className }}</view>
				<view class="invite-link-container">
					<text class="invite-link">{{ inviteLink }}</text>
				</view>
				<view class="invite-tip">复制链接，发送给好友。好友打开小程序后将自动处理加入请求。</view>
				<view class="invite-actions">
					<button class="action-btn share-btn" open-type="share" @tap="onShareButtonTap">分享链接</button>
				</view>
			</view>
		</su-popup>
	</view>
</template>

<script>
import { ref, onMounted } from 'vue';
import GroupApi from '@/sheep/api/group/index';
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app';
import sheep from '@/sheep';
// 导入su-popup组件
import suPopup from '@/sheep/ui/su-popup/su-popup.vue';
// 导入左滑删除组件
import SwipeAction from '@/components/swipe-action/swipe-action.vue';

export default {
  components: {
    SwipeAction,
    suPopup
  },
  setup() {
    // 表单数据
    const className = ref('');
    const classDescription = ref('');
    const classId = ref('');
    const originalStatus = ref(0); // 保存原始状态值，但不提供UI编辑
    const isCreator = ref(false); // 是否为创建者
    const showMembers = ref(true); // 是否显示成员列表
    const members = ref([]); // 班级成员列表
    const creatorNickname = ref(''); // 创建者昵称
    const creatorId = ref(null); // 创建者ID

    // 用于跟踪原始值，以检测是否有变化
    const originalClassName = ref('');
    const originalClassDescription = ref('');

    // 邀请链接相关数据
    const inviteLink = ref('');
    const invitePopupRef = ref(null);
    // 获取当前小程序页面路径前缀
    const appPagePath = '/pages/group/join';

    // 用于存储当前要分享的班级信息（供微信分享使用）
    const shareClassId = ref(null);
    const shareClassName = ref('');

    onLoad((options) => {
      classId.value = options.id;
      // 加载页面数据
      loadClassInfo();
    });

    // 加载班级信息
    const loadClassInfo = async () => {
      // 获取班级详情
      const result = await GroupApi.getGroup(classId.value);

      if (result.code !== 0) {
        return;
      }
      // 填充表单数据
      className.value = result.data.name;
      classDescription.value = result.data.remark || '';

      // 保存原始值，用于检测变化
      originalClassName.value = result.data.name;
      originalClassDescription.value = result.data.remark || '';

      // 保存创建者ID
      creatorId.value = result.data.userId;

      // 获取创建者信息
      creatorNickname.value = result.data.nickname || '未知用户';

      // 判断是否为创建者 - 检查当前用户ID是否与班级创建者ID相同
      // 后端返回的userId是班级创建者ID
      const loginUserId = sheep.$store('user').userInfo.id;
      // 确保两个ID都转换为数字类型再进行比较
      isCreator.value = Number(result.data.userId) === Number(loginUserId);

      // 保存原始状态值，但不在UI中显示编辑选项
      originalStatus.value = result.data.status;

      // 如果显示成员，则加载成员列表
      if (showMembers.value) {
        await loadMembers();
      }
    };

    // 加载班级成员
    const loadMembers = async () => {
      const { code, data } = await GroupApi.getGroupUserList(classId.value);
      if (code !== 0) {
        return;
      }
      members.value = data || [];
    };

    // 处理成员移除按钮点击 - 修改参数接收方式
    const handleMemberRemove = (member) => {
      confirmRemoveMember(member);
    };

    // 判断是否是创建者成员
    const isCreatorMember = (member) => {
      return Number(member.id) === Number(creatorId.value);
    };

    // 获取滑动按钮
    const getSwipeButtons = (member) => {
      if (!isCreator.value) return []; // 非创建者不显示按钮

      if (isCreatorMember(member)) {
        // 创建者显示不同的按钮文本
        return [{ text: '创建者', backgroundColor: '#4CAF50', width: 150 }];
      } else {
        // 普通成员显示移除按钮
        return [{ text: '移除', backgroundColor: '#ff5252', width: 150 }];
      }
    };

    // 处理成员操作
    const handleMemberAction = (member, e) => {
      // 只有点击移除按钮时才执行移除操作
      handleMemberRemove(member);
      // 创建者按钮不执行任何操作
    };

    // 确认移除成员
    const confirmRemoveMember = (member) => {
      uni.showModal({
        title: '确认移除',
        content: `确定要将该成员移出班级吗？`,
        success: ({ confirm }) => {
          if (confirm) {
            removeMember(member);
          }
        },
      });
    };

    // 移除成员
    const removeMember = async (member) => {
      const { code } = await GroupApi.kickGroupMember(classId.value, member.id);
      if (code !== 0) {
        sheep.$helper.toast('移除失败');
        return;
      }
      sheep.$helper.toast('已移除成员');
      await loadMembers();
    };

    // 处理班级名称失去焦点事件
    const handleNameBlur = () => {
      if (className.value !== originalClassName.value) {
        submitForm();
      }
    };

    // 处理班级名称回车事件
    const handleNameConfirm = () => {
      if (className.value !== originalClassName.value) {
        submitForm();
      }
    };

    // 处理群说明失去焦点事件
    const handleDescriptionBlur = () => {
      if (classDescription.value !== originalClassDescription.value) {
        submitForm();
      }
    };

    // 处理群说明回车事件
    const handleDescriptionConfirm = () => {
      if (classDescription.value !== originalClassDescription.value) {
        submitForm();
      }
    };

    // 提交表单
    const submitForm = async () => {
      // 检查是否有变化
      if (className.value === originalClassName.value && classDescription.value === originalClassDescription.value) {
        return; // 没有变化，不提交
      }

      // 构建表单数据
      const formData = {
        name: className.value,
        remark: classDescription.value,
        id: classId.value
      };

      // 调用更新班级接口
      const result = await GroupApi.updateGroup(formData);
      if (result.code !== 0) {
        sheep.$helper.toast('更新失败');
        return;
      }

      // 更新成功后，更新原始值
      originalClassName.value = className.value;
      originalClassDescription.value = classDescription.value;

      // 显示成功提示
      sheep.$helper.toast('更新成功');
    };

    // 生成邀请链接
    const generateInviteLink = async (groupId) => {
      try {
        // 调用后端生成带签名的邀请链接
        const res = await GroupApi.generateInviteLink(groupId, 24); // 24小时有效期
        if (res.code === 0) {
          const { timestamp, sign } = res.data;
          // 生成小程序内页面链接
          return `${appPagePath}?groupId=${groupId}&timestamp=${timestamp}&sign=${sign}`;
        } else {
          sheep.$helper.toast(res.msg || '生成邀请链接失败');
          return null;
        }
      } catch (error) {
        sheep.$helper.toast('生成邀请链接失败');
        return null;
      }
    };

    // 显示邀请链接弹窗
    const showInviteLinkModal = (link) => {
      if (link) {
        inviteLink.value = link;
        invitePopupRef.value.open();
      }
    };

    // 点击分享按钮
    const onShareButtonTap = () => {
      // 关闭弹窗
      setTimeout(() => {
        invitePopupRef.value.close();
      }, 200);
    };

    // 邀请好友
    const inviteFriends = async () => {
      if (!classId.value) {
        sheep.$helper.toast('班级信息不完整');
        return;
      }

      // 生成邀请链接
      const link = await generateInviteLink(classId.value);

      // 显示邀请链接弹窗
      showInviteLinkModal(link);

      // 保存当前分享的班级信息，用于微信分享
      shareClassId.value = classId.value;
      shareClassName.value = className.value;
    };

    // 添加学习集
    const addStudySet = () => {
      sheep.$router.go('/pages/set/list', {
        from: 'group',
        action: 'addToClass',
        classId: classId.value,
        className: className.value
      });
    };

    // 课程管理
    const manageCourses = () => {
      sheep.$router.go('/pages/course/course-list', {
        from: 'group',
        action: 'addToClass',
        classId: classId.value,
        className: className.value
      });
    };

    // 删除班级
    const deleteClass = async () => {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该班级吗？',
        success: async ({ confirm }) => {
          if (!confirm) return;
          const res = await GroupApi.deleteGroup(classId.value);
          if (res.code !== 0) {
            return;
          }
          // 清除本地缓存的班级ID
          uni.removeStorageSync('last_group_id');
          sheep.$router.go('/pages/group/switch', {}, { redirect: true });
        },
      });
    };

    // 退出班级
    const exitClass = async () => {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出该班级吗？',
        success: async ({ confirm }) => {
          if (!confirm) return;
          const res = await GroupApi.quitGroup(classId.value);
          if (res.code !== 0) {
            return;
          }
          // 清除本地缓存的班级ID
          uni.removeStorageSync('last_group_id');
          sheep.$router.go('/pages/group/switch', {}, { redirect: true });
        },
      });
    };

    // #ifdef MP-WEIXIN
    // 启用微信小程序分享功能
    uni.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
    });

    // 分享给微信好友
    onShareAppMessage(() => {
      // 使用班级信息
      return {
        title: `邀请加入班级：${className.value}`,
        path: `${appPagePath}?groupId=${classId.value}`,
        imageUrl: sheep.$url.cdn('/group/Faculty.png'), // 使用班级默认图标作为分享图片
      };
    });

    // 分享到朋友圈
    onShareTimeline(() => {
      // 使用班级信息
      return {
        title: `邀请加入班级：${className.value}`,
        query: `groupId=${classId.value}`,
        imageUrl: sheep.$url.cdn('/group/Faculty.png'),
      };
    });
    // #endif

    // 页面加载时获取班级信息
    onMounted(() => {
      // onLoad中已经调用了loadClassInfo，这里不需要重复调用
    });

    return {
      className,
      classDescription,
      classId,
      isCreator,
      showMembers,
      members,
      inviteLink,
      invitePopupRef,
      creatorNickname,
      loadClassInfo,
      loadMembers,
      handleMemberRemove,
      handleMemberAction,
      isCreatorMember,
      getSwipeButtons,
      confirmRemoveMember,
      submitForm,
      handleNameBlur,
      handleNameConfirm,
      handleDescriptionBlur,
      handleDescriptionConfirm,
      inviteFriends,
      addStudySet,
      manageCourses,
      deleteClass,
      exitClass,
      onShareButtonTap,
      sheep
    };
  }
};
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #F8FCFF;
	display: flex;
	flex-direction: column;
}

.form-container {
	background-color: #F8FCFF;
	margin-top: 20rpx;
}

.form-item {
	display: flex;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #EEEEEE;
	background-color: #F8FCFF;

	.label {
		width: 160rpx;
		font-size: 32rpx;
		color: #333333;
	}

	.value {
		flex: 1;
		font-size: 32rpx;
		color: #666666;
		display: flex;
		justify-content: flex-end;
		align-items: center;

		.text-right {
			text-align: right;
			width: 100%;
		}

		.input-field {
			text-align: right;
			width: 70%;
			font-size: 32rpx;
			color: #333333;
		}
	}
}

.function-buttons {
	margin-top: 20rpx;
	background-color: #F8FCFF;
}

.function-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #EEEEEE;
	background-color: #F8FCFF;

	.function-left {
		display: flex;
		align-items: center;

		.function-icon {
			width: 48rpx;
			height: 48rpx;
			margin-right: 20rpx;
		}

		.function-text {
			font-size: 32rpx;
			color: #333333;
		}
	}

	.function-right {
		display: flex;
		align-items: center;

		.enter-text {
			font-size: 28rpx;
			color: #999999;
			margin-right: 10rpx;
		}

		.arrow-icon {
			font-size: 40rpx;
			color: #999999;
			line-height: 1;
			font-weight: 300;
		}
	}

	&.danger {
		.function-text {
			color: #FF5252;
		}

		.arrow-icon {
			font-size: 40rpx;
			color: #999999;
			line-height: 1;
			font-weight: 300;
		}
	}
}

.member-section {
	margin-top: 20rpx;
	background-color: #F8FCFF;
}

.section-header {
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #EEEEEE;
	background-color: #F8FCFF;

	.section-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: normal;
	}
}

.member-list {
	background-color: #F8FCFF;

	.member-item {
		display: flex;
		align-items: center;
		padding: 20rpx 40rpx;
		border-bottom: 1rpx solid #EEEEEE;
		background-color: #F8FCFF;

		.member-avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			overflow: hidden;
			background-color: #F5F5F5;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.member-info {
			flex: 1;
			display: flex;
			align-items: center;

			.member-name {
				font-size: 32rpx;
				color: #333333;
			}

			.creator-tag {
				font-size: 24rpx;
				color: #FFFFFF;
				background-color: #4CAF50;
				padding: 4rpx 12rpx;
				border-radius: 20rpx;
				margin-left: 16rpx;
			}
		}
	}
}

.empty-state {
	padding: 40rpx;
	text-align: center;
	color: #999999;
	font-size: 28rpx;
}

/* SwipeAction组件样式 */
:deep(.swipe-action-container) {
	background-color: #FFFFFF;
}

:deep(.swipe-action-buttons) {
	height: 100%;
}

/* 邀请弹窗样式保持不变 */
.invite-modal {
	background-color: #FFFFFF;
	border-radius: 12rpx;
	padding: 40rpx;
	width: 560rpx;
}

.invite-title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	margin-bottom: 20rpx;
}

.invite-class-name {
	font-size: 32rpx;
	color: #2196F3;
	text-align: center;
	margin-bottom: 30rpx;
}

.invite-link-container {
	background-color: #F5F5F5;
	padding: 20rpx;
	border-radius: 8rpx;
	margin-bottom: 30rpx;
}

.invite-link {
	font-size: 28rpx;
	color: #666666;
	word-break: break-all;
}

.invite-tip {
	font-size: 28rpx;
	color: #999999;
	text-align: center;
	margin-bottom: 30rpx;
}

.invite-actions {
	.action-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 40rpx;
		font-size: 28rpx;
		border: none;
	}

	.share-btn {
		background-color: #4CAF50;
		color: #FFFFFF;
	}
}
</style>
