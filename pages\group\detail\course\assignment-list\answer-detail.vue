<template>
  <view class="answer-detail-page">
    <!-- 背景图 -->
    <image class="bg-image" :src="sheep.$url.cdn('/group/blue-back.png')" mode="aspectFill"></image>

    <!-- 顶部导航 -->
    <easy-navbar :title="assignmentTitle" :transparent="true" />

    <!-- 分数环形进度条 -->
    <view class="score-circle-container">
      <!-- 外圈灰色环 -->
      <view class="circle-outer"></view>

      <!-- 蓝色进度环 -->
      <view class="circle-progress" :style="progressStyle"></view>

      <!-- 中心白色区域 -->
      <view class="circle-inner">
        <view class="score-text">
          <text class="score-value">{{ score }}</text>
          <text class="score-unit">分</text>
        </view>
      </view>
    </view>

    <!-- 答题卡部分 -->
    <view class="answer-card-container">
      <!-- 答题卡标题 -->
      <view class="answer-card-header">
        <view class="header-line"></view>
        <view class="header-card">
          <text class="dot-left">•</text>
          <text class="header-text">答题卡</text>
          <text class="dot-right">•</text>
        </view>
      </view>

      <!-- 对错标识 -->
      <view class="answer-status">
        <view class="status-item">
          <view class="status-dot correct"></view>
          <text class="status-text">答对</text>
        </view>
        <view class="status-item">
          <view class="status-dot wrong"></view>
          <text class="status-text">答错</text>
        </view>
      </view>

      <!-- 题目选择 -->
      <view class="question-numbers">
        <view
          v-for="(item, index) in questions"
          :key="index"
          :class="['question-number', item.isCorrect ? 'correct' : 'wrong']"
          @click="goToQuestion(index)"
        >
          {{ index + 1 }}
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button class="detail-btn" @click="viewDetail">查看详情</button>
    </view>
  </view>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import AssignmentApi from '@/sheep/api/course/assignment';

  // 参数
  const assignmentId = ref('');
  const courseId = ref('');
  const assignmentTitle = ref('作业详情');
  const userId = ref(''); // 新增：接收传入的学生ID
  const isTeacher = ref(false); // 标识是否为老师

  // 分数和题目数据
  const score = ref(0);
  const maxScore = ref(0);
  const questions = ref([]);

  // 计算进度样式
  const progressStyle = computed(() => {
    const roundedScore = Math.round(score.value);
    const percent = maxScore.value > 0 ? (roundedScore / maxScore.value * 100) : 0;
    return {
      background: `conic-gradient(#00D8FF 0deg, #00D8FF ${percent * 3.6}deg, transparent ${percent * 3.6}deg, transparent 360deg)`
    };
  });

// 页面加载
onLoad((options) => {
  if (options.id) {
    assignmentId.value = options.id;
  }
  if (options.courseId) {
    courseId.value = options.courseId;
  }
  if (options.title) {
    assignmentTitle.value = decodeURIComponent(options.title);
  }
  if (options.userId) {userId.value = options.userId}; // 获取传入的学生ID

  // 判断用户角色 - 与 assignment-list.vue 保持一致
  isTeacher.value = sheep.$store('user').userInfo?.isCertified === true;
  getAnswerReport();
});

  // 获取答题报告
  const getAnswerReport = async () => {
    let res;

    if (isTeacher.value) {
      // 老师查看指定学生的答题报告
      res = await AssignmentApi.getAssignmentAnswerReportForTeacher(
        assignmentId.value,
        userId.value
      );
    } else {
      // 学生查看自己的答题报告
      res = await AssignmentApi.getAssignmentAnswerReport(assignmentId.value);
    }

    if (res.code === 0 && res.data) {
      score.value = Math.round(res.data.studentScore || 0);
      if (res.data.questionList?.length > 0) {
        questions.value = res.data.questionList.map((q, idx) => ({
          ...q,
          id: idx + 1
        }));

        // 计算总分值
        maxScore.value = Math.round(
          res.data.questionList.reduce((total, q) => total + (q.score || 0), 0)
        );
      } else {
        questions.value = [];
        maxScore.value = 0;
      }
    }
  };

// 跳转到具体题目
const goToQuestion = (index) => {
  // 跳转到题目详情页面，并传递题目索引和标题
  sheep.$router.go(`/pages/group/detail/course/assignment-list/question-detail?id=${assignmentId.value}&courseId=${courseId.value}&questionIndex=${index}&title=${encodeURIComponent(assignmentTitle.value)}&userId=${userId.value}&isTeacher=${isTeacher.value}`);
};

  // 查看完整详情
  const viewDetail = () => {
    // 跳转到题目详情页面，确保传递标题参数
    sheep.$router.go(`/pages/group/detail/course/assignment-list/question-detail?id=${assignmentId.value}&courseId=${courseId.value}&title=${encodeURIComponent(assignmentTitle.value)}&userId=${userId.value}&isTeacher=${isTeacher.value}`);
  };
</script>

<style scoped lang="scss">
  .answer-detail-page {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    z-index: -1;
  }

  .score-circle-container {
    margin-top: 100rpx;
    position: relative;
    width: 280rpx;
    height: 280rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0rpx 0rpx 30rpx 0rpx rgba(24, 170, 227, 0.17);
    border-radius: 50%;
  }

  .circle-outer {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 48rpx solid #d7ecf4;
    box-sizing: border-box;
    z-index: 1;
  }

  .circle-progress {
    position: absolute;
    width: 260rpx;
    height: 260rpx;
    border-radius: 50%;
    z-index: 2;
  }

  .circle-inner {
    position: absolute;
    width: 180rpx;
    height: 180rpx;
    border-radius: 50%;
    background-color: #f7fcff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3;
  }

  .score-text {
    font-size: 96rpx;
    font-weight: 500;
    color: #36c0f6;
    display: flex;
    align-items: baseline;
    font-family: PingFang SC;
  }

  .score-value {
    font-size: 60rpx;
  }

  .score-unit {
    font-size: 34rpx;
    margin-left: 4rpx;
  }

  .answer-card-container {
    width: 630rpx;
    min-height: 401rpx;
    background: #ffffff;
    box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211, 223, 230, 0.52);
    border-radius: 25rpx;
    opacity: 0.8;

    padding: 40rpx 30rpx 20rpx 30rpx;
    position: relative;
    margin: 60rpx 30rpx;
  }

  .answer-card-header {
    position: absolute;
    top: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .header-line {
    width: 228rpx;
    height: 5rpx;
    background: #1ec5ff;
  }

  .header-card {
    width: 219rpx;
    height: 56rpx;
    background: #e4f8ff;
    border-radius: 0rpx 0rpx 20rpx 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .header-text {
    font-size: 32rpx;
    color: #36c0f6;
    font-weight: 500;
  }

  .dot-left,
  .dot-right {
    color: #36c0f6;
    font-size: 32rpx;
    margin: 0 10rpx;
  }

  .answer-status {
    display: flex;
    justify-content: center;
    margin-top: 30rpx;
    margin-bottom: 30rpx;
  }

  .status-item {
    display: flex;
    align-items: center;
    margin: 0 30rpx;
  }

  .status-dot {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    margin-right: 10rpx;
  }

  .status-dot.correct {
    background-color: #41e282;
  }

  .status-dot.wrong {
    background-color: #ff6263;
  }

  .status-text {
    font-size: 28rpx;
    color: #666;
  }

  .question-numbers {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 20rpx 0;
  }

  .question-number {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    margin: 10rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
  }

  .question-number.correct {
    background-color: #41e282;
  }

  .question-number.wrong {
    background-color: #ff6263;
  }

  .footer {
    width: 100%;
    padding: 40rpx 30rpx;
    position: fixed;
    bottom: 0;
    left: 0;
  }

  .detail-btn {
    width: 690rpx;
    height: 82rpx;
    background: linear-gradient(270deg, rgba(70, 173, 240, 0.89), rgba(0, 222, 255, 0.89));
    border-radius: 41rpx;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
    margin: 0 0 30rpx 0;
  }
</style>