<template>
  <view class="easy-navbar">
    <!-- 导航栏 -->
    <view class="navbar-area" :class="{ 'fixed': fixed, 'transparent': transparent }" :style="{ paddingTop: `${paddingTop}px`, paddingBottom: `${paddingBottom}px`, backgroundColor: navbarBgColor }">
      <!-- 左侧区域：优先使用插槽，如果没有则显示返回按钮 -->
      <view class="left">
        <slot name="left">
          <image
            v-if="showBack"
            class="back-icon"
			      mode="widthFix"
            :src="sheep.$url.cdn('/common/back.png')"
            @click="goBack"
          />
        </slot>
      </view>

      <!-- 中间标题 -->
      <view class="title" :class="{ 'title-empty': !hasTitle }">
        <slot name="title">
          {{ title }}
        </slot>
      </view>

      <!-- 右侧区域：right插槽 -->
      <view class="right">
        <slot name="right"></slot>
      </view>
    </view>

    <!-- 占位元素，防止内容被固定导航栏遮挡，仅在fixed为true时显示 -->
    <view v-if="fixed" class="navbar-placeholder" :style="{ height: `${navbarHeight + paddingBottom}px` }" />
  </view>
</template>

<script setup>
  import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import sheep from '@/sheep';

  const props = defineProps({
    title: {
      type: String,
      default: '', // 默认值为空
    },
    showBack: {
      type: Boolean,
      default: true,
    },
    transparent: {
      type: Boolean,
      default: false, // 默认不透明
    },
    fixed: {
      type: Boolean,
      default: true, // 默认固定定位
    },
    backPath: {
      type: String,
      default: '', // 默认返回上一页
    }
  });

  // 计算标题是否存在
  const hasTitle = computed(() => {
    return props.title && props.title.trim() !== '';
  });

  // const paddingTop = ref(20);
  const menuButtonInfo = ref(null);
  const paddingBottom = ref(10);
  const paddingTop = ref(0);
  const navbarHeight = ref(0);

  // 滚动相关变量
  const scrollTop = ref(0);
  const scrollThreshold = ref(6); // 滚动阈值，超过此值背景变为白色

  // 计算导航栏背景色
  const navbarBgColor = computed(() => {
    if (!props.transparent) {
      return '#ffffff'; // 如果不是透明模式，直接返回白色
    }

    // 直接根据滚动位置决定背景色，不使用渐变透明度
    return scrollTop.value >= scrollThreshold.value ? '#ffffff' : 'transparent';
  });

  onLoad(() => {
    // #ifdef MP-WEIXIN
    // 获取胶囊按钮信息
    menuButtonInfo.value = uni.getMenuButtonBoundingClientRect();
    // 调整导航栏高度，使其能容纳胶囊按钮下方的标题
    paddingTop.value = menuButtonInfo.value.top;
    // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
    navbarHeight.value = paddingTop.value + 35;
    // #endif

    // #ifndef MP-WEIXIN
    // 获取状态栏高度
    const systemInfo = sheep.$helper.sys();
    paddingTop.value = systemInfo.marginTop;
    // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
    navbarHeight.value = paddingTop.value + 35;
    // #endif
  });

  const goBack = () => {
    if (props.backPath) {
      sheep.$router.go(props.backPath);
      return;
    }
    sheep.$router.back();
  };

  // 创建一个定时器来模拟滚动监听
  let scrollTimer = null;

  // 获取页面滚动位置的函数
  const getScrollPosition = () => {
    uni.createSelectorQuery()
      .selectViewport()
      .scrollOffset(res => {
        if (res && res.scrollTop !== undefined) {
          scrollTop.value = res.scrollTop;
        }
      })
      .exec();
  };

  // 组件挂载时设置滚动监听
  onMounted(() => {
    if (props.transparent && props.fixed) {
      // 使用定时器定期检查滚动位置
      scrollTimer = setInterval(getScrollPosition, 30); // 每30ms检查一次

      // 初始化时执行一次，获取初始滚动位置
      getScrollPosition();
    }
  });

  // 组件卸载前清除滚动监听
  onBeforeUnmount(() => {
    if (scrollTimer) {
      clearInterval(scrollTimer);
      scrollTimer = null;
    }
  });
</script>

<style scoped lang="scss">
  .navbar-area {
    display: flex;
    align-items: center;
    height: 35px;
    width: 100%;
    z-index: 999;
    transition: all 0.05s ease;

    &.fixed {
      position: fixed;
      top: 0;
      left: 0;
    }

    &.transparent {
      box-shadow: none;

      &[style*="background-color: #ffffff"] {
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      }
    }
  }

  .left {
    position: absolute;
    left: 15px;
    display: flex;
    align-items: center;
    z-index: 101;

    .back-icon {
      width: 19rpx;
      font-size: 34rpx;
      font-weight: bold;
      color: #333333;
      padding: 10rpx;
    }
  }

  .right {
    position: absolute;
    right: 15px;
    display: flex;
    align-items: center;
    z-index: 101;
  }

  .title {
    width: 100%;
    line-height: 35rpx;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333333;
  }

  .title-empty {
    pointer-events: none;
    opacity: 0;
  }
</style>
