<template>
  <view class="container">
    <view class="top-box"> </view>

    <!-- 优质学习集 -->
    <view class="section">
      <view class="header">
        <text class="title">优质学习集</text>
      </view>
      <view class="wordset-list">
        <view class="wordset-item">
          <easy-card
            v-for="(set, index) in wordSets"
            :key="index"
            :title="set.title"
            :cover-url="set.coverUrl"
            :nickname="set.nickname"
            :avatar="set.avatar"
            :is-store="set.isStore"
            :word-card-count="set?.wordCardCount"
            mode="word"
            @handleClick="goToSetDetail(set.id)"
          />
        </view>
      </view>

      <view class="last-more">
        <view class="more-btn" @click="goToWordSetList">
          <text>查看更多</text>
          <text class="icon">›</text>
        </view>
      </view>
    </view>

    <!-- 精品课程 -->
    <view class="section">
      <view class="header">
        <text class="title">精品课程</text>
      </view>

      <view class="course-list">
        <easy-card
          v-for="(course, index) in courses"
          :key="index"
          :title="course.title"
          :cover-url="course.coverImage || sheep.$url.cdn('/course/lesson.png')"
          :nickname="course.nickname"
          :avatar="course.avatar"
          :is-store="course.isStore"
          :course-count="course.chapterCount || 0"
          mode="course"
          @handleClick="() => goToCourseDetail(course.id)"
        />
      </view>

      <view class="more-btn m-b" @click="goToCourseList">
        <text>查看更多</text>
        <text class="icon">›</text>
      </view>
    </view>

  </view>
</template>

<script setup>
  import { ref } from 'vue';
  import { onShow } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import SetApi from '@/sheep/api/set';
  import LessonApi from '@/sheep/api/course/lesson';
  import EasyCard from '@/components/easy-card/easy-card.vue';

  // 在页面显示时更新底部导航状态
  onShow(() => {
    // 检查是否是直接导航到此页面（非标签页切换）
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    // 如果当前页面路径是直接访问community页面，而不是通过home页面的标签切换
    if (currentPage.route === 'pages/community/index') {
      console.log('直接访问社区页面，获取数据');
      fetchQualityCommunity();
    }
  });

  // 处理页面滚动到底部的事件
  function handleReachBottom() {
    console.log('社区页面触发了滚动到底部事件');
    // 社区页面的滚动到底部逻辑，可以根据需要添加
  }

  // 优质学习集数据
  const wordSets = ref([]);

  const loading = ref(false);
  // 获取优质学习集
  // 精品课程数据
  const courses = ref([]);
  const coursesLoading = ref(false);

  // 获取精品课程
  const fetchQualityCourses = async () => {
    try {
      coursesLoading.value = true;
      const res = await LessonApi.getQualityLessons({ pageNum: 1, pageSize: 8 });
      
      // 处理课程数据，确保所有必要的属性都存在
      if (res.data && res.data.list) {
        courses.value = res.data.list.map(course => ({
          ...course,
          avatar: course.avatar || '',
          nickname: course.nickname || '创建者',
          isStore: course.isStore || 0,
          chapterCount: course.chapterCount || 0
        }));
      } else {
        courses.value = [];
      }
    } catch (err) {
      console.error('获取精品课程失败', err);
      sheep.$helper.toast('暂无课程');
    } finally {
      coursesLoading.value = false;
    }
  };

  // 链式调用：先获取优质学习集，再获取精品课程
  const fetchQualityCommunity = async () => {
    try {
      loading.value = true;
      const res = await SetApi.getQualityWordSets({ pageNum: 1, pageSize: 8 });
      wordSets.value = res.data.list;
      
      // 获取学习集成功后，链式调用获取精品课程
      await fetchQualityCourses();
    } catch (err) {
	 if(err && err.code !== 401){
		 sheep.$helper.toast('网络错误，请重试');
	 }
	  console.log("错误",err);
    } finally {
      loading.value = false;
    }
  };

  // 跳转到课程详情
  const goToCourseDetail = (id) => {
    if (!id) {
      return;
    }
    sheep.$router.go(`/pages/group/detail/course/lesson-detail?id=${id}&isVisitor=true`);
  };

  // 跳转到学习集详情
  const goToSetDetail = async (id) => {
    if (!id) {
      return;
    }
    // 正常跳转到学习集详情页
    sheep.$router.go(`/pages/set/word-card?id=${id}`);
  };

  // 跳转到学习集列表
  const goToWordSetList = () => {
    sheep.$router.go('/pages/set/quality-list');
  };

  // 跳转到课程列表
  const goToCourseList = () => {
    sheep.$router.go('/pages/course/quality-list');
  };

  // 暴露给父组件的方法
  defineExpose({
    // 标签页激活时调用
    onTabActivated(isFirstActivation) {
      // 每次激活都获取数据
      fetchQualityCommunity();
    },

    // 标签页停用时调用
    onTabDeactivated() {
      // console.log('社区页面被停用');
    },

    // 页面显示时调用
    onPageShow() {
      // console.log('社区页面所在的页面显示');
      // 页面重新显示时可能需要刷新数据，根据实际情况决定
    },

    // 页面隐藏时调用
    onPageHide() {
      // console.log('社区页面所在的页面隐藏');
    },

    // 处理页面滚动到底部的事件
    handleReachBottom,
  });
</script>

<style scoped lang="scss">
  .container {
    width: 100%;
    background-color: #f6f6f6;
    padding-top: 60rpx;
    padding-bottom: 30rpx;
    // height: calc(100% - 60rpx);
    min-height: max-content;
  }

  .top-box {
    position: fixed;
    top: 0;
    width: 750rpx;
    height: 350rpx;
    background-image: url('#{$baseImgUrl}/live/live-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
  }

  .section {
    margin-bottom: 40rpx;
    padding: 0 20rpx;
    position: relative;
    z-index: 999;
	&:last-child{
		margin-bottom: 0;
	}
  }

  .header {
    padding: 40rpx 10rpx 25rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 66rpx;
      font-weight: bold;
      color: #333;
    }

    .header-more {
      display: flex;
      align-items: center;

      .more-dots {
        color: #999;
        font-size: 28rpx;
        margin-right: 10rpx;
      }

      .circle-btn {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: #f3f3f3;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #e0e0e0;

        .refresh-icon {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }
  .last-more{
    padding-bottom: 4rpx;
  }

  .more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #666;
    height: 80rpx;
    border-radius: 40rpx;
    margin: 20rpx auto 10rpx;
    border: 1rpx solid #e0e0e0;
    background-color: #fff;
    width: 240rpx;

    .icon {
      margin-left: 8rpx;
      font-size: 60rpx;
      font-weight: 100;
      margin-top: -8rpx;
    }
  }

  .m-b {
    margin-bottom: 100rpx;
  }

  .course-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 10rpx;
    width: 100%;
  }

  .wordset-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10rpx;

    .wordset-item {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 100%;
    }
  }
</style>
