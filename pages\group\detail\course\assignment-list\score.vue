<template>
  <view class="score-page">
    <!-- 背景图 -->
    <image class="bg-image" :src="sheep.$url.cdn('/group/blue-back.png')" mode="aspectFill"></image>
    
    <!-- 顶部导航 -->
    <easy-navbar 
      :title="assignmentTitle" 
      :transparent="true" 
    />
    
    <!-- 分数环形进度条 -->
    <view class="score-circle-container">
      <!-- 外圈灰色环 -->
      <view class="circle-outer"></view>
      
      <!-- 蓝色进度环 -->
      <view class="circle-progress" :style="progressStyle"></view>
      
      <!-- 中心白色区域 -->
      <view class="circle-inner">
        <view class="score-text">
          <text class="score-value">{{score}}</text>
          <text class="score-unit">分</text>
        </view>
      </view>
    </view>
    
    <!-- 底部区域 -->
    <view class="bottom-area">
      <!-- 题目统计 -->
      <view class="questions-stats">
        <text class="stats-text">共<text class="highlight-text">{{total}}</text>题，客观题<text class="highlight-text">{{objective}}</text>道，主观题<text class="highlight-text">{{subjective}}</text>道</text>
      </view>
      
      <!-- 底部按钮 -->
      <view class="footer">
        <button class="check-btn" @click="checkAssignment">查看作业</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import AssignmentApi from '@/sheep/api/course/assignment';

// 参数
const assignmentId = ref('');
const courseId = ref('');
const assignmentTitle = ref('作业成绩');

// 分数
const score = ref(0);
const maxScore = ref(0);

// 题目数量统计
const total = ref(0);
const objective = ref(0);
const subjective = ref(0);

// 答题结果数据
const reportData = ref(null);

// 计算进度样式
const progressStyle = computed(() => {
  // 避免除以0的情况
  const roundedScore = Math.round(score.value);
  const percentage = maxScore.value > 0 ? (roundedScore / maxScore.value * 100) : 0;
  return {
    background: `conic-gradient(#00D8FF 0deg, #00D8FF ${percentage * 3.6}deg, transparent ${percentage * 3.6}deg, transparent 360deg)`
  };
});

// 页面加载
onLoad((options) => {
  if (options.id) {
    assignmentId.value = options.id;
  }
  if (options.courseId) {
    courseId.value = options.courseId;
  }
  if (options.title) {
    assignmentTitle.value = decodeURIComponent(options.title);
  }
  
  // 获取答题报告
  getAnswerReport();
});

// 获取答题报告
const getAnswerReport = async () => {
  const res = await AssignmentApi.getAssignmentAnswerReport(assignmentId.value);
  
  if (res.code === 0 && res.data) {
    reportData.value = res.data;
    
    // 设置分数
    score.value = Math.round(res.data.studentScore || 0);
    
    // 统计题目数据
    if (res.data.questionList && res.data.questionList.length > 0) {
      const questions = res.data.questionList;
      
      // 总题目数
      total.value = questions.length;
      
      // 计算客观题和主观题数量
      let objectiveCount = 0;
      let subjectiveCount = 0;
      let totalScore = 0;
      
      questions.forEach(question => {
        // 判断题(0)、单选题(1)、多选题(2)为客观题
        // 填空题(3)、简答题(4)、互译题(5)为主观题
        if (question.questionType <= 2) {
          objectiveCount++;
        } else {
          subjectiveCount++;
        }
        
        // 累加每道题的分数计算总分
        if (question.score) {
          totalScore += parseFloat(question.score);
        }
      });
      
      objective.value = objectiveCount;
      subjective.value = subjectiveCount;
      
      // 设置总分值为所有题目分数之和
      maxScore.value = Math.round(totalScore);
    }
  }
};

// 查看作业详情
const checkAssignment = () => {
  // 跳转到作业详情页面
  sheep.$router.go(`/pages/group/detail/course/assignment-list/answer-detail?id=${assignmentId.value}&courseId=${courseId.value}&title=${encodeURIComponent(assignmentTitle.value)}`);
};
</script>

<style scoped lang="scss">
.score-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  z-index: -1;
}

.score-circle-container {
  margin-top: 180rpx;
  position: relative;
  width: 353rpx;
  height: 353rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0rpx 0rpx 30rpx 0rpx rgba(24,170,227,0.17);
  border-radius: 50%;
}

.circle-outer {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 60rpx solid #D7ECF4;
  box-sizing: border-box;
  z-index: 1;
}

.circle-progress {
  position: absolute;
  width: 330rpx;
  height: 330rpx;
  border-radius: 50%;
  z-index: 2;
}

.circle-inner {
  position: absolute;
  width: 230rpx;
  height: 230rpx;
  border-radius: 50%;
  background-color: #F7FCFF;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3;
}

.score-text {
  font-size: 120rpx;
  font-weight: 500;
  color: #36C0F6;
  display: flex;
  align-items: baseline;
  font-family: PingFang SC;
}

.score-value {
  font-size: 75rpx;
}

.score-unit {
  font-size: 42rpx;
  margin-left: 4rpx;
}

.bottom-area {
  position: fixed;
  bottom: 50rpx;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.questions-stats {
  margin-bottom: 31rpx;
  width: 408rpx;
  height: 29rpx;
}

.stats-text {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #5A5A5A;
  text-align: center;
}

.highlight-text {
  color: #36C0F6;
}

.footer {
  width: 100%;
  padding: 0 30rpx;
}

.check-btn {
  width: 690rpx;
  height: 82rpx;
  background: linear-gradient(270deg, rgba(70,173,240,0.89), rgba(0,222,255,0.89));
  border-radius: 41rpx;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  margin-bottom: 31rpx;
}
</style> 