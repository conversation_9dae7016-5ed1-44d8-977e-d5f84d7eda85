import request from '@/sheep/request';

const LessonCollectionApi = {
  // 收藏课程
  collectLesson: (lessonId) => {
    return request({
      url: '/course/lesson-collection/create',
      method: 'POST',
      data: { lessonId }
    });
  },
  
  // 取消收藏课程
  uncollectLesson: (lessonId) => {
    return request({
      url: '/course/lesson-collection/cancel',
      method: 'POST',
      params: { id: lessonId }
    });
  },
};

export default LessonCollectionApi; 