import request from '@/sheep/request';

// 分组（班级）管理
const GroupApi = {
	// ==================== 分组基本信息管理 ====================
	createGroup: (data) => {
		return request({
			url: '/member/group/create',
			method: 'POST',
			data,
			custom: {
				showLoading: false,
				showSuccess: true,
				successMsg: '创建成功'
			}
		});
	},

	updateGroup: (data) => {
		return request({
			url: '/member/group/update',
			method: 'PUT',
			data,
			custom: {
				showLoading: false,
				showSuccess: true,
				successMsg: '更新成功'
			}
		});
	},

	deleteGroup: (id) => {
		return request({
			url: '/member/group/delete',
			method: 'DELETE',
			params: { id },
			custom: {
				showLoading: false,
				showSuccess: true,
				successMsg: '删除成功'
			}
		});
	},

	getGroup: (id) => {
		return request({
			url: '/member/group/get',
			method: 'GET',
			params: { id },
			custom: {
				showLoading: false,
			}
		});
	},

	getMyGroup: (id) => {
		return request({
			url: '/member/group/get/my',
			method: 'GET',
			params: { id },
			custom: {
				showLoading: false,
			}
		});
	},

	// ==================== 分组成员管理 ====================
	joinGroup: (groupId) => {
		return request({
			url: '/member/group/join',
			method: 'POST',
			params: { groupId },
			custom: {
				showLoading: false,
				showSuccess: true,
				successMsg: '加入成功'
			}
		});
	},

	// 生成带签名的邀请链接
	generateInviteLink: (groupId, expireHours = 24) => {
		return request({
			url: '/member/group/generate-invite',
			method: 'POST',
			params: { groupId, expireHours },
			custom: {
				showLoading: false,
			}
		});
	},

	// 通过邀请链接加入分组
	joinGroupByInvite: (groupId, timestamp, sign) => {
		return request({
			url: '/member/group/join-by-invite',
			method: 'POST',
			params: { groupId, timestamp, sign },
			custom: {
				showLoading: false,
				showSuccess: true,
				successMsg: '加入成功'
			}
		});
	},

	quitGroup: (groupId) => {
		return request({
			url: '/member/group/quit',
			method: 'POST',
			params: { groupId },
			custom: {
				showLoading: false,
				showSuccess: true,
				successMsg: '退出成功'
			}
		});
	},

	kickGroupMember: (groupId, userId) => {
		return request({
			url: '/member/group/kick',
			method: 'POST',
			params: { groupId, userId },
			custom: {
				showLoading: false,
				showSuccess: true,
				successMsg: '操作成功'
			}
		});
	},

	// ==================== 分组查询 ====================
	getGroupUserList: (groupId) => {
		return request({
			url: `/member/group/list/${groupId}/user`,
			method: 'GET',
			custom: {
				showLoading: false,
			}
		});
	},

	// ==================== 用户相关分组 ====================
	getMyJoinedGroups: () => {
		return request({
			url: '/member/group/my/joined',
			method: 'POST',
			custom: {
				showLoading: false,
			}
		});
	},

	getMyCreatedGroups: () => {
		return request({
			url: '/member/group/my/created',
			method: 'POST',
			custom: {
				showLoading: false,
			}
		});
	},

	// ==================== 用户分组及成员 ====================
	getMyGroupsWithUsers: () => {
		return request({
			url: '/member/group/list/user',
			method: 'GET',
			custom: {
				showLoading: false,
			}
		});
	},
};

export default GroupApi;
