<template>
  <view class="phonetic-container">
    <easy-navbar title="音标训练" :transparent="true" />

    <!-- 添加全屏遮罩层，用于阻止用户在录音或评估期间点击返回按钮 -->
    <view class="mask-layer" v-if="isAssessing"></view>

    <scroll-view 
      class="phonetic-list" 
      scroll-y="true" 
      id="pageScrollView"
      :scroll-top="scrollTop"
	  :scroll-into-view="cardId"
      @touchstart="handleTouchStart"
      @touchend="handleTouchEnd"
      @touchcancel="handleTouchCancel">
      <view v-for="(item, index) in dataList" :key="item.id" :id="`card-${item.id}`" class="card-wrapper">
        <phonetic-card 
                     :id="item.id"
                     :index="item.sort"
                     :content="item.content"
                     :translate="item.translate"
                     :phonetic="item.phonetic"
                     :standardAudioUrl="item.standardAudioUrl"
                     :userAudioUrl="item.userAudioUrl"
                     :score="item.score"
                     :isExpanded="expandedCardId === item.id"
                     @expand-change="handleCardExpand(item.id, $event)"
                     @record-complete="handlerRecordComplete(item, $event)"
                     @recording-state-change="handleRecordingStateChange" />
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { onLoad, onUnload, onHide } from '@dcloudio/uni-app';
  import { PhoneticApi, PronunciationAssessmentApi } from '@/sheep/api/phonetic';
  import { reactive, ref, watch, nextTick, onMounted } from 'vue';
  import PhoneticCard from '@/pages/phonetic/components/phonetic-card.vue';
  import { AiPronunciationAssessmentTypeEnum } from '@/pages/phonetic/util/constants';
  import { LanguageEnum } from '@/sheep/util/language-detector';
  import sheep from '@/sheep';


  const projectId = ref();
  const dataList = ref([]);
  const expandedCardId = ref(null);
  const updateRecordMap = {};
  const isDataSaved = ref(false);
  const isAssessing = ref(false); // 是否正在录音或评估中
  let isAutoSwitching = false; // 防止重复触发
  let lastScrollTop = 0; // 上次滚动位置
  const scrollTop = ref(1); // scroll-view的滚动位置
  let touchStartY = 0; // 触摸开始的Y坐标
  let isTouching = false; // 是否正在触摸
  const DRAG_THRESHOLD = 55.7*3; // 拖动触发切换卡片阈值，单位rpx
  const cardId=ref();   //当前卡片id


  onMounted(() => {
	  
  });

  onUnload(() => {
    console.log("卸载");
    if (!isDataSaved.value) saveData();
  });
  
  onHide(() => {
    console.log("隐藏");
    if (!isDataSaved.value) saveData();
  });
  
  onLoad((options) => {
    if (options.id) {
      projectId.value = options.id;
      loadSubitemData();
    }
  });



  // 处理录音状态变化的方法
  const handleRecordingStateChange = (isRecording) => {
    console.log('录音状态变化:', isRecording);
    isAssessing.value = isRecording;
  };

  // 计算rpx到px的转换
  const rpxToPx = (rpx) => {
    // 获取设备信息，转换rpx为px
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const scale = screenWidth / 750; // 750是设计稿的宽度
    return rpx * scale;
  };

  // 添加触摸事件处理函数
  const handleTouchStart = (e) => {
    if (isAutoSwitching || isAssessing.value) return;
    
    touchStartY = e.touches[0].clientY;
    isTouching = true;
  };

  const handleTouchEnd = (e) => {
    if (!isTouching || isAutoSwitching || isAssessing.value) {
      isTouching = false;
      return;
    }
    
    const touchEndY = e.changedTouches[0].clientY;
    const dragDistance = touchStartY - touchEndY; // 向上滑动为正值
    
    // 转换rpx到px
    const thresholdInPx = rpxToPx(DRAG_THRESHOLD);

    // 获取当前展开卡片的索引
    const currentIndex = dataList.value.findIndex(item => item.id === expandedCardId.value);
    if (currentIndex < 0) {
      isTouching = false;
      return;
    }
    
    // 向上滑动且超过阈值时切换到下一张卡片
    if (dragDistance > thresholdInPx && expandedCardId.value) {
      // 只有当不是最后一张卡片时才切换
      if (currentIndex < dataList.value.length - 1) {
        switchToCard(currentIndex + 1);
      }
    }
    // 向下滑动且超过阈值时切换到上一张卡片
    else if (dragDistance < -thresholdInPx && expandedCardId.value) {
      // 只有当不是第一张卡片时才切换
      if (currentIndex > 0) {
        switchToCard(currentIndex - 1);
      }
    }
    
    isTouching = false;
  };

  const handleTouchCancel = (e) => {
    isTouching = false;
  };

  // 切换到指定索引的卡片
  const switchToCard = (targetIndex) => {
    if (isAssessing.value || targetIndex < 0 || targetIndex >= dataList.value.length) return;
    
    console.log(`准备切换到索引 ${targetIndex} 的卡片`);
    isAutoSwitching = true;
    
    // 关闭当前卡片
    expandedCardId.value = null;
    
    // 使用单一的延迟处理DOM更新和滚动
    setTimeout(() => {
      // 打开目标卡片
      expandedCardId.value = dataList.value[targetIndex].id;
      // 设置cardId以便滚动到当前卡片
      cardId.value = `card-${dataList.value[targetIndex].id}`;
      console.log('已切换到卡片:', expandedCardId.value);
      console.log("cardID===",cardId.value)
      
      // 重置切换状态，允许下一次滑动操作
      isAutoSwitching = false;
    }, 300);
  };
  
  // 验证滚动位置是否正确，如果不正确则再次尝试滚动
  const verifyScrollPosition = (cardIndex, retryCount = 0) => {
    // 不再使用此函数
    return;
  };


  // 切换到下一张卡片（保留此函数以兼容现有代码）
  const switchToNextCard = (currentIndex) => {
    switchToCard(currentIndex + 1);
  };

  const loadSubitemData = async () => {
    const { code, data } = await PhoneticApi.getPhoneticTrainSubitemVOListByProjectId(projectId.value);
    if (code !== 0) {
      return;
    }
    // 将每个 item 转为 reactive 对象，确保属性变更时视图自动更新
    dataList.value = data.map(item => reactive(item));
    
    // 默认不展开任何卡片
    expandedCardId.value = null;
    // 设置初始cardId值为空
    cardId.value = null;
  };

  // 处理卡片展开/收起事件
  const handleCardExpand = (id, isExpanded) => {
    if (isAssessing.value) return; // 如果正在评估中，不允许展开/收起
    
    if (isExpanded) {
      expandedCardId.value = id;
      // 设置cardId值以便滚动到当前卡片
      cardId.value = `card-${id}`;
    } else if (expandedCardId.value === id) {
      expandedCardId.value = null;
    }
  };

  const handlerRecordComplete = async (item, tempFilePath) => {
    try {
      // 设置评估状态为true
      isAssessing.value = true;
      
      // 先检查临时文件是否可访问
      await new Promise((resolve, reject) => {
        uni.getFileInfo({
          filePath: tempFilePath,
          success: (info) => {
            console.log('文件大小:', info.size, '字节');
            if (info.size < 1024) {
              reject(new Error('录音文件太小，可能无效'));
              return;
            }
            resolve();
          },
          fail: (err) => reject(err)
        });
      });
      
      // 准备评估数据
      const formData = {
        filePath: tempFilePath,
        referenceText: item.content,
        type: AiPronunciationAssessmentTypeEnum.TEXT.code,
        language: LanguageEnum.TH.code,
      };

      // 显示加载提示
      uni.showLoading({
        title: '评估中...',
        mask: true
      });

      // 发送评分请求，无重试
      const { code, data } = await PronunciationAssessmentApi.assess(formData);
      
      uni.hideLoading();
      
      if (code !== 0 || !data.success) {
        sheep.$helper.toast('评估失败，请重试');
        isAssessing.value = false; // 评估结束，设置状态为false
        return;
      }

      // 更新分数和录音，使用后端返回的audioUrl
      item.score = data.accuracyScore;
      item.userAudioUrl = data.audioUrl;

      // 保存退出页面时要更新的用户记录数据
      updateRecordMap[item.id] = {
        id: item.recordId,
        projectId: item.projectId,
        subitemId: item.id,
        score: item.score,
        userAudioUrl: data.audioUrl, // 使用后端返回的音频URL
      };
      
      console.log('录音处理完成，服务器音频URL:', data.audioUrl);
      
      // 评估结束，设置状态为false
      isAssessing.value = false;
    } catch (err) {
      uni.hideLoading();
      console.error('录音处理失败:', err);
      sheep.$helper.toast('录音处理失败，请重试');
      // 评估出错，设置状态为false
      isAssessing.value = false;
    }
  };

  // 保存数据
  const saveData = async () => {
    try {
      console.log("正常保存数据:",isDataSaved.value);
      if (isDataSaved.value) return;
      
      const keys = Object.keys(updateRecordMap);
      if (keys.length === 0) {
        isDataSaved.value = true;
        return;
      }

      uni.showLoading({
        title: '保存记录...',
        mask: true
      });

      // 保存记录
      const recordList = Object.values(updateRecordMap);
      if (recordList.length === 0) {
        uni.hideLoading();
        isDataSaved.value = true;
        return;
      }

      // 保存记录
      const result = await PhoneticApi.savePhoneticUserRecord(recordList);
      console.log('保存用户记录结果:', result);

      // 标记为已保存
      isDataSaved.value = true;
      uni.hideLoading();

      if (recordList.length > 0) {
        sheep.$helper.toast('发音练习已保存');
      }
    } catch (err) {
      uni.hideLoading();
      console.error('保存数据失败:', err);
      sheep.$helper.toast('保存失败，请重试');
    }
  };
</script>

<style scoped lang="scss">
  .phonetic-container {
    min-height: 100vh;
    background-color: #A9EAFF;
    display: flex;
    flex-direction: column;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 96rpx; // 从easy-navbar的底部开始
      left: 0;
      width: 100%;
      height: 350rpx;
      background-image: url('#{$baseImgUrl}/live/live-bg.png');
      background-size: 100% auto;
      background-repeat: no-repeat;
      background-position: top center;
      z-index: 0;
    }
  }

  .phonetic-list {
   // flex: 1;
    //padding-top: 96rpx; // 从easy-navbar底部开始
    height: calc(100vh - 188rpx); /* 使用navbar的实际高度 */
  }

  .card-wrapper {
    width: 100%;
	&:last-child{
		// padding-bottom: 300rpx;
	}
  }

  /* 遮罩层样式 */
  .mask-layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 180rpx; /* 仅覆盖navbar高度 */
    background-color: transparent; /* 透明背景 */
    z-index: 999; /* 确保在最上层 */
  }
</style>
