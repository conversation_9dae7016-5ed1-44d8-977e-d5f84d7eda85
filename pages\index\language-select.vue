<template>
  <view class="language-select" :style="{ backgroundImage: 'url(' + backgroundImage + ')' }">
    <!-- 左侧区域：返回按钮 -->
    <view class="left">
        <image
          class="back-icon"
          mode="widthFix"
          :src="sheep.$url.cdn('/common/back.png')"
          @click="goBack"
        />
      </view>
    <view class="header-bg">
      <view class="title">
        请选择你想学习的<span class="highlight">语言</span>
      </view>
    </view>
    <view class="content-wrapper">
      <view class="countries-container">
        <view class="country-row" v-for="i in Math.ceil(countries.length / 2)" :key="i">
          <view
            v-for="(country, index) in countries.slice((i-1)*2, i*2)"
            :key="country.name"
            class="country-item"
            :class="{ selected: selectedCountry && selectedCountry.name === country.name }"
            @click="selectCountry(country)"
          >
            <image :src="sheep.$url.cdn(country.icon)" class="country-icon" />
            <view class="country-text">
              <view class="country-name-wrapper">
                <span class="highlight-bg">{{ country.name }}</span>
              </view>
              <view class="country-english">{{ country.englishName }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="confirm-button">
      <button class="confirm-btn" @click="confirmSelection">选好了</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import sheep from '@/sheep';

const selectedCountry = ref(null);
const countries = [
  { name: '马来西亚', englishName: 'Malaysia', icon: '/index/Malaysia.png', code: 'my' },
  { name: '柬埔寨', englishName: 'Cambodia', icon: '/index/Cambodia.png', code: 'kh' },
  { name: '泰国', englishName: 'Thailand', icon: '/index/Thailand.png', code: 'th' },
  { name: '菲律宾', englishName: 'Philippines', icon: '/index/Philippines.png', code: 'ph' },
  { name: '老挝', englishName: 'Laos', icon: '/index/Laos.png', code: 'la' },
  { name: '印度尼西亚', englishName: 'Indonesia', icon: '/index/Indonesia.png', code: 'id' },
  { name: '文莱', englishName: 'Brunei', icon: '/index/Brunei.png', code: 'bn' },
  { name: '新加坡', englishName: 'Singapore', icon: '/index/Singapore.png', code: 'sg' },
  { name: '越南', englishName: 'Vietnam', icon: '/index/Vietnam.png', code: 'vn' },
  { name: '缅甸', englishName: 'Myanmar', icon: '/index/Myanmar.png', code: 'mm' }
];

// 背景图片路径 - 使用字符串拼接避免自动添加/uniapp前缀
const backgroundImage = computed(() => {
  return sheep.$url.cdn('/index/language_select.png'); // 获取基础URL，不包含任何路径
});

// 返回按钮点击次数计数器
let backClickCount = 0;

// 自动选择泰语并进入主界面
const autoSelectThaiAndEnter = () => {
  // 查找泰语选项
  const thaiLanguage = countries.find(country => country.name === '泰国');
  if (thaiLanguage) {
    // 保存选择的语言到本地存储
    uni.setStorageSync('selected_language', {
      name: thaiLanguage.name,
      englishName: thaiLanguage.englishName,
      code: thaiLanguage.code
    });

    // 显示提示
    uni.showToast({
      title: '已自动选择泰语',
      icon: 'none',
      duration: 1500
    });

    // 延迟跳转，让用户看到提示
    setTimeout(() => {
      // 跳转到首页
      sheep.$router.go('/pages/home/<USER>');
    }, 1500);
  }
};

// 返回上一页
const goBack = () => {
  // 判断是否从编辑页面进入
  const fromEdit = uni.getStorageSync('from_language_edit');
  // 判断是否首次启动进入
  const isFirstLaunch = uni.getStorageSync('is_first_launch');

  if (fromEdit) {
    // 如果是从编辑页面进入，正常返回
    sheep.$router.back();
  } else if (isFirstLaunch) {
    // 首次启动进入的处理
    backClickCount++;

    if (backClickCount === 1) {
      // 第一次点击返回，显示提示
      uni.showModal({
        title: '提示',
        content: '请选择一种语言以继续使用应用',
        showCancel: false,
        confirmText: '我知道了'
      });
    } else {
      // 第二次点击返回，自动选择泰语
      autoSelectThaiAndEnter();
      // 清除首次启动标记
      uni.removeStorageSync('is_first_launch');
    }
  } else {
    // 其他情况正常返回
    sheep.$router.back();
  }
};

// 默认选择泰语
const selectThaiByDefault = () => {
  const thaiLanguage = countries.find(country => country.name === '泰国');
  if (thaiLanguage) {
    selectedCountry.value = thaiLanguage;
  }
};

// 检查是否已经选择过语言
onMounted(() => {
  // 默认选中泰国
  selectThaiByDefault();

  const savedLanguage = uni.getStorageSync('selected_language');
  if (savedLanguage) {
    const found = countries.find(country => country.code === savedLanguage.code);
    if (found) {
      selectedCountry.value = found;
    }
  }

  // 如果是从编辑页面进入，不自动跳转
  const fromEdit = uni.getStorageSync('from_language_edit');
  if (fromEdit) {
    uni.removeStorageSync('from_language_edit');
  } else if (savedLanguage) {
    // 如果已经选择过语言且不是从编辑页面进入，直接跳转到首页
    sheep.$router.go('/pages/home/<USER>');
  }
});

const selectCountry = (country) => {
  if (country.name === '泰国') {
    selectedCountry.value = country;
  } else {
    // 显示功能未开放提示
    uni.showToast({
      title: '该语言功能暂未开放',
      icon: 'none',
      duration: 2000
    });
  }
};

const confirmSelection = () => {
  if (selectedCountry.value) {
    if (selectedCountry.value.name === "泰国") {
      // 保存选择的语言到本地存储
      uni.setStorageSync('selected_language', {
        name: selectedCountry.value.name,
        englishName: selectedCountry.value.englishName,
        code: selectedCountry.value.code
      });

      // 跳转到首页
      sheep.$router.go('/pages/home/<USER>');
    } else {
      sheep.$helper.inDevMsg();
    }
  } else {
    uni.showToast({
      title: '请选择一种语言',
      icon: 'none'
    });
  }
};
</script>

<style scoped>
.language-select {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 0;
  text-align: center;
  position: relative;
}

.left {
  position: absolute;
  left: 15px;
  top: 40px;
  display: flex;
  align-items: center;
  z-index: 101;
}

.back-icon {
  width: 19rpx;
  padding: 10rpx;
}

.header-bg {
  width: 100%;
  padding-top: 40px;
}

.content-wrapper {
  padding-top: 120rpx;
}

.title {
  padding-top:249rpx;
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
}

.highlight {
  color: #2196f3;
  background: #FFF4E7;
  border-radius: 10rpx;
  padding: 0 8rpx;
  position: relative;
  display: inline-block;
  height: 19rpx;
  line-height: 0rpx;
  vertical-align: baseline;
}

.highlight-bg {
  background: #FFF4E7;
  border-radius: 10rpx;
  padding: 0 8rpx;
  position: relative;
  display: inline-block;
  height: 15rpx;
  line-height: 0rpx;
  vertical-align: baseline;
}

.countries-container {
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
}

.country-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  width: 100%;
}

.country-item {
  width: calc(45% - 31rpx);
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px 0 rgba(33, 150, 243, 0.06);
  border: 2px solid transparent;
  padding: 20rpx 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  transition: border-color 0.2s, box-shadow 0.2s;
  position: relative;
}

.country-item.selected {
  border-color: #75DAFF;
  background: #F2FDFE;
  box-shadow: 0 4px 16px 0 rgba(33, 150, 243, 0.12);
}

.country-icon {
  width: 71rpx;
  height: 71rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.country-text {
  padding-top: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.country-name-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 8rpx;
}

.country-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
}

.country-english {
  font-size: 25rpx;
  color: #888;
  text-align: center;
}

.confirm-button {
  margin-top: 32px;
  width: 100%;
  padding: 0 16px;
  position: absolute;
  left: 0;
  bottom: 32px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}

.confirm-btn {
  width: 691rpx;
  height: 81rpx;
  background: linear-gradient(90deg, #1ec8e1 0%, #2196f3 100%);
  color: white;
  border-radius: 24px;
  font-size: 18px;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(33, 150, 243, 0.10);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
