<template>
  <view class="container">
    <easy-navbar title="" />

    <view class="content">
      <!-- 标题区域 -->
      <view class="title-section">
        <text class="main-title">将内容翻译为?</text>
        <text class="sub-title">กรุณาเปลี่ยนเป็นภาษาอะไร</text>
      </view>

      <!-- 语言选择区域 -->
      <view class="language-list">
        <view
          class="language-item"
          v-for="(language, index) in languageList"
          :key="language.code"
          @click="selectLanguage(language)"
        >
          <view class="language-card" :class="{ 'selected': selectedLanguage?.code === language.code }">
            <text class="language-name">{{ language.name }}</text>
            <text class="language-native">{{ language.nativeName }}</text>
            <view v-if="selectedLanguage?.code === language.code" class="check-icon">✓</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import sheep from '@/sheep'

// 语言数据接口
interface Language {
  code: string
  name: string
  nativeName: string
}

// 语言列表数据
const languageList = ref<Language[]>([
  {
    code: 'zh',
    name: '中文',
    nativeName: 'ภาษาจีน'
  },
  {
    code: 'th',
    name: '泰语',
    nativeName: 'ภาษาไทย'
  }
  // 可以根据需要添加更多语言
  // {
  //   code: 'en',
  //   name: '英语',
  //   nativeName: 'English'
  // }
])

// 当前选中的语言
const selectedLanguage = ref<Language | null>(null)

// 选择语言
const selectLanguage = (language: Language) => {
  selectedLanguage.value = language

  // 保存选择的语言到本地存储
  uni.setStorageSync('interpreter_selected_language', {
    code: language.code,
    name: language.name,
    nativeName: language.nativeName
  })

  // 延迟跳转到index页面
  setTimeout(() => {
    sheep.$router.go('/pages/tool/interpreter/index')
  }, 1500)
}
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #F7FCFF;
}

.content {
  padding: 189rpx 40rpx;
}

.title-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.main-title {
  font-weight: 600;
  font-size: 38rpx;
  color: #3D3D3D;
  display: block;
  margin-bottom: 32rpx;
  font-family: PingFang SC;
}

.sub-title {
  font-size: 29rpx;
  color: #919090;
  display: block;
  font-family: Adobe Thai;
}

.language-list {
  display: flex;
  flex-direction: column;
}

.language-item {
  width: 100%;
  margin-bottom: 45rpx;
}

.language-card {
  width: 568rpx;
  height: 171rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 13rpx;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  position: relative;

  &:active {
    transform: scale(0.98);
    background-color: #f8f8f8;
  }

  &.selected {
    background: #E8F4FD;
    border: 2rpx solid #2DC1FF;
    box-shadow: 0 4rpx 16rpx rgba(45, 193, 255, 0.2);
  }
}

.language-name {
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', 'Microsoft YaHei', sans-serif;
  font-size: 29rpx;
  color: #494949;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.language-native {
  font-family: 'Sarabun', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 29rpx;
  color: #919090;
  display: block;
  line-height: 1.2;
}

.check-icon {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  background: #2DC1FF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: bold;
}
</style>
