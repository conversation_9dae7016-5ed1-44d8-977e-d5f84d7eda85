<template>
  <view class="container">
    <easy-navbar title="学习集" :transparent="true" />
    <view v-if="isFromGroupAddToClass" class="add-to-class-tip">
      <text>选择要在班级中使用的学习集: {{ targetClassName }}</text>
    </view>
    <view class="tab-box" :class="{ 'three-tabs': isFromGroupAddToClass }">
      <view class="tab-item" :class="{ selected: selectedTab === 0 }" @click="selectTab(0)">
        我收藏的
      </view>
      <view class="tab-item" :class="{ selected: selectedTab === 1 }" @click="selectTab(1)">
        我创建的
      </view>
      <view v-if="isFromGroupAddToClass" class="tab-item" :class="{ selected: selectedTab === 2 }" @click="selectTab(2)">
        已分享的
      </view>
    </view>
    <view class="content-area">
      <view class="uni-list-item-scarf" v-show="selectedTab === 0">
        <view class="uni-list-item" v-show="selectedTab === 0 && favoriteSets.length === 0">
          <s-empty text="暂无收藏的学习集" />
        </view>

        <easy-card v-show="selectedTab === 0" v-for="(set) in favoriteSets" :key="set.id" :title="set.title" :coverUrl="set.coverUrl"
          :nickname="set.nickname" :avatar="set.avatar" :isStore="set.isStore"
          :isSelected="isFromGroupAddToClass && selectedItems.includes(set.id)" @handleClick="goToSetDetail(set.id)"
          @handleLongPress="showActionSheet(set.id, $event)" />
      </view>
      <view class="uni-list-item-scarf" v-show="selectedTab === 1">
        <view class="uni-list-item" v-show="selectedTab === 1 && createdSets.length === 0">
          <s-empty text="暂无创建的学习集" />
        </view>

        <easy-card v-show="selectedTab === 1" v-for="(set) in createdSets" :key="set.id" :title="set.title" :coverUrl="set.coverUrl"
          :nickname="set.nickname" :avatar="set.avatar" :isStore="set.isStore"
          :isSelected="isFromGroupAddToClass && selectedItems.includes(set.id)" @handleClick="goToSetDetail(set.id)"
          @handleLongPress="showActionSheet(set.id, $event)" />
      </view>
      <view class="uni-list-item-scarf" v-show="selectedTab === 2">
        <view class="uni-list-item" v-show="selectedTab === 2 && sharedSets.length === 0">
          <s-empty text="暂无已分享的学习集" />
        </view>

        <easy-card v-show="selectedTab === 2" v-for="(set) in sharedSets" :key="set.id" :title="set.title" :coverUrl="set.coverUrl"
          :nickname="set.nickname" :avatar="set.avatar" :isStore="set.isStore"
          :isSelected="isFromGroupAddToClass && selectedItems.includes(set.id)" @handleClick="goToSetDetail(set.id)"
          @handleLongPress="showActionSheet(set.id, $event)" />
      </view>
    </view>
    <view class="create-btn" v-show="selectedTab === 1">
      <view class="btn-inner" @click="createNewSet">
        <text class="icon">+</text>
        <text class="text">创建学习集</text>
      </view>
    </view>
    <view class="batch-add-btn" v-show="isFromGroupAddToClass">
      <view class="btn-inner" @click="batchAddToClass">
        <text class="text">保存学习集选择 {{ selectedItems.length > 0 ? `(${selectedItems.length})` : '' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import SetApi from '@/sheep/api/set';
import sheep from '@/sheep';

// 使用索引来表示选中的标签
const selectedTab = ref(0);
// 用户信息
const userInfo = ref(null);
// 是否来自首页换书
const isFromHomeChangeBook = ref(false);
// 是否来自班级添加学习集
const isFromGroupAddToClass = ref(false);
// 目标班级ID
const targetClassId = ref('');
// 目标班级名称
const targetClassName = ref('');
// 已选择的学习集列表
const selectedItems = ref([]);
// 班级原有的学习集ID列表
const originalClassSetIds = ref([]);

function selectTab(index) {
  selectedTab.value = index;
}

// 创建的学习集列表
const createdSets = ref([]);
// 收藏的学习集列表
const favoriteSets = ref([]);
// 已分享的学习集列表
const sharedSets = ref([]);
// 加载状态
const loading = ref(false);
// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  // 添加筛选条件以仅获取当前用户创建的学习集
  own: true,
});

// 获取班级中已有的学习集列表
const getClassSets = () => {
  if (!targetClassId.value) return;
  SetApi.getWordSetPageByClass({ classId: targetClassId.value })
    .then((res) => {
      if (res.data) {
        // 保存班级中已有的学习集ID列表
        originalClassSetIds.value = res.data.list.map(item => item.id);
        // 将已有学习集ID添加到选中列表
        selectedItems.value = [...originalClassSetIds.value];
      }
    })
};

// 获取已分享的学习集列表（班级中的学习集）
const getSharedSets = () => {
  if (!targetClassId.value) return;
  loading.value = true;
  SetApi.getWordSetPageByClass({ classId: targetClassId.value })
    .then((res) => {
      if (res.code === 0) {
        sharedSets.value = res.data.list || [];
      } else {
        sheep.$helper.toast(res.msg || '获取数据失败');
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
      sheep.$helper.toast('网络异常，请稍后重试');
    });
};

// 获取用户创建的学习集列表
const getCreatedSets = () => {
  loading.value = true;
  SetApi.getWordSetPageMy(queryParams)
    .then((res) => {
      if (res.code === 0) {
        createdSets.value = res.data.list || [];
      } else {
        sheep.$helper.toast(res.msg || '获取数据失败');
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
      sheep.$helper.toast('网络异常，请稍后重试');
    });
};

// 获取收藏的学习集列表
const getFavoriteSets = () => {
  loading.value = true;
  SetApi.getWordSetPageFavourite(queryParams)
    .then((res) => {
      if (res.code === 0) {
        // 处理收藏学习集数据
        favoriteSets.value = res.data.list || [];
      } else {
        sheep.$helper.toast(res.msg || '获取数据失败');
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
      sheep.$helper.toast('网络异常，请稍后重试');
    });
};

// 跳转到学习集详情页或设置为置顶或添加到班级
const goToSetDetail = async (id) => {
  // 如果是从首页换书进入，则设置为置顶学习集
  if (isFromHomeChangeBook.value) {
    const response = await SetApi.setTopWordSet(id);
    uni.hideLoading();

    if (response.code === 0) {
      // 通知首页刷新数据
      uni.$emit('refreshLearningSet', {
        timestamp: Date.now()
      });
      // 返回首页
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    }
    return;
  }

  // 如果是从班级添加学习集进入，则添加到班级
  if (isFromGroupAddToClass.value) {
    // 如果已选中，则取消选中；如果未选中，则选中
    const index = selectedItems.value.indexOf(id);
    if (index > -1) {
      selectedItems.value.splice(index, 1);
    } else {
      selectedItems.value.push(id);
    }
    return;
  }

  // 正常跳转到学习集详情页
  sheep.$router.go(`/pages/set/word-card?id=${id}`);
};

// 批量添加学习集到班级
const batchAddToClass = async () => {
  // 找出需要新增的学习集ID（当前选中但不在原列表中的）
  const newSetIds = selectedItems.value.filter(id => !originalClassSetIds.value.includes(id));

  // 找出需要删除的学习集ID（原列表中有但当前未选中的）
  const deleteSetIds = originalClassSetIds.value.filter(id => !selectedItems.value.includes(id));
  let successCount = 0;
    // 执行添加操作
    if (newSetIds.length > 0) {
      const addResponse = await SetApi.batchShareToClass({
        setIds: newSetIds,
        classId: targetClassId.value
      });

      if (addResponse.code !== 0) {
        throw new Error(addResponse.msg || '添加失败');
      }
      successCount += newSetIds.length;
    }

    // 执行删除操作
    if (deleteSetIds.length > 0) {
      const deleteResponse = await SetApi.batchDeleteFromClass({
        setIds: deleteSetIds,
        classId: targetClassId.value
      });

      if (deleteResponse.code !== 0) {
        throw new Error(deleteResponse.msg || '删除失败');
      }
      successCount += deleteSetIds.length;
    }

    // 显示成功消息（即使没有实际变化也提示）
    const message = successCount > 0 ? `成功更新${successCount}个学习集` : '已保存当前选择';
    sheep.$helper.toast(message);

    // 返回班级编辑页面
    setTimeout(() => {
      uni.navigateBack();
    }, 1000);


};

// 添加长按编辑功能
const showActionSheet = async (id, e) => {

  if (e) e.stopPropagation();

  // 如果是从班级添加学习集进入，则不显示操作菜单
  if (isFromGroupAddToClass.value) {
    return;
  }

  // 获取当前用户信息
  const userStore = sheep.$store('user');
  let currentUserInfo = userStore.userInfo;
  if (!currentUserInfo || !currentUserInfo.id) {
    currentUserInfo = await userStore.updateUserData();
  }
  const currentUserId = currentUserInfo?.id;

  // 查找当前学习集的创建者ID
  let setData = null;
  if (selectedTab.value === 0) {
    setData = favoriteSets.value.find(set => set.id === id);
  } else if (selectedTab.value === 1) {
    setData = createdSets.value.find(set => set.id === id);
  } else {
    setData = sharedSets.value.find(set => set.id === id);
  }

  const isCreator = setData && setData.userId && currentUserId && setData.userId == currentUserId;

  // 根据是否为创建者设置不同的菜单项
  const itemList = isCreator ? ['编辑', '删除'] : ['编辑（无权限）', '删除（无权限）'];
  const itemColor = isCreator ? '#000000' : '#CCCCCC';

  // 显示操作菜单
  uni.showActionSheet({
    itemList: itemList,
    itemColor: itemColor,
    success: (res) => {
      if (!isCreator) {
        // 非创建者点击时提示无权限
        sheep.$helper.toast('您没有权限操作此学习集');
        return;
      }

      if (res.tapIndex === 0) {
        // 编辑 - 使用setTimeout确保ActionSheet关闭后再跳转
        setTimeout(() => {
          sheep.$router.go(`/pages/set/edit?id=${id}`);
        }, 100);
      } else if (res.tapIndex === 1) {
        // 删除
        confirmDeleteWordSet(id);
      }
    },
  });
};

// 确认删除
const confirmDeleteWordSet = (id) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个学习集吗？删除后将无法恢复。',
    confirmText: '删除',
    confirmColor: '#ff0000',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '删除中...',
            mask: true,
          });
          const response = await SetApi.deleteWordSet(id);
          uni.hideLoading();

          if (response.code === 0) {
            sheep.$helper.toast('删除成功');
            // 刷新列表
            if (selectedTab.value === 1) {
              getCreatedSets();
            } else if (selectedTab.value === 2) {
              getSharedSets();
            } else {
              getFavoriteSets();
            }

            // 通知其他页面刷新数据
            uni.$emit('refreshWordSets', {
              timestamp: Date.now(),
            });
          } else {
            sheep.$helper.toast(response.msg || '删除失败');
          }
        } catch (error) {
          uni.hideLoading();
          sheep.$helper.toast('网络异常，请稍后再试');
          console.error('删除学习集失败:', error);
        }
      }
    },
  });
};

// 创建新学习集
const createNewSet = () => {
  sheep.$router.go('/pages/set/create');
};

// 监听标签切换
watch(selectedTab, (newVal) => {
  if (newVal === 1) {
    getCreatedSets();
  } else if (newVal === 2) {
    getSharedSets();
  } else {
    getFavoriteSets();
  }
});

onShow(() => {
  // 默认加载收藏的学习集数据
  if (selectedTab.value === 0) {
    getFavoriteSets();
  } else if (selectedTab.value === 1) {
    getCreatedSets();
  } else if (selectedTab.value === 2) {
    getSharedSets();
  }
});

onLoad((options) => {
  if (options.selectedTab) {
    selectedTab.value = parseInt(options.selectedTab);
  }

  // 检查是否来自首页换书
  if (options.from === 'home' && options.action === 'changeBook') {
    isFromHomeChangeBook.value = true;
    // 如果是来自首页换书，默认切换到"我创建的"标签
    selectedTab.value = 1;
  }

  // 检查是否来自班级添加学习集
  if (options.from === 'group' && options.action === 'addToClass') {
    isFromGroupAddToClass.value = true;
    targetClassId.value = options.classId;
    targetClassName.value = options.className || '未知班级';
    // 默认切换到"我创建的"标签
    selectedTab.value = 1;

    // 获取班级已有的学习集
    getClassSets();
  }
});
</script>

<style scoped lang="scss">
.uni-list-item-scarf {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .uni-list-item {
    width: 100%;
  }
}


.container {
  width: 100%;
  margin: 0 auto;
  background-color: #f8fcff;
  min-height: 100vh;
  position: relative;
  padding-bottom: 120rpx;
}

.add-to-class-tip {
  width: 688rpx;
  padding: 20rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #239eed;
  background-color: rgba(35, 158, 237, 0.1);
  border-radius: 10rpx;
  margin: 20rpx auto 0;
}

.tab-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 688rpx;
  height: 100rpx;
  background: #ffffff;
  border-radius: 50rpx;
  margin: 20rpx auto;

  .tab-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 329rpx;
    height: 80rpx;
    background: #ffffff;
    border-radius: 40rpx;
    color: #3a3939;
    font-size: 32rpx;

    &.selected {
      background: #239eed;
      color: #ffffff;
    }
  }

  &.three-tabs {
    .tab-item {
      width: 216rpx;
      font-size: 28rpx;
    }
  }
}

.content-area {
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.create-btn,
.batch-add-btn {
  position: fixed;
  bottom: 48rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 686rpx;
  height: 98rpx;

  .btn-inner {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 49rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

    .icon {
      font-size: 40rpx;
      color: #239eed;
      margin-right: 12rpx;
    }

    .text {
      font-size: 32rpx;
      color: #239eed;
      font-weight: 500;
    }
  }
}

.batch-add-btn {
  .btn-inner {
    background: #239eed;

    .text {
      color: #ffffff;
    }
  }
}
</style>
