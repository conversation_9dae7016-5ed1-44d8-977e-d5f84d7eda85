<script setup>
  import { onLaunch, onShow, onError } from '@dcloudio/uni-app';
  import { ShoproInit } from './sheep';

  onLaunch(() => {
    // 延时隐藏原生导航栏
    // setTimeout(() => {
    //   uni.hideTabBar();
    // }, 200);

    // 加载Shopro底层依赖
    ShoproInit();
    
    // 检查是否已选择语言
    checkLanguageSelection();
  });
  
  // 检查是否已选择语言
  const checkLanguageSelection = () => {
    const savedLanguage = uni.getStorageSync('selected_language');
    // 如果没有选择过语言，跳转到语言选择页面
    if (!savedLanguage) {
      // 设置标记，表示是首次启动进入语言选择页面，返回时不应跳转到任何页面
      uni.setStorageSync('is_first_launch', true);
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/index/language-select'
        });
      }, 300);
    }
  };

  onShow((options) => {
    // #ifdef APP-PLUS
    // 获取urlSchemes参数
    const args = plus.runtime.arguments;
    if (args) {
    }

    // 获取剪贴板
    uni.getClipboardData({
      success: (res) => {},
    });
    // #endif

    // #ifdef MP-WEIXIN
    // 确认收货回调结果
    console.log(options, 'options');
    // #endif
  });
</script>

<style lang="scss">
  @import '@/sheep/scss/index.scss';
</style>
