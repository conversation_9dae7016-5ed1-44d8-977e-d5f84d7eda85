<template>
  <view class="phonetic-card" :class="{ 'expanded': expanded }" @tap="toggleExpand">
    <!-- 折叠状态 -->
    <view class="card-content collapsed" v-if="!expanded">
      <view class="left-content">
        <view class="number-text">{{ index }}.{{ content }}</view>
        <view class="translate">{{ translate }}</view>
      </view>
      <view class="right-content" v-if="hasScore">
        <view class="score">{{ score }}</view>
      </view>
    </view>

    <!-- 展开状态 -->
    <view class="card-content expanded" v-else>
      <!-- 左上角序号 -->
      <view class="number-badge">
        <image class="number-bg" :src="numberBgUrl" mode="aspectFit" />
        <text class="number-text">{{ index }}</text>
      </view>

      <!-- 中间内容区域 -->
      <view class="content-area" :class="{ 'content-area-no-score': !hasScore }">
        <!-- 音标 -->
        <view class="phonetic-text">{{ phonetic }}</view>
        <!-- 目标文 -->
        <view class="content-text">{{ content }}</view>
        <!-- 翻译 -->
        <view class="translate">{{ translate }}</view>
      </view>

      <view class="score-area" v-if="hasScore">
        <view class="score-container">
          <image v-if="elementsVisible" :src="scoreImage" mode="aspectFit" 
            :class="{'score-image-animation': showScoreImage}" />
          <view v-if="elementsVisible" class="score-text-container">
            <text class="score-text" 
              :class="{'score-text-animation': showScoreImage}">{{ score }}</text>
            <text class="score-comment" 
              :class="{'comment-animation': showScoreImage}">{{ comment }}</text>
          </view>
        </view>

        <!-- 分段式进度条 -->
        <view class="score-progress-container">
          <!-- 等级标签 - 放在上方 -->
          <view class="level-labels">
            <view class="level-label">很差</view>
            <view class="level-label">较差</view>
            <view class="level-label">一般</view>
            <view class="level-label">良好</view>
            <view class="level-label">优秀</view>
          </view>

          <!-- 进度条背景 -->
          <view class="score-progress-bg">
            <!-- 单个连续进度条 -->
            <view class="score-section-container">
              <view class="score-fill" :class="{'with-transition': enableTransition}" :style="{ width: `${animatedScore}%` }"></view>

              <!-- 视觉分隔线 -->
              <view class="segment-divider" style="left: 19.5%"></view>
              <view class="segment-divider" style="left: 39.5%"></view>
              <view class="segment-divider" style="left: 59.5%"></view>
              <view class="segment-divider" style="left: 79.5%"></view>
            </view>
          </view>

          <!-- 分数标签 -->
          <view class="score-labels">
            <view class="score-label">0</view>
            <view class="score-label">39</view>
            <view class="score-label">59</view>
            <view class="score-label">74</view>
            <view class="score-label">89</view>
            <view class="score-label">100</view>
          </view>
        </view>
      </view>

      <!-- 底部按钮区域 -->
      <view class="button-area">
        <!-- 标准发音 -->
        <view class="action-button" @tap.stop="playStandardAudio" :class="standardAudioClass">
          <image class="button-icon" :src="isPlayingStandard ? playingStandardIconUrl : standardIconUrl"
            mode="aspectFit" />
          <text class="button-text">标准发音</text>
        </view>

        <!-- 按住录音 -->
        <view class="action-button record-button" @touchstart.stop="startRecording" @touchmove.stop="moveRecording"
          @touchend.stop="stopRecording" @touchcancel.stop="cancelRecording">

          <!-- 按住录音之前样式 -->
          <view class="record-button-before" :class="recordBeforeClass">
            <image class="button-icon" :src="recordIconUrl" mode="aspectFit" />
            <text class="button-text">按住录音</text>
          </view>

          <!-- 按住录音之后样式 -->
          <view class="record-button-after" :class="recordAfterClass">
            <!-- 替换静态wave.gif为动态音浪 -->
            <view class="wave-container">
              <view 
                v-for="(bar, index) in waveBars" 
                :key="index" 
                class="wave-bar"
                :style="{
                  height: `${bar}rpx`,
                  animationDelay: `${index * 0.05}s`
                }"
              ></view>
            </view>
            <text class="text">上滑取消</text>
          </view>

          <!-- 滑动取消样式 -->
          <view class="record-button-slide" :class="recordSlideClass">
            <!-- 替换静态wave.gif为动态音浪 -->
            <view class="wave-container">
              <view 
                v-for="(bar, index) in waveBars" 
                :key="index" 
                class="wave-bar wave-bar-cancel"
                :style="{
                  height: `${bar}rpx`,
                  animationDelay: `${index * 0.05}s`
                }"
              ></view>
            </view>

            <view class="solidCancel">
              <text class="text">松手取消</text>
              <image class="chacha" :src="solidChacha" mode="widthFix" />
            </view>
          </view>
        </view>

        <!-- 我的发音 -->
        <view class="action-button" @tap.stop="playUserAudio" :class="userAudioClass">
          <image class="button-icon" :src="isPlayingUser ? playingUserIconUrl : userIconUrl" mode="aspectFit" />
          <text class="button-text">我的发音</text>
        </view>

      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, onUnmounted, computed, nextTick, onMounted } from 'vue';
import sheep from '@/sheep';
import recorderManagerSingleton from '@/sheep/recorderManager';

// 添加节流函数辅助优化
const throttle = (fn, delay) => {
  let lastCall = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return fn.apply(this, args);
    }
  };
};

// 定义组件属性
const props = defineProps({
  /**
   * ID
   */
  id: {
    type: [Number, String],
    required: true
  },
  /**
   * 序号
   */
  index: {
    type: [Number, String],
    default: 1,
  },
  /**
   * 音标
   */
  phonetic: {
    type: String,
    default: '',
  },
  /**
   * 目标语言文本
   */
  content: {
    type: String,
    default: '',
  },
  /**
   * 翻译文本
   */
  translate: {
    type: String,
    default: '',
  },
  /**
   * 标准音频URL
   */
  standardAudioUrl: {
    type: String,
    default: '',
  },
  /**
   * 用户录音URL
   */
  userAudioUrl: {
    type: String,
    default: '',
  },
  /**
   * 分数
   */
  score: {
    type: [Number, String],
    default: undefined,
  },
  /**
   * 是否展开
   */
  isExpanded: {
    type: Boolean,
    default: false,
  },
});

// 定义事件
const emit = defineEmits(['record-complete', 'expand-change', 'scroll-position', 'recording-state-change']);

// 展开状态
const expanded = ref(props.isExpanded);

// 监听isExpanded属性变化
watch(() => props.isExpanded, (newVal) => {
  expanded.value = newVal;
  if (expanded.value && hasScore.value) {
    // 如果是展开状态且有分数，触发进度条动画
    resetAndAnimateScore();
  }
});

// 图片资源URL
const numberBgUrl = ref(sheep.$url.cdn('/phonetic/note.png'));
const standardIconUrl = ref(sheep.$url.cdn('/phonetic/play.png'));
const playingStandardIconUrl = ref(sheep.$url.cdn('/phonetic/pause.png'));
const recordIconUrl = ref(sheep.$url.cdn('/phonetic/microphone.png'));
const recordingIconUrl = ref(sheep.$url.cdn('/phonetic/wave.gif'));
const userIconUrl = ref(sheep.$url.cdn('/phonetic/talk.png'));
const playingUserIconUrl = ref(sheep.$url.cdn('/phonetic/shut_up.png'));
const solidChacha = ref(sheep.$url.cdn('/phonetic/chacha.png'));

// 状态变量
const isPlayingStandard = ref(false);
const isPlayingUser = ref(false);
const isRecording = ref(false);
const isCancelArea = ref(false); // 是否在取消区域
const touchStartY = ref(0); // 触摸开始的Y坐标
const cancelThreshold = 60; // 判定为取消的阈值（向上移动的距离）

// 计算属性：根据状态控制显示样式的类名
const recordBeforeClass = computed(() => ({
  'show-element': !isRecording.value,
  'hide-element': isRecording.value
}));

const recordAfterClass = computed(() => ({
  'show-element': isRecording.value && !isCancelArea.value,
  'hide-element': !isRecording.value || isCancelArea.value
}));

const recordSlideClass = computed(() => ({
  'show-element': isRecording.value && isCancelArea.value,
  'hide-element': !isRecording.value || !isCancelArea.value
}));

// 标准发音和我的发音按钮的显示隐藏类
const standardAudioClass = computed(() => ({
  'show-element': !isRecording.value,
  'hide-element': isRecording.value
}));

const userAudioClass = computed(() => ({
  'show-element': !isRecording.value,
  'hide-element': isRecording.value
}));

// 音频上下文
let audioContext = null;
// 不再需要定义本地的录音管理器和初始化函数，我们使用单例模式

// 进度条动画值
const animatedScore = ref(0);
// 控制分数图标动画
const showScoreImage = ref(false);
// 控制元素可见性
const elementsVisible = ref(false);
// 记录上次分数
const lastScore = ref(null);
// 控制进度条是否有过渡动画
const enableTransition = ref(false);

// 音浪相关状态
const currentDb = ref(0);
const maxDb = ref(90);
const minDb = ref(10);
const waveBarCount = 20;
const waveBars = ref(Array(waveBarCount).fill(minDb.value));
let unregisterDbCallback = null;
let lastUpdateTime = 0; // 记录上次更新时间
const updateInterval = 16; // 更新间隔降低到16ms (约60fps)，保证流畅感
let lastSignificantDb = 0; // 上一次有效分贝值
const dbChangeThreshold = 1; // 降低阈值，让小变化也能更新

// 根据分贝值更新音浪 - 简化版本，但保持动态性
const updateWaveBarsFull = (db) => {
  // 记录当前分贝值
  currentDb.value = db;
  
  // 轻量级节流，只检查时间间隔
  const now = Date.now();
  if (now - lastUpdateTime < updateInterval) return;
  lastUpdateTime = now;
  
  // 最小高度和最大高度
  const minHeight = minDb.value;
  const maxHeight = maxDb.value;
  const baseHeight = minHeight + (maxHeight - minHeight) * (db / 100);
  
  // 保存当前状态，用于计算相邻影响
  const currentBars = [...waveBars.value];
  
  // 更新波形，保持一定的随机性和动态感
  for (let i = 0; i < waveBarCount; i++) {
    // 恢复更多随机性，增加动态感
    const sinFactor = Math.sin(Date.now() / 200 + i * 0.8) * 0.3 + 0.7;
    const randomFactor = 0.8 + Math.random() * 0.4;
    
    // 计算新高度
    let height = baseHeight * sinFactor * randomFactor;
    
    // 添加相邻柱子影响
    if (i > 0 && i < waveBarCount - 1) {
      height += (currentBars[i-1] - currentBars[i+1]) * 0.15;
    }
    
    // 更新数组值
    waveBars.value[i] = Math.max(minHeight, Math.min(maxHeight, Math.floor(height)));
  }
  
  // 处理奇偶柱子高度差异
  for (let i = 0; i < waveBarCount; i += 2) {
    waveBars.value[i] = Math.max(minHeight, waveBars.value[i] * 0.85);
    if (i + 1 < waveBarCount) {
      waveBars.value[i+1] = Math.min(maxHeight, waveBars.value[i+1] * 1.15);
    }
  }
};

// 轻量级节流的更新函数
const updateWaveBars = throttle(updateWaveBarsFull, 16);

// 初始化音浪波形 - 保持动态随机效果
const initWaveBars = () => {
  // 生成初始波形，使用正弦波模式使相邻柱子有高度差
  const baseLine = minDb.value + 10;
  const amplitude = 8;
  
  // 初始化波形
  for (let i = 0; i < waveBarCount; i++) {
    const value = baseLine + Math.sin(i * 0.8) * amplitude;
    waveBars.value[i] = Math.floor(value);
  }
};

// 监听分数变化，记录分数变化
watch(() => props.score, (newScore) => {
  if (newScore !== lastScore.value) {
    lastScore.value = newScore;
    // 如果当前是展开状态，立即触发动画
    if (expanded.value && hasScore.value) {
      resetAndAnimateScore();
    }
  }
}, { immediate: true });

// 监听展开状态变化
watch(() => expanded.value, (isExpanded) => {
  // 展开时，如果有分数，则触发动画
  if (isExpanded && hasScore.value) {
    resetAndAnimateScore();
  }
});

// 重置并动画显示分数的函数
const resetAndAnimateScore = () => {
  // 禁用过渡动画
  enableTransition.value = false;
  
  // 重置状态
  animatedScore.value = 0;
  showScoreImage.value = false;
  elementsVisible.value = false;

  // 添加小延时确保过渡效果正常触发
  setTimeout(() => {
    // 启用过渡动画后再设置值
    enableTransition.value = true;
    
    // 设置最终值为根据分段计算后的填充百分比
    animatedScore.value = calculateFillPercentage(scoreNum.value);
    
    // 在进度条动画完成后显示分数图标和文字
    setTimeout(() => {
      elementsVisible.value = true; // 先让元素可见
      showScoreImage.value = true;  // 再触发动画
    }, 700); // 等待进度条动画基本完成
  }, 100);
};

// 切换展开/折叠状态
const toggleExpand = () => {
  if (isPlayingStandard.value || isPlayingUser.value || isRecording.value) return;
  expanded.value = !expanded.value;
  emit('expand-change', expanded.value);
};

// 初始化音频上下文
const initAudioContext = () => {
  if (!audioContext) {
    audioContext = uni.createInnerAudioContext();

    // 监听播放结束事件
    audioContext.onEnded(() => {
      isPlayingStandard.value = false;
      isPlayingUser.value = false;
    });

    // 监听播放错误事件
    audioContext.onError((err) => {
      isPlayingStandard.value = false;
      isPlayingUser.value = false;
      // 隐藏可能存在的 loading
      uni.hideLoading();
      // 提示用户
      sheep.$helper.toast('音频播放失败，请重试');
      sheep.$helper.error(`音频播放失败: ${JSON.stringify(err)}`);
    });
  }
};

// 播放标准音频
const playStandardAudio = () => {
  // 如果正在录音，不执行任何操作
  if (isRecording.value) return;

  // 如果正在播放用户音频，则停止
  if (isPlayingUser.value) {
    audioContext.stop();
    isPlayingUser.value = false;
  }

  // 如果正在播放标准音频，则停止
  if (isPlayingStandard.value) {
    audioContext.stop();
    isPlayingStandard.value = false;
    return;
  }

  initAudioContext();

  // 设置音频源
  audioContext.src = props.standardAudioUrl;

  // 播放
  audioContext.play();
  isPlayingStandard.value = true;

  // 监听播放结束
  audioContext.onEnded(() => {
    isPlayingStandard.value = false;
  });
};

// 播放用户录音
const playUserAudio = () => {
  // 如果正在录音，不执行任何操作
  if (isRecording.value) return;

  // 检查是否有用户录音
  if (!props.userAudioUrl) {
    sheep.$helper.toast('暂无录音');
    return;
  }

  // 如果正在播放标准音频，则停止
  if (isPlayingStandard.value) {
    audioContext.stop();
    isPlayingStandard.value = false;
  }

  // 如果正在播放用户音频，则停止
  if (isPlayingUser.value) {
    audioContext.stop();
    isPlayingUser.value = false;
    return;
  }

  try {
    initAudioContext();

    // 添加正在加载的提示
    uni.showLoading({
      title: '加载音频中...',
      mask: false
    });

    // 打印用户音频URL
    // 这里可以使用调试工具查看音频URL
    console.log('开始播放用户音频:', props.userAudioUrl);

    // 设置音频源
    audioContext.src = props.userAudioUrl;
    console.log('音频源设置完成:', audioContext.src);
    audioContext.play();



    // 监听加载完成事件
    audioContext.onCanplay(() => {

      console.log('音频加载完成，准备播放');

      uni.hideLoading();
      // 播放
      audioContext.play();
      isPlayingUser.value = true;
    });

    // 监听播放结束
    audioContext.onEnded(() => {
      console.log('用户音频播放结束');
      isPlayingUser.value = false;
    });



  } catch (err) {
    uni.hideLoading();
    isPlayingUser.value = false;
    sheep.$helper.toast('音频播放失败');
    console.error('播放用户音频失败:', err);
  }
};

// 开始录音
const startRecording = (event) => {
  console.log('开始录音');

  if (isPlayingStandard.value || isPlayingUser.value || isRecording.value) return;

  if (audioContext) {
    audioContext.stop();
    audioContext.pause();
    isPlayingStandard.value = false;
    isPlayingUser.value = false;
  }

  // 记录开始触摸的Y坐标
  touchStartY.value = event.touches[0].clientY;
  isRecording.value = true;
  // 发送录音状态变化事件
  emit('recording-state-change', true);
  isCancelArea.value = false;
  
  // 初始化音浪
  initWaveBars();
  
  // 注册分贝回调
  unregisterDbCallback = recorderManagerSingleton.onDbChange(updateWaveBars);
  
  // 使用录音管理器单例
  recorderManagerSingleton.start({
    format: 'PCM',           // 录音格式
    sampleRate: 16000,       // 采样率
    numberOfChannels: 1,     // 单声道
    frameSize: 4 
  });
};

// 触摸移动时处理
const moveRecording = (event) => {
  if (!isRecording.value) return;

  const currentY = event.touches[0].clientY;
  const moveDistance = touchStartY.value - currentY;

  // 如果向上移动距离超过阈值，进入取消区域
  isCancelArea.value = moveDistance > cancelThreshold;
};

// 停止录音
const stopRecording = () => {
  console.log('停止录音');
  if (!isRecording.value) return;

  isRecording.value = false;
  // 发送录音状态变化事件
  emit('recording-state-change', false);
  
  // 取消分贝回调
  if (unregisterDbCallback) {
    unregisterDbCallback();
    unregisterDbCallback = null;
  }

  // 使用录音管理器单例
  recorderManagerSingleton.stop(
    // 回调函数，只有在非取消状态下会被调用
    (tempFilePath) => {
      emit('record-complete', tempFilePath);
    },
    // 是否为取消录音
    isCancelArea.value
  );

  isCancelArea.value = false;
};

// 触摸取消处理（意外中断如来电等情况）
const cancelRecording = () => {
  console.log('触摸取消处理');
  if (!isRecording.value) return;

  // 取消分贝回调
  if (unregisterDbCallback) {
    unregisterDbCallback();
    unregisterDbCallback = null;
  }

  // 使用录音管理器单例的取消方法
  recorderManagerSingleton.cancel();

  // 异步处理防止 recorderManagerSingleton 未接收到取消事件就改变数据状态，造成逻辑错误
  setTimeout(() => {
    isRecording.value = false;
    // 发送录音状态变化事件
    emit('recording-state-change', false);
    isCancelArea.value = false;
    sheep.$helper.toast('录音已取消');
  }, 0)
};

// 组件卸载时清理资源
onUnmounted(() => {
  if (audioContext) {
    audioContext.destroy();
    audioContext = null;
  }

  // 停止录音如果还在录音中
  if (isRecording.value) {
    recorderManagerSingleton.cancel();
    isRecording.value = false;
    // 发送录音状态变化事件
    emit('recording-state-change', false);
  }
  
  // 清理分贝回调
  if (unregisterDbCallback) {
    unregisterDbCallback();
    unregisterDbCallback = null;
  }
});

// 修改录音状态变化处理，优化资源使用
watch(() => isRecording.value, (recording) => {
  if (!recording && unregisterDbCallback) {
    // 停止录音时确保回调被清理
    unregisterDbCallback();
    unregisterDbCallback = null;
  }
});

const scoreImage = computed(() => {
  if (props.score >= 80) {
    return sheep.$url.cdn('/phonetic/score_excellent.png');
  }
  if (props.score >= 60) {
    return sheep.$url.cdn('/phonetic/score_good.png');
  }
  if (props.score >= 50) {
    return sheep.$url.cdn('/phonetic/score_fair.png');
  }
  if (props.score >= 25) {
    return sheep.$url.cdn('/phonetic/score_poor.png');
  }
  if (props.score >= 0) {
    return sheep.$url.cdn('/phonetic/score_bad.png');
  }
});

const comment = computed(() => {
  if (props.score >= 80) {
    return 'Excellent';
  }
  if (props.score >= 60) {
    return 'Good';
  }
  if (props.score >= 50) {
    return 'Fair';
  }
  if (props.score >= 25) {
    return 'Poor';
  }
  if (props.score >= 0) {
    return 'Bad';
  }
});

// 判断是否有分数的计算属性
const hasScore = computed(() => {
  return props.score !== null && props.score !== undefined;
});

// 当前分数值计算属性
const scoreNum = computed(() => {
  if (!hasScore.value) return 0;
  return Number(props.score) || 0;
});

// 计算填充百分比的函数
const calculateFillPercentage = (score) => {
  // 定义进度条的视觉分段位置（与CSS中segment-divider位置对应）
  // 第一段: 0-20%
  // 第二段: 20-40%
  // 第三段: 40-60%
  // 第四段: 60-80%
  // 第五段: 80-100%
  
  if (score <= 0) return 0;
  if (score >= 100) return 100;
  
  if (score < 40) {
    // 第一段 (0-39) 对应进度条的0-20%
    return (score / 39) * 20;
  } else if (score < 60) {
    // 第二段 (40-59) 对应进度条的20-40%
    return 20 + ((score - 39) / 20) * 20;
  } else if (score < 75) {
    // 第三段 (60-74) 对应进度条的40-60%
    return 40 + ((score - 59) / 15) * 20;
  } else if (score < 90) {
    // 第四段 (75-89) 对应进度条的60-80%
    return 60 + ((score - 74) / 15) * 20;
  } else {
    // 第五段 (90-100) 对应进度条的80-100%
    return 80 + ((score - 89) / 11) * 20;
  }
};

// 分数等级计算属性
const scoreLevel = computed(() => {
  if (!hasScore.value) return '';
  const score = scoreNum.value;

  if (score >= 89) return '优秀';
  if (score >= 74) return '良好';
  if (score >= 59) return '一般';
  if (score >= 39) return '较差';
  return '很差';
});
</script>

<style scoped lang="scss">
.phonetic-card {
  width: 690rpx;
  background: #FFFFFF;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.1);
  border-radius: 25rpx;
  margin: 30rpx auto;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &.expanded {
    height: 66vh;
  }

  .card-content {
    position: relative;

    &.collapsed {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 140rpx;
      padding: 0 30rpx;

      .left-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .right-content {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: center;
        width: 100rpx;
      }

      .number-text {
        font-size: 32rpx;
        font-weight: 400;
        color: #464646;
        margin-bottom: 10rpx;
      }

      .translate {
        font-size: 28rpx;
        color: #8F8F8F;
      }

      .score {
        font-size: 50rpx;
        font-weight: bold;
        color: #28B5F2;
        text-align: right;
      }
    }

    &.expanded {
      height: 100%;
      padding: 40rpx 30rpx;
      position: relative;
    }
  }

  .number-badge {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    width: 90rpx;
    height: 90rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .number-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .number-text {
      margin-top: 30rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #0A7090;
      z-index: 2;
    }
  }

  .content-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 5vh;
    margin-bottom: 5vh;

    &.content-area-no-score {
      margin-bottom: 34vh;
    }

    .phonetic-text {
      font-size: 26rpx;
      font-weight: 400;
      color: #79DFFF;
      margin-bottom: 8px;
    }

    .content-text {
      font-size: 48rpx;
      font-weight: 500;
      color: #3AC9F6;
      margin-bottom: 14rpx;
    }

    .translate {
      font-size: 28rpx;
      font-weight: 400;
      color: #393939;
    }
  }

  .score-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 4.5vh;

    .score-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 400rpx;
      height: 297rpx;

      image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        transition: all 0.5s ease;
        
        &.score-image-animation {
          animation: scoreImagePop 0.6s ease forwards;
        }
      }

      .score-text-container {
        position: absolute;
        top: 56%;
        left: 51%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        z-index: 2;
        flex-direction: column;
      }

      .score-text {
        font-weight: bold;
        font-size: 76rpx;
        color: #FFFEFE;
        text-shadow: 0 2rpx 4rpx #999999;
        
        &.score-text-animation {
          animation: scoreTextPop 0.7s ease forwards;
          animation-delay: 0.2s; // 稍微延迟，在图标出现后显示
        }
      }

      .score-comment {
        font-weight: bold;
        font-size: 32rpx;
        color: #FFFEFE;
        text-shadow: 0 1rpx 3rpx rgba(103, 103, 103, 0.75);
        
        &.comment-animation {
          animation: commentPop 0.7s ease forwards;
          animation-delay: 0.4s; // 比分数再多延迟一点
        }
      }
    }

    .score-progress-container {
      width: 511rpx; // 5段 * 95rpx + 4个间隔 * 6pt = 475rpx + 36rpx = 511rpx
      height: 120rpx;
      position: relative;

      .level-labels {
        position: relative;
        width: 100%;
        height: 34rpx;
        margin-bottom: 10rpx;
        display: flex;
        justify-content: space-between;

        .level-label {
          width: 95rpx;
          font-size: 25rpx;
          color: #666666;
          text-align: center;
          white-space: nowrap;
        }
      }

      .score-progress-bg {
        position: relative;
        width: 100%;
        height: 10rpx;
        background-color: transparent;

        .score-section-container {
          position: relative;
          width: 100%;
          height: 100%;
          background-color: rgba(40, 181, 242, 0.15);
          border-radius: 5rpx;
          overflow: hidden;

          .score-fill {
            height: 100%;
            background-color: #28B5F2;
            border-radius: 5rpx;
            box-shadow: 0 0 4rpx rgba(40, 181, 242, 0.5);
            
            &.with-transition {
              transition: width 0.8s ease-in;
            }
          }

          .segment-divider {

            position: absolute;
            top: 0;
            width: 16rpx;
            height: 100%;
            background-color: white;
            z-index: 2;

            &::before,
            &::after {
              content: ' ';
              position: absolute;
              top: 0;
              width: 50%;
              height: 100%;
            }
              &::before {
                left: -6rpx;
                background-image: radial-gradient(circle 6rpx at left, transparent 6rpx, white 50%);
              }

              &::after {
                right: -6rpx;
                background-image: radial-gradient(circle 6rpx at right, transparent 6rpx, white 50%);
              }
            }
          }
        }

        .score-labels {
          position: relative;
          width: 100%;
          margin-top: 16rpx;
          height: 24rpx;

          .score-label {
            position: absolute;
            font-size: 25rpx;
            color: #666666;
            transform: translateX(-50%);

            &:nth-child(1) {
              transform: translateX(0);
              left: -8rpx;
            }

            &:nth-child(2) {
              left: calc(95rpx + 6rpx / 2);
            }

            &:nth-child(3) {
              left: calc(2 * 95rpx + 1.5 * 10rpx);
            }

            &:nth-child(4) {
              left: calc(3 * 95rpx + 2.5 * 9rpx);
            }

            &:nth-child(5) {
              left: calc(4 * 95rpx + 3.5 * 9rpx);
            }

            &:nth-child(6) {
              left: 100%;
              transform: translateX(-100%);
              white-space: nowrap;
              left: calc(5 * 95rpx + 3.5 * 14rpx);
            }
          }
        }
      }
    }

    .button-area {
      display: flex;
      justify-content: space-around;
      align-items: center;

      .action-button {
        display: flex;
        flex-direction: column;
        align-items: center;

        .button-icon {
          width: 120rpx;
          height: 120rpx;
          margin-bottom: 10rpx;
        }

        .button-text {
          font-size: 24rpx;
          color: #666666;
        }
      }

      .record-button-before {
        .button-icon {
          width: 120rpx;
          height: 120rpx;
        }
      }
    }
  }

  /* 显示隐藏控制类 */
  .show-element {
    display: flex !important;
  }

  .hide-element {
    display: none !important;
  }

  .record-button-before {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .record-button-after {
    width: 403rpx;
    height: 84rpx;
    background: linear-gradient(90deg, rgba(97, 246, 255, 0.89), rgba(65, 181, 255, 0.89));
    border-radius: 42rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    box-shadow: 0 0 4px 4px rgba(65, 181, 255, 0.6);

    .wave-container {
      display: flex;
      align-items: center; // 保持原有的中心对齐
      justify-content: space-between;
      height: 50rpx;
      width: 240rpx; // 适当增加宽度
    }
    
    .wave-bar {
      width: 5rpx; // 适中宽度
      background-color: #ffffff;
      border-radius: 3rpx; // 保留圆角
      transition: height 0.08s ease; // 原有的过渡时间
    }

    .text {
      position: absolute;
      font-size: 28rpx;
      color: #999999;
      left: 50%;
      transform: translate(-50%, 0);
      top: -80rpx;
    }
  }

  .record-button-slide {
    width: 403rpx;
    height: 84rpx;
    background: #FF8080;
    border-radius: 42rpx;
    box-shadow: 0 0 4px 4px rgba(255, 128, 128, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .wave-container {
      display: flex;
      align-items: center; // 保持原有的中心对齐
      justify-content: space-between;
      height: 50rpx;
      width: 240rpx; // 适当增加宽度
    }
    
    .wave-bar-cancel {
      width: 5rpx; // 适中宽度
      background-color: #ffffff;
      border-radius: 3rpx; // 保留圆角
      transition: height 0.08s ease; // 原有的过渡时间
    }

    .solidCancel {
      position: absolute;
      top: -140rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .text {
      font-size: 28rpx;
      color: #999999;
      margin-bottom: 10rpx;
    }

    .chacha {
      width: 53rpx;
      height: 53rpx;
    }
  }

@keyframes scoreImagePop {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  70% {
    opacity: 0.8;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scoreTextPop {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  70% {
    opacity: 0.9;
    transform: scale(1.15);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes commentPop {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(-10rpx);
  }
  70% {
    opacity: 0.8;
    transform: scale(1.1) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
