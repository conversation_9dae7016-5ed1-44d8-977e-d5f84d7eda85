<template>
  <view class="teaching-plan-container">
    <s-empty v-if="!teachingPlans?.length" />

    <view v-else class="course-list">
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import sheep from '@/sheep';

  // 接收班级ID参数
  const props = defineProps({
    classId: {
      type: String,
      default: '',
    },
  });

  // 课时列表
  const teachingPlans = ref([]);

  // TODO 加载教案数据
  const loadCourses = async () => {
    sheep.$helper.inDevMsg();
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadCourses();
  });
</script>

<style lang="scss" scoped>
</style>
