<template>
  <view class="container">
    <easy-navbar title="搜索结果" />

    <!-- 学习集搜索结果 -->
    <view class="section" v-if="wordSetState.pagination.total > 0">
      <view class="section-title">学习集</view>
      <view class="word-set-container">
        <view class="word-set-item">
          <easy-card
            v-for="(item, index) in wordSetState.pagination.list"
            :key="index"
            :title="item.title"
            :cover-url="item.coverUrl"
            :nickname="item.nickname"
            :avatar="item.avatar"
            :is-store="item.isStore"
            :word-card-count="item?.wordCardCount"
            mode="word"
            @handleClick="goToSetDetail(item.id)" />
        </view>

        <view 
          class="load-more-btn"
          v-if="wordSetState.pagination.list.length < wordSetState.pagination.total && wordSetState.loadStatus !== 'loading'"
          @click="loadWordSetMore"
        >
          <text>点击加载更多</text>
        </view>
        <view class="loading-text" v-if="wordSetState.loadStatus === 'loading'">
          <text>加载中...</text>
        </view>
        <view class="no-more-text" v-if="wordSetState.pagination.list.length >= wordSetState.pagination.total && wordSetState.pagination.total > 0">
          <text>没有更多了</text>
        </view>
      </view>
    </view>

    <!-- 课程搜索结果 -->
    <view class="section" v-if="courseState.pagination.total > 0">
      <view class="section-title">课程</view>
      <view class="course-container">
        <course-card
          v-for="(item, index) in courseState.pagination.list"
          :key="index"
          :course="{
            id: item.id,
            title: item.title,
            subtitle: item.description,
            nickname: item.nickname
          }"
          :icon-url="item.coverImage || sheep.$url.cdn('/course/lesson.png')"
          @click="goToCourseDetail(item.id)"
        />

        <view 
          class="load-more-btn"
          v-if="courseState.pagination.list.length < courseState.pagination.total && courseState.loadStatus !== 'loading'"
          @click="loadCourseMore"
        >
          <text>点击加载更多</text>
        </view>
        <view class="loading-text" v-if="courseState.loadStatus === 'loading'">
          <text>加载中...</text>
        </view>
        <view class="no-more-text" v-if="courseState.pagination.list.length >= courseState.pagination.total && courseState.pagination.total > 0">
          <text>没有更多了</text>
        </view>
      </view>
    </view>

    <!-- 无搜索结果 -->
    <view class="no-result" v-if="wordSetState.pagination.total === 0 && courseState.pagination.total === 0">
      <text>暂无搜索结果</text>
    </view>
  </view>
</template>

<script setup>
  import { onLoad } from '@dcloudio/uni-app';
  import { reactive, ref } from 'vue';
  import sheep from '@/sheep';
  import { SearchApi } from '@/sheep/api/set/search';
  import LessonApi from '@/sheep/api/course/lesson';
  import _ from 'lodash-es';

  const keyword = ref('');

  const wordSetState = reactive({
    currentTab: 0,
    loadStatus: '',
    pagination: {
      list: [],
      total: 0,
      pageNo: 1,
      pageSize: 8,
    },
  });

  const courseState = reactive({
    loadStatus: '',
    pagination: {
      list: [],
      total: 0,
      pageNo: 1,
      pageSize: 10,
    },
  });

  const initData = () => {
    const data = uni.getStorageSync("search_comprehensive_result");
    if (!data) {
      sheep.$router.back();
      return;
    }
    
    // 初始化学习集数据
    if (data.learning && data.learning.list) {
      wordSetState.pagination.list = data.learning.list;
      wordSetState.pagination.total = data.learning.total;
    }
    
    // 初始化课程数据
    if (data.course && data.course.list) {
      courseState.pagination.list = data.course.list;
      courseState.pagination.total = data.course.total;
    }
    
    keyword.value = data.keyword || '';
  }

  // 跳转到学习集详情页
  const goToSetDetail = async (id) => {
    if (!id) {
      return;
    }
    sheep.$router.go(`/pages/set/word-card?id=${id}`);
  };

  // 跳转到课程详情页
  const goToCourseDetail = async (id) => {
    if (!id) {
      return;
    }
    sheep.$router.go(`/pages/group/detail/course/lesson-detail?id=${id}&isVisitor=true`);
  };

  const loadWordSetMore = async () => {
    if (wordSetState.loadStatus === 'loading') {
      return;
    }
    wordSetState.loadStatus = 'loading';
    wordSetState.pagination.pageNo += 1;
    try {
      const { code, data } = await SearchApi.learning({
        keyword: keyword.value,
        pageNo: wordSetState.pagination.pageNo,
        pageSize: wordSetState.pagination.pageSize,
      });
      if (code !== 0) {
        wordSetState.loadStatus = '';
        return;
      }
      // 合并列表
      wordSetState.pagination.list = _.concat(wordSetState.pagination.list, data.list);
      wordSetState.pagination.total = data.total;
      wordSetState.loadStatus = wordSetState.pagination.list.length < wordSetState.pagination.total ? '' : 'noMore';
    } catch (error) {
      wordSetState.loadStatus = '';
      console.error('加载学习集失败:', error);
    }
  }

  const loadCourseMore = async () => {
    if (courseState.loadStatus === 'loading') {
      return;
    }
    courseState.loadStatus = 'loading';
    courseState.pagination.pageNo += 1;
    try {
      const { code, data } = await LessonApi.searchLessons({
        type: 5,
        keyword: keyword.value,
        pageNo: courseState.pagination.pageNo,
        pageSize: courseState.pagination.pageSize,
      });
      if (code !== 0) {
        courseState.loadStatus = '';
        return;
      }
      // 合并列表
      courseState.pagination.list = _.concat(courseState.pagination.list, data.list);
      courseState.pagination.total = data.total;
      courseState.loadStatus = courseState.pagination.list.length < courseState.pagination.total ? '' : 'noMore';
    } catch (error) {
      courseState.loadStatus = '';
      console.error('加载课程失败:', error);
    }
  }

  const getWordSets = async () => {
    wordSetState.loadStatus = 'loading';
    const { code, data } = await SearchApi.learning({
      keyword: keyword.value,
      pageNo: wordSetState.pagination.pageNo,
      pageSize: wordSetState.pagination.pageSize,
    });
    if (code !== 0) {
      wordSetState.loadStatus = 'noMore';
      return;
    }
    // 合并列表
    wordSetState.pagination.list = _.concat(wordSetState.pagination.list, data.list);
    wordSetState.pagination.total = data.total;
    wordSetState.loadStatus = wordSetState.pagination.list.length < wordSetState.pagination.total ? 'more' : 'noMore';
  }

  const getCourses = async () => {
    courseState.loadStatus = 'loading';
    const { code, data } = await LessonApi.searchLessons({
      type: 5,
      keyword: keyword.value,
      pageNo: courseState.pagination.pageNo,
      pageSize: courseState.pagination.pageSize,
    });
    if (code !== 0) {
      courseState.loadStatus = 'noMore';
      return;
    }
    // 合并列表
    courseState.pagination.list = _.concat(courseState.pagination.list, data.list);
    courseState.pagination.total = data.total;
    courseState.loadStatus = courseState.pagination.list.length < courseState.pagination.total ? 'more' : 'noMore';
  }

  onLoad((options) => {
    keyword.value = options.keyword;
    initData();
  });


</script>

<style scoped lang="scss">
  .container {
    background: #F8FCFF;
    min-height: 100vh;

    .section {
      margin-bottom: 40rpx;

      .section-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        padding: 30rpx 30rpx 20rpx;
        border-bottom: 2rpx solid #E5E5E5;
        margin-bottom: 20rpx;
      }
    }

    .word-set-container {
      padding: 0 30rpx;

      .word-set-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
    }

    .course-container {
      padding: 0 30rpx;
    }

    .no-result {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400rpx;
      color: #999;
      font-size: 28rpx;
    }

    .load-more-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80rpx;
      margin: 20rpx 0;
      background: #007AFF;
      border-radius: 40rpx;
      color: white;
      font-size: 28rpx;
      cursor: pointer;
    }

    .load-more-btn:active {
      background: #0056CC;
    }

    .loading-text, .no-more-text {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60rpx;
      margin: 20rpx 0;
      color: #999;
      font-size: 24rpx;
    }
  }
</style>
