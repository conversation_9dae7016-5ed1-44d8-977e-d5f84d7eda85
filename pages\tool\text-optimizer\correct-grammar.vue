<template>
  <view class="correct-grammar">
    <!-- 顶部导航栏 -->
    <easy-navbar title="语法纠错" />

    <dragon-drag-button :autoDocking="true" :size="88" :xEdge="0" :yEdge="200">
      <view class="menu-btn" @click="openSidebar">
        <image class="menu-icon" :src="sheep.$url.cdn('/text-optimizer/history.png')"></image>
      </view>
    </dragon-drag-button>

    <!-- 侧边导航栏 -->
    <su-popup :show="showSidebar" type="right" @maskClick="closeSidebar" backgroundColor="#ffffff" :safeArea="true"
      :animation="true">
      <view :style="{ marginTop: `${marginTop}px` }" class="sidebar-container" @click="closeAllDropdowns">
        <!-- 侧边栏标题栏 -->
        <view class="sidebar-header">
          <view class="header-content">
            <text class="sidebar-title">历史记录</text>
            <view class="new-btn" @click="createNewConversation">
              <text class="new-btn-text">新对话</text>
              <text class="new-btn-icon">+</text>
            </view>
          </view>
        </view>
        <!-- 侧边栏内容区 -->
        <scroll-view :scroll-top="sidebarScrollTop" :show-scrollbar="true" class="sidebar-content" enhanced="true"
          scroll-y="true">
          <template v-if="isLoadingConversations">
            <view class="loading-container">
              <view class="loading-indicator"></view>
              <text class="loading-text">加载中...</text>
            </view>
          </template>
          <template v-else-if="conversationList.length > 0">
            <view v-for="item in conversationList" :key="item.id" class="sidebar-item">
              <view class="item-content" @click="switchConversation(item.id)">
                <view class="title-row">
                  <text class="item-title">{{ item.title || '新对话' }}</text>
                </view>
              </view>
              <text class="item-time">{{ formatDate(item.createTime, 'MM-DD HH:mm') }}</text>
              <!-- 添加三点菜单 -->
              <view class="item-menu">
                <view class="menu-icon" @click.stop="showMenu(item)">
                  <text class="menu-dots">⋮</text>
                </view>
                <!-- 下拉菜单 -->
                <view class="dropdown-menu" v-if="item.showDropdown">
                  <view class="dropdown-item custom-title" @click.stop="showCustomTitleDialog(item)">
                    <text class="dropdown-text">自定义标题</text>
                  </view>
                  <view class="dropdown-item delete" @click.stop="confirmDelete(item)">
                    <text class="dropdown-text">删除</text>
                  </view>
                </view>
              </view>
            </view>
            <view v-if="hasMoreConversations" class="load-more" @click="loadMoreConversations">
              <text>加载更多</text>
            </view>
          </template>
          <template v-else>
            <view class="empty-tip">
              <text>暂无语法纠错历史记录</text>
              <view class="empty-tip-sub">点击右上角"新对话"开始纠错</view>
            </view>
          </template>

          <!-- 为底部安全区域添加占位 -->
          <view class="safe-area-placeholder" :style="{ height: safeAreaBottom + 'px' }"></view>
        </scroll-view>
      </view>
    </su-popup>

    <!-- 内容区域 -->
    <scroll-view class="content" scroll-y scroll-with-animation :scroll-top="scrollTop" refresher-enabled
      :refresher-triggered="refreshing" @refresherrefresh="onRefresh" refresher-threshold="80"
      refresher-background="#f4fbff">
      <view class="chat-list">
        <template v-for="(item, index) in chatList" :key="index">
          <!-- 用户输入的句子 -->
          <view class="user-message">
            <rich-text class="sentence-box" :nodes="formatPlainText(item.userText)" user-select="text"></rich-text>
          </view>

          <!-- AI回复 -->
          <view class="ai-response">
            <view class="result-container">
              <!-- 打字机效果时使用text渲染兼容光标紧跟后面闪烁，打字机结束后改为rich-text渲染富文本达到更好显示效果(类似chatgpt) -->
              <template v-if="item.typing">
                <text class="explanation-content" user-select="text">
                  {{ item.explanation }}<text class="cursor"></text>
                </text>
              </template>
              <template v-else>
                <rich-text class="explanation-content" :nodes="formatRichText(item.explanation)" user-select="text" />
              </template>
              <!-- 在最后一条消息且正在加载时显示打字指示器 -->
              <!-- <view v-if="index === chatList.length - 1 && item.typing" class="typing-indicator">
                <text class='cursor'></text> -->
                <!-- <view class="typing-dot"></view>
                <view class="typing-dot"></view>
                <view class="typing-dot"></view> -->
              <!-- </view> -->
            </view>
          </view>
        </template>
      </view>
    </scroll-view>

    <!-- 底部输入框 -->
    <view class="input-container" :style="{ paddingBottom: safeAreaBottom + 'px', bottom: keyboardHeight + 'px' }">
      <view class="input-box" :class="{ 'input-box-active': isInputActive }">
        <view class="char-count">{{ inputText.length }}/200</view>
        <textarea class="input-textarea" placeholder="请输入句子或文章" v-model="inputText" @focus="onInputFocus"
          @blur="onInputBlur" :adjust-position="false" auto-height maxlength="200"></textarea>
        <image class="send-icon" :src="sheep.$url.cdn('/text-optimizer/fly.png')" @tap="submitText" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
import CorrectGrammarApi from '@/sheep/api/text-optimizer/correctGrammar';
import ConversationApi from '@/sheep/api/text-optimizer/conversation';
import SuPopup from '@/sheep/ui/su-popup/su-popup';
import sheep from '@/sheep';
import { formatDate } from '@/sheep/util';
import { AiModelEnum } from '@/sheep/util/const';
import { createStreamHandler } from '@/sheep/util/stream-parser';

// 用户信息
const userInfo = computed(() => sheep.$store('user').userInfo);
const isLogin = computed(() => sheep.$store('user').isLogin);

// 用户输入
const inputText = ref('');
const safeAreaBottom = ref(0);
const isInputActive = ref(false);
const scrollTop = ref(0);
const refreshing = ref(false);
const statusBarHeight = ref(0);
const keyboardHeight = ref(0);
const menuButtonInfo = ref(null);
const marginTop = ref(0);

// #ifdef MP-WEIXIN
// 获取胶囊按钮信息
menuButtonInfo.value = uni.getMenuButtonBoundingClientRect();
// 调整导航栏高度，使其能容纳胶囊按钮下方的标题
marginTop.value = menuButtonInfo.value.bottom;
// #endif

// 设置键盘高度监听
const setupKeyboardListener = () => {
  // 监听键盘高度变化
  uni.onKeyboardHeightChange((res) => {
    keyboardHeight.value = res.height;

    // 当键盘弹出时，滚动到底部确保视图正确
    if (res.height > 0) {
      setTimeout(() => {
        scrollToBottom();
      }, 200);
    }
  });
};

// 处理键盘弹出
const onInputFocus = () => {
  isInputActive.value = true;
  // 手动触发一次滚动到底部
  setTimeout(() => {
    scrollToBottom();
  }, 200);
};

// 处理键盘收起
const onInputBlur = () => {
  isInputActive.value = false;
};

// 组件卸载前清除键盘事件监听
onBeforeUnmount(() => {
  uni.offKeyboardHeightChange();
});

// 侧边导航栏状态
const showSidebar = ref(false);
const conversationList = ref([]);
const conversationPageNo = ref(1);
const conversationPageSize = ref(10);
const hasMoreConversations = ref(false);
const sidebarScrollTop = ref(0);
const isLoadingConversations = ref(false);

// 打开侧边导航栏
const openSidebar = () => {
  showSidebar.value = true;
  // 重置分页参数
  conversationPageNo.value = 1;
  isLoadingConversations.value = true;
  loadConversations().finally(() => {
    isLoadingConversations.value = false;
  });
};

// 关闭侧边导航栏
const closeSidebar = () => {
  showSidebar.value = false;
};

// 加载对话列表
const loadConversations = async () => {
  const { code, data } = await ConversationApi.getConversationPageByModelName({
    pageNo: conversationPageNo.value,
    pageSize: conversationPageSize.value,
    userId: userInfo.value.id,
    modelName: AiModelEnum.GRAMMAR_CORRECTION.name,
  });

  if (code !== 0) {
    return;
  }

  // 使用新API后，不需要再筛选，直接使用返回的列表
  const conversationData = data.list || [];

  // 如果是第一页，替换列表；否则追加
  if (conversationPageNo.value === 1) {
    conversationList.value = conversationData;
  } else {
    conversationList.value = [...conversationList.value, ...conversationData];
  }

  // 判断原始数据是否可能还有更多
  hasMoreConversations.value = conversationData.length >= conversationPageSize.value;
};

// 加载更多对话
const loadMoreConversations = async () => {
  if (!hasMoreConversations.value || isLoadingConversations.value) return;

  try {
    // 记录当前滚动位置
    const oldHeight = conversationList.value.length * 48; // 估算高度

    // 页码增加
    conversationPageNo.value++;

    // 设置加载状态
    isLoadingConversations.value = true;

    // 加载更多数据
    await loadConversations();

    // 加载完成后滚动到合适位置
    await nextTick();
    sidebarScrollTop.value = oldHeight - 10; // 稍微往上滚一点，让用户看到新加载的内容开始
  } catch (error) {
    // 发生错误时，恢复页码
    conversationPageNo.value--;
    hasMoreConversations.value = false;
  } finally {
    // 确保在所有情况下都重置加载状态
    isLoadingConversations.value = false;
  }
};

// 切换对话
const switchConversation = async (id) => {
  if (id === conversationId.value) {
    closeSidebar();
    return;
  }

  // 设置新的会话ID
  conversationId.value = id.toString();
  // 保存到缓存
  uni.setStorageSync('grammar_correction_conversation_id', id.toString());

  // 重置聊天列表和分页
  chatList.value = [];
  pageNo.value = 1;
  hasMore.value = true;

  // 加载新会话的消息
  await loadHistoryMessages();

  // 关闭侧边栏
  closeSidebar();

  // 滚动到底部
  setTimeout(() => {
    scrollToBottom();
  }, 300);
};

// 创建新对话
const createNewConversation = async () => {
  // 检查最新一条消息是否为空
  if (chatList.value.length > 0) {
    const lastChat = chatList.value[chatList.value.length - 1];
    // 判断最后一条消息是否有效完整（用户输入和AI回复都存在）
    if (!lastChat.userText.trim() || !lastChat.explanation.trim()) {
      sheep.$helper.toast('当前对话尚未完成，无法创建新对话');
      return;
    }
  } else if (conversationId.value && chatList.value.length === 0) {
    // 当前有会话ID但没有消息，可以直接使用
    sheep.$helper.toast('当前对话为空，请直接在此对话中输入内容');
    return;
  }

  // 创建新的会话
  await createConversation(true);

  // 关闭侧边栏
  closeSidebar();
};

// 状态和数据
const chatList = ref([]); // 每条消息结构：{ userText, correctedText, explanation, pendingText }
const conversationId = ref('');
const loading = ref(false);
const pageNo = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  const query = uni.createSelectorQuery();
  query.select('.chat-list').boundingClientRect();
  query.select('.content').boundingClientRect();
  query.exec((res) => {
    if (res && res[0] && res[1]) {
      const [chatList, content] = res;
      if (chatList && content) {
        scrollTop.value = Math.max(0, chatList.height - content.height);
      }
    }
  });
};

// 加载更多历史消息
const loadMoreHistoryMessages = async () => {
  if (!conversationId.value || loading.value || !hasMore.value) return;

  loading.value = true;
  const { code, data } = await CorrectGrammarApi.getMessagePage(
    pageNo.value,
    pageSize.value,
    Number(conversationId.value),
    null,
    null,
    null,
  );

  if (code !== 0) {
    loading.value = false;
    hasMore.value = false;
    if (refreshing.value) {
      // 显示提示
      sheep.$helper.toast('已经是最早的消息了');
    }
    return;
  }

  // 按时间顺序排序消息（新的在后面）
  const messages = [...data.list].sort((a, b) => new Date(a.createTime) - new Date(b.createTime));

  // 按对话对处理消息
  const newChats = [];
  // 按对组织消息
  const messagesMap = {};

  // 先将消息按照回复ID组织
  for (const message of messages) {
    if (message.type === 'user') {
      // 用户消息
      if (!messagesMap[message.id]) {
        messagesMap[message.id] = { user: message };
      } else {
        messagesMap[message.id].user = message;
      }
    } else if (message.type === 'assistant') {
      // AI回复消息
      if (message.replyId) {
        if (!messagesMap[message.replyId]) {
          messagesMap[message.replyId] = { assistant: message };
        } else {
          messagesMap[message.replyId].assistant = message;
        }
      }
    }
  }

  // 组装对话对
  for (const key in messagesMap) {
    const pair = messagesMap[key];
    if (pair.user && pair.assistant) {
      newChats.push({
        id: key, // 添加id用于排序
        userText: pair.user.content,
        correctedText: '',
        explanation: pair.assistant.content || '',
        createTime: pair.user.createTime, // 添加创建时间用于排序
      });
    }
  }

  // 按时间排序（旧的消息在前面，新的消息在后面）
  newChats.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));

  // 如果是下拉刷新（加载更旧的消息），将新消息添加到列表前面
  // 避免闪动，使用临时变量先组装完整列表
  // 一次性更新chatList，减少UI重绘次数
  chatList.value =
    pageNo.value === 1
      ? [...newChats]
      : refreshing.value
        ? [...newChats, ...chatList.value]
        : [...newChats, ...chatList.value];

  // 检查是否还有更多消息
  hasMore.value = data.list.length >= pageSize.value;

  // 增加页码
  if (hasMore.value) {
    pageNo.value++;
  } else if (refreshing.value && newChats.length === 0) {
    // 如果下拉刷新时没有新数据，显示提示
    sheep.$helper.toast('已经是最早的消息了');
  }
  loading.value = false;
};

// 下拉刷新处理 - 加载更早的消息
const onRefresh = async () => {
  refreshing.value = true;
  if (!loading.value && hasMore.value) {
    try {
      // 记录当前滚动高度和聊天列表高度
      let oldScrollTop = 0;
      let oldChatListHeight = 0;

      const query = uni.createSelectorQuery();
      query.select('.chat-list').boundingClientRect();
      query.selectViewport().scrollOffset();
      await new Promise((resolve) => {
        query.exec((res) => {
          if (res[0] && res[1]) {
            oldChatListHeight = res[0].height;
            oldScrollTop = res[1].scrollTop;
          }
          resolve();
        });
      });

      // 加载新消息
      await loadMoreHistoryMessages();

      // 等待DOM更新，避免使用setTimeout造成的闪烁
      await nextTick();

      // 使用requestAnimationFrame确保在下一次重绘前调整滚动位置
      requestAnimationFrame(() => {
        const query = uni.createSelectorQuery();
        query.select('.chat-list').boundingClientRect();
        query.exec((res) => {
          if (res[0]) {
            const newChatListHeight = res[0].height;
            const heightDiff = newChatListHeight - oldChatListHeight;

            // 调整滚动位置，使用户看到的内容保持不变
            if (heightDiff > 0) {
              scrollTop.value = oldScrollTop + heightDiff;
            }

            // 静默关闭刷新状态，避免额外的UI刷新
            refreshing.value = false;
          }
        });
      });
    } catch (error) {
      refreshing.value = false;
    }
  } else {
    if (!hasMore.value) {
      sheep.$helper.toast('已经是最早的消息了');
    }
    // 立即关闭刷新状态，减少不必要的延迟
    refreshing.value = false;
  }
};

// 获取安全区域高度并创建会话
onMounted(async () => {
  const systemInfo = sheep.$helper.sys();
  safeAreaBottom.value = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom || 0 : 0;
  // 获取状态栏高度
  statusBarHeight.value = systemInfo.statusBarHeight || 0;

  // 设置键盘高度监听
  setupKeyboardListener();

  // 读取缓存中的会话ID
  const cachedConversationId = uni.getStorageSync('grammar_correction_conversation_id');
  if (cachedConversationId) {
    conversationId.value = cachedConversationId;
    // 加载历史消息
    await loadHistoryMessages();
    // 确保显示最新消息
    setTimeout(() => {
      scrollToBottom();
    }, 300);
  } else {
    // 没有缓存的会话ID，先获取会话列表
    await loadLatestConversation();
  }

  // 预先获取键盘高度
  // #ifdef APP-PLUS
  plus.key.getKeyboardHeight((height) => {
    if (height > 0) {
      keyboardHeight.value = height;
    }
  });
  // #endif
});

// 加载最新的会话
const loadLatestConversation = async () => {
  if (!isLogin.value) {
    // 未登录时创建新会话
    await createConversation();
    return;
  }

  try {
    isLoadingConversations.value = true;

    // 使用新API获取语法纠错类型的会话
    const { code, data } = await ConversationApi.getConversationPageByModelName({
      pageNo: 1,
      pageSize: 10,
      userId: userInfo.value.id,
      modelName: AiModelEnum.GRAMMAR_CORRECTION.name,
    });

    if (code !== 0) {
      // API调用失败时创建新会话
      await createConversation();
      return;
    }

    const conversationData = data.list || [];

    if (conversationData.length > 0) {
      // 有语法纠错会话，进入最新一个（数组第一个元素通常是最新的）
      const latestConversation = conversationData[0];
      conversationId.value = latestConversation.id.toString();
      uni.setStorageSync('grammar_correction_conversation_id', conversationId.value);

      // 加载该会话的历史消息
      await loadHistoryMessages();

      // 确保显示最新消息
      setTimeout(() => {
        scrollToBottom();
      }, 300);
    } else {
      // 没有语法纠错会话，创建新会话
      await createConversation();
    }
  } catch (error) {
    // 出错时创建新会话作为后备方案
    await createConversation();
  } finally {
    isLoadingConversations.value = false;
  }
};

// 创建会话
const createConversation = async (forceNew = false) => {
  if (conversationId.value && !forceNew) return;

  try {
    const { code, data } = await ConversationApi.createConversation(
      AiModelEnum.GRAMMAR_CORRECTION.id,
    );
    if (code !== 0) {
      console.error('创建会话失败，错误码:', code);
      return false;
    }
    conversationId.value = data;
    // 清空聊天记录
    chatList.value = [];
    // 保存会话ID到缓存
    uni.setStorageSync('grammar_correction_conversation_id', data);
    return true;
  } catch (error) {
    console.error('创建会话异常:', error);
    return false;
  }
};

// 加载历史消息
const loadHistoryMessages = async () => {
  if (!conversationId.value) return;

  // 重置分页参数
  pageNo.value = 1;
  hasMore.value = true;
  chatList.value = [];

  await loadMoreHistoryMessages();
};

// 将普通文本转换为适合rich-text显示的格式
const formatPlainText = (text) => {
  if (!text) return '';

  // 将普通文本转换为HTML格式，确保安全 - 仅替换换行符为<br>
  return text.replace(/\n/g, '<br>');
};

// 将文本转换为富文本格式
const formatRichText = (text) => {
  if (!text) return '';

  // 替换换行符为<br>标签
  let formatted = text.replace(/\n/g, '<br>');

  // 匹配粗体文本 **文本**
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // 匹配斜体文本 *文本*
  formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');

  // 匹配代码块
  formatted = formatted.replace(
    /```([^`]+)```/g,
    '<pre style="background-color:#f5f5f5;padding:8px;border-radius:4px;font-family:monospace;">$1</pre>',
  );

  // 匹配行内代码
  formatted = formatted.replace(
    /`([^`]+)`/g,
    '<code style="background-color:#f5f5f5;padding:2px 4px;border-radius:2px;font-family:monospace;">$1</code>',
  );

  // 匹配标题 (# 文本)
  formatted = formatted.replace(
    /^# (.*?)$/gm,
    '<h1 style="font-size:20px;margin:10px 0;">$1</h1>',
  );
  formatted = formatted.replace(
    /^## (.*?)$/gm,
    '<h2 style="font-size:18px;margin:8px 0;">$1</h2>',
  );

  // 匹配列表项
  formatted = formatted.replace(/^- (.*?)$/gm, '<li style="margin-left:20px;">$1</li>');
  formatted = formatted.replace(/^(\d+)\. (.*?)$/gm, '<li style="margin-left:20px;">$1. $2</li>');

  // 匹配颜色高亮（简单实现）
  formatted = formatted.replace(
    /==([^=]+)==/g,
    '<span style="background-color:#ffffcc;">$1</span>',
  );

  // 匹配删除线
  formatted = formatted.replace(/~~([^~]+)~~/g, '<del>$1</del>');

  return formatted;
};

// 提交文本进行语法纠错
const submitText = async () => {
  // 新增：打字机未输出完成时不允许提交
  if (chatList.value.length > 0 && chatList.value[chatList.value.length - 1].typing) {
    sheep.$helper.toast('请等待AI回复输出完成后再提交');
    return;
  }
  // 验证输入
  if (!sheep.$helper.trim(inputText.value)) {
    sheep.$helper.toast('请输入需要纠错的内容');
    return;
  }

  // 防止重复提交
  if (loading.value) return;
  loading.value = true;

  // 确保会话已创建
  if (!conversationId.value) {
    try {
      // 尝试加载最新会话，如果没有则创建新会话
      await loadLatestConversation();
      if (!conversationId.value) {
        sheep.$helper.toast('会话创建失败，请重试');
        loading.value = false; // 重置loading状态
        return;
      }
    } catch (error) {
      console.error('加载会话失败:', error);
      sheep.$helper.toast('加载会话失败，请重试');
      loading.value = false; // 重置loading状态
      return;
    }
  }

  const text = inputText.value;

  // 先添加用户消息
  const newChat = {
    userText: text,
    correctedText: '',
    explanation: '',
    pendingText: '',
    typing: true, // 新增
  };
  chatList.value.push(newChat);

  // 清空输入
  inputText.value = '';

  // 滚动到底部
  await scrollToBottom();

  const numericConversationId = Number(conversationId.value);

  // 显示加载提示
  uni.showLoading({
    title: '生成中...',
    mask: true
  });

  // 用于跟踪是否已收到第一块数据
  let firstChunkReceived = false;
  let streamCompleted = false;

  // 调用API进行语法纠错
  try {
    // 调用API并获取响应
    const apiResponse = await CorrectGrammarApi.correctGrammar({
      conversationId: numericConversationId,
      originalText: text,
    }, {
      enableChunked: true,
      // 严格打字机分批显示，流式回调只追加pendingText，定时器逐步追加到explanation，流式结束时不直接补齐
      onChunkReceived: createStreamHandler((content, jsonData) => {
        const index = chatList.value.length - 1;
        const lastChat = chatList.value[index];
        if (lastChat) {
          if (!lastChat.pendingText) lastChat.pendingText = '';
          lastChat.pendingText += content;
          startTyping(index);
        }
        // 只在第一次收到数据时隐藏加载提示
        if (!firstChunkReceived) {
          uni.hideLoading();
          firstChunkReceived = true;
          nextTick(() => {
            scrollToBottom();
          });
        }
        if (firstChunkReceived && !streamCompleted) {
          nextTick(() => {
            scrollToBottom();
          });
        }
        // 检测流式响应结束标记
        if (jsonData && jsonData.data && jsonData.data.done === true) {
          streamCompleted = true;
          loading.value = false;
          // 不要直接补齐pendingText，让定时器自然跑完
          uni.hideLoading();
        }
      }, {
        // 配置选项
        silent: false, // 是否静默处理错误
        contentPath: 'data.receive.content', // 内容在JSON数据中的路径
        successCode: 0 // 成功状态码
      })
    });

    // 流式处理完成
    streamCompleted = true;

    // 如果需要，可以在这里添加更新会话标题的代码
    if (chatList.value.length === 1) { // 第一条消息
      let title = text.trim().substring(0, 20);
      if (text.length > 20) title += '...';
      await updateConversationTitle(numericConversationId, title);
    }

    // 最后一次滚动到底部
    if (streamCompleted) {
      await nextTick();
      await scrollToBottom();
    }
  } catch (error) {
    console.error('语法纠错请求失败:', error);
    // 发生错误，移除未成功发送的消息
    if (chatList.value.length > 0) {
      chatList.value.pop();
    }
    sheep.$helper.toast('网络请求失败，请重试');
  } finally {
    // 确保无论如何都重置加载状态
    loading.value = false;
    // 确保加载提示被隐藏
    uni.hideLoading();
  }

};

// 下拉菜单相关
const showMenu = (item) => {
  // 先关闭所有其他下拉菜单
  conversationList.value.forEach((conv) => {
    if (conv.id !== item.id) {
      conv.showDropdown = false;
    }
  });
  // 切换当前项的下拉菜单显示状态
  item.showDropdown = !item.showDropdown;
};

// 自定义标题对话框
const showCustomTitleDialog = (item) => {
  // 隐藏下拉菜单
  item.showDropdown = false;

  // 显示输入对话框
  uni.showModal({
    title: '编辑标题',
    content: '',
    editable: true,
    placeholderText: '请输入标题',
    confirmText: '确定',
    confirmColor: '#2979ff',
    cancelText: '取消',
    cancelColor: '#999',
    // 设置默认值为当前标题
    success: async (res) => {
      if (res.confirm) {
        // 用户点击确定
        let newTitle = res.content.trim();

        // 如果用户没有输入任何内容，使用默认标题
        if (!newTitle) {
          newTitle = '新对话';
        }

        // 调用API更新标题
        await updateConversationTitle(item.id, newTitle);
      }
    },
  });
};

// 更新会话标题
const updateConversationTitle = async (id, title) => {
  // 调用API更新会话标题
  const { code } = await ConversationApi.updateConversation({
    id,
    title,
  });

  if (code !== 0) {
    return;
  }

  // 更新成功，更新列表中的标题
  const conversation = conversationList.value.find((item) => item.id === id);
  if (conversation) {
    conversation.title = title;
  }
};

// 确认删除对话框
const confirmDelete = (item) => {
  // 隐藏下拉菜单
  item.showDropdown = false;

  // 显示确认删除对话框
  uni.showModal({
    title: '删除会话',
    content: '删除后将无法恢复，确定要删除吗？',
    confirmText: '删除',
    confirmColor: '#ff4d4f',
    cancelText: '取消',
    cancelColor: '#999',
    success: async (res) => {
      if (res.confirm) {
        // 用户点击确定，执行删除操作
        await deleteConversation(item.id);
      }
    },
  });
};

// 删除会话
const deleteConversation = async (id) => {
  // 调用API删除会话
  const { code } = await ConversationApi.deleteConversation(id);

  if (code !== 0) {
    return;
  }

  // 删除成功，从列表中移除该项
  conversationList.value = conversationList.value.filter((item) => item.id !== id);

  // 如果当前正在查看的会话被删除，创建新会话
  if (conversationId.value === id) {
    await createConversation(true);
  }

  // 如果列表为空且还有更多会话可加载，尝试加载更多
  if (conversationList.value.length === 0 && hasMoreConversations.value) {
    conversationPageNo.value = 1;
    await loadConversations();
  }
};

// 点击页面其他区域关闭所有下拉菜单
const closeAllDropdowns = () => {
  conversationList.value.forEach((item) => {
    item.showDropdown = false;
  });
};

// 动态打字机速度函数，根据长度返回间隔ms
function getTypingInterval(len) {
  if (len >= 100) return 10;
  if (len <= 20) return 80;
  return 80 - Math.floor((len - 20) * (70 / 80)); // 20~100之间，80~10ms
}

// 定时器Map，key为消息索引
const typingTimers = {};

function startTyping(index) {
  // 先清理旧定时器
  if (typingTimers[index]) {
    clearInterval(typingTimers[index]);
    typingTimers[index] = null;
  }
  const chat = chatList.value[index];
  if (!chat || !chat.pendingText || chat.pendingText.length === 0) {
    if (chat) chat.typing = false; // 没有内容时直接结束
    return;
  }
  chat.typing = true; // 开始打字机动画
  const interval = getTypingInterval(chat.pendingText.length);
  typingTimers[index] = setInterval(() => {
    const chat = chatList.value[index];
    if (chat && chat.pendingText && chat.pendingText.length > 0) {
      // 判断是否是最后一个字符
      if (chat.pendingText.length === 1) {
        // 最后一个字符，先关闭光标
        chat.typing = false;
        chat.explanation += chat.pendingText;
        chat.pendingText = '';
        clearInterval(typingTimers[index]);
        typingTimers[index] = null;
        // 新增：最后一个字符后滚动到底部
        nextTick(() => {
          scrollToBottom();
        });
      } else {
        const toAdd = chat.pendingText.slice(0, 1);
        chat.pendingText = chat.pendingText.slice(1);
        chat.explanation += toAdd;
        // 新增：每次内容有变化都滚动到底部
        nextTick(() => {
          scrollToBottom();
        });
        // 动态调整速度
        const newInterval = getTypingInterval(chat.pendingText.length);
        if (newInterval !== interval) {
          clearInterval(typingTimers[index]);
          typingTimers[index] = null;
          startTyping(index); // 用新速度重启
        }
      }
    }
  }, interval);
}
</script>

<style lang="scss" scoped>
.correct-grammar {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4fbff;
}

// 菜单按钮样式
.menu-btn {
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &:active {
    opacity: 0.8;
  }
}

.menu-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

// 侧边导航栏样式
.sidebar-container {
  width: 280px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  box-sizing: border-box;
  position: relative;
}

.sidebar-header {
  height: 44px;
  border-bottom: 1px solid #eee;
  z-index: 10;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 100%;
}

.sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.new-btn {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  padding: 4px 8px;
  border-radius: 16px;

  &:active {
    opacity: 0.8;
  }
}

.new-btn-text {
  font-size: 14px;
  color: #0066cc;
  font-weight: bold;
}

.new-btn-icon {
  margin-left: 4px;
  color: #0066cc;
  font-weight: bold;
}

.sidebar-content {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  /* 增加iOS滚动流畅度 */
  height: calc(100vh - 44px - var(--status-bar-height, 0px));
  /* 减去header高度和顶部间距 */
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-item {
  position: relative;
  padding: 15rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.title-row {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.item-title {
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.item-time {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180rpx;
  margin-right: 2rpx;
}

.item-menu {
  position: relative;
  padding-left: 2rpx;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-dots {
  font-size: 30rpx;
  color: #999;
  font-weight: bold;
  line-height: 1;
}

.dropdown-menu {
  position: absolute;
  top: 50rpx;
  right: -10rpx;
  width: 180rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  overflow: hidden;
  border: 1rpx solid #eeeeee;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-text {
  font-size: 26rpx;
}

.custom-title {
  color: #666;
}

.delete {
  color: #ff4d4f;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 150px;
  color: #999;

  .empty-tip-sub {
    font-size: 24rpx;
    color: #bbb;
    margin-top: 10rpx;
  }
}

.safe-area-placeholder {
  width: 100%;
  min-height: 20px;
}

.content {
  flex: 1;
  overflow-y: auto;
}

.chat-list {
  display: flex;
  flex-direction: column;
  padding: 20px 16px calc(90px + var(--safe-area-inset-bottom));
}

.user-message {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
  padding-left: 60px;

  .sentence-box {
    background-color: #d1e6fb;
    padding: 12px 16px;
    border-radius: 12px 12px 2px 12px;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    max-width: 85%;
    text-align: left;
  }
}

.ai-response {
  margin-bottom: 24px;
  padding-right: 16px;
}

.result-container {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

  .correct-sentence {
    margin-bottom: 16px;

    .correct-title {
      font-weight: bold;
      color: #333;
      font-size: 16px;
    }

    .correct-text {
      font-weight: bold;
      color: #333;
      font-size: 16px;
      line-height: 1.5;

    }
  }

  .explanation-content {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    display: block;

  }

  // 打字机
  .cursor {
    display: inline-block;
    width: 6rpx;
    height: 36rpx;
    background: skyblue;
    vertical-align: bottom;
    margin-left: 2px;
    animation: blink 1.3s infinite;
  }
}


@keyframes blink {

  0%,
  100% {
    background: skyblue;
  }

  50% {
    background: transparent;
    opacity: 0;
  }
}

.input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background-color: #f4fbff;
  z-index: 100;
  transition: bottom 0.25s ease;
}

.input-box {
  position: relative;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border: 1px solid #e2e2e2;
  border-radius: 30px;
  padding: 8px 16px;
  transition: background-color 0.3s ease;

  .char-count {
    position: absolute;
    top: -36rpx;
    right: 20rpx;
    font-size: 24rpx;
    color: #999;
    z-index: 1;
  }

  &.input-box-active {
    background-color: #f0f0f0;
  }

  textarea {
    flex: 1;
    border: none;
    background: transparent;
    height: 36px;
    font-size: 15px;
  }

  .send-icon {
    width: 26px;
    height: 26px;
    margin-left: 8px;
    color: #46adf0;
  }
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0;
  color: #0066cc;
  font-size: 14px;

  &:active {
    opacity: 0.7;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-indicator {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  border-radius: 50%;
  background-color: #46adf0;
  animation: typing-animation 1.4s infinite ease-in-out both;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-animation {

  0%,
  80%,
  100% {
    transform: scale(0.6);
    opacity: 0.6;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
