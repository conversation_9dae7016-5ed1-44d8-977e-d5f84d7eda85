import request from '@/sheep/request';

// 视频提炼 API
const VideoRefinementApi = {
  // 获取历史记录列表
  getResultList: () => {
    return request({
      url: '/ai/video-refinement/get-result-list',
      method: 'GET',
      custom: {
        showLoading: false,
      },
    });
  },

    getResultPage: (data) => {
    return request({
      url: '/ai/video-refinement/get-history-page',
      method: 'POST',
      data,
      custom: {
        showLoading: false,
      },
    });
  },

  // 获取视频提炼结果
  getResult: (messageId) => {
    return request({
      url: '/ai/video-refinement/get-result',
      method: 'GET',
      params: {
        messageId,
      },
    });
  },

  // 视频提炼请求
  processVideo: (fileLink) => {
    return request({
      url: '/ai/video-refinement/process',
      method: 'POST',
      params: {
        fileLink,
      },
      custom: {
        showLoading: false,
      },
    });
  },

  // 清空视频提炼历史记录
  clearHistory: () => {
    return request({
      url: '/ai/video-refinement/clear-history',
      method: 'DELETE',
      custom: {
        showSuccess: true,
        successMsg: '历史记录已清空',
      },
    });
  },
  //清除用户失败的视频提炼历史
  clearFailedHistory: () => {
    return request({
      url: '/ai/video-refinement/clear-failed-history-by-content',
      method: 'DELETE',
      custom: {
        showSuccess: false,
      },
    });
  },
};


export default VideoRefinementApi;
