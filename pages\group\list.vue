<template>
  <view class="container">
    <easy-navbar title="班级" backPath='/pages/my/index' />

    <!-- 切换选项卡 -->
    <view class="tabs">
      <view
        :class="['tab-item', activeTab === 'joined' ? 'active' : '']"
        @click="switchTab('joined')"
      >
        <text>我加入的</text>
      </view>
      <view
        :class="['tab-item', activeTab === 'created' ? 'active' : '']"
        @click="switchTab('created')"
      >
        <text>我创建的</text>
      </view>
    </view>

    <!-- 班级列表 - 我加入的 -->
    <view class="class-list" v-if="activeTab === 'joined'">
      <view v-if="joinedClasses.length === 0" class="empty-state">
        <text>暂无加入的班级</text>
      </view>
      <view class="class-item" v-for="(item, index) in joinedClasses" :key="index" @click="goToClassDetail(item)">
        <view class="class-header">
          <view class="class-avatar">
            <image :src="sheep.$url.cdn('/group/Faculty.png')" />
          </view>
          <view class="class-name">{{ item.name }}</view>
        </view>
        <view class="class-footer">
          <view class="class-meta">
            {{ item.studySetCount || 0 }}个学习集
            {{ item.remark ? ` | ${item.remark}` : ' | 暂无班级说明' }}
          </view>
          <view class="class-menu-wrapper">
            <view class="class-menu" @click.stop="toggleMenu(item, index, false)">
              <text class="menu-dots">...</text>
            </view>
            <!-- 下拉菜单 - 已加入班级 -->
            <view class="dropdown-menu" v-if="showMenuIndex === index && !isCreatedMenu">
              <view class="dropdown-item" @click.stop="inviteFriends">
                <image :src="sheep.$url.cdn('/group/invited.png')" />
                <text>邀请好友</text>
              </view>
              <view class="dropdown-item" @click.stop="addStudySet">
                <image :src="sheep.$url.cdn('/group/add.png')" />
                <text>添加学习集</text>
              </view>
              <view class="dropdown-item exit" @click.stop="exitClass">
                <image :src="sheep.$url.cdn('/group/logout.png')" />
                <text>退出班级</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 班级列表 - 我创建的 -->
    <view class="class-list" v-if="activeTab === 'created'">
      <view v-if="createdClasses.length === 0" class="empty-state">
        <text>暂无创建的班级</text>
      </view>
      <view class="class-item" v-for="(item, index) in createdClasses" :key="index" @click="goToClassDetail(item)">
        <view class="class-header">
          <view class="class-avatar">
            <image :src="sheep.$url.cdn('/group/Faculty.png')" />
          </view>
          <view class="class-name">{{ item.name }}</view>
        </view>
        <view class="class-footer">
          <view class="class-meta">
            {{ item.studySetCount || 0 }}个学习集
            {{ item.remark ? ` | ${item.remark}` : ' | 暂无班级说明' }}
          </view>
          <view class="class-menu-wrapper">
            <view class="class-menu" @click.stop="toggleMenu(item, index, true)">
              <text class="menu-dots" >...</text>
            </view>
            <!-- 下拉菜单 - 创建的班级 -->
            <view class="dropdown-menu" v-if="showMenuIndex === index && isCreatedMenu">
              <view class="dropdown-item" @click.stop="inviteFriends">
                <image :src="sheep.$url.cdn('/group/invited.png')" />
                <text>邀请好友</text>
              </view>
              <view class="dropdown-item" @click.stop="addStudySet">
                <image :src="sheep.$url.cdn('/group/add.png')" />
                <text>添加学习集</text>
              </view>
              <view class="dropdown-item" @click.stop="editClass">
                <image :src="sheep.$url.cdn('/group/add.png')" />
                <text>编辑班级</text>
              </view>
              <view class="dropdown-item exit" @click.stop="deleteClass">
                <image :src="sheep.$url.cdn('/group/logout.png')" />
                <text>删除班级</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 创建班级按钮 -->
      <view class="create-button" @click="createClass">
        <text class="create-icon">+</text>
        <text class="create-text">创建班级</text>
      </view>
    </view>

    <!-- 点击其他区域关闭菜单 -->
    <view class="backdrop" v-if="showMenuIndex !== null" @click="closeAllMenus"></view>

    <!-- 邀请链接弹窗 -->
    <su-popup ref="invitePopupRef" type="center">
      <view class="invite-modal">
        <view class="invite-title">邀请加入班级</view>
        <view class="invite-class-name">{{ inviteClassName }}</view>
        <view class="invite-link-container">
          <text class="invite-link">{{ inviteLink }}</text>
        </view>
        <view class="invite-tip">复制链接，发送给好友。好友打开小程序后将自动处理加入请求。</view>
        <view class="invite-actions">
<!--          <button class="action-btn copy-btn" @click="copyInviteLink">复制链接</button>-->
          <button class="action-btn share-btn" open-type="share" @tap="onShareButtonTap">分享链接</button>
        </view>
      </view>
    </su-popup>
  </view>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { onShow, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app';
  import GroupApi from '@/sheep/api/group/index';
  import sheep from '@/sheep';
  // 导入su-popup组件
  import suPopup from '@/sheep/ui/su-popup/su-popup.vue';

  // 响应式数据
  const activeTab = ref('joined'); // 默认选中"我加入的"
  const showMenuIndex = ref(null); // 当前显示菜单的索引
  const currentClass = ref(null);
  const isCreatedMenu = ref(false); // 是否是创建班级的菜单
  const joinedClasses = ref([]);
  const createdClasses = ref([]);

  // 邀请链接相关数据
  const inviteLink = ref('');
  const inviteClassName = ref('');
  const invitePopupRef = ref(null);
  // 获取当前小程序页面路径前缀
  const appPagePath = '/pages/group/join';

  // 用于存储当前要分享的班级信息（供微信分享使用）
  const shareClassId = ref(null);
  const shareClassName = ref('');

  // 获取当前用户的全部班级信息
  const loadAllClasses = () => {
    loadJoined();
    loadCreated();
  };

  const loadJoined = async () => {
    const res = await GroupApi.getMyJoinedGroups();
    if (res.code !== 0) {
      return;
    }
    joinedClasses.value = res.data;
  };

  const loadCreated = async () => {
    const res = await GroupApi.getMyCreatedGroups();
    if (res.code !== 0) {
      return;
    }
    createdClasses.value = res.data;
  };

  watch(activeTab, () => {
    if (activeTab.value === 'joined') {
      loadJoined();
    } else if (activeTab.value === 'created') {
      loadCreated();
    }
  });

  // 生成邀请链接
  const generateInviteLink = async (groupId) => {
    try {
      // 调用后端生成带签名的邀请链接
      const res = await GroupApi.generateInviteLink(groupId, 24); // 24小时有效期
      if (res.code === 0) {
        const { timestamp, sign } = res.data;
        // 生成小程序内页面链接
        return `${appPagePath}?groupId=${groupId}&timestamp=${timestamp}&sign=${sign}`;
      } else {
        sheep.$helper.toast(res.msg || '生成邀请链接失败');
        return null;
      }
    } catch (error) {
      sheep.$helper.toast('生成邀请链接失败');
      return null;
    }
  };

  // 显示邀请链接弹窗
  const showInviteLinkModal = (link, className) => {
    inviteLink.value = link;
    inviteClassName.value = className;
    invitePopupRef.value.open();
  };

  // 复制邀请链接
  const copyInviteLink = () => {
    uni.setClipboardData({
      data: inviteLink.value,
      success: () => {
        sheep.$helper.toast('已复制到剪贴板');
        // 延迟关闭弹窗
        setTimeout(() => {
          invitePopupRef.value.close();
        }, 1500);
      },
    });
  };

  // 点击分享按钮
  const onShareButtonTap = () => {
    // 关闭弹窗
    setTimeout(() => {
      invitePopupRef.value.close();
    }, 200);
  };

  // 切换选项卡
  const switchTab = (tab) => {
    activeTab.value = tab;
    closeAllMenus();
  };

  // 跳转到班级详情
  const goToClassDetail = (classItem) => {
    sheep.$router.go(`/pages/group/index?id=${classItem.id}`);
  };

  // 切换菜单显示
  const toggleMenu = (classItem, index, isCreated) => {
    if (showMenuIndex.value === index) {
      showMenuIndex.value = null;
    } else {
      currentClass.value = classItem;
      isCreatedMenu.value = isCreated;
      showMenuIndex.value = index;
    }
  };

  // 关闭所有菜单
  const closeAllMenus = () => {
    showMenuIndex.value = null;
  };

  // 创建班级
  const createClass = () => {
    sheep.$router.go('/pages/group/add');
  };

  // 邀请好友
  const inviteFriends = async () => {
    if (!currentClass.value || !currentClass.value.id) {
      sheep.$helper.toast('班级信息不完整');
      return;
    }

    // 生成邀请链接
    const link = await generateInviteLink(currentClass.value.id);

    // 显示邀请链接弹窗
    if (link) {
      showInviteLinkModal(link, currentClass.value.name);
    }

    // 保存当前分享的班级信息，用于微信分享
    shareClassId.value = currentClass.value.id;
    shareClassName.value = currentClass.value.name;

    // 关闭菜单
    closeAllMenus();
  };

  // 添加学习集
  const addStudySet = () => {
    sheep.$helper.inDevMsg();
    closeAllMenus();
  };

  // 编辑班级
  const editClass = () => {
    // 跳转到编辑页面
    sheep.$router.go(`/pages/group/edit?id=${currentClass.value.id}`);
    closeAllMenus();
  };

  // 删除班级
  const deleteClass = async () => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除该班级吗？',
      success: async ({ confirm }) => {
        if (!confirm) return;
        const res = await GroupApi.deleteGroup(currentClass.value.id);
        if (res.code !== 0) {
          return;
        }
        loadAllClasses();
      },
    });
    closeAllMenus();
  };

  // 退出班级
  const exitClass = async () => {
    uni.showModal({
      title: '确认退出',
      content: '确定要退出该班级吗？',
      success: async ({ confirm }) => {
        if (!confirm) return;
        const res = await GroupApi.quitGroup(currentClass.value.id);
        if (res.code !== 0) {
          return;
        }
        loadAllClasses();
      },
    });
    closeAllMenus();
  };

  // 检查剪贴板中是否包含班级邀请链接
  const checkClipboardForInviteLink = () => {
    // 读取剪贴板内容
    uni.getClipboardData({
      success: (res) => {
        const clipText = res.data || '';

        // 检查是否包含邀请链接特征
        const match = clipText.match(/join\?groupId=(\d+)/);
        if (match && match[1]) {
          const groupId = match[1];

          // 显示提示
          uni.showModal({
            title: '班级邀请',
            content: '检测到班级邀请链接，是否加入该班级？',
            confirmText: '加入',
            cancelText: '取消',
            success: ({ confirm }) => {
              if (confirm) {
                // 加入班级
                handleJoinGroup(groupId);

                // 清空剪贴板，防止重复提示
                uni.setClipboardData({
                  data: '',
                  success: () => {
                  },
                });
              }
            },
          });
        }
      },
    });
  };

  // 处理加入班级
  const handleJoinGroup = async (groupId) => {
    if (!groupId) return;
    const result = await GroupApi.joinGroup(groupId);
    if (result.code !== 0) {
      return;
    }
    loadAllClasses();
  };

  // #ifdef MP-WEIXIN
  // 启用微信小程序分享功能
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline'],
  });

  // 分享给微信好友
  onShareAppMessage(() => {
    // 如果有设置要分享的班级，则使用该班级信息
    if (shareClassId.value) {
      return {
        title: `邀请加入班级：${shareClassName.value}`,
        path: `${appPagePath}?groupId=${shareClassId.value}`,
        imageUrl: sheep.$url.cdn('/group/Faculty.png'), // 使用班级默认图标作为分享图片
      };
    }

    // 默认分享当前页面
    return {
      title: '班级',
      path: '/pages/group/list',
      imageUrl: sheep.$url.cdn('/group/Faculty.png'),
    };
  });

  // 分享到朋友圈
  onShareTimeline(() => {
    // 如果有设置要分享的班级，则使用该班级信息
    if (shareClassId.value) {
      return {
        title: `邀请加入班级：${shareClassName.value}`,
        query: `groupId=${shareClassId.value}`,
        imageUrl: sheep.$url.cdn('/group/Faculty.png'),
      };
    }

    // 默认分享当前页面
    return {
      title: '班级',
      query: '',
      imageUrl: sheep.$url.cdn('/group/Faculty.png'),
    };
  });
  // #endif

  onMounted(() => {
    loadAllClasses();
    // checkClipboardForInviteLink();
  });

  onShow(() => {
    loadAllClasses();
  });

  // 暴露给父组件的方法
  defineExpose({
    inviteFriends,
  });
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    background-color: #F8FCFF;
  }

  .backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: 50;
  }

  /* 选项卡样式 */
  .tabs {
    display: flex;
    background-color: #ffffff;
    padding: 15rpx 30rpx;
    border-radius: 50rpx;
    margin: 40rpx 40rpx 20rpx 40rpx;
  }

  .tab-item {
    flex: 1;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #333;
    margin: 0 10rpx;
    border-radius: 40rpx;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &.active {
      background-color: #2196f3;
      color: #fff;
    }
  }

  /* 班级列表样式 */
  .class-list {
    padding: 20rpx;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    padding-top: 200rpx;

    text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .class-item {
    background-color: #ffffff;
    border: 2px solid #D8D8D8;
    border-radius: 18rpx;
    margin: 0 20rpx 28rpx 20rpx;
    padding: 20rpx 26rpx 26rpx;
  }

  .class-header {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .class-avatar {
    width: 62rpx;
    height: 62rpx;
    margin-right: 12rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .class-name {
    font-size: 32rpx;
    color: #202020;
  }

  .class-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .class-meta {
    font-size: 26rpx;
    color: #ACACAC;
  }

  .class-menu-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .class-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10rpx;
  }

  .menu-dots {
    font-size: 36rpx;
    color: #666;
    font-weight: bold;
    line-height: 1;
    transform: translateY(-12rpx);
  }

  /* 下拉菜单样式 */
  .dropdown-menu {
    position: absolute;
    top: 60rpx;
    right: 0;
    width: 260rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    z-index: 100;
    overflow: hidden;
    padding: 10rpx 0;
  }

  .dropdown-item {
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    image {
      width: 36rpx;
      height: 36rpx;
      margin-right: 20rpx;
    }

    text {
      font-size: 28rpx;
      color: #333;
      flex: 1;
    }

    &:active {
      background-color: #f9f9f9;
    }

    &.exit {
      text {
        color: #ff5252;
      }
    }
  }

  /* 创建班级按钮 */
  .create-button {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 80%;
    height: 100rpx;
    padding: 3rpx 40rpx;
    border-radius: 50rpx;
    margin-left: 40rpx;
    margin-right: 40rpx;
    margin-bottom: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99;
    background-color: #ffffff;
    border: 1px solid #4184FF;
  }

  .create-icon {
    font-size: 36rpx;
    color: #2196f3;
    font-weight: bold;
    margin-right: 10rpx;
  }

  .create-text {
    font-size: 32rpx;
    color: #2196f3;
  }

  /* 邀请链接弹窗样式 */
  .invite-modal {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 40rpx;
    width: 560rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }

  .invite-title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    text-align: center;
    color: #333;
  }

  .invite-class-name {
    font-size: 32rpx;
    color: #2196f3;
    margin-bottom: 30rpx;
    text-align: center;
    font-weight: 500;
  }

  .invite-link-container {
    width: 100%;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    padding: 20rpx 30rpx;
    margin-bottom: 30rpx;
    box-sizing: border-box;
  }

  .invite-link {
    font-size: 28rpx;
    color: #666;
    word-break: break-all;
  }

  .invite-tip {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 30rpx;
    text-align: center;
    line-height: 1.5;
  }

  .invite-actions {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  // 复制链接分享功能开放的样式
  //.action-btn {
  //  width: 48%;
  //  height: 80rpx;
  //  line-height: 80rpx;
  //  text-align: center;
  //  border-radius: 40rpx;
  //  font-size: 28rpx;
  //  border: none;
  //  padding: 0;
  //}

  .action-btn {
    width: 96%;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;
    padding: 0;
  }

  .copy-btn {
    background-color: #2196f3;
    color: #fff;
  }

  .share-btn {
    background-color: #4caf50;
    color: #fff;

    &::after {
      border: none;
    }
  }
</style>
