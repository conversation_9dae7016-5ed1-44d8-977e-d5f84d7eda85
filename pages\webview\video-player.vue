<template>
  <view class="video-player-container">
    <video 
      id="myVideo"
      class="video-player"
      :src="videoUrl"
      controls
      autoplay
      object-fit="contain"
      @fullscreenchange="handleFullscreenChange"
    ></video>
    
    <!-- 播放控制按钮 -->
    <view class="controls-container" v-if="!isFullscreen">
      <view class="back-button" @click="goBack">
        <slot name="left">
          <image
            v-if="showBack"
            class="back-icon"
            mode="widthFix"
            :src="sheep.$url.cdn('/common/back.png')"
            @click="goBack"
          />
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { onLoad, onHide } from '@dcloudio/uni-app';
import sheep from '@/sheep';

// 视频播放器引用
const videoContext = ref(null);
const videoUrl = ref('');
const videoTitle = ref('');
const isFullscreen = ref(false);
const showBack = ref(false);

// 页面参数处理
onLoad((options) => {
  if (options.url) {
    videoUrl.value = decodeURIComponent(options.url);
  }
  
  if (options.title) {
    videoTitle.value = decodeURIComponent(options.title);
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: videoTitle.value
    });
  }

  // 设置是否显示返回按钮
  if (options.showBack !== undefined) {
    showBack.value = options.showBack === 'true' || options.showBack === true;
  }
});

// 组件挂载
onMounted(() => {
  // 获取视频上下文
  videoContext.value = uni.createVideoContext('myVideo');
});

// 页面隐藏时处理
onHide(() => {
  // 如果视频在播放，暂停播放
  if (videoContext.value) {
    videoContext.value.pause();
  }
});

// 组件卸载
onUnmounted(() => {
  // 如果视频在全屏状态，退出全屏
  if (isFullscreen.value && videoContext.value) {
    videoContext.value.exitFullScreen();
  }
});

// 监听全屏状态变化
const handleFullscreenChange = (e) => {
  isFullscreen.value = e.detail.fullScreen;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

</script>

<style scoped lang="scss">
.video-player-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  width: 100%;
  height: 100%;
}

.controls-container {
  position: absolute;
  top: 80rpx;
  left: 0;
  width: 100%;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  z-index: 10;
}

.back-button, .fullscreen-button {
  width: 19rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.back-icon {
  width: 30rpx;
  height: 30rpx;
}

.iconfont {
  font-size: 40rpx;
}
</style>