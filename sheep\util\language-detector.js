/**
 * 语言枚举
 */
export const LanguageEnum = {
  ZH: {
    label: '中文',
    code: 'zh',
  },
  TH: {
    label: '泰文',
    code: 'th',
  },
}

/**
 * 阿里云 TTS 配置枚举
 * speaker: 发音人
 * speechRate: 语速（-500~500）
 * pitchRate: 语调
 */
export const TtsAliyunConfigEnum = {
  ZH: {
    speaker: 'xiaoyun',
    speechRate: 0,
    pitchRate: 0,
  },
  TH: {
    speaker: 'waan',
    speechRate: -400,
    pitchRate: 0,
  }
}

/**
 * OpenAI TTS 配置枚举
 * speaker: 发音人
 * speechRate: 语速（0.25~4.0）
 */
export const TtsOpenAiConfigEnum = {
  ZH: {
    speaker: 'alloy',
    speechRate: 0.0,
  },
  TH: {
    speaker: 'alloy',
    speechRate: 0.25,
  }
}

/**
 * 语音合成类型枚举
 */
export const TtsTypeEnum = {
  ALIYUN: 'aliyun',
  OPENAI: 'openai',
}

/**
 * 检测文本语言并返回语言标签
 */
export const getTextLanguage = (text) => {
  // 处理空输入
  if (!text || text.length === 0) {
    return;
  }

  let chineseCount = 0;
  let thaiCount = 0;

  // 统计每种语言的字符数量
  for (let char of text) {
    const code = char.charCodeAt(0);
    if (
      (code >= 0x4E00 && code <= 0x9FFF) || // 中文字符
      (code >= 0x30 && code <= 0x39) ||     // 数字
      ((code >= 0x0041 && code <= 0x005A) || (code >= 0x0061 && code <= 0x007A)) // 英文字符
    ) {
      chineseCount++;
    } else if (code >= 0x0E00 && code <= 0x0E7F) {
      thaiCount++;
    }
  }

  // 找到最多字符的语言
  const maxCount = Math.max(chineseCount, thaiCount);
  if (maxCount === 0) {
    return;
  }

  if (maxCount === chineseCount) {
    return LanguageEnum.ZH;
  } else if (maxCount === thaiCount) {
    return LanguageEnum.TH;
  }
};

/**
 * 根据文本内容和TTS类型返回对应的TTS配置
 */
export const getTtsConfig = (text, ttsType) => {
  const language = getTextLanguage(text);
  if (!language) {
    return null;
  }

  if (!ttsType) {
    return null;
  }

  const langKey = language.code.toUpperCase();

  let config = null;
  switch(ttsType) {
    case TtsTypeEnum.ALIYUN:
      config = TtsAliyunConfigEnum[langKey];
      break;
    case TtsTypeEnum.OPENAI:
      config = TtsOpenAiConfigEnum[langKey];
      break;
  }

  return {
    ...language,
    ...config,
  };
};

/**
 * 根据label获取对应的code
 */
export const getLanguageCodeByLabel = (label) => {
  const language = Object.values(LanguageEnum).find(
    (lang) => lang.label === label
  );
  return language ? language.code : null;
};

/**
 * 根据label获取对应枚举
 */
export const getLanguageByLabel = (label) => {
  return Object.values(LanguageEnum).find(
    (lang) => lang.label === label
  );
};

/**
 * 图片路径常量
 */
export const ImagePath = {
  COPY_ICON: '/translate/copy.png',
  COPY_ICON_GRAY: '/translate-pdf/copy.png',
  PLAY_ICON: {
    GREEN_STATIC: '/translate/green_voice.png',
    GREEN_ANIMATED: '/translate/green_voice.gif',
    WHITE_STATIC: '/translate/white_voice.png',
    WHITE_ANIMATED: '/translate/white_voice.gif',
    GRAY_STATIC: '/translate-pdf/trumpet-gray.png',
    BLUE_STATIC: '/video-refinement/play.png',
    BLUE_ANIMATED: '/video-refinement/playing.gif',
  },
  ARROW_LEFT_BLACK: '/translate-pdf/left-arrows-black.png',
  ARROW_LEFT_GRAY: '/translate-pdf/left-arrows-gray.png',
  ARROW_RIGHT_BLACK: '/translate-pdf/right-arrows-black.png',
  ARROW_RIGHT_GRAY: '/translate-pdf/right-arrows-gray.png',
};

/**
 * 播放类型枚举
 */
export const PlayType = {
  SINGLE: 'single',
  ALL_SOURCE: 'allSource',
  ALL_TRANSLATION: 'allTranslation',
};
