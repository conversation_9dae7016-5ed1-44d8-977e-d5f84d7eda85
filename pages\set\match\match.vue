<template>
  <view class="container">
    <!-- 顶部导航栏 - 自定义返回按钮 -->
    <easy-navbar :title="title" :transparent="true" :showBack="false">
      <template #left>
        <view class="close-btn" @click="handleBack">
          <text class="close-icon">×</text>
        </view>
      </template>
    </easy-navbar>

    <!-- 卡片主体区域 -->
    <view class="cards-container">
      <!-- 左侧卡片列 -->
      <view class="card-column">
        <view v-for="(card, index) in leftColumnCards" :key="'left-' + index" class="card-wrapper">
          <view
            :class="{
              'thai-card': card.type === 'thai',
              'chinese-card': card.type === 'chinese',
              'selected-blue': isCardSelectedFirst(card) && !isWrongMatch,
              'selected-green':
                isCardSelectedSecond(card) && !isWrongMatch && isCorrectPairSelected,
              'selected-red':
                (isCardSelectedFirst(card) || isCardSelectedSecond(card)) && isWrongMatch,
              'card-hidden': isCardMatched(card.type, card.originalIndex),
            }"
            class="card"
            @click="selectCard(card)"
          >
            <text class="card-text">{{ card.text }}</text>
          </view>
        </view>
      </view>

      <!-- 右侧卡片列 -->
      <view class="card-column">
        <view
          v-for="(card, index) in rightColumnCards"
          :key="'right-' + index"
          class="card-wrapper"
        >
          <view
            :class="{
              'thai-card': card.type === 'thai',
              'chinese-card': card.type === 'chinese',
              'selected-blue': isCardSelectedFirst(card) && !isWrongMatch,
              'selected-green':
                isCardSelectedSecond(card) && !isWrongMatch && isCorrectPairSelected,
              'selected-red':
                (isCardSelectedFirst(card) || isCardSelectedSecond(card)) && isWrongMatch,
              'card-hidden': isCardMatched(card.type, card.originalIndex),
            }"
            class="card"
            @click="selectCard(card)"
          >
            <text class="card-text">{{ card.text }}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 提示文字容器 -->
    <view class="time-prompt" :class="{
      'error-prompt': isShowingErrorPrompt,
      'success-prompt': isShowingSuccessPrompt,
      'visible': isShowingPrompt
    }">
      {{ promptText }}
    </view>
  </view>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import { onLoad, onUnload } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import SetApi from '@/sheep/api/set'; // 导入SetApi用于获取单词数据
  import SessionApi from '@/sheep/api/set/session'; // 导入SessionApi用于创建学习会话和记录

  // 页面参数
  const options = ref({});

  // 标题显示配对时间
  const title = ref('0.1秒');

  // 所有单词数据
  const originalWordPairs = ref([]);
  const loading = ref(true);

  // 游戏统计相关
  const gameStats = ref({
    time: '0.0',
    penaltyTime: '0.0',
    totalPairs: 0,
    finalDisplayTime: '0.0' // 添加一个字段保存最终显示的时间
  });

  // 学习会话相关
  const sessionId = ref(null);
  const MODE_MATCH = 3;

  // 分页相关
  const PAIRS_PER_PAGE = 4; // 每页显示5组配对
  const currentPage = ref(1);
  const totalPages = computed(() => {
    if (!originalWordPairs.value || originalWordPairs.value.length === 0) {
      return 0;
    }
    return Math.ceil(originalWordPairs.value.length / PAIRS_PER_PAGE);
  });
  const currentWordPairs = ref([]);

  // 获取学习集单词卡数据
  const fetchSetData = async (setId) => {
    loading.value = true;
    // 调用API获取单词集数据
    const { code, data } = await SetApi.getWordSet(setId);

    if (code !== 0) {
      loading.value = false;
      return;
    }

    // 处理列表包装的响应结构
    let setData = null;
    if (data && data.list && data.list.length > 0) {
      // 如果响应是列表结构，获取第一个元素
      setData = data.list[0];
    } else if (data) {
      // 直接使用data对象
      setData = data;
    }

    if (setData && setData.wordCards && Array.isArray(setData.wordCards)) {
      // 处理单词卡数据，转换为匹配游戏需要的格式
      const wordPairs = setData.wordCards.map(item => {
        // 从定义中获取所有定义
        let translation = '';
        if (item.definitions && Array.isArray(item.definitions)) {
          translation = item.definitions
            .map(def => def.definition)
            .filter(def => def && def.trim() !== '')
            .join('，');
        }

        // 返回泰语和中文的配对
        return {
          thai: item.word || '', // 泰语单词
          chinese: translation || '暂无定义', // 中文翻译
        };
      });

      // 过滤掉无效的单词对（确保泰语和中文都有内容）
      const validWordPairs = wordPairs.filter(pair =>
        pair.thai && pair.thai.trim() !== '' &&
        pair.chinese && pair.chinese.trim() !== ''
      );

      if (validWordPairs.length === 0) {
        sheep.$helper.toast('学习集为空');
        loading.value = false;
        return;
      }

      // 更新单词数据
      originalWordPairs.value = validWordPairs;

      // 初始化游戏
      initGameData();
    }
  };

  // 所有卡片的混合数组
  const allCards = ref([]);

  // 左右两列的卡片
  const leftColumnCards = ref([]);
  const rightColumnCards = ref([]);

  // 随机排列后的数据（保留用于配对逻辑）
  const thaiWords = ref([]);
  const chineseWords = ref([]);

  // 记录匹配状态 - 会根据当前页面动态调整大小
  const thaiMatched = ref([]);
  const chineseMatched = ref([]);
  const correctPairs = ref([]);
  const allCorrectPairs = ref([]);

  // 游戏总体状态
  const totalMatched = ref(0);

  // 选中状态
  const selectedThaiIndex = ref(null);
  const selectedChineseIndex = ref(null);
  const selectedCard = ref(null);
  const firstSelectedCard = ref(null);  // 第一个选中的卡片
  const secondSelectedCard = ref(null); // 第二个选中的卡片
  const isWrongMatch = ref(false);
  const isCorrectPairSelected = ref(false); // 正确配对被选中

  // 是否正在展示页面完成动画
  const isShowingPageComplete = ref(false);

  // 检查卡片是否是第一个选中的卡片
  const isCardSelectedFirst = (card) => {
    if (!card || !firstSelectedCard.value) return false;
    return card.type === firstSelectedCard.value.type &&
           card.originalIndex === firstSelectedCard.value.originalIndex;
  };

  // 检查卡片是否是第二个选中的卡片
  const isCardSelectedSecond = (card) => {
    if (!card || !secondSelectedCard.value) return false;
    return card.type === secondSelectedCard.value.type &&
           card.originalIndex === secondSelectedCard.value.originalIndex;
  };

  // 打乱数组顺序
  const shuffleArray = (array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  // 获取当前页的单词配对
  const getCurrentPageWordPairs = () => {
    const startIndex = (currentPage.value - 1) * PAIRS_PER_PAGE;
    const endIndex = Math.min(startIndex + PAIRS_PER_PAGE, originalWordPairs.value.length);
    return originalWordPairs.value.slice(startIndex, endIndex);
  };

  // 初始化当前页游戏数据
  const initCurrentPageData = () => {
    // 获取当前页的单词配对
    currentWordPairs.value = getCurrentPageWordPairs();

    // 创建并打乱泰语词和中文词（保留用于配对逻辑）
    const shuffledThai = shuffleArray(currentWordPairs.value.map(pair => pair.thai));
    const shuffledChinese = shuffleArray(currentWordPairs.value.map(pair => pair.chinese));

    thaiWords.value = shuffledThai;
    chineseWords.value = shuffledChinese;

    // 初始化匹配状态 - 根据当前页的数量创建
    const pairsCount = currentWordPairs.value.length;

    if (pairsCount > 0) {
      thaiMatched.value = Array(pairsCount).fill(false);
      chineseMatched.value = Array(pairsCount).fill(false);
    } else {
      thaiMatched.value = [];
      chineseMatched.value = [];
    }

    // 构建全部卡片数组
    const cards = [];

    // 添加泰语卡片
    for (let i = 0; i < shuffledThai.length; i++) {
      cards.push({
        type: 'thai',
        text: shuffledThai[i],
        originalIndex: i  // 原始索引，用于匹配
      });
    }

    // 添加中文卡片
    for (let i = 0; i < shuffledChinese.length; i++) {
      cards.push({
        type: 'chinese',
        text: shuffledChinese[i],
        originalIndex: i  // 原始索引，用于匹配
      });
    }

    // 打乱所有卡片
    const shuffledCards = shuffleArray(cards);
    allCards.value = shuffledCards;

    // 将卡片分配到左右两列
    const halfLength = Math.ceil(shuffledCards.length / 2);
    leftColumnCards.value = shuffledCards.slice(0, halfLength);
    rightColumnCards.value = shuffledCards.slice(halfLength);

    // 清空当前页已匹配的对
    correctPairs.value = [];

    // 重置选中状态
    selectedThaiIndex.value = null;
    selectedChineseIndex.value = null;
    selectedCard.value = null;
    firstSelectedCard.value = null;
    secondSelectedCard.value = null;
    isWrongMatch.value = false;
    isCorrectPairSelected.value = false;
  };

  // 初始化游戏数据
  const initGameData = () => {
    if (originalWordPairs.value.length === 0) {
      sheep.$helper.toast('单词卡为空，无法开始学习')
      return;
    }

    // 初始化游戏状态变量
    thaiWords.value = [];
    chineseWords.value = [];
    thaiMatched.value = [];
    chineseMatched.value = [];
    leftColumnCards.value = [];
    rightColumnCards.value = [];
    allCards.value = [];

    // 重置游戏状态
    currentPage.value = 1;
    totalMatched.value = 0;
    allCorrectPairs.value = [];

    // 初始化当前页
    initCurrentPageData();
  };

  // 加载下一页
  const loadNextPage = () => {
    // 对保存当前页完成的配对到总配中
    allCorrectPairs.value = [...allCorrectPairs.value, ...correctPairs.value];

    // 增加页码
    currentPage.value++;

    // 如果还有下一页，初始化下一页数据
    if (currentPage.value <= totalPages.value) {
      isShowingPageComplete.value = true;
      
      // 暂停计时器并保存当前时间状态
      if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
      }
      
      // 更新一次计时器显示，确保时间准确
      updateTimer();

      // 延迟加载下一页，给用户一个视觉反馈
      setTimeout(() => {
        initCurrentPageData();
        isShowingPageComplete.value = false;
        
        // 重新启动计时器
        if (!timerInterval) {
          // 重新设置开始时间，保持累计时间不变
          startTime = Date.now() - (parseFloat(title.value.replace('秒', '')) - penaltyTime.value) * 1000;
          timerInterval = setInterval(updateTimer, 100);
        }
      }, 1500);
    } else {
      // 已经是最后一页，游戏完成
      gameComplete();
    }
  };

  // 辅助函数 - 检查卡片是否已匹配
  const isCardMatched = (type, index) => {
    try {
      if (type === 'thai') {
        if (!thaiMatched.value || !Array.isArray(thaiMatched.value)) return false;
        if (index < 0 || index >= thaiMatched.value.length) return false;
        return thaiMatched.value[index] === true;
      } else if (type === 'chinese') {
        if (!chineseMatched.value || !Array.isArray(chineseMatched.value)) return false;
        if (index < 0 || index >= chineseMatched.value.length) return false;
        return chineseMatched.value[index] === true;
      }
      return false;
    } catch (error) {
      return false;
    }
  };

  // 检查游戏是否完成
  const checkGameComplete = () => {
    try {
      // 确保匹配数组已初始化
      if (!thaiMatched.value || !chineseMatched.value) {
        return false;
      }

      // 确保匹配数组是数组类型
      if (!Array.isArray(thaiMatched.value) || !Array.isArray(chineseMatched.value)) {
        return false;
      }

      // 检查所有泰文卡片是否已匹配
      const allThaiMatched = thaiMatched.value.length > 0 && thaiMatched.value.every(matched => matched === true);
      // 检查所有中文卡片是否已匹配
      const allChineseMatched = chineseMatched.value.length > 0 && chineseMatched.value.every(matched => matched === true);

      if (allThaiMatched && allChineseMatched) {
        // 停止计时器
        clearInterval(timerInterval);
        timerInterval = null;
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  };

  // 导航到结果页面
  const navigateToResultPage = () => {
    try {
      // 使用保存的最终显示时间
      const finalTime = gameStats.value.finalDisplayTime;

      // 导航到结果页面
      sheep.$router.go('/pages/set/match/result', {
        time: finalTime,
        setId: options.value.id || options.value.setId
      },{redirect: true});
    } catch (error) {
      uni.navigateBack();
    }
  };

  // 提示相关状态
  const isShowingPrompt = ref(false);
  const isShowingErrorPrompt = ref(false);
  const isShowingSuccessPrompt = ref(false);
  const promptText = ref('');

  // 显示提示
  const showPrompt = (text, isError) => {
    promptText.value = text;
    isShowingErrorPrompt.value = isError;
    isShowingSuccessPrompt.value = !isError;
    isShowingPrompt.value = true;

    // 2秒后淡出
    setTimeout(() => {
      isShowingPrompt.value = false;
      setTimeout(() => {
        promptText.value = '';
      }, 300); // 淡出动画时间
    }, 2000);
  };


  // 选择卡片
  const selectCard = (card) => {
    try {
      // 如果卡片已匹配，不做任何操作
      if (isCardMatched(card.type, card.originalIndex)) {
        return;
      }

      // 如果正在显示错误状态，不做任何操作
      if (isWrongMatch.value) {
        return;
      }

      // 如果是第一张卡片
      if (firstSelectedCard.value === null) {
        firstSelectedCard.value = card;
        return;
      }

      // 如果点击了同一张卡片，不做任何操作
      if (firstSelectedCard.value.type === card.type &&
          firstSelectedCard.value.originalIndex === card.originalIndex) {
        return;
      }

      // 如果是第二张卡片
      secondSelectedCard.value = card;

      // 判断是否为泰语-中文配对
      let thaiIndex = -1;
      let chineseIndex = -1;

      if (firstSelectedCard.value.type === 'thai') {
        thaiIndex = firstSelectedCard.value.originalIndex;
      } else if (firstSelectedCard.value.type === 'chinese') {
        chineseIndex = firstSelectedCard.value.originalIndex;
      }

      if (secondSelectedCard.value.type === 'thai') {
        thaiIndex = secondSelectedCard.value.originalIndex;
      } else if (secondSelectedCard.value.type === 'chinese') {
        chineseIndex = secondSelectedCard.value.originalIndex;
      }

      // 仅当选择了一个泰语和一个中文单词时才检查配对
      if (thaiIndex !== -1 && chineseIndex !== -1) {
        // 获取原始单词
        const thaiWord = thaiWords.value[thaiIndex];
        const chineseWord = chineseWords.value[chineseIndex];

        // 检查是否是正确配对
        // 正确配对：当前页中，对应索引的泰语单词和中文单词来自同一个词对
        const currentPagePairs = getCurrentPageWordPairs();
        let isCorrect = false;

        for (const pair of currentPagePairs) {
          if (pair.thai === thaiWord && pair.chinese === chineseWord) {
            isCorrect = true;
            break;
          }
        }

        // 记录匹配结果
        recordMatch(thaiWord, chineseWord, isCorrect);

        if (isCorrect) {
          // 正确配对：更新匹配状态
          isCorrectPairSelected.value = true;
          
          // 不添加时间奖励
          showPrompt('配对成功', false); // 显示绿色提示
          
          // 检查这是否是当前页面的最后一对
          const isLastPairOfPage = thaiMatched.value.filter(matched => matched === true).length === thaiMatched.value.length - 1 &&
                                  chineseMatched.value.filter(matched => matched === true).length === chineseMatched.value.length - 1;
          
          // 如果是最后一对，立即停止计时器
          if (isLastPairOfPage) {
            // 立即停止计时器
            if (timerInterval) {
              clearInterval(timerInterval);
              timerInterval = null;
            }
            // 更新一次计时器显示，确保最终时间准确
            updateTimer();
            // 保存当前显示的时间（不带"秒"字）
            gameStats.value.finalDisplayTime = title.value.replace('秒', '');
          }
          
          // 延迟更新UI，以便用户看到卡片选择效果
          setTimeout(() => {
            // 确保匹配数组已初始化
            if (!Array.isArray(thaiMatched.value)) {
              thaiMatched.value = Array(thaiWords.value.length).fill(false);
            }
            if (!Array.isArray(chineseMatched.value)) {
              chineseMatched.value = Array(chineseWords.value.length).fill(false);
            }

            // 标记为已匹配
            thaiMatched.value[thaiIndex] = true;
            chineseMatched.value[chineseIndex] = true;

            // 增加匹配计数
            totalMatched.value++;

            // 添加到已配对列表
            correctPairs.value.push({
              thaiIndex,
              chineseIndex,
              thaiWord,
              chineseWord
            });

            // 重置选择状态
            firstSelectedCard.value = null;
            secondSelectedCard.value = null;
            isCorrectPairSelected.value = false;

            // 检查游戏是否完成
            if (checkGameComplete()) {
              // 如果有下一页，加载下一页
              if (currentPage.value < totalPages.value) {
                loadNextPage();
              } else {
                // 游戏全部完成
                gameComplete();
              }
            }
          }, 800);
        } else {
          // 错误配对：显示错误，增加惩罚时间s
          isWrongMatch.value = true;
          addTimePenalty(2); // 增加2秒惩罚
          showPrompt('+2s', true); // 显示红色提示
          // 延迟重置状态
          setTimeout(() => {
            firstSelectedCard.value = null;
            secondSelectedCard.value = null;
            isWrongMatch.value = false;
          }, 1000);
        }
      } else {
        // 选择了两个相同类型的卡片，视为错误
        isWrongMatch.value = true;
        addTimePenalty(2); // 增加2秒惩罚
        showPrompt('+2s', true); // 显示红色提示
        // 延迟重置状态
        setTimeout(() => {
          firstSelectedCard.value = null;
          secondSelectedCard.value = null;
          isWrongMatch.value = false;
        }, 1000);
      }
    } catch (error) {
      // 重置状态
      firstSelectedCard.value = null;
      secondSelectedCard.value = null;
      isWrongMatch.value = false;
    }
  };

  // 学习完成
  const gameComplete = () => {
    try {
      // 停止计时器
      if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
      }
      
      // 更新一次计时器显示，确保最终时间准确
      updateTimer();
      
      // 保存当前显示的时间（不带"秒"字）
      gameStats.value.finalDisplayTime = title.value.replace('秒', '');
      
      // 创建一个副本，防止后续操作修改
      const finalDisplayTime = gameStats.value.finalDisplayTime;
      
      // 调用完成会话函数并导航到结果页
      completeSession().then(() => {
        // 确保使用之前保存的时间
        gameStats.value.finalDisplayTime = finalDisplayTime;
        navigateToResultPage();
      });
    } catch (error) {
      sheep.$router.back();
    }
  };

  // 计时相关
  let startTime = 0;
  let timerInterval = null;
  const penaltyTime = ref(0); // 使用响应式引用存储惩罚/奖励时间

  // 开始计时
  const startTimer = () => {
    startTime = Date.now();
    penaltyTime.value = 0; // 重置惩罚/奖励时间
    timerInterval = setInterval(updateTimer, 100); // 每100ms更新一次
  };

  // 更新计时显示
  const updateTimer = () => {
    // 如果计时器已停止，不再更新时间
    if (!timerInterval) {
      return;
    }
    
    const currentTime = Date.now();
    const rawElapsedSeconds = (currentTime - startTime) / 1000;
    let finalElapsedSeconds = rawElapsedSeconds + penaltyTime.value;

    // 确保最终时间不低于0
    if (finalElapsedSeconds < 0) {
      finalElapsedSeconds = 0;
    }

    title.value = `${finalElapsedSeconds.toFixed(1)}秒`;
  };

  // 增加时间惩罚
  const addTimePenalty = (seconds) => {
    const numSeconds = Number(seconds);
    penaltyTime.value = Number(penaltyTime.value) + numSeconds;
    updateTimer(); // 立即更新显示
  };



  // 停止计时
  const stopTimer = () => {
    if (timerInterval) {
      clearInterval(timerInterval);
    }
  };

  // 自定义返回方法
  const handleBack = () => {
    // 显示确认对话框
    uni.showModal({
      title: '提示',
      content: '确定要退出匹配学习吗？当前进度将不会保存。',
      cancelText: '继续学习',
      confirmText: '退出',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确认退出
          stopTimer(); // 停止计时器

          // 如果会话存在，完成会话
          if (sessionId.value) {
            SessionApi.completeSession(sessionId.value)
		      }

          sheep.$router.back(); // 返回上一页
        }
      }
    });
  };

  // 创建或获取学习会话
  const initSession = async (setId) => {
    // 获取正在进行中的配对模式学习会话
    const { code, data } = await SessionApi.getOngoingSession({
      setId: setId,
      mode: MODE_MATCH,
    });

    // 检查返回的数据是否有效（处理code=0但data为null的情况）
    if (code === 0 && data && data.id) {
      sessionId.value = data.id;
      return;
    }
  };

  // 记录匹配情况
  const recordMatch = async (thaiWord, chineseWord, isCorrect) => {
    if (!sessionId.value) return;
    await SessionApi.createMatchRecord({
      setId: options.value.id || options.value.setId,
      sessionId: sessionId.value,
      word: thaiWord,
      definition: chineseWord,
      isCorrect: isCorrect ? 1 : 0, // 将布尔值转换为 1/0
    });
  };

  // 完成会话
  const completeSession = async () => {
    if (!sessionId.value) {
      return Promise.resolve(); // 返回已解决的Promise
    }

    // 使用保存的最终显示时间
    const finalTime = parseFloat(gameStats.value.finalDisplayTime);
    const formattedTime = finalTime.toFixed(1);

    // 创建会话成绩
    await SessionApi.createSessionResult({
      sessionId: sessionId.value,
      setId: options.value.id || options.value.setId,
      timeTaken: finalTime, // 使用精确的时间，不再四舍五入
    });

    // 调用API完成会话
    await SessionApi.completeSession(sessionId.value);

    // 更新完成状态
    const currentLearnSet = uni.getStorageSync('currentLearnSet') || {};
    const completedWordIds = currentLearnSet.completedWordIds || [];

    // 添加本次学习的单词ID到完成列表
    const currentWordIds = originalWordPairs.value.map(pair => pair.id);
    // 更新本地存储
    currentLearnSet.completedWordIds = [...new Set([...completedWordIds, ...currentWordIds])];
    uni.setStorageSync('currentLearnSet', currentLearnSet);

    // 保存游戏完成的时间统计
    const savedFinalDisplayTime = gameStats.value.finalDisplayTime;
    gameStats.value = {
      time: formattedTime,
      penaltyTime: penaltyTime.value.toFixed(1),
      totalPairs: originalWordPairs.value.length,
      finalDisplayTime: savedFinalDisplayTime // 使用之前保存的值，而不是formattedTime
    };

    return Promise.resolve();
  };

  // 页面加载时
  onLoad((opt) => {
    options.value = opt;
    if (opt.id || opt.setId) {
      fetchSetData(opt.id || opt.setId);
      initSession(opt.id || opt.setId); // 初始化学习会话

      // 加载完数据后自动开始计时
      setTimeout(() => {
        startTimer();
      }, 500);
    } else {
      loading.value = false;
    }
  });

  // 页面卸载时
  onUnload(() => {
    // 停止计时
    stopTimer();
  });
</script>

<style scoped>

  /* 提示文字样式 */
  .time-prompt {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 80rpx;
    font-weight: bold;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 100;
  }

  .time-prompt.visible {
    opacity: 1;
  }

  .error-prompt {
    color: #FF5959;
    text-shadow: 0 2rpx 4rpx rgba(255, 89, 89, 0.3);
  }

  .success-prompt {
    color: #12D5B2;
    text-shadow: 0 2rpx 4rpx rgba(91, 250, 125, 0.3);
  }


  .container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f4f8fc;
  }

  /* 自定义返回按钮样式 */
  .close-btn {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6rpx;
  }

  .close-icon {
    font-size: 50rpx;
    font-weight: bold;
    color: #333;
    line-height: 1;
  }

  /* 卡片区域样式 */
  .cards-container {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
    flex: 1;
    margin-top: 20rpx;
  }

  .card-column {
    width: 48%;
    height: 90%;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  .card-wrapper {
    min-height: 120rpx;
    margin-bottom: 20rpx;
  }

  .card {
    width: 270rpx;
    height: 200rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    min-height: 160rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 35rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
  }

  .card-text {
    font-size: 32rpx;
    text-align: center;
  }

  /* 选中状态 */
  .selected-blue {
    background-color: #EAFFFB;
    border: 1px solid #40ECC6;
  }

  .selected-green {
    background-color: #EAFFFB;
    border: 1px solid #40ECC6;
  }

  .selected-red {
    background-color: #FFE8E8;
    border: 1px solid #FF5555;
  }

  /* 隐藏卡片但保持空间 */
  .card-hidden {
    opacity: 0;
    pointer-events: none;
  }

</style>
