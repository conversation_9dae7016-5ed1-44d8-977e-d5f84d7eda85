<template>
  <view class="chat-page">
    <!-- 使用course-navbar组件 -->
    <course-navbar
      title="用户18750"
      :showBack="true"
      placeholder=""
    >
      <template #right>
        <view class="custom-right">
          <image class="nav-icon" :src="qrcodeIcon" @click="showQRCode" />
          <image class="nav-icon" :src="setupIcon" @click="showSettings" />
        </view>
      </template>
    </course-navbar>

    <!-- 聊天内容区域 -->
    <view class="chat-content">
      <!-- 时间显示 -->
      <view class="time-display">21:36</view>

      <!-- 系统提示 -->
      <view class="system-message">你创建了对话</view>
      <view class="system-message">发送的消息会自动翻译为其他对话成员的语言</view>

      <!-- 聊天消息列表 -->
      <view class="message-list">
        <view
          v-for="message in messages"
          :key="message.id"
          class="message-item"
          :class="message.type === 'sent' ? 'my-message' : 'other-message'"
        >
          <!-- 自己发送的消息（蓝色气泡） -->
          <view v-if="message.type === 'sent'" class="message-wrapper">
            <view class="message-bubble my-bubble">
              <!-- 原句 -->
              <view class="original-text" v-if="message.displayMode !== 'translation'">
                <text class="message-text" :style="{ fontSize: fontSize + 'rpx' }">{{ message.originalText }}</text><image class="play-icon" :src="whitePlayIcon" @click="playMessage(message)" />
              </view>
              <!-- 翻译 -->
              <view class="translation-text" v-if="message.displayMode !== 'original'">
                <text class="light-text-white" v-show="message.displayMode === 'translation'" :style="{ fontSize: fontSize + 'rpx' }">{{ message.translationText }}</text><image class="play-icon" v-if="message.displayMode === 'translation'" :src="whitePlayIcon" @click="playMessage(message)" />
                <text class="light-text-white" v-show="message.displayMode !== 'translation'" :style="{ fontSize: fontSize + 'rpx' }">{{ message.translationText }}</text>
              </view>
            </view>
            <!-- 翻译选项（移到气泡外面） -->
            <view class="translation-options my-translation-options">
              <view class="option-btn" :class="{ active: message.displayMode === 'both' }" @click="setMessageDisplayMode(message, 'both')">双语</view>
              <view class="option-btn" :class="{ active: message.displayMode === 'original' }" @click="setMessageDisplayMode(message, 'original')">原文</view>
              <view class="option-btn" :class="{ active: message.displayMode === 'translation' }" @click="setMessageDisplayMode(message, 'translation')">译文</view>
            </view>
          </view>

          <!-- 对方发送的消息（白色气泡） -->
          <view v-else class="message-wrapper">
            <view class="message-bubble other-bubble">
              <!-- 原句 -->
              <view class="original-text" v-if="message.displayMode !== 'translation'">
                <text class="message-text dark-text" :style="{ fontSize: fontSize + 'rpx' }">{{ message.originalText }}</text><image class="play-icon" :src="bluePlayIcon" @click="playMessage(message)" />
              </view>
              <!-- 翻译 -->
              <view class="translation-text" v-if="message.displayMode !== 'original'">
                <text class="light-text" v-show="message.displayMode === 'translation'" :style="{ fontSize: fontSize + 'rpx' }">{{ message.translationText }}</text><image class="play-icon" v-if="message.displayMode === 'translation'" :src="bluePlayIcon" @click="playMessage(message)" />
                <text class="light-text" v-show="message.displayMode !== 'translation'" :style="{ fontSize: fontSize + 'rpx' }">{{ message.translationText }}</text>
              </view>
            </view>
            <!-- 翻译选项（移到气泡外面） -->
            <view class="translation-options">
              <view class="option-btn" :class="{ active: message.displayMode === 'both' }" @click="setMessageDisplayMode(message, 'both')">双语</view>
              <view class="option-btn" :class="{ active: message.displayMode === 'original' }" @click="setMessageDisplayMode(message, 'original')">原文</view>
              <view class="option-btn" :class="{ active: message.displayMode === 'translation' }" @click="setMessageDisplayMode(message, 'translation')">译文</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部输入区域 -->
    <view class="bottom-input">
      <!-- 上方按钮区域 -->
      <view class="button-row">
        <!-- 左侧静音控制 -->
        <image
          class="sound-btn"
          :src="isSoundEnabled ? soundOnIcon : soundOffIcon"
          @click="toggleSound"
        />

        <!-- 中间说话按钮 -->
        <view class="speak-button" @click="startSpeak">
          <text class="speak-text">点击说话</text>
        </view>
      </view>

      <!-- 下方语言切换区域 -->
      <view class="language-controls">
        <!-- 原文语言 -->
        <view class="language-item">
          <text class="language-text" @click="expandSourceLanguages" >{{ selectedSourceLanguage.label }}</text>
        </view>

        <!-- 翻转按钮 -->
        <image class="flip-btn" :src="flipIcon" @click="flipLanguages" />

        <!-- 翻译的语言 -->
        <view class="language-item">
          <text class="language-text" @click="expandTargetLanguages" >{{ selectedLanguage.label }}</text>
          <!-- 展开按钮 -->
          <image class="expand-btn" :src="expandIcon" @click="expandTargetLanguages" />
        </view>
      </view>
    </view>
  </view>

  <!-- 语言选择弹窗 -->
  <su-popup :show="showLanguagePopup" type="bottom" round="20" @close="closeLanguagePopup">
    <view class="language-popup">
      <!-- 区域1：搜索框 -->
      <view class="search-area">
        <view class="search-box">
          <image class="search-icon" :src="sheep.$url.cdn('/tool/search.png')" mode="aspectFit" />
          <input class="search-input" placeholder="搜索" />
        </view>
      </view>

      <!-- 区域2和3：语言选择区域 -->
      <view class="language-content">
        <!-- 标题行 -->
        <view class="language-headers">
          <view class="left-header">
            <view class="column-title">对方语言</view>
          </view>
          <view class="right-header">
            <view class="column-title">我的语言</view>
          </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-area">
          <!-- 区域2：对方语言区域（左侧） -->
          <view class="left-content">
            <view class="column-subtitle">{{ opponentUserName }}:{{ opponentLanguage }}</view>
          </view>

          <!-- 区域3：我的语言列表（右侧） -->
          <view class="right-content">
            <scroll-view class="language-scroll" scroll-y="true" :scroll-with-animation="true">
              <!-- 常用语言部分 -->
              <view class="language-section">
                <view class="section-title">常用语言</view>
                <view class="language-list">
                  <view
                    v-for="language in currentCommonLanguages"
                    :key="'common-' + language.value"
                    class="language-option"
                    @click="selectLanguage(language)"
                  >
                    <text class="language-name" :class="{ active: language.selected }">{{ language.label }}</text>
                    <image v-if="language.selected" class="chosen-icon" :src="chosenIcon" />
                  </view>
                </view>
              </view>

              <!-- 所有语言部分 -->
              <view class="language-section">
                <view class="section-title">所有语言</view>
                <view class="language-list">
                  <view
                    v-for="language in currentAllLanguages"
                    :key="'all-' + language.value"
                    class="language-option"
                    @click="selectLanguage(language)"
                  >
                    <text class="language-name" :class="{ active: language.selected }">{{ language.label }}</text>
                    <image v-if="language.selected" class="chosen-icon" :src="chosenIcon" />
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>
  </su-popup>

  <!-- 二维码弹窗 - 自定义定位 -->
  <view v-if="showQRCodePopup" class="qrcode-overlay" @click="closeQRCodePopup">
    <view class="qrcode-popup" @click.stop>
      <!-- 标题 -->
      <view class="qrcode-title">扫码加入对话</view>

      <!-- 二维码图片 -->
      <view class="qrcode-container">
        <image class="qrcode-image" :src="qrCodeData.qrCodeUrl" mode="aspectFit" />
      </view>

      <!-- 对话号码 -->
      <view class="dialog-number-container">
        <text class="dialog-number-text">对话号码:{{ qrCodeData.dialogNumber }}</text>
        <image class="copy-icon" :src="copyIcon" @click="copyDialogNumber" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import CryptoJS from '@/sheep/util/crypto.js';
import NlsApi from '@/sheep/api/voice/nls.js';
import {getTtsConfig, TtsTypeEnum} from "@/sheep/util/language-detector";
import sheep from "@/sheep";

// --- 持久化存储键 ---
const STORAGE_KEY_AUTO_PLAY = 'interpreter_auto_play';
const STORAGE_KEY_FROM_LANG = 'interpreter_from_language';
const STORAGE_KEY_TO_LANG = 'interpreter_to_language';

// --- 配置信息 ---
const APP_KEY = "60310af61cc10ed1";
const APP_SECRET = "bEfx49Wj0d1Un0doD7PfauVKWnghvxN9";

// 图标资源
const qrcodeIcon = sheep.$url.cdn('/tool/QRcode.png')
const setupIcon = sheep.$url.cdn('/tool/setup.png')
const bluePlayIcon = sheep.$url.cdn('/tool/blueplay.png')
const whitePlayIcon = sheep.$url.cdn('/tool/whiteplay.png')
const soundOnIcon = sheep.$url.cdn('/tool/enablesound.png')
const soundOffIcon = sheep.$url.cdn('/tool/mute.png')
const expandIcon = sheep.$url.cdn('/tool/languageexpand.png')
const flipIcon = sheep.$url.cdn('/tool/languageflip.png')
const chosenIcon = sheep.$url.cdn('/tool/chosen.png')
const copyIcon = sheep.$url.cdn('/tool/copy-number.png')

// --- 响应式状态 ---
const isRecording = ref(false);
const messages = ref([]); // 改为使用messages而不是chatBubbles
const scrollTop = ref(0);
const audioQueue = ref([]); // 音频播放队列
const isAudioPlaying = ref(false); // 是否有音频正在自动播放
const isAutoPlayEnabled = ref(true); // 是否开启自动播放
const manuallyPlayingId = ref(null); // 正在手动播放的bubble id

// 声音状态（用于底部控制）
const isSoundEnabled = ref(true)

// 字体大小设置
const fontSize = ref(25) // 默认字体大小32rpx

// 显示模式：both(双语), original(原文), translation(译文)
const displayMode = ref('both')

// 语言选择弹窗状态
const showLanguagePopup = ref(false)

// 二维码弹窗状态
const showQRCodePopup = ref(false)

// 二维码相关数据
const qrCodeData = ref({
  dialogNumber: 'd42507',
  qrCodeUrl: 'https://env-00jxhbece7rd.normal.cloudstatic.cn/2024/11/11/93878719-74744246-f082d2ff5004e01bbc2bfabb9cf7f23.png?expire_at=1731294481&er_sign=1b07dc7d93027bec9605f4f20a8acd1a'
})

// 语言选择数据
const languageOptions = ref([
  { value: 'zh-CHS', label: '中文' },
  { value: 'en', label: '英文' },
  { value: 'th', label: '泰语' },
  { value: 'vi', label: '越南语' },
  { value: 'id', label: '印尼语' },
  { value: 'ms', label: '马来语' },
  { value: 'km', label: '高棉语' },
  { value: 'lo', label: '老挝语' },
  { value: 'my', label: '缅甸语' },
  { value: 'tl', label: '菲律宾语' },
]);
const fromLanguageIndex = ref(0); // 默认中文
const toLanguageIndex = ref(2);   // 默认泰语

// --- 录音和 WebSocket ---
let recorderManager;
let socketTask;
let audioBuffer = [];
let isConnecting = ref(false);

// 保留UI需要的状态
const currentLanguageType = ref('target')
const opponentUserName = ref('用户18750')
const opponentLanguage = ref('泰语')

// 原文语言选择数据（保持UI兼容）
const sourceLanguageOptions = ref([
  { value: 'zh-CHS', label: '中文', selected: true, isCommon: true },
  { value: 'th', label: '泰语', selected: false, isCommon: true },
  { value: 'en', label: '英文', selected: false, isCommon: false },
  { value: 'vi', label: '越南语', selected: false, isCommon: false },
  { value: 'id', label: '印尼语', selected: false, isCommon: false },
  { value: 'ms', label: '马来语', selected: false, isCommon: false },
  { value: 'km', label: '高棉语', selected: false, isCommon: false },
  { value: 'lo', label: '老挝语', selected: false, isCommon: false },
  { value: 'my', label: '缅甸语', selected: false, isCommon: false },
  { value: 'tl', label: '菲律宾语', selected: false, isCommon: false }
])

// 目标语言选择数据（保持UI兼容）
const targetLanguageOptions = ref([
  { value: 'zh-CHS', label: '中文', selected: false, isCommon: true },
  { value: 'th', label: '泰语', selected: true, isCommon: true },
  { value: 'en', label: '英文', selected: false, isCommon: false },
  { value: 'vi', label: '越南语', selected: false, isCommon: false },
  { value: 'id', label: '印尼语', selected: false, isCommon: false },
  { value: 'ms', label: '马来语', selected: false, isCommon: false },
  { value: 'km', label: '高棉语', selected: false, isCommon: false },
  { value: 'lo', label: '老挝语', selected: false, isCommon: false },
  { value: 'my', label: '缅甸语', selected: false, isCommon: false },
  { value: 'tl', label: '菲律宾语', selected: false, isCommon: false }
])

// 计算属性：获取当前选中的原文语言对象
const selectedSourceLanguage = computed(() => {
  return sourceLanguageOptions.value.find(lang => lang.selected) || sourceLanguageOptions.value[0]
})

// 计算属性：获取当前选中的目标语言对象
const selectedLanguage = computed(() => {
  return targetLanguageOptions.value.find(lang => lang.selected) || targetLanguageOptions.value[1]
})

// 计算属性：根据当前语言类型返回相应的常用语言
const currentCommonLanguages = computed(() => {
  if (currentLanguageType.value === 'source') {
    return sourceLanguageOptions.value.filter(lang => lang.isCommon)
  } else {
    return targetLanguageOptions.value.filter(lang => lang.isCommon)
  }
})

// 计算属性：根据当前语言类型返回相应的所有语言
const currentAllLanguages = computed(() => {
  if (currentLanguageType.value === 'source') {
    return sourceLanguageOptions.value
  } else {
    return targetLanguageOptions.value
  }
})



// 显示二维码弹窗
const showQRCode = () => {
  showQRCodePopup.value = true
}

// 关闭二维码弹窗
const closeQRCodePopup = () => {
  showQRCodePopup.value = false
}

// 复制对话号码
const copyDialogNumber = () => {
  // 复制到剪贴板
  uni.setClipboardData({
    data: qrCodeData.value.dialogNumber,
    success: () => {
      uni.showToast({
        title: '已复制对话号码',
        icon: 'success'
      })
    }
  })
}

// 字体大小变化监听
const handleFontSizeChange = (newSize) => {
  fontSize.value = newSize
}

onMounted(() => {
  recorderManager = uni.getRecorderManager();
  setupRecorderListeners();

  // 加载用户设置
  const savedAutoPlay = uni.getStorageSync(STORAGE_KEY_AUTO_PLAY);
  if (savedAutoPlay !== '' && savedAutoPlay !== null) {
    isAutoPlayEnabled.value = savedAutoPlay;
  }

  const savedFromLang = uni.getStorageSync(STORAGE_KEY_FROM_LANG);
  if (savedFromLang !== '' && savedFromLang !== null) {
    fromLanguageIndex.value = savedFromLang;
  }

  const savedToLang = uni.getStorageSync(STORAGE_KEY_TO_LANG);
  if (savedToLang !== '' && savedToLang !== null) {
    toLanguageIndex.value = savedToLang;
  }

  // 同步UI语言选择与实际翻译设置
  syncUILanguageSelection();

  // 从本地存储读取静音状态
  const savedSoundEnabled = uni.getStorageSync('isSoundEnabled');
  if (savedSoundEnabled !== '' && savedSoundEnabled !== null) {
    isSoundEnabled.value = savedSoundEnabled;
  }

  // 从本地存储读取字体大小设置
  const savedFontSize = uni.getStorageSync('chatFontSize')
  if (savedFontSize) {
    fontSize.value = savedFontSize
  } else {
    fontSize.value = 25
  }

  // 监听字体大小变化事件
  uni.$on('fontSizeChanged', handleFontSizeChange)
});

onUnmounted(() => {
  if (isRecording.value) {
    recorderManager.stop();
  }
  if (socketTask) {
    socketTask.close();
  }
  // 清理音频资源
  stopAllAudio();
  uni.$off('fontSizeChanged', handleFontSizeChange)
});

const getSign = (salt, curtime) => {
  const str = APP_KEY + salt + curtime + APP_SECRET;
  return CryptoJS.SHA256(str).toString();
};

const connectWebSocket = () => {
  const salt = Math.random().toString(36).substring(2);
  const curtime = Math.floor(Date.now() / 1000);
  const sign = getSign(salt, curtime);
  const from = languageOptions.value[fromLanguageIndex.value].value;
  const to = languageOptions.value[toLanguageIndex.value].value;

  const url = `wss://openapi.youdao.com/stream_speech_trans?appKey=${APP_KEY}&salt=${salt}&curtime=${curtime}&sign=${sign}&signType=v4&from=${from}&to=${to}&format=wav&channel=1&version=v1&rate=16000`;

  socketTask = uni.connectSocket({
    url,
    success: () => {},
    fail: (err) => {
      console.error('WebSocket 连接失败:', err);
      uni.showToast({ title: '网络开小差了，请稍后重试', icon: 'none' });
    },
  });

  socketTask.onOpen(() => {
    isConnecting.value = false;

    // 在连接成功后开始录音;
    const options = {
      duration: 60000,
      sampleRate: 16000,
      numberOfChannels: 1,
      format: 'wav',
      frameSize: 1.28, // 按照有道文档建议，每 40ms 发送 1280 字节，16000*16/8*0.04 = 1280, 1280/1024=1.25k
    };
    recorderManager.start(options);

    // 发送缓存的音频数据
    if (audioBuffer.length > 0) {
      audioBuffer.forEach(buffer => {
        socketTask.send({ data: buffer });
      });
      audioBuffer = []; // 清空缓冲区
    }
  });

  socketTask.onMessage((res) => {
    const data = JSON.parse(res.data);
    if (data.errorCode === "0") {
      if (data.action === "started") {
        console.log("握手成功");
      } else {
        handleWsMessage(data);
      }
    } else if (data.errorCode !== "304") { // 304是会话闲置超时，不视为错误
      console.error('WebSocket 消息错误:', data);
      uni.showToast({ title: `服务开了个小差(${data.errorCode})，请稍后再试`, icon: 'none' });
    }
  });

  socketTask.onClose((res) => {
    console.log('WebSocket 已关闭:', res);
    isConnecting.value = false;
  });

  socketTask.onError((err) => {
    console.error('WebSocket 错误:', err);
    isConnecting.value = false;
    uni.showToast({ title: '网络开小差了，请稍后重试', icon: 'none' });
  });
};

// 显示设置
const showSettings = () => {
  sheep.$router.go(`/pages/tool/interpreter/components/settings`);
}

// 播放消息
const playMessage = async (message) => {
  if (!message || (!message.translationText && !message.originalText)) {
    console.warn('该消息没有可播放的文本')
    return
  }

  // 如果正在播放其他手动音频，先停止
  if (manualPlayAudioContext) {
    manualPlayAudioContext.stop();
    manualPlayAudioContext.destroy();
    manualPlayAudioContext = null;
  }

  // 如果点击的是正在播放的音频，则停止
  if (manuallyPlayingId.value === message.id) {
    manuallyPlayingId.value = null;
    return;
  }

  manuallyPlayingId.value = message.id;

  try {
    // 播放翻译文本，如果没有则播放原文
    const textToPlay = message.translationText || message.originalText;
    const audioSrc = await synthesizeAndStoreSpeech(textToPlay, message.id);
    if (!audioSrc) {
      manuallyPlayingId.value = null;
      return;
    }

    manualPlayAudioContext = uni.createInnerAudioContext();
    manualPlayAudioContext.src = audioSrc;

    // 根据静音状态设置音量
    manualPlayAudioContext.volume = isSoundEnabled.value ? 1.0 : 0.0;

    manualPlayAudioContext.play();

    manualPlayAudioContext.onEnded(() => {
      manuallyPlayingId.value = null;
      if (manualPlayAudioContext) {
        manualPlayAudioContext.destroy();
        manualPlayAudioContext = null;
      }
    });
    manualPlayAudioContext.onError(() => {
      manuallyPlayingId.value = null;
      if (manualPlayAudioContext) {
        manualPlayAudioContext.destroy();
        manualPlayAudioContext = null;
      }
    });
  } catch (error) {
    console.error('手动播放失败:', error);
    manuallyPlayingId.value = null;
  }
}

const setupRecorderListeners = () => {
  recorderManager.onStart(() => {
    isRecording.value = true;
    createNewMessage();
  });

  recorderManager.onStop(() => {
    isRecording.value = false;
    if (socketTask) {
      const endData = JSON.stringify({ end: "true" });
      socketTask.send({
        data: endData,
        success: () => console.log('结束帧已发送.'),
        fail: (err) => console.error('发送结束帧失败:', err),
      });
    }
    // --- 如果没有识别到任何文本，则删除空消息 ---
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage && !lastMessage.originalText) {
      messages.value.pop();
      return; // 直接返回，不进行后续TTS和播放处理
    }

    // --- 处理剩余未转换的翻译文本 ---
    if (lastMessage && lastMessage.translationText) {
      const remainingText = lastMessage.translationText.substring(lastMessage.fullTranslatedText.length).trim();
      if (remainingText) {
        synthesizeAndEnqueueSpeech(remainingText);
        // 更新已处理文本记录，防止重复
        lastMessage.fullTranslatedText = lastMessage.translationText;
      }
    }

    // 检查并继续处理音频队列
    processAudioQueue();
  });

  recorderManager.onFrameRecorded((res) => {
    const { frameBuffer } = res;
    if (!frameBuffer || frameBuffer.byteLength === 0) {
      return;
    }

    // 如果正在播放音频，则忽略当前录音帧，防止将播放的声音传回去
    if (isAudioPlaying.value) {
      return;
    }

    // 如果 WebSocket 正在连接或已连接，则发送数据，否则存入缓冲区
    if (socketTask && socketTask.readyState === 1) {
      socketTask.send({
        data: frameBuffer,
        fail: (err) => console.error('发送帧数据失败:', err),
      });
    } else {
      audioBuffer.push(frameBuffer);
    }
  });

  recorderManager.onError((err) => {
    console.error('录音错误:', err);
    isRecording.value = false;
    uni.showToast({ title: '录音失败，请重试', icon: 'none' });
  });
};

const toggleRecording = () => {
  if (isRecording.value) {
    recorderManager.stop();
  } else {
    if (isConnecting.value) {
      uni.showToast({ title: '正在连接中，请稍候...', icon: 'none' });
      return;
    }
    isConnecting.value = true;
    connectWebSocket();
  }
};

const createNewMessage = () => {
  messages.value.push({
    id: Date.now() + Math.random(), // 唯一ID
    type: 'sent',
    originalText: '',
    translationText: '',
    placeholder: true,
    fullOriginalText: '',
    fullTranslatedText: '',
    audioSrc: null, // 用于存储TTS音频文件路径
    displayMode: 'both'
  });
  scrollToBottom();
};

const handleWsMessage = (data) => {
  const lastMessage = messages.value[messages.value.length - 1];
  if (!lastMessage || !data.result) return;

  const isFinal = !data.result.partial;
  const sentence = data.result.context || '';
  const translation = data.result.tranContent || '';

  const displayOriginal = lastMessage.fullOriginalText + sentence;
  const displayTranslated = lastMessage.fullTranslatedText + translation;

  lastMessage.originalText = displayOriginal;
  lastMessage.translationText = displayTranslated;
  lastMessage.placeholder = false;

  if (isFinal && sentence) {
    lastMessage.fullOriginalText += sentence;
  }

  if (isFinal && translation) {
    const newTranslation = translation.substring(lastMessage.fullTranslatedText.length).trim();
    if (newTranslation) {
      synthesizeAndEnqueueSpeech(newTranslation);
    }
    lastMessage.fullTranslatedText += translation;
  }

  scrollToBottom();
};

const scrollToBottom = () => {
  nextTick(() => {
    scrollTop.value = 99999;
  });
};

// --- TTS 功能 ---
let autoPlayAudioContext = null; // 自动播放的音频上下文
let manualPlayAudioContext = null; // 手动播放的音频上下文

const stopAllAudio = (onlyAutoPlay = false) => {
  if (autoPlayAudioContext) {
    autoPlayAudioContext.destroy();
    autoPlayAudioContext = null;
  }
  if (!onlyAutoPlay && manualPlayAudioContext) {
    manualPlayAudioContext.destroy();
    manualPlayAudioContext = null;
  }
  isAudioPlaying.value = false;
  manuallyPlayingId.value = null;
  audioQueue.value = [];
};

/**
 * 处理音频队列播放
 */
const processAudioQueue = () => {
  // 自动播放已禁用，跳过播放
  if (!isAutoPlayEnabled.value) {
    return;
  }

  // 已有音频在播放，跳过
  if (isAudioPlaying.value) {
    return;
  }

  // 音频队列为空，跳过播放
  if (audioQueue.value.length === 0) {
    return;
  }

  // 音频文件路径为空，跳过播放
  const audioFilePath = audioQueue.value.shift();
  if (!audioFilePath) {
    return;
  }

  // 开始播放音频
  isAudioPlaying.value = true;
  autoPlayAudioContext = uni.createInnerAudioContext();
  autoPlayAudioContext.src = audioFilePath;

  // 根据静音状态设置音量
  autoPlayAudioContext.volume = isSoundEnabled.value ? 1.0 : 0.0;

  autoPlayAudioContext.onEnded(() => {
    isAudioPlaying.value = false;
    if (autoPlayAudioContext) {
      autoPlayAudioContext.destroy();
      autoPlayAudioContext = null;
    }
    processAudioQueue();
  });

  autoPlayAudioContext.onError((res) => {
    console.error('自动播放错误:', audioFilePath, res.errMsg);
    isAudioPlaying.value = false;
    if (autoPlayAudioContext) {
      autoPlayAudioContext.destroy();
      autoPlayAudioContext = null;
    }
    processAudioQueue();
  });

  autoPlayAudioContext.play();
};

const synthesizeAndStoreSpeech = async (text, messageId) => {
  if (!text || !messageId) return null;

  try {
    const ttsConfig = getTtsConfig(text, TtsTypeEnum.ALIYUN);
    const params = {
      text,
      speaker: ttsConfig.speaker,
      speechRate: ttsConfig.speechRate,
      pitchRate: ttsConfig.pitchRate,
      displayCaptions: false,
    };

    console.log('开始TTS合成:', text);
    const res = await NlsApi.ttsAliyun(params, { showLoading: false });

    // 检查网络请求是否失败（返回false）
    if (res === false) {
      console.error('TTS 请求失败: 网络请求错误，可能是登录令牌问题');
      // 延迟重试一次
      await new Promise(resolve => setTimeout(resolve, 1000));
      const retryRes = await NlsApi.ttsAliyun(params, { showLoading: false });
      if (retryRes === false || retryRes?.msg) {
        console.error('TTS 重试失败:', retryRes?.msg || '网络错误');
        return null;
      }
      // 使用重试结果
      const manager = uni.getFileSystemManager();
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;
      await new Promise((resolve, reject) => {
        manager.writeFile({
          filePath: tempFilePath,
          data: retryRes,
          encoding: 'binary',
          success: resolve,
          fail: reject,
        });
      });

      const message = messages.value.find(m => m.id === messageId);
      if (message) {
        message.audioSrc = tempFilePath;
        return tempFilePath;
      }
      return null;
    }

    // 检查API返回的错误信息
    if (res?.msg) {
      console.error('TTS 请求失败:', res.msg);
      return null;
    }

    const manager = uni.getFileSystemManager();
    const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;
    await new Promise((resolve, reject) => {
      manager.writeFile({
        filePath: tempFilePath,
        data: res,
        encoding: 'binary',
        success: resolve,
        fail: reject,
      });
    });

    const message = messages.value.find(m => m.id === messageId);
    if (message) {
      message.audioSrc = tempFilePath;
      return tempFilePath;
    }
  } catch (error) {
    console.error('TTS 合成或文件保存失败:', error);
  }
  return null;
};

const synthesizeAndEnqueueSpeech = async (text) => {
  const lastMessage = messages.value[messages.value.length - 1];
  if (!lastMessage) return;

  const audioSrc = await synthesizeAndStoreSpeech(text, lastMessage.id);
  if (audioSrc && isAutoPlayEnabled.value) {
    audioQueue.value.push(audioSrc);
  }
};

// 监听音频队列变化
watch(audioQueue, () => {
  processAudioQueue();
}, { deep: true });

// 同步UI语言选择与实际翻译设置
const syncUILanguageSelection = () => {
  // 清除所有选择
  sourceLanguageOptions.value.forEach(lang => lang.selected = false)
  targetLanguageOptions.value.forEach(lang => lang.selected = false)

  // 根据实际的翻译设置更新UI选择
  const fromLang = languageOptions.value[fromLanguageIndex.value]
  const toLang = languageOptions.value[toLanguageIndex.value]

  if (fromLang) {
    const sourceOption = sourceLanguageOptions.value.find(lang => lang.value === fromLang.value)
    if (sourceOption) {
      sourceOption.selected = true
    }
  }

  if (toLang) {
    const targetOption = targetLanguageOptions.value.find(lang => lang.value === toLang.value)
    if (targetOption) {
      targetOption.selected = true
    }
  }
};

// --- 语言选择 ---
const onFromLanguageChange = (e) => {
  fromLanguageIndex.value = e.detail.value;
  uni.setStorageSync(STORAGE_KEY_FROM_LANG, fromLanguageIndex.value);
};

const onToLanguageChange = (e) => {
  toLanguageIndex.value = e.detail.value;
  uni.setStorageSync(STORAGE_KEY_TO_LANG, toLanguageIndex.value);
};

const swapLanguages = () => {
  const temp = fromLanguageIndex.value;
  fromLanguageIndex.value = toLanguageIndex.value;
  toLanguageIndex.value = temp;
  uni.setStorageSync(STORAGE_KEY_FROM_LANG, fromLanguageIndex.value);
  uni.setStorageSync(STORAGE_KEY_TO_LANG, toLanguageIndex.value);
};

const onAutoPlayChange = (e) => {
  isAutoPlayEnabled.value = e.detail.value;
  uni.setStorageSync(STORAGE_KEY_AUTO_PLAY, isAutoPlayEnabled.value);
  if (!isAutoPlayEnabled.value) {
    // 如果关闭自动播放，则停止当前所有自动播放
    stopAllAudio(true);
  }
};

// 开始说话 - 替换为录音功能
const startSpeak = () => {
  toggleRecording();
}

// 设置显示模式
const setDisplayMode = (mode) => {
  displayMode.value = mode
}

// 设置单条消息的显示模式
const setMessageDisplayMode = (message, mode) => {
  message.displayMode = mode
}

// 打开语言选择弹窗
const openLanguagePopup = () => {
  showLanguagePopup.value = true
}

// 关闭语言选择弹窗
const closeLanguagePopup = () => {
  showLanguagePopup.value = false
}

// 选择语言
const selectLanguage = (language) => {
  if (currentLanguageType.value === 'source') {
    // 选择原文语言
    sourceLanguageOptions.value.forEach(lang => {
      lang.selected = false
    })
    language.selected = true

    // 同步更新实际的翻译语言设置
    const langIndex = languageOptions.value.findIndex(lang => lang.value === language.value)
    if (langIndex !== -1) {
      fromLanguageIndex.value = langIndex
      uni.setStorageSync(STORAGE_KEY_FROM_LANG, fromLanguageIndex.value)
    }
  } else {
    // 选择目标语言
    targetLanguageOptions.value.forEach(lang => {
      lang.selected = false
    })
    language.selected = true

    // 同步更新实际的翻译语言设置
    const langIndex = languageOptions.value.findIndex(lang => lang.value === language.value)
    if (langIndex !== -1) {
      toLanguageIndex.value = langIndex
      uni.setStorageSync(STORAGE_KEY_TO_LANG, toLanguageIndex.value)
    }
  }

  // 关闭弹窗
  closeLanguagePopup()
}

// 切换静音状态
const toggleSound = () => {
  isSoundEnabled.value = !isSoundEnabled.value

  // 立即应用静音状态到当前播放的音频
  if (autoPlayAudioContext) {
    autoPlayAudioContext.volume = isSoundEnabled.value ? 1.0 : 0.0;
  }

  if (manualPlayAudioContext) {
    manualPlayAudioContext.volume = isSoundEnabled.value ? 1.0 : 0.0;
  }

  // 保存静音状态到本地存储
  uni.setStorageSync('isSoundEnabled', isSoundEnabled.value);

  console.log('切换静音状态:', isSoundEnabled.value)
}

// 展开原文语言选择
const expandSourceLanguages = () => {
  currentLanguageType.value = 'source'
  openLanguagePopup()
}

// 展开目标语言选择
const expandTargetLanguages = () => {
  currentLanguageType.value = 'target'
  openLanguagePopup()
}

// 展开语言选择（保留兼容性）
const expandLanguages = () => {
  expandTargetLanguages()
}

// 翻转语言 - 同步UI和实际翻译设置
const flipLanguages = () => {
  // 交换实际的翻译语言设置
  swapLanguages();

  // 同步更新UI显示的语言选择
  const currentSourceSelected = selectedSourceLanguage.value
  const currentTargetSelected = selectedLanguage.value

  if (currentSourceSelected && currentTargetSelected) {
    // 清除当前选择
    sourceLanguageOptions.value.forEach(lang => lang.selected = false)
    targetLanguageOptions.value.forEach(lang => lang.selected = false)

    // 在目标语言列表中找到对应的原文语言
    const sourceInTarget = targetLanguageOptions.value.find(lang => lang.value === currentSourceSelected.value)
    // 在原文语言列表中找到对应的目标语言
    const targetInSource = sourceLanguageOptions.value.find(lang => lang.value === currentTargetSelected.value)

    if (sourceInTarget && targetInSource) {
      // 设置新的选择
      sourceInTarget.selected = true
      targetInSource.selected = true
    }
  }
}


</script>

<style scoped lang="scss">
.chat-page {
  width: 100%;
  min-height: 100vh;
  background-color: #F7FCFF;
  display: flex;
  flex-direction: column;
}

// 隐藏course-navbar的搜索框
:deep(.search-container) {
  display: none !important;
}

// 调整course-navbar右侧区域位置
:deep(.right) {
  right: 30rpx !important; // 调整右侧位置，因为搜索框被隐藏了
}

// 自定义导航栏右侧样式
.custom-right {
  display: flex;
  align-items: center;
  margin-right: 184rpx;
  gap: 30rpx;
}

.nav-icon {
  width: 35rpx;
  height: 34rpx;
}

// 聊天内容区域
.chat-content {
  flex: 1;
  // 为底部输入区域留出空间
  padding: 30rpx 30rpx 250rpx;
}

.time-display {
  text-align: center;
  margin-bottom: 44rpx;
  font-family: PingFang SC,serif;
  font-size: 21rpx;
  color: #868686;
}

.system-message {
  text-align: center;
  margin-bottom: 45rpx;
  line-height: 1.4;
  font-family: PingFang SC,serif;
  font-size: 21rpx;
  color: #868686;
}

.message-list {
  margin-top: 37rpx;
}

.message-item {
  margin-bottom: 40rpx;
  display: flex;
  width: 100%;
}

.my-message {
  justify-content: flex-end;

  .message-bubble {
    align-self: flex-end;
  }
}

.other-message {
  justify-content: flex-start;

  .message-wrapper {
    align-self: flex-start;
  }
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.message-bubble {
  max-width: 607rpx;
  min-width: 120rpx;
  width: fit-content;
  border-radius: 20rpx;
  padding: 30rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.my-bubble {
  background: #2DC1FF;

  // 右侧尖角
  &::after {
    content: '';
    position: absolute;
    right: -15rpx;
    top: 20rpx;
    width: 0;
    height: 0;
    border-left: 15rpx solid #2DC1FF;
    border-top: 10rpx solid transparent;
    border-bottom: 10rpx solid transparent;
  }
}

.other-bubble {
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0 rgba(211,223,230,0.52);

  // 左侧尖角
  &::after {
    content: '';
    position: absolute;
    left: -15rpx;
    top: 20rpx;
    width: 0;
    height: 0;
    border-right: 15rpx solid #FFFFFF;
    border-top: 10rpx solid transparent;
    border-bottom: 10rpx solid transparent;
  }
}

.message-text {
  line-height: 1.5;
  font-family: PingFang SC, -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 25rpx;
  display: inline;
  word-break: break-all;
}

.my-bubble .message-text {
  color: #FFFFFF;
}

.dark-text {
  color: #313030;
}

.light-text {
  color: #6C6B6B;
  font-family: 'Adobe Thai', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 25rpx;
  line-height: 1.5;
}

.light-text-white {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Adobe Thai', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 25rpx;
  line-height: 1.5;
}

.play-icon {
  width: 19rpx;
  height: 23rpx;
  display: inline-block;
  vertical-align: text-bottom;
  margin-left: 8rpx;
  margin-bottom: 6rpx;
}



.translation-text {
  margin-top: 10rpx;
}

.translation-options {
  width: 289rpx;
  height: 50rpx;
  background: #F8F8F8;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  margin-top: 20rpx;
  margin-left: 30rpx; // 与气泡左边缘对齐
  position: relative;
}

.my-translation-options {
  margin-left: auto;
  margin-right: 30rpx; // 与右侧气泡边缘对齐
}

.option-btn {
  width: 96rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFang SC,serif;
  font-size: 21rpx;
  color: #2DC1FF;
  background: transparent;
  border-radius: 25rpx;
  cursor: pointer;
  /* 屏蔽原生点击效果 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  outline: none;
  /* transition: background-color 0.2s ease, color 0.2s ease; */

  &.active {
    background: #2DC1FF;
    color: #FFFFFF;
  }
}

// 底部输入区域
.bottom-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  box-shadow: 1rpx 1rpx 12rpx 0 rgba(211,223,230,0.4);
  padding: 26rpx 29rpx 67rpx 57rpx;
  border-top: 1rpx solid #E5E5E5;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.button-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.sound-btn {
  width: 36rpx;
  height: 36rpx;
  flex-shrink: 0;
}

.speak-button {
  flex: 1;
  width: 593rpx;
  height: 82rpx;
  background: linear-gradient(270deg, rgba(70,173,240,0.89), rgba(0,222,255,0.89));
  border-radius: 41rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }
}

.speak-text {
  font-family: PingFang SC, -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 29rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.language-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666666;
  font-size: 24rpx;
  width: 100%;
  max-width: 400rpx;
  margin: 0 auto;
}

.language-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  min-width: 0; /* 允许文本截断 */
}

.language-item:first-child {
  justify-content: flex-end;
  text-align: right;
}

.language-item:last-child {
  justify-content: flex-start;
  text-align: left;
}

.language-text {
  color: #666666;
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120rpx; /* 限制最大宽度 */
}

.expand-btn {
  width: 13rpx;
  height: 7rpx;
}

.flip-btn {
  width: 20rpx;
  height: 17rpx;
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin: 0 20rpx; /* 左右固定间距 */
}


.speak-text {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.language-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.switch-text {
  font-size: 28rpx;
  color: #666666;
}

.switch-arrow,
.switch-dropdown {
  width: 24rpx;
  height: 24rpx;
}

// 语言选择弹窗样式
.language-popup {
  height: 1453rpx;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
}

// 区域1：搜索框区域
.search-area {
  padding:  55rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  padding: 20rpx 30rpx;
  gap: 20rpx;
  width: 581rpx;
  height: 35rpx;
  border-radius: 38rpx;
  border: 2px solid #A9A9AF;
  opacity: 0.8;
  font-family: PingFang SC,serif;
  font-size: 25rpx;
  color: #B0B0B6;
}

.search-icon {
  width: 28rpx;
  height: 29rpx;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
}

// 区域2和3：语言内容区域
.language-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 55rpx 55rpx 55rpx;
}

.language-headers {
  display: flex;
  margin-bottom: 40rpx;
  gap: 30rpx; // 与content-area保持一致的间距
}

.left-header {
  flex: 1;
}

.right-header {
  flex: 1;
}

.column-title {
  margin-bottom: 20rpx;
  font-family: PingFang SC,serif;
  font-weight: bold;
  font-size: 29rpx;
  color: #3F3F3F;
}

.column-subtitle {
  font-family: PingFang SC,serif;
  font-size: 25rpx;
  color: #868686;
}

.content-area {
  flex: 1;
  display: flex;
  gap: 30rpx;
  min-height: 800rpx; // 简化高度设置
}

// 区域2：对方语言区域（左侧）
.left-content {
  flex: 1;
  background: transparent;
}

// 区域3：我的语言区域（右侧）
.right-content {
  flex: 1;
  padding-left: 0; // 确保与右侧标题对齐
}

.language-scroll {
  height: 1100rpx; // 固定高度，确保可以滚动
}

.language-section {
  margin-bottom: 60rpx;
}

.section-title {
  width: 135rpx;
  height: 50rpx;
  background: #F5F5F5;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  font-family: PingFang SC,serif;
  font-size: 21rpx;
  color: #868686;
}

.language-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding: 0; // 移除左右边距，与顶部对齐
}

.language-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0; // 调整上下间距
  cursor: pointer;
  min-height: 50rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.language-name {
  flex: 1;
  text-align: left; // 确保文本左对齐
  font-family: PingFang SC,serif;
  font-size: 25rpx;
  color: #393939;

  &.active {
    color: #2DC1FF;
  }
}

.chosen-icon {
  width: 24rpx;
  height: 18rpx;
  margin-left: 20rpx;
}

// 二维码弹窗遮罩层
.qrcode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 200rpx 94rpx; // 从导航栏下方开始，右侧留间隙
}

// 二维码弹窗样式
.qrcode-popup {
  padding: 60rpx 40rpx 50rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 447rpx;
  height: 540rpx;
  background: #FFFFFF;
  border-radius: 43rpx;

  // 添加右上角小三角形，指向二维码按钮
  &::before {
    content: '';
    position: absolute;
    top: -24rpx;
    right: 170rpx; // 指向右上角的二维码按钮位置
    width: 0;
    height: 0;
    border-left: 24rpx solid transparent;
    border-right: 24rpx solid transparent;
    border-bottom: 24rpx solid #FFFFFF;
  }
}

.qrcode-title {
  margin-bottom: 50rpx;
  text-align: center;
  font-family: PingFang SC,serif;
  font-weight: bold;
  font-size: 38rpx;
  color: #2F2F2F;
}

.qrcode-container {
  width: 480rpx;
  height: 480rpx;
  margin-bottom: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 16rpx;
}

.qrcode-image {
  width: 326rpx;
  height: 339rpx;
}

.dialog-number-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  justify-content: center;
}

.dialog-number-text {
  font-family: PingFang SC,serif;
  font-size: 26rpx;
  color: #999999;
}

.copy-icon {
  width: 28rpx;
  height: 28rpx;
  cursor: pointer;
  opacity: 0.8;

  &:active {
    opacity: 0.6;
  }
}
</style>
