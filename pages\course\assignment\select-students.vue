<template>
  <view class="select-students-container">
    <!-- 顶部导航栏 -->
    <easy-navbar :title="className" left-icon="back"></easy-navbar>
    
    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input">
        <image class="search-icon" :src="searchIcon" mode="aspectFit"></image>
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索" 
          placeholder-class="placeholder"
          @confirm="handleSearch"
          @blur="handleSearch"
        />
      </view>
    </view>
    
    <!-- 学生列表 -->
    <view class="student-list">
      <view 
        class="student-item" 
        v-for="(student, index) in studentList" 
        :key="index"
        @click="toggleSelect(student)"
      >
        <view class="checkbox" :class="{ active: student.selected }">
          <view v-if="student.selected" class="check-icon"></view>
        </view>
        <view class="student-avatar">
          <image :src="student.avatar" mode="aspectFill"></image>
        </view>
        <view class="student-info">
          <view class="student-name">{{ student.nickname }}</view>
          <view class="student-phone">{{ student.phone || student.mobile || '' }}</view>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮区域 -->
    <view class="bottom-bar">
      <view class="select-all" @click="selectAll">
        <view class="checkbox" :class="{ active: isAllSelected }">
          <view v-if="isAllSelected" class="check-icon"></view>
        </view>
        <text>全选</text>
      </view>
      <view class="confirm-btn" @click="confirmSelection">确定</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import sheep from '@/sheep';
import GroupApi from '@/sheep/api/group';

// 搜索图标
const searchIcon = sheep.$url.cdn('/index/search-icon.png');

// 班级ID
const classId = ref('');

// 班级名称
const className = ref('默认班级');

// 搜索关键词
const searchKeyword = ref('');

// 学生列表
const studentList = ref([]);

// 原始学生列表（用于搜索过滤）
const originalStudentList = ref([]);

// 是否全选
const isAllSelected = computed(() => {
  if (studentList.value.length === 0) return false;
  return studentList.value.every(item => item.selected);
});

// 切换选中状态
const toggleSelect = (student) => {
  student.selected = !student.selected;
};

// 全选/取消全选
const selectAll = () => {
  const newStatus = !isAllSelected.value;
  studentList.value.forEach(student => {
    student.selected = newStatus;
  });
};

// 确认选择
const confirmSelection = () => {
  const selectedStudents = studentList.value.filter(student => student.selected);
  
  if (selectedStudents.length === 0) {
    sheep.$helper.toast('请至少选择一名学生');
    return;
  }
  
  // 收集选中学生的ID
  const selectedStudentIds = selectedStudents.map(student => Number(student.id));
  
  // 传递数据回上一页
  // @ts-ignore 忽略uni未定义的错误
  uni.$emit('studentSelected', {
    classId: classId.value,
    selectedCount: selectedStudents.length,
    totalCount: studentList.value.length,
    selectedStudentIds: selectedStudentIds
  });
  
  sheep.$router.back();
};

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value) {
    // 如果搜索关键词为空，恢复原始列表
    studentList.value = [...originalStudentList.value];
    return;
  }
  
  // 根据关键词过滤学生列表
  const keyword = searchKeyword.value.toLowerCase();
  studentList.value = originalStudentList.value.filter(student => 
    student.name.toLowerCase().includes(keyword) || 
    (student.mobile && student.mobile.includes(keyword)) ||
    (student.nickname && student.nickname.toLowerCase().includes(keyword))
  );
};

// 获取URL参数
const getParams = () => {
  // @ts-ignore
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.options) {
    classId.value = currentPage.options.classId || '';
    
    // 获取已选中的学生ID列表
    const selectedIdsStr = currentPage.options.selectedIds || '';
    let selectedStudentIds = [];
    
    if (selectedIdsStr) {
      selectedStudentIds = JSON.parse(decodeURIComponent(selectedIdsStr));
    }
    
    // 如果有班级ID，获取班级详情和学生列表
    if (classId.value) {
      getClassDetails(selectedStudentIds);
    }
  }
};

// 获取班级详情和学生列表
const getClassDetails = async (selectedStudentIds = []) => {
  // 获取班级详情
  const res = await GroupApi.getMyGroupsWithUsers() as any;

  if (res.code === 0 && res.data) {
    // 找到当前班级
    const currentClass = res.data.find(item => item.id == classId.value);

    if (currentClass) {
      // 设置班级名称
      className.value = currentClass.name || '未命名班级';

      // 转换学生数据格式
      if (currentClass.users && currentClass.users.length > 0) {
        const students = currentClass.users.map(user => ({
          id: user.id || '',
          name: user.name || user.nickname || '未命名',
          phone: user.mobile || '',
          avatar: user.avatar || sheep.$url.cdn('/common/avatar.png'),
          // 根据传入的已选中学生ID列表设置选中状态
          selected: selectedStudentIds.includes(Number(user.id)),
          // 保存原始数据，以便后续使用
          ...user
        }));

        studentList.value = students;
        originalStudentList.value = [...students];
      }
    }
  }
};

onMounted(() => {
  // 获取URL参数
  getParams();
});
</script>

<style lang="scss" scoped>
.select-students-container {
  min-height: 100vh;
  background-color: #fff;
  padding-bottom: 120rpx;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 10rpx 30rpx 23rpx 30rpx;
  background-color: #fff;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border-radius: 40rpx;
    padding: 15rpx 20rpx;
    margin-right: 20rpx;
    position: relative;

    .search-icon {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      left: 50%;
      transform: translateX(calc(-50% - 50rpx));
    }

    input {
      flex: 1;
      font-size: 28rpx;
      text-align: center;
      padding-left: 32rpx;
      
      &:focus {
        text-align: left;
        & + .search-icon {
          left: 20rpx;
          transform: none;
        }
      }
    }

    .placeholder {
      color: #999;
    }
  }

  .search-btn {
    font-size: 28rpx;
    color: #333;
  }
}

.student-list {
  padding: 0 30rpx;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #EEEEEE;
  
  &:last-child {
    border-bottom: none;
  }
  
  .checkbox {
    width: 44rpx;
    height: 44rpx;
    border-radius: 50%;
    border: 1px solid #DDDDDD;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    
    &.active {
      border-color: #2FBDF5;
      background-color: #2FBDF5;
    }
    
    .check-icon {
      width: 16rpx;
      height: 10rpx;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transform: rotate(-45deg);
    }
  }
  
  .student-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
    overflow: hidden;
    margin-right: 20rpx;
    background: #39BEF4;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .student-info {
    flex: 1;
    
    .student-name {
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
      margin-bottom: 5rpx;
    }
    
    .student-phone {
      font-size: 26rpx;
      color: #999999;
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 62rpx;
  
  .select-all {
    display: flex;
    align-items: center;
    
    .checkbox {
      width: 35rpx;
      height: 35rpx;
      border-radius: 50%;
      border: 1px solid #DDDDDD;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15rpx;
      
      &.active {
        border-color: #3DB3F2;
        background-color: #3DB3F2;
      }
      
      .check-icon {
        width: 16rpx;
        height: 10rpx;
        border-left: 2px solid #fff;
        border-bottom: 2px solid #fff;
        transform: rotate(-45deg);
      }
    }
    
    text {
      font-size: 28rpx;
      color: #333333;
    }
  }
  
  .confirm-btn {
    flex: 1;
    height: 70rpx;
    background: linear-gradient(90deg, #33C9FF, #3098F9);
    border-radius: 35rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 30rpx;
    margin-left: 30rpx;
  }
}
</style> 