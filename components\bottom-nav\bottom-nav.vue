<template>
	<view>
		<view class="holder-height">

		</view>
		

		
		<view class="bottom-nav">
			<image
			  class="tabbar-top-bg"
			  mode="widthFix"
			  :src="sheep.$url.static('/uniapp/tabbar/tabbar-top-bg.png')"
			/>
			
			
			<view class="nav-item home-nav" :class="{'show-active': activeTab === 0 }" @click="switchTab(0)">
				<view class="icon">
					<image class="normal" src="/static/icon/index.png" mode="heightFix" />
					<image class="active" src="/static/icon/index-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					首页
				</view>

			</view>
			
			<view class="nav-item community-nav" :class="{ 'show-active': activeTab === 1 }" @click="switchTab(1)">
				<view class="icon">
					<image class="normal" src="/static/icon/community.png" mode="heightFix" />
					<image class="active" src="/static/icon/community-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					社区
				</view>

			</view>
			
			
			<view class="nav-item tool-item tool-nav" :class="{ 'show-active': activeTab === 2 }" @click="switchTab(2)">
				<view class="tool-circle-container">
					<view class="tool-circle">
						<image class="tool-icon" src="/static/icon/tool.png" mode="heightFix" />
					</view>
				</view>
				<view class="nav-title tool-title">
					AI工具
				</view>
			</view>
			
			
			<view class="nav-item phonetic-nav" :class="{ 'show-active': activeTab === 3 }" @click="switchTab(3)">

				<view class="icon">
					<image class="normal" src="/static/icon/phonetic.png" mode="heightFix" />
					<image class="active" src="/static/icon/phonetic-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					音标
				</view>

			</view>
			
			
			
			<view class="nav-item my-nav" :class="{'show-active': activeTab === 4 }" @click="switchTab(4)">
				<view class="icon">
					<image class="normal" src="/static/icon/my.png" mode="heightFix" />
					<image class="active" src="/static/icon/my-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					我的
				</view>
			</view>
		</view>
	</view>

</template>

<script setup>
	import {
		ref
	} from 'vue'
	import { onShow,onHide } from '@dcloudio/uni-app';
	import sheep from '@/sheep';

	const props = defineProps({
		currentTab: {
			type: Number,
			default: 0
		}
	})

	const currentTab = ref(props.currentTab)
	const activeTab = ref(-1)
	
	

	const tabPaths = [
		'/pages/index/index',
		'/pages/community/index',
		'/pages/tool/index',
		'/pages/phonetic/index',
		'/pages/my/index'
	]
	
	//首次加载时，由组件激活
	const switchTab = (index) => {
		//不要使用 sheep.$router.go 中的switch 性能太差 有bug 连续点击tabbar按钮会无法跳转
		uni.switchTab({
			url: tabPaths[index]
		})
	}
	
	// 首次切换到组件时，会因为动画结束且没有界面显示，而没有看到icon动画
	onShow(()=>{
		activeTab.value = currentTab.value;
	})
	
	// 将其-1，否则动画显示逻辑错误 
	onHide(()=>{
		activeTab.value = -1;
	})
	

</script>

<style scoped>
	.holder-height {
		height: 160rpx;
	}
	
	.bottom-nav .tabbar-top-bg{
		position: absolute;
		width: 100vw;
		top: -60rpx;
	}

	.bottom-nav {
		position: fixed;
		bottom: 0;
		width: 100%;
		background-color: rgba(255, 255, 255, 0.98);
		display: flex;
		justify-content: space-around;
		padding: 24rpx 0 34rpx;
		backdrop-filter: blur(10px);
		z-index: 999;
	}

	.bottom-nav .nav-item {
		text-align: center;
		position: relative;
		width: 100rpx;
		height: 100rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		transition: all 0.3s ease;
		padding: 0;
	}
	
	.bottom-nav .nav-item:active {
		transform: scale(0.9);
	}

	.bottom-nav .nav-item .icon {
		position: relative;
		height: 48rpx;
		margin-bottom: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.bottom-nav .nav-item image {
		height: 48rpx;
		position: absolute;
		width: 48rpx;
		transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
	}
	
	
	.bottom-nav .nav-item:nth-child(1) image {
	}
	
	.nav-item .normal {
		opacity: 1;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-item .active {
		opacity: 0;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-item.show-active .normal {
		opacity: 0;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-item.show-active .active {
		opacity: 1;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-title {
		font-size: 24rpx;
		color: #666;
		line-height: 24rpx;
		height: 24rpx;
		margin-top: 2rpx;
		text-align: center;
		width: 100%;
		transition: all 0.3s ease;
		bottom: 10rpx;
	}
	

	/* 各导航项激活时的文本颜色 */
	.home-nav.show-active .nav-title {
		color: #106ee5;
	}
	
	.community-nav.show-active .nav-title {
		color: #ff6743;
	}
	
	.tool-nav.show-active .nav-title {
		color: #5394ff;
	}
	
	.phonetic-nav.show-active .nav-title {
		color: #8b6edf;
	}
	
	.my-nav.show-active .nav-title {
		color: #efaa4a;
	}
	
	/* 工具按钮特殊样式 */
	.tool-item {
		position: relative;
		height: 120rpx; /* 增加高度以容纳突出的圆形 */
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
	}
	
	.tool-circle-container {
		position: absolute;
		top: -50rpx;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 110rpx;
	}
	

	
	.tool-item .tool-circle {
		position: relative;
		width: 110rpx;
		height: 110rpx;
		background: linear-gradient(142deg, rgba(67,240,255,0.89), rgba(60,92,255,0.89));
		box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(39,115,255,0.61);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}
	
	.tool-item .tool-circle .tool-icon{
		width: 80rpx;
		height: 80rpx;
		/* 不需要绝对定位，因为父元素tool-circle已经有flex居中 */
		transform: none;
		left: auto;
		top: auto;
	}
	
	.tool-title {
		position: absolute;
		bottom: 10rpx; /* 调整底部位置，使其与其他按钮文本在同一水平线上 */
		left: 50%;
		transform: translateX(-50%);
		width: 100%;
	}
	
</style>