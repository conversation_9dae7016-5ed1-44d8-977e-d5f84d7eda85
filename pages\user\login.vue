<template>
  <!-- 全屏登录页面 -->
  <view class="login-page">
    <easy-navbar :hasTitle="false" :transparent="true" />
    
    <!-- 引入授权模态框组件 -->
    <s-auth-modal />

    <view class="login-container">
      <!-- 验证码登录 -->
      <view v-if="loginMode === 'phone'">
        <!-- 标题 -->
        <view class="login-title-wrapper">
          <view class="login-title">验证码登录</view>
        </view>

        <!-- 表单区域 -->
        <view class="login-form">
          <!-- 手机号输入框 -->
          <view class="input-group">
            <input
              type="number"
              class="phone-input"
              placeholder="请输入您的手机号"
              placeholderClass="input-placeholder"
              v-model="form.mobile"
            />
            <view class="input-line"/>
          </view>

          <!-- 验证码输入框 -->
          <view class="input-group">
            <view class="code-input-container">
              <input
                type="number"
                class="code-input"
                placeholder="请输入验证码"
                placeholderClass="input-placeholder"
                v-model="form.code"
                maxlength="4"
              />
              <view class="get-code-btn" :class="{'disabled': cooldown > 0}" @tap="getCode">
                {{ cooldown > 0 ? `${cooldown}秒后重试` : '获取验证码' }}
              </view>
            </view>
            <view class="input-line"/>
          </view>

          <!-- 登录按钮 -->
          <button class="login-button" @tap="loginSubmit">登录</button>

          <!-- 协议区域 -->
          <view class="agreement-area" :class="{ shake: currentProtocol }">
            <view class="radio-wrapper" @tap="onChange">
              <view class="radio-outer" :class="{'checked': state.protocol}">
                <view class="radio-inner" v-if="state.protocol"></view>
              </view>
              <text class="agreement-text">
                我已同意并阅读 <text class="agreement-link" @tap.stop="onProtocol('用户协议')">《用户协议》</text> <text class="agreement-link" @tap.stop="onProtocol('隐私协议')">《隐私政策》</text>
              </text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 账号登录 -->
      <view v-else>
        <!-- 标题 -->
        <view class="login-title-wrapper">
          <view class="login-title">密码登录</view>
        </view>

        <!-- 表单区域 -->
        <view class="login-form">
          <!-- 账号输入框 -->
          <view class="input-group">
            <input
              class="phone-input"
              placeholder="请输入您的手机号"
              placeholderClass="input-placeholder"
              v-model="accountForm.mobile"
            />
            <view class="input-line"/>
          </view>

          <!-- 密码输入框 -->
          <view class="input-group">
            <view class="password-input-container">
              <input
                type="password"
                class="password-input"
                placeholder="请输入密码"
                placeholderClass="input-placeholder"
                v-model="accountForm.password"
              />
              <view class="forgot-password" @tap="onForgotPassword">
                忘记密码?
              </view>
            </view>
            <view class="input-line"/>
          </view>

          <!-- 登录按钮 -->
          <button class="login-button" @tap="accountLoginSubmit">登录</button>

          <!-- 协议区域 -->
          <view class="agreement-area" :class="{ shake: currentProtocol }">
            <view class="radio-wrapper" @tap="onChange">
              <view class="radio-outer" :class="{'checked': state.protocol}">
                <view class="radio-inner" v-if="state.protocol"></view>
              </view>
              <text class="agreement-text">
                我已同意并阅读 <text class="agreement-link" @tap.stop="onProtocol('用户协议')">《用户协议》</text> <text class="agreement-link" @tap.stop="onProtocol('隐私协议')">《隐私政策》</text>
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 登录方式切换 -->
      <view class="login-mode-switch">
        <view class="divider-text">其他方式登录</view>
        <view class="login-modes">
          <!-- 微信登录 -->
          <view class="login-mode-item" @tap="thirdLogin('wechat')">
            <image class="login-mode-icon" :src="sheep.$url.cdn('/common/wechat.png')" mode="aspectFit" />
            <text class="login-mode-text">微信登录</text>
          </view>
          
          <!-- 手机号登录按钮 -->
          <view v-if="loginMode === 'account'" class="login-mode-item" @tap="switchLoginMode('phone')">
            <image class="login-mode-icon" :src="sheep.$url.cdn('/user/phone_login.png')" mode="aspectFit" />
            <text class="login-mode-text">验证码登录</text>
          </view>
          
          <!-- 账号登录按钮 -->
          <view v-if="loginMode === 'phone'" class="login-mode-item" @tap="switchLoginMode('account')">
            <image class="login-mode-icon" :src="sheep.$url.cdn('/user/member_login.png')" mode="aspectFit" />
            <text class="login-mode-text">密码登录</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import sheep from '@/sheep';
  import { closeAuthModal, showAuthModal } from '@/sheep/hooks/useModal';
  import AuthUtil from '@/sheep/api/member/auth';

  const loginMode = ref('phone'); // 'phone'手机号登录 或 'account'账号登录

  const state = reactive({
    protocol: false,
  });

  const currentProtocol = ref(false);
  const cooldown = ref(0);

  // 手机号登录表单数据
  const form = reactive({
    mobile: '',
    code: ''
  });
  
  // 账号登录表单数据
  const accountForm = reactive({
    mobile: '', // 账号
    password: '' // 密码
  });

  // 切换登录模式
  function switchLoginMode(mode) {
    loginMode.value = mode;
  }

  // 勾选协议
  function onChange() {
    state.protocol = !state.protocol;
  }

  // 获取验证码
  async function getCode() {
    if (cooldown.value > 0) return;

    if (!form.mobile || form.mobile.length !== 11) {
      sheep.$helper.toast('请输入正确的手机号');
      return;
    }

    const { code } = await AuthUtil.sendSmsCode(form.mobile, 1);
    if (code === 0) {
      cooldown.value = 60;
      const timer = setInterval(() => {
        cooldown.value--;
        if (cooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    }
  }

  // 忘记密码
  function onForgotPassword() {
    // 直接设置modal store的状态，打开忘记密码模态框
    sheep.$store('modal').$patch((state) => {
      state.auth = 'resetPassword';
    });
  }

  // 显示初始密码弹窗
  function showInitialPasswordModal(initialPassword) {
    return new Promise((resolve) => {
      uni.showModal({
        title: '初始密码',
        content: `您的初始密码为：${initialPassword}，请妥善保管`,
        confirmText: '确认',
        cancelText: '重置密码',
        success: function (res) {
          if (res.cancel) {
            // 点击了重置密码按钮
            // 先关闭当前模态框
            uni.hideLoading();
            
            // 使用setTimeout确保当前模态框完全关闭
            setTimeout(() => {
              // 创建一个独立的修改密码处理函数
              const handlePasswordChange = () => {
                // 在本地存储中设置一个标记，表示需要打开修改密码模态框
                uni.setStorageSync('open_change_password', true);
                
                // 直接跳转到用户信息页面
                sheep.$router.go('/pages/user/info', {}, { redirect: true });
              };
              
              // 执行密码修改处理
              handlePasswordChange();
            }, 300);
            
            // 返回'reset'表示用户选择了重置密码
            resolve('reset');
          } else {
            // 点击确认按钮，返回'confirm'
            resolve('confirm');
          }
        },
        // 用户点击右上角关闭按钮或遮罩层时
        fail: function() {
          resolve('confirm'); // 默认为确认
        }
      });
    });
  }

  // 手机号登录提交
  async function loginSubmit() {
    if (!form.mobile || form.mobile.length !== 11) {
      sheep.$helper.toast('请输入正确的手机号');
      return;
    }

    if (!form.code) {
      sheep.$helper.toast('请输入验证码');
      return;
    }

    if (!state.protocol) {
      currentProtocol.value = true;
      setTimeout(() => {
        currentProtocol.value = false;
      }, 1000);
      sheep.$helper.toast('请勾选同意');
      return;
    }

    // 提交数据
    const { code, data } = await AuthUtil.smsLogin({
      mobile: form.mobile,
      code: form.code
    });

    if (code === 0) {
      // 检查是否有初始密码
      if (data && data.initialPassword) {
        const action = await showInitialPasswordModal(data.initialPassword);
        if (action === 'reset') {
          // 如果用户选择重置密码，不执行返回操作
          return;
        }
      }
      // 其他情况，直接返回上一页
      sheep.$router.back();
    }
  }
  
  // 账号登录提交
  async function accountLoginSubmit() {
    if (!accountForm.mobile) {
      sheep.$helper.toast('请输入账号');
      return;
    }

    if (!accountForm.password) {
      sheep.$helper.toast('请输入密码');
      return;
    }

    if (!state.protocol) {
      currentProtocol.value = true;
      setTimeout(() => {
        currentProtocol.value = false;
      }, 1000);
      sheep.$helper.toast('请勾选同意');
      return;
    }

    // 提交数据
    const { code, data } = await AuthUtil.login(accountForm);
    if (code === 0) {
      // 检查是否有初始密码
      if (data && data.initialPassword) {
        const action = await showInitialPasswordModal(data.initialPassword);
        if (action === 'reset') {
          // 如果用户选择重置密码，不执行返回操作
          return;
        }
      }
      // 其他情况，直接返回上一页
      sheep.$router.back();
    }
  }

  // 第三方授权登陆（微信）
  const thirdLogin = async (provider) => {
    if (!state.protocol) {
      currentProtocol.value = true;
      setTimeout(() => {
        currentProtocol.value = false;
      }, 1000);
      sheep.$helper.toast('请勾选同意');
      return;
    }
    const loginRes = await sheep.$platform.useProvider(provider).login();
    if (loginRes) {
      const userInfo = await sheep.$store('user').getInfo();
      
      // 检查是否有初始密码
      if (loginRes.data && loginRes.data.initialPassword) {
        const action = await showInitialPasswordModal(loginRes.data.initialPassword);
        if (action === 'reset') {
          // 如果用户选择重置密码，不执行返回操作
          return;
        }
      }
      
      // 其他情况，直接返回上一页
      sheep.$router.back();

      // 如果用户已经有头像和昵称，不需要再次授权
      if (userInfo.avatar && userInfo.nickname) {
        return;
      }

      // 触发小程序授权信息弹框
      // #ifdef MP-WEIXIN
      showAuthModal('mpAuthorization');
      // #endif
    }
  };

  // 查看协议
  function onProtocol(title) {
    closeAuthModal();
    sheep.$router.go('/pages/public/richtext', {
      title,
    });
  }
</script>

<style lang="scss" scoped>
  .login-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    z-index: 999;
  }

  .login-container {
    width: 100%;
    height: 100%;
    padding: 0 60rpx;
    box-sizing: border-box;
    position: relative;
  }

  .login-title-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 90rpx;
    margin-top: 80rpx;
    margin-bottom: 100rpx;
  }

  .back-icon {
    position: absolute;
    left: 0;
    width: 36rpx;
    height: 36rpx;
    z-index: 10;
  }

  .login-title {
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
  }

  .login-form {
    width: 100%;
  }

  .input-group {
    margin-bottom: 60rpx;
  }

  .input-placeholder {
    color: #999;
    font-size: 30rpx;
  }

  .phone-input, .code-input, .password-input {
    height: 80rpx;
    font-size: 30rpx;
    width: 100%;
  }

  .input-line {
    height: 1px;
    background-color: #eee;
    width: 100%;
  }

  .code-input-container, .password-input-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .code-input, .password-input {
    flex: 1;
  }

  .get-code-btn {
    color: #27BEFF;
    font-size: 28rpx;
    width: 180rpx;
    text-align: right;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .get-code-btn.disabled {
    color: #999;
  }

  .forgot-password {
    color: #27BEFF;
    font-size: 28rpx;
    width: 180rpx;
    text-align: right;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .login-button {
    width: 100%;
    height: 90rpx;
    background: linear-gradient(270deg, rgba(70,173,240,0.89), rgba(0,222,255,0.89));
    color: #ffffff;
    border-radius: 45rpx;
    margin-top: 126rpx;
    margin-bottom: 44rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
  }

  .agreement-area {
    margin-top: 0;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
  }

  .radio-wrapper {
    display: flex;
    align-items: center;
  }

  .radio-outer {
    width: 30rpx;
    height: 30rpx;
    border-radius: 50%;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10rpx;
    box-sizing: border-box;
    flex-shrink: 0;
  }

  .radio-outer.checked {
    border-color: #1989fa;
  }

  .radio-inner {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: #1989fa;
  }

  .agreement-text {
    font-size: 24rpx;
    color: #999;
    line-height: 1.4;
  }

  .agreement-link {
    color: #27BEFF;
  }

  .login-mode-switch {
    margin-top: 330rpx;
  }
  
  .divider-text {
    position: relative;
    text-align: center;
    color: #999;
    font-size: 24rpx;
    margin-bottom: 40rpx;
  }
  
  .divider-text::before,
  .divider-text::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 25%;
    height: 1px;
    background-color: #eee;
  }
  
  .divider-text::before {
    left: 10%;
  }
  
  .divider-text::after {
    right: 10%;
  }
  
  .login-modes {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 40rpx;
  }
  
  .login-mode-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120rpx; /* 设置固定宽度 */
  }
  
  .login-mode-icon {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 16rpx;
  }
  
  .login-mode-text {
    font-size: 24rpx;
    color: #666;
    width: 100%; /* 使文本宽度占满父容器 */
    text-align: center; /* 文本居中 */
    height: 36rpx; /* 设置固定高度 */
    line-height: 36rpx; /* 行高与高度一致，实现垂直居中 */
  }

  .shake {
    animation: shake 0.05s linear 4 alternate;
  }

  @keyframes shake {
    from {
      transform: translateX(-10rpx);
    }
    to {
      transform: translateX(10rpx);
    }
  }
</style>
