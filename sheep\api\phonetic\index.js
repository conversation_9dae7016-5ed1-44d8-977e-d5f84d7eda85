import { apiPath, baseUrl, tenantId } from '@/sheep/config';
import request from '@/sheep/request';
import sheep from '@/sheep';

// 音标训练接口
export const PhoneticApi = {
  getPhoneticTrainProjectPage: (data) => {
    return request({
      url: '/phonetic/train-project/page',
      method: 'GET',
      data,
      custom: {
        showLoading: false
      },
    })
  },
  getPhoneticTrainSubitemVOListByProjectId: (projectId) => {
    return request({
      url: `/phonetic/train-project/phonetic-train-subitem/vo-list-by-project-id`,
      method: 'GET',
      data: { projectId },
      custom: {
        showLoading: false
      },
    })
  },
  getPhoneticUserRecordListByProjectId: (projectId) => {
    return request({
      url: `/phonetic/user-record/list-by-project-id`,
      method: 'GET',
      data: { projectId },
      custom: {
        showLoading: false
      },
    })
  },
  // 保存用户音标记录
  savePhoneticUserRecord: (data) => {
    return request({
      url: '/phonetic/user-record/save',
      method: 'POST',
      data,
      custom: {
        showLoading: true,
        loadingMsg: '保存进度中...',
      },
    })
  }
}

//  发音评估接口
export const PronunciationAssessmentApi = {
  assess: ({ filePath, referenceText, type, language }) => {
    const token = uni.getStorageSync('token');
    uni.showLoading({
      title: '正在评估...',
      mask: true,
      fail: () => {
        uni.hideLoading();
      },
    });
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${baseUrl}${apiPath}/phonetic/pronunciation-assessment/assess`,
        filePath,
        name: 'audioFile',
        header: {
          Accept: '*/*',
          'tenant-id': tenantId,
          Authorization: token,
        },
        formData: { referenceText, type, language },
        success: (res) => {
          let result = JSON.parse(res.data);
          if (result.error === 1) {
            uni.showToast({
              icon: 'none',
              title: result.msg,
            });
          } else {
            uni.hideLoading();
            setTimeout(() => {
              sheep.$helper.toast('评估完成');
            }, 200)
            return resolve(result);
          }
        },
        fail: (error) => {
          console.log('上传失败：', error);
          return resolve(false);
        },
        complete: () => {
          uni.hideLoading();
        },
      });
    });
  },
};
