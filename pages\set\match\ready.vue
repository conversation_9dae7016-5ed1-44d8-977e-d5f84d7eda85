<template>
	<view class="container">
		<!-- 顶部导航栏 - 自定义返回按钮 -->
		<easy-navbar :title="'配对'" :transparent="true" />

		<!-- 主要内容区域 -->
		<view class="content">
			<!-- 图片区域 -->
			<view class="image-box">
				<image :src="sheep.$url.cdn('/set/ready.png')" class="ready-image" mode="widthFix" />
			</view>
			
			<!-- 文字说明区域 -->
			<view class="text-box">
				<text class="title">将所有的词语与相应的定义匹配</text>
			</view>
			
			<!-- 开始按钮 -->
			<view class="button-box">
				<view class="start-button" @click="handleStart">
					<text class="start-text">开始游戏</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import sheep from '@/sheep';
import EasyNavbar from '@/components/easy-navbar/easy-navbar.vue';
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 定义数据
const setId = ref('');

// 获取页面参数
onLoad((options) => {
	if (options.id || options.setId) {
		setId.value = options.id || options.setId;
	}
});

// 开始游戏方法
const handleStart = () => {
	// 跳转到匹配游戏页面
	sheep.$router.go('/pages/set/match/match', { setId: setId.value },{redirect: true});
};
</script>

<style scoped>
.container {
	width: 100%;
	height: 100vh;
	background-color: #f4f8fc;
	display: flex;
	flex-direction: column;
}

.content {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start;
	padding: 0 40rpx;
}

.image-box {
	width: 100%;
	display: flex;
	justify-content: center;
	margin-top: 160rpx;
}

.ready-image {
	width: 70%;
	max-width: 500rpx;
}

.text-box {
	width: 100%;
	margin-top: 60rpx;
	padding: 20rpx 0;
	box-sizing: border-box;
	display: flex;
	justify-content: center;
}

.title {
	font-size: 36rpx;
	color: #333;
	font-weight: 400;
	text-align: center;
	line-height: 1.5;
}

.button-box {
	width: 100%;
	margin-top: 60rpx;
	padding: 0 20rpx;
	box-sizing: border-box;
	display: flex;
	justify-content: center;
}

.start-button {
	width: 100%;
	height: 100rpx;
	background-color: #46ADF0;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.start-text {
	color: #fff;
	font-size: 36rpx;
	font-weight: 500;
	text-align: center;
}
</style>
