<template>
  <view class="container">
    <easy-navbar :title="setInfo?.title" :transparent="true" />

    <!-- 统计信息 -->
    <view class="stats">
      <text class="word-count">共<text class="word-number">{{ wordList.length }}</text>词</text>
      <view class="actions-group">
        <view class="star-btn" @click="toggleCollect">
          <uni-icons :type="isCollected ? 'star-filled' : 'star'" size="22" :color="isCollected ? '#FFC107' : '#BBBBBB'"></uni-icons>
          <text class="btn-text">收藏</text>
        </view>
        <view class="more-actions" @click="toggleMenu($event)">
          <uni-icons type="more-filled" size="24" color="#BBBBBB"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 遮罩层 - 点击关闭菜单 -->
    <view class="mask" v-if="showMenu" @click="closeMenu"></view>

    <!-- 右侧悬浮菜单 -->
    <view class="floating-menu" v-show="showMenu" :style="menuStyle">
      <!-- 创建者显示的选项 -->
      <template v-if="isCreator">
<!--        <view class="menu-item" @click="toggleVisibility">
          <image class="menu-icon" :src="sheep.$url.cdn('/set/copy.png')" mode="aspectFit"></image>
          <text class="menu-text">
            {{ setInfo.visibility == 0 ? '仅自己可见' : '所有人可见' }}</text
          >
        </view> -->
        <view class="menu-item" @click="createCopy">
          <image class="menu-icon" :src="sheep.$url.cdn('/set/copy.png')" mode="aspectFit"></image>
          <text class="menu-text">制作副本</text>
        </view>
        <view class="menu-item" @click="editSet">
          <image class="menu-icon" :src="sheep.$url.cdn('/set/edit.png')" mode="aspectFit"></image>
          <text class="menu-text">编辑</text>
        </view>
       <view class="menu-item" @click="toggleClassShare">
          <image class="menu-icon" :src="sheep.$url.cdn('/set/share.png')" mode="aspectFit"></image>
          <text class="menu-text">分享到班级</text>
        </view>
        <view class="menu-item" @click="shareToFriend">
          <image
            class="menu-icon"
            :src="sheep.$url.cdn('/set/share2.png')"
            mode="aspectFit"
          ></image>
          <text class="menu-text">分享给好友</text>
        </view>
        <view class="menu-item delete" @click="deleteSet">
          <image class="menu-icon" :src="sheep.$url.cdn('/set/delete.png')" mode="aspectFit"></image>
          <text class="menu-text">删除学习集</text>
        </view>
      </template>

      <!-- 非创建者显示的选项 -->
      <template v-else>
        <view class="menu-item" @click="createCopy">
          <image class="menu-icon" :src="sheep.$url.cdn('/set/copy.png')" mode="aspectFit"></image>
          <text class="menu-text">制作副本</text>
        </view>
       <view class="menu-item" @click="shareToClass" v-if="setInfo && setInfo.visibility !== 1">
          <image class="menu-icon" :src="sheep.$url.cdn('/set/share.png')" mode="aspectFit"></image>
          <text class="menu-text">分享到班级</text>
        </view>
        <view class="menu-item" @click="shareToFriend">
          <image
            class="menu-icon"
            :src="sheep.$url.cdn('/set/share2.png')"
            mode="aspectFit"
          ></image>
          <text class="menu-text">分享给好友</text>
        </view>
      </template>
    </view>

    <!-- 单词列表 -->
    <view class="word-list">
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>
      <view v-else-if="wordList.length === 0" class="empty-state">
        <text>暂无单词卡</text>
      </view>
      <view v-else class="swiper-container">

        
        <swiper 
          class="word-swiper" 
          :current="currentCardIndex" 
          :indicator-dots="false" 
          :autoplay="false" 
          :display-multiple-items="1"
          previous-margin="54rpx"
          next-margin="54rpx"
          :circular="false"
          @change="onSwiperChange"
          @animationfinish="onSwiperAnimationFinish"
          @transition="onSwiperTransition"
          :disable-touch="false"
          :disable-programmatic-animation="false"
          :duration="250"
          :skip-hidden-item-layout="true"
        >
          <swiper-item v-for="(word, index) in wordList" :key="word.id || index" :item-id="`card-${index}`">
            <view class="word-card" :class="getCardClass(index)">
              <!-- 背景图片容器 -->
              <view class="image-container">
                <!-- 底部清晰图片层 -->
                <image class="card-image-clear" 
                  :src="word.imageLoaded ? (word.imageUrl || sheep.$url.cdn('/set/new-back.png')) : sheep.$url.cdn('/set/new-back.png')" 
                  mode="aspectFill"></image>
                <!-- 从上到下的渐变模糊层 -->
                <template v-if="word.imageUrl && word.imageLoaded">
                  <view class="top-blur-container">
                    <!-- 渐变遮罩 -->
                    <view class="blur-gradient-mask"></view>
                    <!-- 模糊图层 -->
                    <image class="top-blur-image" :src="word.imageUrl" mode="aspectFill"></image>
                  </view>
                </template>
                <!-- 无图片时的默认背景 -->
                <view class="default-background" v-else>
                  <!-- 默认背景的渐变色遮罩 -->
                  <view class="default-gradient-mask"></view>
                </view>
              </view>
              <view class="card-content">
                <!-- 暂时注释播放功能 -->
                <!-- <view class="sound-icon" @click="playSound(word, index)">
                  <image
                    class="sound-image"
                    :src="
                      playingIndex === index
                        ? sheep.$url.cdn('/video-refinement/playing.gif')
                        : sheep.$url.cdn('/video-refinement/play.png')
                    "
                    mode="aspectFit"
                  >
                  </image>
                </view> -->
                
                <!-- 文字内容区域 -->
                <view class="text-content" :class="{ 'has-image': word.imageUrl }">
                  <!-- 音标显示 -->
                  <view class="phonetic-row" v-if="word.phoneticSymbol">
                    <text class="phonetic-symbol">{{ word.phoneticSymbol }}</text>
                  </view>
                  
                  <!-- 词语 -->
                  <view class="word-row">
                    <text class="word-text">{{ word.text }}</text>
                  </view>
                  
                  <!-- 定义 -->
                  <view class="translation-row">
                    <text class="word-translation">{{ word.translation }}</text>
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="tab-row">
        <view class="tab-item" @click="navigateTo('wordCard')">
          <image
            class="tab-button-icon"
            :src="sheep.$url.cdn('/set/new-car.png')"
            mode="aspectFit"
          ></image>
        </view>
        <view class="tab-item" @click="navigateTo('study')">
          <image
            class="tab-button-icon"
            :src="sheep.$url.cdn('/set/new-study.png')"
            mode="aspectFit"
          ></image>
        </view>
      </view>
      <view class="tab-row">
        <view class="tab-item" @click="navigateTo('test')">
          <image
            class="tab-button-icon"
            :src="sheep.$url.cdn('/set/new-test.png')"
            mode="aspectFit"
          ></image>
        </view>
        <view class="tab-item" @click="navigateTo('match')">
          <image
            class="tab-button-icon"
            :src="sheep.$url.cdn('/set/new-match.png')"
            mode="aspectFit"
          ></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted, reactive, nextTick } from 'vue';
  import sheep from '@/sheep';
  import SetApi from '@/sheep/api/set';
  import SetCollectionApi from '@/sheep/api/set/collection';
  import NlsApi from '@/sheep/api/voice/nls';
  import { getTtsConfig, TtsTypeEnum } from '@/sheep/util/language-detector';

  // 获取路由参数
  const setId = ref('');
  const isCollected = ref(false);
  const wordList = ref([]);
  const setInfo = ref(null);
  const loading = ref(false);
  const showMenu = ref(false);
  const menuStyle = reactive({
    top: '190rpx',
    right: '30rpx',
  });
  const playingIndex = ref(-1); // 当前正在播放的单词索引
  const audioContext = ref(null); // 音频上下文对象
  const isCreator = ref(false);
  // const isSharedToClass = ref(false);
  const currentCardIndex = ref(0);
  const loadingImages = ref(false); // 添加图片加载状态标记
  
  // 卡片的样式类计算 - 简化版本，减少不必要的计算
  const getCardClass = (index) => {
    // 简化判断，只区分当前卡片和非当前卡片
    return index === currentCardIndex.value ? { 'active': true } : { 'inactive': true };
  };

  onMounted(() => {
    // 初始化音频上下文
    audioContext.value = null;

    // 清空数据
    wordList.value = [];
    setInfo.value = null;

    // 获取路由参数
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    let id = '';

    if (currentPage && currentPage.options) {
      id = currentPage.options.id;
    } else if (currentPage && currentPage.$page && currentPage.$page.options) {
      id = currentPage.$page.options.id;
    }

    console.log('获取到ID:', id);
    setId.value = id;

    if (id) {
      getSetInfo(id);
    }
    uni.$on('refreshSetInfo', refreshSetInfo);
  });
  // 处理刷新事件
  const refreshSetInfo = async (id) => {
    getSetInfo(id);
  };

  // 获取学习集详情
  const getSetInfo = (id) => {
    loading.value = true;

    // 检查ID是否有效
    if (!id) {
      loading.value = false;
      sheep.$helper.toast('无效的学习集');
      return;
    }

    try {
      SetApi.getWordSet(id, true)
        .then((res) => {
          loading.value = false;

          if (res.code !== 0) {
            sheep.$helper.toast(res.msg || '获取学习集失败');
            return;
          }

          // 处理列表包装的响应结构
          let setData = null;
          if (res.data && res.data.list && res.data.list.length > 0) {
            // 如果响应是列表结构，获取第一个元素
            setData = res.data.list[0];
          } else if (res.data) {
            // 直接使用data对象
            setData = res.data;
          }

          if (setData) {
            // 检查是否为创建者
            checkCreatorStatus(setData);

            // 设置收藏状态
            isCollected.value = !!setData.isStore;
            console.log('学习集收藏状态:', isCollected.value);

            // 先保存学习集基本信息
            setInfo.value = {
              ...setData,
              // 使用返回的用户信息
              creatorNickname: setData.nickname || '用户',
              creatorAvatar: setData.avatar || '',
            };

            // 延迟处理单词卡数据，避免一次处理太多数据导致渲染问题
            setTimeout(() => {
              processWordCards(setData);
            }, 100);
          } else {
            sheep.$helper.toast('获取学习集数据失败');
          }
        })
        .catch((error) => {
          loading.value = false;
          sheep.$helper.toast('网络异常，请稍后再试');
        });
    } catch (e) {
      loading.value = false;
      sheep.$helper.toast('程序异常，请稍后再试');
    }
  };

  // 处理单词卡数据
  const processWordCards = (setData) => {
    try {
      // 处理学习集中包含的单词卡数据
      if (setData.wordCards && Array.isArray(setData.wordCards)) {
        console.log('原始单词卡数据:', setData.wordCards);

        // 预处理卡片数据
        const processedCards = setData.wordCards.map((item, index) => {
          // 从定义中获取所有定义
          let translation = '';
          if (item.definitions && Array.isArray(item.definitions)) {
            // 正确获取每个definition对象中的definition属性
            translation = item.definitions
              .map((def) => def.definition)
              .filter((def) => def && def.trim() !== '')
              .join('，'); // 使用逗号拼接定义，而不是换行符
          }

          // 确保每个字段都有默认值，防止undefined导致渲染问题
          return {
            id: item.id || `temp-${index}`,
            text: item.word || '',
            phoneticSymbol: item.phoneticSymbol || '',
            translation: translation || '暂无定义',
            imageUrl: item.imageUrl || '', // 存储图片URL
            audioUrl: item.audioUrl || '',
            imageLoaded: false // 初始设置为未加载状态
          };
        });
        
        // 使用nextTick确保DOM更新
        nextTick(() => {
          wordList.value = processedCards;
          
          // 懒加载初始图片
          setTimeout(() => {
            loadInitialImages();
          }, 300);
        });
      } else {
        wordList.value = [];
      }
    } catch (e) {
      sheep.$helper.toast('处理数据异常');
    }
  };
  
  // 新增函数：加载初始图片（仅加载前3张或总数，取较小值）
  const loadInitialImages = () => {
    if (!wordList.value.length) return;
    
    loadingImages.value = true;
    
    // 计算初始加载数量：最多3张，或全部（如果总数小于3）
    const initialLoadCount = Math.min(3, wordList.value.length);
    
    // 加载初始图片
    for (let i = 0; i < initialLoadCount; i++) {
      loadCardImage(i);
    }
    
    loadingImages.value = false;
  };
  
  // 新增函数：加载指定索引的卡片图片
  const loadCardImage = (index) => {
    // 检查索引有效性和是否已加载过
    if (index < 0 || index >= wordList.value.length || wordList.value[index].imageLoaded || !wordList.value[index].imageUrl) {
      return;
    }
    
    // 加载图片
    uni.getImageInfo({
      src: wordList.value[index].imageUrl,
      success(res) {
        // 图片加载成功，更新状态
        if (wordList.value[index]) {
          wordList.value[index].imageLoaded = true;
        }
      },
      fail(err) {
        console.error('图片加载失败:', err);
        // 加载失败，确保界面不显示错误
        if (wordList.value[index]) {
          wordList.value[index].imageUrl = '';
        }
      }
    });
  };
  
  // 移除原有的预加载方法
  // const preloadAllImages = () => { ... };

  // 检查是否为创建者
  const checkCreatorStatus = async (setData) => {
    if (!setData) return;

    try {
      // 获取当前登录用户信息
      const userStore = sheep.$store('user');

      // 确保用户信息已加载
      let userInfo = userStore.userInfo;
      if (!userInfo || !userInfo.id) {
        userInfo = await userStore.updateUserData();
      }

      // 比较创建者ID和当前用户ID，使用userId字段
      const currentUserId = userInfo?.id;
      const creatorId = setData.userId; // 使用userId作为创建者ID

      // 检查创建者ID是否与当前用户ID匹配
      isCreator.value = !!(creatorId && currentUserId && creatorId === currentUserId);
    } catch (e) {
      isCreator.value = false;
    }
  };

  // 播放声音
  const playSound = async (word, index) => {
    // 如果有正在播放的音频，先停止
    if (audioContext.value) {
      audioContext.value.stop();
      // 如果点击的是当前正在播放的，则仅停止播放
      if (playingIndex.value === index) {
        playingIndex.value = -1;
        audioContext.value = null;
        return;
      }
    }

    try {
      // 设置当前播放索引，显示加载动画
      playingIndex.value = index;

      // 检测文本语言
      const ttsConfig = getTtsConfig(word.text, TtsTypeEnum.ALIYUN);
      if (!ttsConfig) {
        sheep.$helper.toast('无法识别文本语言');
        return;
      }

      // 准备文本转语音的参数
      const ttsParams = {
        text: word.text,
        speaker: ttsConfig.speaker,
        speechRate: ttsConfig.speechRate,
        pitchRate: ttsConfig.pitchRate,
        displayCaptions: false
      };

      // 调用API获取音频数据
      const res = await NlsApi.ttsAliyun(ttsParams);

      // 如果请求失败
      if (res?.msg === 0) {
        playingIndex.value = -1;
        return;
      }

      // 使用文件系统将音频数据保存为临时文件
      const manager = uni.getFileSystemManager();
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;

      await new Promise((resolve, reject) => {
        manager.writeFile({
          filePath: tempFilePath,
          data: res,
          encoding: 'binary',
          success: resolve,
          fail: (err) => {
            console.error('写入音频文件失败:', err);
            reject(err);
          },
        });
      });

      // 创建音频上下文并播放
      const innerAudioContext = uni.createInnerAudioContext();
      audioContext.value = innerAudioContext;
      innerAudioContext.autoplay = true;
      innerAudioContext.src = tempFilePath;

      console.log('开始播放语音');

      // 监听播放结束事件
      innerAudioContext.onPlay(() => {
        console.log('开始播放');
      });

      innerAudioContext.onEnded(() => {
        console.log('播放结束');
        playingIndex.value = -1;
        audioContext.value = null;

        // 删除临时文件
        manager.unlink({
          filePath: tempFilePath,
          fail: (err) => {
            console.log('删除临时音频文件失败:', err);
          },
        });
      });

      innerAudioContext.onStop(() => {
        console.log('播放停止');
        // 删除临时文件
        manager.unlink({
          filePath: tempFilePath,
          fail: (err) => {
            console.log('删除临时音频文件失败:', err);
          },
        });
      });

      innerAudioContext.onError((res) => {
        console.error('播放失败:', res);
        sheep.$helper.toast('播放失败');
        playingIndex.value = -1;
        audioContext.value = null;

        // 删除临时文件
        manager.unlink({
          filePath: tempFilePath,
          fail: (err) => {
            console.log('删除临时音频文件失败:', err);
          },
        });
      });
    } catch (error) {
      console.error('语音播放过程中出错:', error);
      sheep.$helper.toast('语音播放失败');
      playingIndex.value = -1;
      audioContext.value = null;
    }
  };

  // 切换菜单显示
  const toggleMenu = (event) => {
    const touchY = event.touches[0].clientY;

    // 设置菜单的位置
    menuStyle.top = touchY + 'px';
    menuStyle.right = '20rpx';

    showMenu.value = true;
  };

  // 关闭菜单
  const closeMenu = () => {
    showMenu.value = false;
  };

  // 跳转到不同功能页面
  const navigateTo = (page) => {
    switch (page) {
      case 'wordCard':
        sheep.$router.go(`/pages/set/car/car?setId=${setId.value}&reset=false`);
        break;
      case 'study':
        sheep.$router.go(`/pages/set/study/study?setId=${setId.value}`);
        break;
      case 'test':
        sheep.$router.go(`/pages/set/test/test?setId=${setId.value}`);
        break;
      case 'match':
        sheep.$router.go(`/pages/set/match/ready?id=${setId.value}`);
        break;
      default:
        break;
    }
  };

  // 临时的功能函数
  const toggleVisibility = async () => {
    let visibility = 0;
    console.log('toggleVisibility', setInfo.value.visibility);
    if (setInfo.value.visibility === 0) {
      visibility = 1;
    } else {
      visibility = 0;
    }
    const res = await SetApi.setVisibility({ id: setId.value, visibility });
    if (res.code === 0) {
      getSetInfo(setId.value)
      sheep.$helper.toast('设置成功');
    }
    closeMenu();
  };

  const editSet = () => {
    if (!setId.value) {
      sheep.$helper.toast('学习集ID无效');
      return;
    }

    // 实现跳转到编辑页面的功能
    sheep.$router.go(`/pages/set/edit?id=${setId.value}`);
    closeMenu();
  };

  const toggleClassShare = () => {
    // 关闭菜单
    closeMenu();
    // 如果setInfo为空或是"仅自己"可见，提示用户修改可见范围
    if (!setInfo.value || setInfo.value.visibility === 1) {
      sheep.$helper.toast('仅自己可见的学习集无法分享到班级');
      return;
    }
    
    // 检查是否有已分享的班级ID
    let selectedClassIdsParam = '';
    if (setInfo.value.classIds && Array.isArray(setInfo.value.classIds)) {
      selectedClassIdsParam = encodeURIComponent(JSON.stringify(setInfo.value.classIds));
    }
    
    // 跳转到班级选择页面，传递学习集ID和已选班级IDs
    sheep.$router.go(`/pages/set/select-group?setId=${setId.value}&isCreated=true&selectedIds=${selectedClassIdsParam}`);
  };

  const shareToClass = () => {
    // 关闭菜单
    closeMenu();
    
    // 检查学习集可见性
    if (!setInfo.value || setInfo.value.visibility === 1) {
      return;
    }
    
    // 检查是否有已分享的班级ID
    let selectedClassIdsParam = '';
    if (setInfo.value.classIds && Array.isArray(setInfo.value.classIds)) {
      selectedClassIdsParam = encodeURIComponent(JSON.stringify(setInfo.value.classIds));
    }
    
    // 跳转到班级选择页面，传递学习集ID和已选班级IDs
    sheep.$router.go(`/pages/set/select-group?setId=${setId.value}&isCreated=true&selectedIds=${selectedClassIdsParam}`);
  };

  const shareToFriend = () => {
    sheep.$helper.inDevMsg();
    closeMenu();
  };

  const deleteSet = () => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这个学习集吗？删除后不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            uni.showLoading({
              title: '删除中...',
              mask: true,
            });

            const response = await SetApi.deleteWordSet(setId.value);

            if (response.code === 0) {
              sheep.$helper.toast('删除成功');

              // 触发刷新事件
              uni.$emit('refreshWordSets', {
                timestamp: Date.now(),
              });

              // 延迟返回，让用户看到提示
              setTimeout(() => {
                uni.navigateBack();
              }, 1500);
            } else {
              sheep.$helper.toast(response.msg || '删除失败');
            }
          } catch (error) {
            console.error('删除学习集失败:', error);
            sheep.$helper.toast('删除失败，请稍后再试');
          } finally {
            uni.hideLoading();
            closeMenu();
          }
        } else {
          closeMenu();
        }
      },
    });
  };

  // 制作副本
  const createCopy = () => {
    if (!setId.value) {
      sheep.$helper.toast('学习集ID无效');
      return;
    }
    // 实现跳转到制作副本页面的功能
    sheep.$router.go(`/pages/set/copy?id=${setId.value}`);
    closeMenu();
  };

  // 临时的收藏功能
  const toggleCollect = async () => {
    if (!setId.value) {
      sheep.$helper.toast('学习集ID无效');
      return;
    }

    try {
      if (isCollected.value) {
        // 已收藏，取消收藏
        const res = await SetCollectionApi.cancelCollection(setId.value);
        if (res.code === 0) {
          isCollected.value = false;
          sheep.$helper.toast('已取消收藏');
        } else {
          sheep.$helper.toast(res.msg || '取消收藏失败');
        }
      } else {
        // 未收藏，添加收藏
        const res = await SetCollectionApi.createCollection({
          setId: setId.value,
        });
        if (res.code === 0) {
          isCollected.value = true;
          sheep.$helper.toast('收藏成功');
        } else {
          sheep.$helper.toast(res.msg || '收藏失败');
        }
      }
    } catch (error) {
      console.error('切换收藏状态时出错:', error);
      sheep.$helper.toast('操作失败，请稍后再试');
    }
  };

  const onSwiperChange = (e) => {
    // 获取当前索引
    const currentIndex = e.detail.current;
    
    // 更新当前卡片索引
    if (currentCardIndex.value !== currentIndex) {
      currentCardIndex.value = currentIndex;
    }
    
    // 根据当前索引，预加载下一张图片
    const nextIndex = currentIndex + 2; // 预加载当前索引+2的图片
    
    // 检查是否需要加载新图片
    if (nextIndex < wordList.value.length) {
      // 异步加载下一张图片，避免影响滑动性能
      setTimeout(() => {
        loadCardImage(nextIndex);
      }, 300);
    }
  };

  // swiper动画完成时的事件处理
  const onSwiperAnimationFinish = (e) => {
    // 简化处理，避免重复触发状态更新
    // change事件已经处理了索引更新
  };
  
  // swiper过渡动画中的事件处理 - 简化以避免抖动
  const onSwiperTransition = (e) => {
    // 移除实时更新，只通过change事件来更新索引
    // 避免在滑动过程中频繁改变状态导致的抖动
  };
</script>

<style lang="scss" scoped>
  .container {
    height: 100vh;
    background-color: #f8fcff;
    position: relative;
    overflow: hidden;
  }

  .header {
    padding: 40rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .back-btn {
      font-size: 40rpx;
      color: #333;

      .circle-icon {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        background-color: #999;
      }
    }

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .nickname {
        font-size: 32rpx;
        color: #333;
      }
    }

    .actions {
      display: flex;
      gap: 20rpx;

      .action-btn {
        font-size: 36rpx;
        color: #666;

        .circle-icon {
          width: 36rpx;
          height: 36rpx;
          border-radius: 50%;
          background-color: #999;
        }
      }
    }
  }

  .stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 30rpx;
    margin-bottom: 26rpx;

    .word-count {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      
      .word-number {
        color: #0052B4;
        font-weight: 600;
      }
    }

    .actions-group {
      display: flex;
      align-items: center;
    }

    .star-btn {
      display: flex;
      align-items: center;
      margin-right: 40rpx;

      .btn-text {
        font-size: 28rpx;
        color: #333;
        margin-left: 8rpx;
      }
    }

    .more-actions {
      padding: 4rpx;
    }
  }

  .floating-menu {
    position: fixed;
    right: 20rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
    padding: 20rpx 0;
    z-index: 10;
    width: 280rpx;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 16rpx 24rpx;

      &:active {
        background-color: #f5f5f5;
      }

      &.delete {
        .menu-text {
          color: #ff4d4f;
        }
      }

      .menu-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 20rpx;
      }

      .menu-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  .word-list {
    padding: 0;

    .loading,
    .empty-state {
      padding: 60rpx 0;
      text-align: center;
      color: #999;
      font-size: 28rpx;
    }

    .swiper-container {
      position: relative;
      height: 800rpx;
      width: 100%;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      transition: padding 0.3s ease;
    }

    
    .word-swiper {
      height: 800rpx;
      width: 100%;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      /* iOS性能优化 */
      -webkit-overflow-scrolling: touch;
      /* 防止滑动卡顿 */
      will-change: transform;
      -webkit-transform: translateZ(0);
    }

              .word-card {
      width: 630rpx;
      height: 710rpx; /* 统一高度 */
      position: relative;
      margin: 0 6rpx;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
      transform: scale(0.95);
      opacity: 0.6; /* 降低默认不透明度，增加对比 */
      transition: transform 0.22s ease-out, opacity 0.22s ease-out; /* 进一步加快过渡速度 */
      /* 简化iOS优化以减少渲染负担 */
      -webkit-backface-visibility: hidden;
      -webkit-transform: translateZ(0);

      &.active {
        transform: scale(1);
        opacity: 1;
        z-index: 2;
        box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2); /* 增强活动卡片阴影 */
      }
      
      &.inactive {
        transform: scale(0.95);
        opacity: 0.6;
      }
      
      /* 删除相邻卡片特殊样式，简化渲染 */

      .image-container {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        overflow: hidden;
      }

      .card-image-clear {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      /* 删除原有的模糊相关样式 */
      .blur-effect, .single-blur-image, .gradient-overlay {
        display: none;
      }
      
      /* 从上到下渐变模糊效果 */
      .top-blur-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 2;
        /* 使用渐变遮罩实现从上到下的模糊淡出效果 */
        -webkit-mask-image: -webkit-linear-gradient(to bottom,
          rgba(0,0,0,1) 0%, 
          rgba(0,0,0,0.95) 20%, 
          rgba(0,0,0,0.85) 40%, 
          rgba(0,0,0,0.6) 60%, 
          rgba(0,0,0,0.3) 80%, 
          rgba(0,0,0,0) 95%);
        mask-image: linear-gradient(to bottom, 
          rgba(0,0,0,1) 0%, 
          rgba(0,0,0,0.95) 20%, 
          rgba(0,0,0,0.85) 40%, 
          rgba(0,0,0,0.6) 60%, 
          rgba(0,0,0,0.3) 80%, 
          rgba(0,0,0,0) 95%);
      }
      
      .blur-gradient-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, 
          rgba(60,60,60,0.7) 0%,
          rgba(60,60,60,0.6) 30%, 
          rgba(60,60,60,0.4) 60%,
          rgba(60,60,60,0.2) 85%,
          rgba(60,60,60,0) 100%);
        z-index: 4;
        /* iOS优化 */
        -webkit-transform: translateZ(0);
      }
      
      .top-blur-image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        /* 对整个图像进行模糊，但使用遮罩确保只有顶部是模糊的 */
        filter: blur(20px);
        -webkit-filter: blur(20px);
        transform: scale(1.05);
        -webkit-transform: scale(1.05) translateZ(0);
        /* iOS性能优化 */
        will-change: filter;
        -webkit-backface-visibility: hidden;
        z-index: 3;
      }

      .default-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #333333;
        z-index: 2;
      }
      
      .default-gradient-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, 
          rgba(80,80,80,1) 0%,
          rgba(70,70,70,0.8) 30%, 
          rgba(60,60,60,0.7) 60%,
          rgba(50,50,50,0.6) 100%);
        z-index: 3;
        /* iOS优化 */
        -webkit-transform: translateZ(0);
      }

      .card-content {
        position: relative;
        z-index: 10;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;

        .text-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          padding: 40rpx 0 140rpx 0;
        }

        .phonetic-row {
          margin-bottom: 10rpx;
          width: 100%;
        }

        .phonetic-symbol {
          font-family: "Adobe Thai", sans-serif;
          font-weight: 400;
          font-size: 38rpx;
          color: #E5F6FF;
          display: block;
          text-align: center;
          line-height: 1.2;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
        }

        .word-row {
          margin-bottom: 20rpx;
          width: 100%;
        }

        .word-text {
          font-family: "Adobe Thai", sans-serif;
          font-weight: 400;
          font-size: 50rpx;
          color: #FFFFFF;
          display: block;
          text-align: center;
          line-height: 1.2;
          text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.4);
        }

        .translation-row {
          width: 100%;
        }

        .word-translation {
          font-family: "PingFang SC", sans-serif;
          font-weight: 400;
          font-size: 29rpx;
          color: #FFFFFF;
          display: block;
          text-align: center;
          line-height: 1.4;
          max-width: 100%;
          word-wrap: break-word;
          overflow-wrap: break-word;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  .mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: 5;
  }

  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    padding: 20rpx 10rpx 130rpx 10rpx;

    .tab-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 9rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
    }

    .tab-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50%;
      padding: 5rpx;

      .tab-button-icon {
        width: 351rpx;
        height: 203rpx;
      }
    }
  }

  .circle-icon {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    background-color: #999;

    &.active {
      background-color: #ffd700;
    }

    &.blue {
      background-color: #3a8ee6;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
</style>
