<template>
  <view class="result-container">
    <!-- 顶部导航栏 -->
    <easy-navbar title="重点提炼" />

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 核心词汇部分 -->
      <view>
        <view class="section-item">
          <view class="divider"></view>
          <view class="section-title">核心词汇</view>
        </view>
        <view class="section-block">
          <view class="content-section">
            <view class="section-container">
              <view class="vocab-list">
                <!-- 词汇项 -->
                <view v-for="(word, index) in vocabList" :key="index" class="vocab-item">
                  <view class="vocab-content">
                    <view class="vocab-thai">{{ word.word }}({{ word.phonetic }})</view>
                    <view class="vocab-chinese">{{ word.translation }}</view>
                  </view>
                  <view class="vocab-actions">
                    <view class="action-audio-btn" @tap="playAudio(word)">
                      <image
                        class="audio-icon"
                        :src="getPlayIconSource(word)"
                        mode="heightFix"
                      ></image>
                    </view>
                    <view class="action-audio-btn" @tap="copyWord(word)">
                      <image
                        class="copy-icon"
                        :src="sheep.$url.cdn('/video-refinement/copy.png')"
                      ></image>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 底部按钮 -->
              <view class="bottom-actions">
                <view class="action-button copy-all" @tap="copyAllWords">复制全部单词</view>
                <view class="action-button generate-set" @tap="generateLearningSet"
                  >生成学习集</view
                >
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 核心内容部分 -->
      <view>
        <view class="section-item">
          <view class="divider"></view>
          <view class="section-title">内容总结</view>
        </view>
        <!-- 内容总结部分 -->
        <view class="section-block">
          <view class="content-section">
            <view class="section-container">
              <rich-text class="section-content" :nodes="formatPlainText(summarize)" user-select="text"></rich-text>
              <!-- 新增复制内容总结按钮 -->
              <view class="bottom-actions bottom-actions-full-width">
                <text class="action-button copy-all action-padding" @tap="copySummarize">复制内容总结</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 核心内容部分 -->
      <view>
        <view class="section-item">
          <view class="divider"></view>
          <view class="section-title">核心内容</view>
        </view>
        <view class="section-block">
          <view class="content-section">
            <view class="section-container">
              <rich-text class="section-content" :nodes="formatPlainText(originalText)" user-select="text"></rich-text>
              <!-- 新增复制核心内容按钮 -->
              <view class="bottom-actions bottom-actions-full-width">
                <text class="action-button copy-all action-padding" @tap="copyOriginalText">复制核心内容</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 添加底部安全区域 -->
    <view class="safe-area-inset-bottom"></view>
  </view>
</template>

<script setup>
  import { ref } from 'vue';
  import { onLoad, onUnload, onHide } from '@dcloudio/uni-app';
  import EasyNavbar from '@/components/easy-navbar/easy-navbar.vue';
  import VideoRefinementApi from '@/sheep/api/video-refinement/video-refinement';
  import NlsApi from '@/sheep/api/voice/nls';
  import sheep from '@/sheep';
  import { getTtsConfig, TtsTypeEnum } from '@/sheep/util/language-detector';

  const videoUrl = ref('');
  const loading = ref(true); // 页面加载状态
  const currentPlayingWord = ref(null); // 当前正在播放的单词
  let audioContext = null; // 音频上下文
  const originalText = ref(); // 原始文本
  const summarize = ref(); // 内容摘要
  const vocabList = ref([]); // 词汇列表

  onLoad(async (option) => {
    videoUrl.value = decodeURIComponent(option.videoUrl);
    const messageId = option.messageId;
    // 加载提炼结果
    await loadRefinementResult(messageId);
    uni.hideLoading();
  });

  // 页面卸载时清理
  onUnload(() => {
    stopAudio(); // 停止音频播放
  });
  onHide(() => {
    stopAudio();
  });

  // 获取提炼结果
  const loadRefinementResult = async (messageId) => {
    try {
      const { data } = await VideoRefinementApi.getResult(messageId);

      if (data) {
        // 提炼完成，显示结果
        originalText.value = data.originalText || '未能获取核心内容';
        summarize.value = data.summarize || '未能获取内容总结';
        vocabList.value = data.keywords || [];
        loading.value = false;
        uni.hideLoading();
      } else {
        throw new Error('获取提炼结果失败');
      }
    } catch (error) {
      console.error('获取提炼结果失败:', error);
      uni.hideLoading();
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  };

  // 获取播放图标路径
  const getPlayIconSource = (word) => {
    const basePath = '/video-refinement/';
    return sheep.$url.cdn(
      basePath + (currentPlayingWord.value === word ? 'playing.gif' : 'play.png'),
    );
  };

  // 播放音频
  const playAudio = async (word) => {
    if (!word) return;

    // 如果正在播放，停止并重新播放
    if (currentPlayingWord.value) {
      stopAudio(); // 停止当前播放
    }

    currentPlayingWord.value = word; // 设置当前播放的单词
    await commonPlayText(word.word); // 播放文本
  };

  // 停止音频播放
  const stopAudio = () => {
    if (audioContext) {
      audioContext.stop();
      audioContext.destroy();
      audioContext = null;
    }
    currentPlayingWord.value = null; // 重置当前播放的单词
  };

  // 通用播放文本函数
  const commonPlayText = async (text) => {
    let ttsConfig = getTtsConfig(text, TtsTypeEnum.ALIYUN);

    const params = {
      text,
      speaker: ttsConfig.speaker,
      speechRate: ttsConfig.speechRate,
      pitchRate: ttsConfig.pitchRate,
      displayCaptions: false,
    };

    const res = await NlsApi.ttsAliyun(params); // 文本转语音

    if (rec?.msg) {
      stopAudio();
      return;
    }

    const manager = uni.getFileSystemManager();
    const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;
    await new Promise((resolve, reject) => {
      manager.writeFile({
        filePath: tempFilePath,
        data: res,
        encoding: 'binary',
        success: resolve,
        fail: reject,
      });
    });
    await playFromUrl(tempFilePath); // 播放音频文
  };

  // 从URL播放音频
  const playFromUrl = (audioUrl) => {
    return new Promise((resolve, reject) => {
      try {
        audioContext = uni.createInnerAudioContext();

        if (!audioContext) {
          reject(new Error('无法创建音频上下文'));
          return;
        }

        audioContext.src = audioUrl;

        audioContext.onEnded(() => {
          stopAudio(); // 播放结束
          resolve();
        });

        audioContext.onError(() => {
          stopAudio(); // 播放错误
          reject(new Error('音频播放失败'));
        });

        // 设置超时处理，防止播放图标一直显示
        setTimeout(() => {
          if (currentPlayingWord.value) {
            stopAudio();
          }
        }, 10000); // 10秒超时

        audioContext.play(); // 开始播放
      } catch (error) {
        stopAudio();
        reject(error);
      }
    });
  };

  // 复制单词
  const copyWord = (word) => {
    sheep.$helper.copyText(`${word.word} (${word.phonetic})\n${word.translation}`);
  };

  // 复制所有单词
  const copyAllWords = () => {
    const allWords = vocabList.value
      .map((word) => `${word.word} (${word.phonetic})\n${word.translation}`)
      .join('\n\n');
    sheep.$helper.copyText(allWords);
  };

  // 生成学习集
  const generateLearningSet = () => {
    if (!vocabList.value || vocabList.value.length === 0) {
      sheep.$helper.toast('没有可用的词汇数据');
      return;
    }

    // 格式化词汇数据为创建学习集表单所需的格式
    const formData = {
      title: '',
      coverUrl: '',
      visibility: 0,
      wordCards: vocabList.value.map(word => ({
        word: word.word,
        imageUrl: '',
        audioUrl: '',
        definitions: [
          {
            definition: word.translation // 使用翻译作为定义
          }
        ]
      }))
    };

    // 将数据转换为 URL 安全的字符串
    const encodedData = encodeURIComponent(JSON.stringify(formData));
    
    // 跳转到创建学习集页面，并传递完整的表单数据
    sheep.$router.go(`/pages/set/create?formData=${encodedData}`);
  };

  // 复制内容总结
  const copySummarize = () => {
    if (!summarize.value) return;
    sheep.$helper.copyText(summarize.value);
    sheep.$helper.toast('内容总结已复制');
  };

  // 复制核心内容
  const copyOriginalText = () => {
    if (!originalText.value) return;
    sheep.$helper.copyText(originalText.value);
    sheep.$helper.toast('核心内容已复制');
  };

  // 将普通文本转换为适合rich-text显示的格式
  const formatPlainText = (text) => {
    if (!text) return '';
    return text.replace(/\n/g, '<br>');
  };
</script>

<style lang="scss" scoped>
  .result-container {
    min-height: 100vh;
    padding-bottom: 40rpx;
    background-color: #f8fcff;
  }

  .content-wrapper {
    margin-top: 20rpx;
    padding: 15rpx 15rpx;
  }

  .section-block {
    margin-bottom: 30rpx;
  }

  /* 内容区块样式 */
  .content-section {
    display: flex;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 1px 3px rgba(158, 156, 156, 0.1);
  }

  .section-item {
    display: flex;
    align-items: center; /* 让元素垂直居中对齐 */
    margin-top: 6rpx;
    margin-bottom: 20rpx;
    margin-left: 30rpx;
  }

  // 添加竖向分隔线
  .divider {
    width: 9rpx;
    height: 34rpx;
    border-radius: 4.5rpx;
    background-color: #239eed;
    margin-right: 10rpx;
  }

  .section-title {
    font-family: 'SourceHanSansCN-Medium', sans-serif;
    font-size: 37.5rpx;
    font-weight: 600;
    color: #222222;
  }

  .section-container {
    flex: 1;
  }

  .section-content {
    font-family: 'SourceHanSansCN-Normal', sans-serif;
    font-size: 29rpx;
    line-height: 1.6;
    color: #333;
  }

  /* 词汇列表样式 */
  .vocab-list {
    margin-bottom: 25rpx;
  }

  .vocab-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;
    padding: 24rpx;
    background-color: #f4fdf8;
    border-radius: 16rpx;
  }

  .vocab-content {
    flex: 1;
  }

  .vocab-thai {
    margin-bottom: 8rpx;
    font-family: 'SourceHanSansCN-Medium', sans-serif;
    font-size: 29rpx;
    color: #0c0c0c;
  }

  .vocab-chinese {
    font-family: 'SourceHanSansCN-Medium', sans-serif;
    font-size: 29rpx;
    font-weight: Normal;
    color: #171717;
  }

  .vocab-actions {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 53rpx;
    height: 53rpx;
  }

  .action-audio-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 53rpx;
    height: 53rpx;
    background-color: #e8f3fb;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .audio-icon,
  .copy-icon {
    width: 25rpx;
    height: 25rpx;
  }

  /* 底部按钮样式 */
  .bottom-actions {
    display: flex;
    justify-content: space-between;
    width: 80%;
    margin-top: 30rpx;
    margin-right: 15rpx;
    margin-left: auto;
  }

  .bottom-actions-full-width{
    margin-top: 20rpx;
    width: 100%;
    justify-content: flex-end;
  }

  .action-button {
    display: inline-block;
    padding: 26rpx 40rpx;
    font-family: 'SourceHanSansCN-Regular', sans-serif;
    font-size: 28rpx;
    font-weight: 500;
    border-radius: 36.5rpx;
  }

  .copy-all {
    margin-right: 15rpx;
    color: #0b85d4;
    background-color: #e5f5ff;
  }

  .generate-set {
    margin-left: 15rpx;
    color: #ffffff;
    background-color: #239eed;
  }

  /* 添加底部安全区域样式 */
  .safe-area-inset-bottom {
    width: 100%;
    height: env(safe-area-inset-bottom);
  }
</style>
