<template>
  <view class="user-page">
    <!-- 顶部用户信息 -->
    <view class="header">
      <view class="user-info" @tap="handleLogin">
        <image
          v-if="isLogin && userInfo?.avatar"
          class="avatar"
          :src="sheep.$url.cdn(userInfo?.avatar)"
        />
        <image v-else class="avatar" :src="sheep.$url.static('/uniapp/my/avatar.png')" />
        <view class="user-details">
          <template v-if="isLogin">
            <template v-if="userInfo?.isCertified">
              <view class="username-wrapper">
                <text class="username">{{ userInfo?.nickname }}</text>
              </view>
              <view class="user-certified-badge">
                <view class="icon-container">
                  <image
                    class="certified-icon"
                    :src="sheep.$url.cdn('/user/certified.png')"
                    mode="aspectFit"
                  />
                </view>
                <text class="certified-text">已认证</text>
              </view>
            </template>
            <template v-else>
              <text class="username">{{ userInfo?.nickname }}</text>
            </template>
          </template>
          <template v-else>
            <text class="username">未登录用户</text>
          </template>
        </view>
      </view>
      <template v-if="isLogin">
        <view class="edit-profile" @tap="sheep.$router.go('/pages/user/info')">
          <text>编辑资料</text>
        </view>
      </template>
      <template v-else>
        <view class="edit-profile" @tap="handleLogin">
          <text>登录</text>
        </view>
      </template>
    </view>

    <!-- 成就卡片 -->
    <view class="stats-card">
      <view class="stats-title">最近获得的成就</view>
      <view class="stats-desc">一共解锁了0次勋章</view>
      <view class="stats-action">
        <text class="action-text" @tap="sheep.$helper.inDevMsg()">去切换</text>
      </view>
      <view class="award">
        <image
          class="award-icon"
          mode="widthFix"
          :src="sheep.$url.static('/uniapp/my/word-award.png')"
        />
        <text class="award-text">未获得</text>
      </view>
    </view>

    <!-- 功能按钮列表 -->
    <template v-if="isLogin && userInfo?.isCertified">
      <view class="teacher-action-buttons">
        <view class="teacher-button-item" @tap="goToFirstClassDetail">
          <image class="teacher-icon" :src="sheep.$url.static('/uniapp/my/class2.png')" />
          <text class="teacher-label">班级</text>
        </view>
        <view class="teacher-button-item" @tap="handleSetClick">
          <image class="teacher-icon" :src="sheep.$url.static('/uniapp/my/set.png')" />
          <text class="teacher-label">学习集</text>
        </view>

        <view class="teacher-button-item" @tap="goToCourseDetail">
          <image class="teacher-icon" :src="sheep.$url.static('/uniapp/my/course2.png')" />
          <text class="teacher-label">课程</text>
        </view>
        <view class="teacher-button-item" @tap="sheep.$helper.inDevMsg()">
          <image class="teacher-icon" :src="sheep.$url.static('/uniapp/my/teaching-plan2.png')" />
          <text class="teacher-label">教案</text>
        </view>
      </view>
    </template>
    <template v-else>
      <!-- 功能按钮列表 -->
      <view class="action-buttons">
        <view
          class="button-card class-card"
          @tap="goToFirstClassDetail"
          :style="{
            'background-image': 'url(' + sheep.$url.static('/uniapp/my/student-class.png') + ')',
          }"
        >
          <view class="card-content">
            <text class="card-title">我的班级</text>
            <text class="card-desc">挖掘更多学习资源</text>
          </view>
        </view>
        <view
          class="button-card set-card"
          @click="handleSetClick"
          :style="{
            'background-image': 'url(' + sheep.$url.static('/uniapp/my/student-set.png') + ')',
          }"
        >
          <view class="card-content">
            <text class="card-title">我的学习集</text>
            <text class="card-desc">创建优质学习集</text>
          </view>
        </view>
      </view>
    </template>

    <!-- 设置列表 -->
    <view class="settings-list">
  <!--<view class="settings-item"  @tap="sheep.$router.go('/pages/index/language-select')">
        <view class="left">
          <image
            class="function-icon"
            mode="widthFix"
            :src="sheep.$url.static('/uniapp/my/shoucang.png')"
          />
          <text>收藏夹</text>
        </view>
        <image class="arrow" mode="widthFix" :src="sheep.$url.static('/uniapp/my/come2.png')" />
      </view>-->

      <view class="settings-item" @tap="sheep.$helper.inDevMsg()">
        <view class="left">
          <image
            class="function-icon"
            mode="widthFix"
            :src="sheep.$url.static('/uniapp/my/chengjiu.png')"
          />
          <text>我的成就</text>
        </view>
        <image class="arrow" mode="widthFix" :src="sheep.$url.static('/uniapp/my/come2.png')" />
      </view>
      <view class="settings-item" @tap="sheep.$helper.inDevMsg()">
        <view class="left">
          <image
            class="function-icon"
            mode="widthFix"
            :src="sheep.$url.static('/uniapp/my/yeguo.png')"
          />
          <text>我的椰果</text>
        </view>
        <view class="right">
          <image class="arrow" mode="widthFix" :src="sheep.$url.static('/uniapp/my/come2.png')" />
        </view>
      </view>

      <view class="settings-item" @tap="goToLanguageSettings">
        <view class="left">
          <image
            class="function-icon"
            mode="widthFix"
            :src="sheep.$url.static('/uniapp/my/shoucang.png')"
          />
          <text>语言设置</text>
        </view>
        <view class="right">
          <text class="language-name" v-if="selectedLanguage">{{selectedLanguage.name}}</text>
          <image class="arrow" mode="widthFix" :src="sheep.$url.static('/uniapp/my/come2.png')" />
        </view>
      </view>

      <!-- <view class="settings-item" @tap="sheep.$router.go('/pages/chat/index')"> -->
      <view class="settings-item" @tap="openContactModal">
        <view class="left">
          <image
            class="function-icon"
            mode="widthFix"
            :src="sheep.$url.static('/uniapp/my/kefu.png')"
          />
          <text>客服中心</text>
        </view>
        <image class="arrow" mode="widthFix" :src="sheep.$url.static('/uniapp/my/come2.png')" />
      </view>
      <view
        class="settings-item"
        @tap="sheep.$router.go('/pages/public/richtext', { title: '关于我们' })"
      >
        <view class="left">
          <image
            class="function-icon"
            mode="widthFix"
            :src="sheep.$url.static('/uniapp/my/guanyu.png')"
          />
          <text>关于我们</text>
        </view>
        <image
          class="arrow"
          mode="widthFix"
          :src="sheep.$url.static('/uniapp/my/come2.png')"
        />
      </view>
      <view v-if="isLogin" class="settings-item logout" @tap="handleLogout">
        <view class="left">
          <image
            class="function-icon"
            mode="widthFix"
            :src="sheep.$url.static('/uniapp/my/tuichu.png')"
          />
          <text>退出登录</text>
        </view>
        <image
          class="arrow"
          mode="widthFix"
          :src="sheep.$url.static('/uniapp/my/come2.png')"
        />
      </view>
    </view>

    <!-- 底部导航 -->
    <!-- <bottom-nav :currentTab="4" /> -->

    <!-- 客服弹窗 - 确认此部分已存在 -->
    <su-popup ref="contactPopupRef" type="center" :is-mask-click="true">
      <view class="contact-popup-content">
        <view class="popup-title">联系客服</view>
        <view class="popup-desc">点击下方按钮，开始与客服沟通</view>
        <button
          open-type="contact"
          bindcontact="handleContact"
          session-from="user_profile_page_popup"
          class="popup-confirm-btn"
          @tap="closeContactModal"
        >
          联系客服
        </button>
        <view class="popup-close-btn" @tap="closeContactModal">取消</view>
      </view>
    </su-popup>
  </view>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { onShow } from '@dcloudio/uni-app';
  import { showAuthModal } from '@/sheep/hooks/useModal';
  import sheep from '@/sheep';
  import GroupApi from '@/sheep/api/group/index';

  // 背景图片
  const headerBg = sheep.$url.css('/uniapp/my/my-bg.png');
  const cardBg = sheep.$url.css('/uniapp/my/award.png');

  // 当前选择的语言
  const selectedLanguage = ref(null);

  // 处理页面滚动到底部的事件
  function handleReachBottom() {
    console.log('我的页面触发了滚动到底部事件');
    // 我的页面的滚动到底部逻辑，可以根据需要添加
  }

  // 用户信息和登录状态
  const userInfo = ref({});
  const isLogin = computed(() => sheep.$store('user').isLogin);

  const getUserInfo = async () => {
    userInfo.value = await sheep.$store('user').getInfo();
  };

  // 获取当前选择的语言
  const getSelectedLanguage = () => {
    const savedLanguage = uni.getStorageSync('selected_language');
    if (savedLanguage) {
      selectedLanguage.value = savedLanguage;
    }
  };

  // 跳转到语言设置页面
  const goToLanguageSettings = () => {
    // 设置标记，表示从编辑页面进入语言选择
    uni.setStorageSync('from_language_edit', true);
    sheep.$router.go('/pages/index/language-select');
  };

  // 点击学习集
  const handleSetClick = () => {
    sheep.$router.go('/pages/set/list');
  };

  // 点击课程
  const goToCourseDetail = () => {
    sheep.$router.go('/pages/course/index');
  };

  // 页面生命周期
  onMounted(() => {
    getSelectedLanguage();
  });

  onShow(() => {
    // 每次进入页面时都刷新用户数据
    if (isLogin.value) {
      getUserInfo();
    }
    // 每次显示页面时都刷新语言设置
    // getSelectedLanguage();
  });

  // 登录相关方法
  const handleLogin = () => {
    if (isLogin.value) {
      return;
    }
    showAuthModal();
  };

  // 客服弹窗 Ref
  const contactPopupRef = ref(null); // 确保已定义 ref

  // 打开客服弹窗 - 新增方法
  const openContactModal = () => {
    if (contactPopupRef.value) {
      contactPopupRef.value.open();
    }
  };

  // 关闭客服弹窗 - 确认或新增方法
  const closeContactModal = () => {
    if (contactPopupRef.value) {
      contactPopupRef.value.close();
    }
  };

  // 微信客服回调 - 确认或新增方法
  const handleContact = (e) => {
    console.log('客服回调:', e.detail);
    // 可以在这里添加联系客服后的逻辑
  };

  // 退出登录处理 - 确认或新增方法
  const handleLogout = () => {
    uni.showModal({
      title: '提示',
      content: '确定退出登录吗？',
      success: async (res) => {
        if (res.confirm) {
          await sheep.$store('user').logout();
          // 刷新页面或跳转到登录页等操作
          sheep.$router.go('/pages/index/index'); // 或者其他目标页面
        }
      },
    });
  };

  // 跳转到第一个班级的详情页面
  const goToFirstClassDetail = async () => {
    // 先检查本地存储中是否有最后访问的班级ID
    const lastGroupId = uni.getStorageSync('last_group_id');
    if (lastGroupId) {
      try {
        // 验证班级是否仍然有效
        const result = await GroupApi.getGroup(lastGroupId);
        if (result.code === 0) {
          // 班级有效，直接跳转
          sheep.$router.go(`/pages/group/index?id=${lastGroupId}`);
          return;
        } else {
          // 班级无效，清除缓存
          uni.removeStorageSync('last_group_id');
        }
      } catch (error) {
        // 请求失败，清除缓存
        uni.removeStorageSync('last_group_id');
      }
    }

    // 如果没有有效的缓存班级，则获取班级列表
    // 先获取我加入的班级列表
    const joinedRes = await GroupApi.getMyJoinedGroups();

    // 如果有加入的班级，跳转到第一个班级
    if (joinedRes.data && joinedRes.data.length > 0) {
      const firstClass = joinedRes.data[0];
      uni.setStorageSync('last_group_id', firstClass.id);
      sheep.$router.go('/pages/group/index?id=' + firstClass.id);
      return;
    }

    // 如果没有加入的班级，获取我创建的班级列表
    const createdRes = await GroupApi.getMyCreatedGroups();

    // 如果有创建的班级，跳转到第一个班级
    if (createdRes.data && createdRes.data.length > 0) {
      const firstClass = createdRes.data[0];
      uni.setStorageSync('last_group_id', firstClass.id);
      sheep.$router.go(`/pages/group/index?id=` + firstClass.id);
      return;
    }

    // 如果既没有加入的班级也没有创建的班级，显示弹出框询问是否创建班级
    uni.showModal({
      title: '暂无班级',
      content: '您还没有加入或创建任何班级，是否立即创建班级？',
      confirmText: '创建班级',
      cancelText: '取消创建',
      success: ({ confirm }) => {
        if (confirm) {
          // 用户点击"创建班级"，跳转到班级创建页
          sheep.$router.go('/pages/group/add');
        }
      },
    });
  };

  // 暴露给父组件的方法
  defineExpose({
    // 标签页激活时调用
    onTabActivated(isFirstActivation) {
      // 每次激活都需要更新用户信息，因为可能在其他页面登录/登出
      if (isLogin.value) {
        getUserInfo();
      }
    },

    // 标签页停用时调用
    onTabDeactivated() {
      // console.log('我的页面被停用');
    },

    // 页面显示时调用
    onPageShow() {
      // console.log('我的页面所在的页面显示');
      // 页面重新显示时可能需要刷新数据，根据实际情况决定
    },

    // 页面隐藏时调用
    onPageHide() {
      // console.log('我的页面所在的页面隐藏');
    },

    // 处理页面滚动到底部的事件
    handleReachBottom,
  });
</script>

<style lang="scss" scoped>
  // 变量定义
  $primary-color: #fff;
  $secondary-color: #222;
  $background-color: #f6f6f6;
  $border-color: #e5e5e5;
  $border-color-light: #eee;
  $warning-color: #ff4d4f;
  $grey-color: #b0b0b0;
  $orange-color: #ff710c;
  $blue-color: #2196f3;

  $font-size-xs: 20rpx;
  $font-size-sm: 24rpx;
  $font-size-base: 28rpx;
  $font-size-md: 32rpx;
  $font-size-lg: 36rpx;

  $spacing-base: 10rpx;
  $spacing-sm: 16rpx;
  $spacing-md: 20rpx;
  $spacing-lg: 30rpx;
  $spacing-xl: 40rpx;

  // 主页面样式
  .user-page {
    background-color: $background-color;
    height: 100%;
    overflow: visible; /* 确保不裁剪子元素的阴影 */
  }

  // 头部区域
  .header {
    background: v-bind(headerBg) center/cover no-repeat;
    padding: 60rpx $spacing-md 0;
    height: 418rpx;
    width: 750rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        margin-right: $spacing-md;
      }

      .user-details {
        .username {
          color: $primary-color;
          font-size: $font-size-lg;
          font-weight: bold;
        }

        .username-wrapper {
          display: flex;
          align-items: center;
        }

        .user-role-icon {
          width: 38rpx;
          height: 38rpx;
          margin-left: 10rpx;
        }

        .user-certified-badge {
          display: flex;
          align-items: center;
          width: fit-content;
          position: relative;
          background: linear-gradient(#ffd16b, #ffd5b7);
          border-radius: 22rpx;
          padding: 2rpx 14rpx 2rpx 10rpx;
          margin-top: $spacing-sm;
          margin-left: 12rpx;

          .icon-container {
            width: 28rpx;
            height: 28rpx;
            position: relative;

            .certified-icon {
              width: 100%;
              height: 100%;
              position: absolute;
              left: -12rpx;
              transform: scale(1.8);
              transform-origin: center;
            }
          }

          .certified-text {
            font-size: $font-size-sm;
            font-family: PingFang-SC-Medium;
            color: #ee7600;
            padding-bottom: 1rpx;
          }
        }
      }
    }

    .edit-profile {
      background-color: rgba(255, 255, 255, 0.2);
      padding: $spacing-base $spacing-md;
      border-radius: 30rpx;

      text {
        color: $primary-color;
        font-size: $font-size-base;
      }
    }
  }

  // 成就卡片
  .stats-card {
    position: relative;
    background: v-bind(cardBg) center/cover no-repeat;
    margin: 0 auto;
    padding: $spacing-lg $spacing-xl;
    border-radius: 20rpx;
    color: #f8f8f8;
    width: 680rpx;
    height: 224rpx;
    top: -54rpx;
    box-sizing: border-box;

    .stats-title {
      font-size: $font-size-lg;
      font-weight: bold;
    }

    .stats-desc {
      font-size: $font-size-base;
      margin: $spacing-sm 0;
    }

    .stats-action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: $orange-color;

      .action-text {
        background-color: $primary-color;
        padding: $spacing-base $spacing-md;
        border-radius: 30rpx;
        font-size: $font-size-base;
      }
    }

    .award {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 50%;
      right: $spacing-xl;
      transform: translate(0%, -50%);

      .award-icon {
        width: 100rpx;
        margin-bottom: $spacing-base;
      }

      .award-text {
        font-size: $font-size-sm;
        color: $primary-color;
      }
    }
  }

  // 功能按钮
  .action-buttons {
    display: flex;
    justify-content: space-between;
    padding: $spacing-md $spacing-lg;
    background-color: $background-color;
    position: relative;
    top: -50rpx;
    flex-wrap: wrap;

    .button-card {
      display: flex;
      align-items: center;
      width: 342rpx;
      height: 125rpx;
      border-radius: 16rpx;
      box-sizing: border-box;
      padding-left: 155rpx;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .card-content {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .card-title {
        font-size: $font-size-base;
        color: $secondary-color;
        font-weight: normal;
        margin-bottom: 6rpx;
      }

      .card-desc {
        font-size: $font-size-xs;
        color: $grey-color;
      }
    }
  }

  // 认证教师功能按钮
  .teacher-action-buttons {
    display: flex;
    justify-content: space-between;
    background-color: $background-color;
    padding: 0 30rpx;
    position: relative;
    top: -50rpx;

    .teacher-button-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 22.5rpx;

      .teacher-icon {
        width: 127rpx;
        height: 126rpx;
      }

      .teacher-label {
        position: relative;
        top: -22rpx;
        font-size: $font-size-base;
        color: $secondary-color;
      }
    }
  }

  // 设置列表
  .settings-list {
    width: 692rpx;
    height: auto; /* 改为自适应高度 */
    //min-height: 540rpx; /* 设置最小高度 */
    box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211, 223, 230, 0.8);
    border-radius: 25rpx;
    margin: 0 auto;
    margin-bottom: 20rpx; /* 添加底部外边距 */
    position: relative;
    top: -30rpx;
    z-index: 99;
    overflow: hidden;

    .settings-item {
      height: 111rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: $background-color;
      padding: 0 49rpx;

      .left {
        display: flex;
        align-items: center;

        .function-icon {
          width: 30rpx;
          position: relative;
          margin-right: 18rpx;
        }

        text {
          font-size: $font-size-base;
          color: $secondary-color;
          vertical-align: text-bottom;
        }
      }

      .right {
        display: flex;
        align-items: center;

        .integral-num {
          font-size: $font-size-xs;
          color: $grey-color;
          margin-right: 8rpx;
          position: relative;
          top: -1px;
        }

        .language-name {
          font-size: $font-size-sm;
          color: $blue-color;
          margin-right: 10rpx;
        }
      }

      .arrow {
        width: 15rpx;
      }

      &:first-child .function-icon {
        top: 0rpx;
      }
    }
  }

  .contact-button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font-size: inherit;
    color: inherit;
    line-height: inherit;
    text-align: left;
  }

  // 客服弹窗样式 - 确认或新增
  .contact-popup-content {
    width: 600rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .popup-title {
      font-size: $font-size-lg;
      color: $secondary-color;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .popup-desc {
      font-size: $font-size-base;
      color: $grey-color;
      margin-bottom: 40rpx;
      text-align: center;
    }

    .popup-confirm-btn {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      background: linear-gradient(90deg, #ff8a46 0%, #ff5e41 100%);
      color: #fff;
      font-size: 30rpx;
      text-align: center;
      margin-bottom: 20rpx;
      border: none; /* 移除默认边框 */
      padding: 0; /* 移除默认内边距 */
      &::after {
        border: none; /* 移除伪元素边框 */
      }
    }

    .popup-close-btn {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      background-color: #f5f5f5;
      color: #666;
      font-size: 30rpx;
      text-align: center;
    }
  }
</style>
