<template>
  <view class="chapter-list">
    <!-- 空数据状态 -->
    <s-empty v-if="unitList.length === 0" text="暂无章节数据" />
    
    <!-- 章节列表 -->
    <view v-else class="list-content">
      <!-- 单元列表 -->
      <view v-for="(unit, unitIndex) in unitList" :key="unitIndex" class="unit-item">
        <!-- 单元标题 -->
        <view class="unit-header" @click="toggleUnit(unitIndex)">
          <view class="unit-title">
            <text class="unit-name">{{ unitIndex + 1 }} {{ unit.title }}</text>
          </view>
          <view class="unit-arrow">
            <image 
              :src="unit.open ? sheep.$url.cdn('/group/unfold-up.png') : sheep.$url.cdn('/group/unfold-down.png')"
              class="arrow-icon"
            />
          </view>
        </view>
        
        <!-- 课时列表 -->
        <view class="chapter-container" v-if="unit.open">
          <view 
            v-for="(chapter, chapterIndex) in unit.chapters" 
            :key="chapterIndex"
            class="chapter-item"
            @click="navToChapterDetail(chapter)"
          >
            <!-- 左侧状态圆点 -->
            <view class="chapter-status">
              <view class="status-dot" :class="{'completed': chapter.completed}"></view>
            </view>
            
            <!-- 课时信息 -->
            <view class="chapter-info">
              <view class="chapter-title-row">
                <text class="chapter-title">{{ (unitIndex + 1) + '.' + (chapterIndex + 1) }} {{ chapter.title }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import sheep from '@/sheep';
import LessonChapterApi from '@/sheep/api/course/lessonchapter';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  },
  keyword: {
    type: String,
    default: ''
  },
  groupId: {
    type: [String, Number],
    default: null
  }
});

// 单元列表数据
const unitList = ref([]);

// 加载章节数据
const loadChapters = async () => {
  // 使用父章节分页API获取顶级章节
  const res = await LessonChapterApi.getParentLessonChapterPage({
    courseId: props.courseId,
    pageSize: 100 // 确保获取所有章节
  });

  if (res.code === 0 && res.data && res.data.list && res.data.list.length > 0) {
    // 保存顶级章节数据
    const parentChapters = res.data.list;

    // 格式化顶级章节为单元
    formatChaptersIntoUnits(parentChapters);
  } else {
    unitList.value = [];
  }
};

// 将章节数据格式化为单元
const formatChaptersIntoUnits = (parentChapters) => {
  const formattedUnits = [];
  
  // 对顶级章节进行排序
  parentChapters.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  
  // 将每个顶级章节作为一个单元，默认为折叠状态
  parentChapters.forEach((parentChapter) => {
    formattedUnits.push({
      id: parentChapter.id,
      title: parentChapter.name, // 使用name字段作为标题
      open: false, // 默认为折叠状态
      chapters: [] // 初始化为空数组，后续按需加载
    });
  });

  // 如果有搜索关键词，过滤单元
  if (props.keyword) {
    const keyword = props.keyword.toLowerCase();
    const filteredUnits = formattedUnits.filter(unit => 
      unit.title.toLowerCase().includes(keyword)
    );
    unitList.value = filteredUnits;
  } else {
    unitList.value = formattedUnits;
  }
  
  // 如果只有一个单元，默认展开
  if (unitList.value.length === 1) {
    toggleUnit(0);
  }
};

// 加载单元的子章节
const loadUnitChapters = async (parentId, unitIndex) => {
  // 使用子章节列表API获取子章节，传递groupId参数
  const res = await LessonChapterApi.getChildLessonChapterListByParentId(parentId, props.groupId);

  if (res.code === 0 && res.data) {
    const childChapters = res.data;

    // 对子章节进行排序
    childChapters.sort((a, b) => (a.sort || 0) - (b.sort || 0));

    // 格式化子章节数据
    const formattedChildren = childChapters.map(child => ({
      id: child.id,
      title: child.name, // 使用name字段作为标题
      completed: child.status === 1, // 状态为1表示已完成
      parentId: child.parentId,
      parentName: child.parentName
    }));

    // 更新对应单元的子章节数据
    if (unitList.value[unitIndex]) {
      unitList.value[unitIndex].chapters = formattedChildren;
    }
  }
};

// 切换单元的展开/折叠状态
const toggleUnit = (index) => {
  // 切换当前单元的展开状态
  unitList.value[index].open = !unitList.value[index].open;
  
  // 如果是展开状态且该单元的子章节为空，则加载子章节
  if (unitList.value[index].open && unitList.value[index].chapters.length === 0) {
    loadUnitChapters(unitList.value[index].id, index);
  }
};

// 导航到章节详情
const navToChapterDetail = (chapter) => {
  const url = `/pages/course/chapter-detail?id=${chapter.id}`;
  if (props.groupId) {
    sheep.$router.go(`${url}&groupId=${props.groupId}`);
  } else {
    sheep.$router.go(url);
  }
};

// 监听关键词变化
watch(() => props.keyword, () => {
  if (props.keyword) {
    // 使用搜索API进行搜索
    searchChapters();
  } else {
    // 没有关键词时加载全部章节
    loadChapters();
  }
});

// 使用搜索API搜索章节
const searchChapters = async () => {
  if (!props.keyword || !props.courseId) return;
  const res = await LessonChapterApi.searchLessonChapters(props.keyword, props.courseId);

  if (res.code === 0 && res.data) {
    // 分离父章节和子章节
    const parentChapters = res.data.filter(chapter => !chapter.parentId);
    const childChapters = res.data.filter(chapter => chapter.parentId);

    // 格式化父章节为单元
    const formattedUnits = parentChapters.map(parentChapter => ({
      id: parentChapter.id,
      title: parentChapter.name,
      open: false, // 搜索结果默认为折叠状态
      chapters: childChapters
        .filter(child => child.parentId === parentChapter.id)
        .map(child => ({
          id: child.id,
          title: child.name,
          completed: child.status === 1,
          parentId: child.parentId,
          parentName: child.parentName
        }))
    }));

    unitList.value = formattedUnits;
  } else {
    unitList.value = [];
  }
};

// 监听课程ID变化
watch(() => props.courseId, () => {
  loadChapters();
}, { immediate: true });

</script>

<style scoped lang="scss">
.chapter-list {
  width: 100%;
}

.list-content {
  border-radius: 12rpx;
  padding: 0;
}

/* 单元样式 */
.unit-item {
  margin-bottom: 20rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.unit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #FFFFFF;
  height: 80rpx;
}

.unit-title {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-start;
  height: 100%;
}

.unit-name {
  width: auto;
  min-width: 140rpx;
  height: 28rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #222222;
  line-height: 28rpx;
  text-align: left;
}

.unit-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 23rpx;
  height: 14rpx;
  transition: transform 0.3s;
}

/* 章节容器 */
.chapter-container {
  position: relative;
  top: -30rpx;
  padding: 10rpx 0 20rpx;
}

/* 章节样式 */
.chapter-item {
  display: flex;
  align-items: center;
  padding: 36rpx 30rpx 36rpx 90rpx;
  position: relative;
}

.chapter-status {
  position: absolute;
  left: 60rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 20rpx;
  height: 20rpx;
  z-index: 2;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #CCCCCC;
  
  &.completed {
    background-color: #0FD7AA;
  }
}

/* 章节连接线 */
.chapter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 68rpx;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #CCCCCC;
  z-index: 1;
}

.chapter-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.chapter-title-row {
  display: flex;
  align-items: center;
}

.chapter-title {
  width: 100%;
  height: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 25rpx;
  color: #222222;
  line-height: 24rpx;
}
</style>