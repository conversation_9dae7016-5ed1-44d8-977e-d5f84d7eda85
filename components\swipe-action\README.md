# SwipeAction 滑动操作组件

一个通用的左滑显示操作按钮的组件，适用于列表项的快捷操作。

## 特性

- 支持自定义多个操作按钮
- 每个按钮可自定义颜色、宽度和文本
- 提供滑动阈值判断，优化用户体验
- 支持禁用滑动功能
- 支持按钮点击后自动关闭
- 暴露外部方法控制开关状态

## 使用方法

### 基础用法

```vue
<template>
  <swipe-action
    :buttons="[{ text: '删除', backgroundColor: '#ff5252', width: 150 }]"
    @button-click="handleButtonClick"
  >
    <view class="custom-item">
      <!-- 自定义内容 -->
      <text>列表项内容</text>
    </view>
  </swipe-action>
</template>

<script setup>
import SwipeAction from '@/components/swipe-action';

const handleButtonClick = (event) => {
  const { button, index } = event;
  console.log('点击了按钮:', button.text);
};
</script>
```

### 多按钮用法

```vue
<template>
  <swipe-action
    :buttons="buttons"
    @button-click="handleButtonClick"
  >
    <view class="custom-item">
      <!-- 自定义内容 -->
      <text>列表项内容</text>
    </view>
  </swipe-action>
</template>

<script setup>
import SwipeAction from '@/components/swipe-action';

const buttons = [
  { text: '标记', backgroundColor: '#3c9cff', width: 120 },
  { text: '编辑', backgroundColor: '#f9ae3d', width: 120 },
  { text: '删除', backgroundColor: '#fa3534', width: 120 }
];

const handleButtonClick = (event) => {
  const { button, index } = event;
  console.log('点击了按钮:', button.text, '索引:', index);
};
</script>
```

### 禁用滑动

```vue
<template>
  <swipe-action
    :disabled="true"
    :buttons="[{ text: '删除', backgroundColor: '#ff5252', width: 150 }]"
  >
    <view class="custom-item">
      <text>禁用滑动的列表项</text>
    </view>
  </swipe-action>
</template>
```

### 使用组件方法控制状态

```vue
<template>
  <swipe-action
    ref="swipeActionRef"
    :buttons="buttons"
    @button-click="handleButtonClick"
  >
    <view class="custom-item">
      <text>列表项内容</text>
    </view>
  </swipe-action>
  
  <button @click="openSwipeAction">打开滑动菜单</button>
  <button @click="closeSwipeAction">关闭滑动菜单</button>
</template>

<script setup>
import { ref } from 'vue';
import SwipeAction from '@/components/swipe-action';

const swipeActionRef = ref(null);
const buttons = [
  { text: '编辑', backgroundColor: '#f9ae3d', width: 120 },
  { text: '删除', backgroundColor: '#fa3534', width: 120 }
];

const openSwipeAction = () => {
  swipeActionRef.value.open();
};

const closeSwipeAction = () => {
  swipeActionRef.value.close();
};

const handleButtonClick = (event) => {
  const { button, index } = event;
  console.log('点击了按钮:', button.text);
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| buttons | 操作按钮配置数组 | Array | [] |
| disabled | 是否禁用滑动 | Boolean | false |
| autoClose | 点击按钮后是否自动关闭 | Boolean | true |

### 按钮配置项

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| text | 按钮文本 | String | - |
| backgroundColor | 按钮背景色 | String | #ff5252 |
| color | 按钮文字颜色 | String | #ffffff |
| width | 按钮宽度（rpx） | Number | 150 |

### 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| button-click | 点击操作按钮时触发 | { button, index } |

### 方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开滑动菜单，显示操作按钮 | - |
| close | 关闭滑动菜单，隐藏操作按钮 | - |

### 插槽

| 名称 | 说明 |
| --- | --- |
| default | 自定义内容区域 |

## 注意事项

1. 请确保传入的buttons数组格式正确，每个按钮对象都应包含必要的属性。
2. 内容区域可以通过插槽自定义，以适应不同的UI需求。
3. 滑动操作组件会自动处理滑动手势和动画效果。
4. 如需在多个列表项中只允许一个处于打开状态，请在外部逻辑中实现该控制。 