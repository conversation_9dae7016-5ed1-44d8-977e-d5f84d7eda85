import request from '@/sheep/request';

const ConversationApi = {
  // 创建会话
  createConversation: (modelID) => {
    return request({
      url: '/ai/thai-chat/create-conversation',
      method: 'POST',
      params: {
        modelID,
      },
    });
  },

  // 根据模型名创建会话
  createConversationByName: (modelName) => {
    return request({
      url: '/ai/thai-chat/create-conversation-byName',
      method: 'POST',
      params: {
        modelName,
      },
      custom: {
        showLoading: false,
      }
    });
  },

  // 获取对话分页
  getConversationPage: ({pageNo, pageSize, userId, title, createTime}) => {
    return request({
      url: '/ai/thai-chat/page',
      method: 'GET',
      params: {
        pageNo,
        pageSize,
        userId,
        title,
        createTime
      },
      custom: {
        showLoading: false,
      }
    });
  },

  // 根据模型名称获取对话分页
  getConversationPageByModelName: ({pageNo, pageSize, userId, title, modelName, createTime}) => {
    return request({
      url: '/ai/thai-chat/page-by-model-name',
      method: 'GET',
      params: {
        pageNo,
        pageSize,
        userId,
        title,
        modelName,
        createTime
      },
      custom: {
        showLoading: false,
      }
    });
  },

  // 更新聊天对话
  updateConversation: ({id, title, pinned, modelId, knowledgeId, systemMessage, temperature, maxTokens, maxContexts}) => {
    return request({
      url: '/ai/thai-chat/update',
      method: 'PUT',
      data: {
        id,          // 对话编号（必填）
        title,       // 对话标题（可选）
        pinned,      // 是否置顶（可选）
        modelId,     // 模型编号（可选）
        knowledgeId, // 知识库编号（可选）
        systemMessage, // 角色设定（可选）
        temperature, // 温度参数（可选）
        maxTokens,   // 单条回复的最大Token数量（可选）
        maxContexts  // 上下文的最大Message数量（可选）
      },
      custom: {
        loadingMsg: '更新中...',
      }
    });
  },

  // 删除AI对话
  deleteConversation: (id) => {
    return request({
      url: '/ai/thai-chat/delete-conversation',
      method: 'POST',
      params: {
        id  // 对话编号（必填）
      },
      custom: {
        loadingMsg: '删除中...',
        showSuccess: true,
        successMsg: '删除成功'
      }
    });
  },

  // 删除单条历史记录
  deleteHistory: (id) => {
    return request({
      url: '/ai/thai-chat/delete-message',
      method: 'DELETE',
      params: {
        id, // 消息编号（必填）
      },
      custom: {
        loadingMsg: '删除中...',
        showSuccess: true,
        successMsg: '删除成功',
      },
    });
  }
};

export default ConversationApi;
