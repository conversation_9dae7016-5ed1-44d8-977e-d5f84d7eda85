<template>
  <view class="lesson-detail-page">
    <!-- 顶部导航 -->
    <course-navbar 
      :title="lessonDetail?.title || '课程详情'" 
      placeholder="搜一搜"
      @search="handleSearch"
      @clear="resetSearch"
    />

    <!-- 标签页 -->
    <view class="tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-item" 
        :class="{ active: activeTab === index }"
        @click="changeTab(index)"
      >
        <text>{{ tab.name }}</text>
      </view>
    </view>
    
    <!-- 内容区域 - 使用条件渲染替代swiper -->
    <view class="content-container">
      <!-- 章节 -->
      <scroll-view v-if="tabs[activeTab]?.component === 'chapter-list'" scroll-y class="scroll-container">
        <view class="component-container">
          <chapter-list 
            v-if="courseId" 
            :course-id="courseId"
            :keyword="searchKeyword"
            :groupId="groupId"
          />
        </view>
      </scroll-view>
      
      <!-- 作业 -->
      <scroll-view v-if="tabs[activeTab]?.component === 'assignment-list'" scroll-y class="scroll-container">
        <view class="component-container">
          <assignment-list
            v-if="courseId"
            :course-id="courseId"
            :keyword="searchKeyword"
            :groupId="groupId"
            :isCreator="isCreator"
          />
        </view>
      </scroll-view>
      
      <!-- 资料 -->
      <scroll-view v-if="tabs[activeTab]?.component === 'document-list'" scroll-y class="scroll-container">
        <view class="component-container">
          <document-list 
            v-if="courseId" 
            :course-id="courseId"
            :keyword="searchKeyword"
            :groupId="groupId"
          />
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
  import { ref, reactive, onMounted, nextTick } from 'vue';
import sheep from '@/sheep';
import LessonApi from '@/sheep/api/course/lesson';
import ChapterList from '@/pages/group/detail/course/chapter-list/chapter-list.vue';
import AssignmentList from '@/pages/group/detail/course/assignment-list/assignment-list.vue';
import DocumentList from '@/pages/group/detail/course/document-list/document-list.vue';
import CourseNavbar from '@/components/course-navbar/course-navbar.vue';
import { onLoad } from '@dcloudio/uni-app';
import { WxaSubscribeTemplate } from '@/sheep/util/const';

// uni-app 全局类型
const getCurrentPages = () => [];

// 课程ID
const courseId = ref(null);
// 课程详情
const lessonDetail = ref({});
// 搜索关键词
const searchKeyword = ref('');
// 群组ID
const groupId = ref(null);
// 是否是游客模式（从社区页面进入）
const isVisitor = ref(false);
// 是否是创建者
const isCreator = ref(false);

// 标签页定义
const tabs = reactive([
  { name: '作业', component: 'assignment-list' },
  { name: '章节', component: 'chapter-list' },
  { name: '资料', component: 'document-list' }
]);

// 当前激活的标签页
const activeTab = ref(0);

// onLoad生命周期，在页面加载时获取参数
onLoad((options) => {
  if (options) {
    courseId.value = options.id;
    groupId.value = options.groupId;
    
    // 获取创建者标志
    if (options.isCreator === 'true') {
      isCreator.value = true;
    }
    
    // 检查是否是从社区页面进入（游客模式）
    if (options.isVisitor === 'true') {
      isVisitor.value = true;
      
      // 重新初始化标签页，移除作业选项
      tabs.splice(0, tabs.length);
      tabs.push({ name: '章节', component: 'chapter-list' });
      tabs.push({ name: '资料', component: 'document-list' });
    }
    
    // 如果有tab参数，设置初始选项卡
    if (options.tab !== undefined) {
      const tabIndex = parseInt(options.tab);
      if (tabIndex >= 0 && tabIndex < tabs.length) {
        activeTab.value = tabIndex;
      }
    }
    
    // 获取课程详情
    getLessonDetail();
  }
});

// 切换标签页
const changeTab = async (index) => {
  activeTab.value = index;

  // 进入作业页面，学生弹出订阅消息提示
  if (tabs[index]?.component === 'assignment-list') {
    const { isCertified } = await sheep.$store('user').getInfo();
    if (isCertified) {
      return;
    }
    sheep.$platform.useProvider('wechat').subscribeMessage([WxaSubscribeTemplate.ASSIGNMENT_RELEASE, WxaSubscribeTemplate.ASSIGNMENT_SUBMISSION_REMINDER]);
  }
};

// 处理搜索
const handleSearch = (keyword) => {
  searchKeyword.value = keyword;
  // 对于章节和资料标签页，搜索逻辑已在各子组件中实现
};

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = '';
  // 重置搜索状态
};

// 获取URL参数 - 仅作为备用方法
const getParams = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.options) {
    courseId.value = currentPage.options.id;
    
    // 如果有tab参数，设置初始选项卡
    if (currentPage.options.tab !== undefined) {
      const tabIndex = parseInt(currentPage.options.tab);
      if (tabIndex >= 0 && tabIndex < tabs.length) {
        activeTab.value = tabIndex;
      }
    }
  }
};

// 获取课程详情
const getLessonDetail = async () => {
  if (!courseId.value) return;
  try {
    const res = await LessonApi.getLesson(courseId.value);
    if (res.code === 0 && res.data) {
      lessonDetail.value = res.data;
    }
  } catch (error) {
    console.error('获取课程详情出错:', error);
  }
};

// 页面加载完成
onMounted(() => {
  if (!courseId.value) {
    getParams();
    getLessonDetail();

    sheep.$platform.useProvider('wechat').load();
  }
});
</script>

<style scoped lang="scss">
.lesson-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F7FCFF;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background-color: #fff;
  padding: 15rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 0;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #777777;
  line-height: 71rpx;
}

.tab-item.active {
  font-weight: bold;
  color: #414141; 
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 72rpx;
  height: 14rpx;
  background: linear-gradient(91deg, rgba(97,246,255,0.89), rgba(18,157,237,0.89));
  border-radius: 7rpx;
  z-index: -1; /* 关键修改 */
}

/* 内容容器样式 */
.content-container {
  flex: 1;
  height: 0;
  position: relative;
}

.scroll-container {
  height: 100%;
}

.component-container {
  padding: 20rpx;
}
</style>
