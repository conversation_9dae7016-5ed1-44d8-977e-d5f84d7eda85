<template>
  <view class="answer-page">
    <!-- 背景图 -->
    <image class="bg-image" :src="sheep.$url.cdn('/group/blue-back.png')" mode="aspectFill"></image>

    <!-- 顶部导航 -->
    <easy-navbar :title="assignmentTitle" :transparent="true" />

    <!-- 问题内容 - 支持滑动 -->
    <view class="content-wrapper" v-show="questionList.length > 0">
      <swiper
        class="swiper-container"
        :current="currentIndex"
        @change="handleSwiperChange"
        :duration="300"
        :key="'swiper-' + refreshFlag"
      >
        <swiper-item v-for="(question, index) in questionList" :key="`question-${index}-${refreshFlag}`">
          <view class="question-container">
            <view class="question-type">
              <image class="question-type-bg" :src="getQuestionTypeIcon(question.questionType)" mode="aspectFill"></image>
              <text>{{getQuestionTypeName(question.questionType)}}</text>
            </view>

            <view class="question-progress">
              <text class="current-page">{{index + 1}}</text>
              <text class="total-page">/{{questionList.length}}</text>
            </view>

            <!-- 问题标题 -->
            <view class="question-title">
              <text>{{index + 1}}.{{question.questionStem}}</text>
            </view>

            <!-- 选项列表 - 单选题、多选题和判断题 -->
            <view class="options-list" v-if="question.questionType === 0 || question.questionType === 1 || question.questionType === 2">
              <view
                v-for="(option, optIdx) in getQuestionOptions(question)"
                :key="`option-${optIdx}-${refreshFlag}`"
                :class="['option-item', isOptionSelected(index, option.content) ? 'option-selected' : '']"
                @click="selectOption(index, option.content)"
              >
                <view class="option-circle">
                  <text>{{option.serial}}</text>
                </view>
                <text class="option-text">{{option.content}}</text>
              </view>
            </view>

            <!-- 填空题、简答题和互译题的输入框 -->
            <view class="text-input-container" v-if="question.questionType === 3 || question.questionType === 4 || question.questionType === 5">
              <textarea
                class="text-input"
                placeholder="请输入你的答案"
                v-model.lazy="textAnswers[index]"
                :maxlength="question.questionType === 4 ? -1 : 500"
                @blur="updateTextAnswer(index)"
              />
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 空数据状态 -->
    <s-empty v-if="questionList.length === 0" text="暂无题目" />

    <!-- 底部按钮 -->
    <view class="footer" v-if="questionList.length > 0">
      <button class="next-btn" @click="nextQuestion">
        {{ isLastQuestion ? '提交' : '下一题' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { onLoad, onUnload, onHide } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import AssignmentApi from '@/sheep/api/course/assignment';
import { QUESTION_TYPES, getJudgeOptions } from '@/pages/group/util/constants';

// 问题列表
const questionList = ref([]);
// 作业标题
const assignmentTitle = ref('作业详情');

// 参数
const courseId = ref('');
const assignmentId = ref('');

// 当前问题索引
const currentIndex = ref(0);
// 当前选择的选项（改为使用数组存储每道题的选择）
const selectedOptions = ref([]);
// 文本输入答案（用于填空题、简答题等）
const textAnswers = ref([]);
// 用户的答案
const userAnswers = ref([]);

// 初始化数据
const initAnswerData = () => {
  // 初始化每道题的选择和文本答案
  questionList.value.forEach((_, index) => {
    selectedOptions.value[index] = '';
    textAnswers.value[index] = '';
  });
};

// 获取作业题目
const getQuestionsByAssignmentId = async (id) => {
  const res = await AssignmentApi.viewAssignmentReleaseQuestion({
    assignmentReleaseId: id
  });

  if (res.code === 0 && res.data) {
    // 处理题目数据
    const processedQuestions = res.data.map(question => {
      // 如果有作业标题信息，设置作业标题
      if (question.assignmentTitle && !assignmentTitle.value) {
        assignmentTitle.value = question.assignmentTitle;
      }

      // 处理判断题，添加默认选项
      if (question.questionType === QUESTION_TYPES.JUDGE && (!question.optionList || question.optionList.length === 0)) {
        question.optionList = getJudgeOptions();
      }
      return question;
    });

    questionList.value = processedQuestions;
    // 初始化答案数据
    initAnswerData();

    // 如果没有获取到标题，尝试从第一题获取
    if (processedQuestions.length > 0 && processedQuestions[0].assignmentName) {
      assignmentTitle.value = processedQuestions[0].assignmentName;
    }

    // 处理历史答案
    loadHistoricalAnswers(processedQuestions);
  } else {
    sheep.$helper.toast('获取题目失败');
    questionList.value = [];
  }
};

// 当前问题
const currentQuestion = computed(() => {
  return questionList.value[currentIndex.value] || {};
});

// 是否为最后一题
const isLastQuestion = computed(() => {
  return currentIndex.value === questionList.value.length - 1;
});

// 问题类型
const getQuestionTypeName = (type) => {
  switch (type) {
    case QUESTION_TYPES.JUDGE:
      return '判断题';
    case QUESTION_TYPES.SINGLE:
      return '单选题';
    case QUESTION_TYPES.MULTIPLE:
      return '多选题';
    case QUESTION_TYPES.FILL:
      return '填空题';
    case QUESTION_TYPES.SHORT:
      return '简答题';
    case QUESTION_TYPES.TRANSLATE:
      return '互译题';
    default:
      return '单选题';
  }
};

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    assignmentId.value = options.id;
  }
  if (options.courseId) {
    courseId.value = options.courseId;
  }
  // 如果URL中有标题参数
  if (options.title) {
    assignmentTitle.value = decodeURIComponent(options.title);
  }

  // 根据assignmentId获取真实题目数据
  if (assignmentId.value) {
    getQuestionsByAssignmentId(assignmentId.value);
    // 不再额外调用API获取标题，直接使用传入的标题
  }
});

// 添加标志跟踪是否正在提交最终答案
const isSubmittingFinal = ref(false);

// 判断某个选项是否被选中
const isOptionSelected = (questionIndex, value) => {
  const question = questionList.value[questionIndex];
  if (question && question.questionType === QUESTION_TYPES.MULTIPLE) { // 多选题特殊处理
    const selectedValues = selectedOptions.value[questionIndex] || '';
    if (selectedValues === value) return true;
    return selectedValues.split('|').includes(value);
  }
  // 其他题型直接比较
  return selectedOptions.value[questionIndex] === value;
};

// 选择选项
const selectOption = (questionIndex, value) => {
  const question = questionList.value[questionIndex];

  // 多选题特殊处理（支持多选）
  if (question.questionType === QUESTION_TYPES.MULTIPLE) {
    // 如果已经选择了该选项，则取消选择
    if (selectedOptions.value[questionIndex] && selectedOptions.value[questionIndex].includes(value)) {
      const options = selectedOptions.value[questionIndex].split('|');
      const index = options.indexOf(value);
      if (index > -1) {
        options.splice(index, 1);
      }
      selectedOptions.value[questionIndex] = options.join('|');
    }
    // 如果未选择，则添加选择
    else {
      if (!selectedOptions.value[questionIndex]) {
        selectedOptions.value[questionIndex] = value;
      } else {
        selectedOptions.value[questionIndex] += `|${value}`;
      }
    }
  }
  // 单选题和判断题（只能选一个）
  else {
    selectedOptions.value[questionIndex] = value;
  }
};

// 处理滑动切换
const handleSwiperChange = (e) => {
  currentIndex.value = e.detail.current;
};

// 下一题
const nextQuestion = () => {
  // 保存当前题目的答案
  saveCurrentAnswer();

  // 判断是否是最后一题
  if (currentIndex.value < questionList.value.length - 1) {
    // 进入下一题
    currentIndex.value++;
  } else {
    // 提交答案
    submitAnswers();
  }
};

// 保存当前题目的答案
const saveCurrentAnswer = () => {
  const question = questionList.value[currentIndex.value];

  if (question.questionType === QUESTION_TYPES.JUDGE || question.questionType === QUESTION_TYPES.SINGLE || question.questionType === QUESTION_TYPES.MULTIPLE) {
    if (selectedOptions.value[currentIndex.value]) {
      let answer = selectedOptions.value[currentIndex.value];
      // 对于选择题，需要将选项内容转换为对应的serial值
      answer = convertContentToSerial(question, answer);
      userAnswers.value[currentIndex.value] = {
        questionId: question.id,
        userAnswer: answer
      };
    }
  } else if (question.questionType === QUESTION_TYPES.FILL || question.questionType === QUESTION_TYPES.SHORT || question.questionType === QUESTION_TYPES.TRANSLATE) {
    // 填空题、简答题、互译题直接提交文字内容
    userAnswers.value[currentIndex.value] = {
      questionId: question.id,
      userAnswer: textAnswers.value[currentIndex.value]
    };
  }
};

// 提交答案
const submitAnswers = async () => {
  // 设置提交标志为true
  isSubmittingFinal.value = true;

  // 保存最后一题的答案
  saveCurrentAnswer();

  // 检查是否所有题目都已完成
  const uncompletedQuestions = [];
  questionList.value.forEach((question, index) => {
    let hasAnswer = false;

    if (question.questionType === QUESTION_TYPES.JUDGE || question.questionType === QUESTION_TYPES.SINGLE || question.questionType === QUESTION_TYPES.MULTIPLE) {
      // 选择题、判断题、多选题
      hasAnswer = selectedOptions.value[index] && selectedOptions.value[index].trim() !== '';
    } else if (question.questionType === QUESTION_TYPES.FILL || question.questionType === QUESTION_TYPES.SHORT || question.questionType === QUESTION_TYPES.TRANSLATE) {
      // 填空题、简答题、互译题
      hasAnswer = textAnswers.value[index] && textAnswers.value[index].trim() !== '';
    }

    if (!hasAnswer) {
      uncompletedQuestions.push(index + 1);
    }
  });

  // 如果有未完成的题目，提示用户并阻止提交
  if (uncompletedQuestions.length > 0) {
    sheep.$helper.toast('还有题目没有完成，请完成所有题目后再提交');
    isSubmittingFinal.value = false; // 重置提交标志
    return;
  }

  // 过滤掉空答案
  const validAnswers = userAnswers.value.filter(answer => answer && answer.questionId && answer.userAnswer);

  if (validAnswers.length === 0) {
    sheep.$helper.toast('您尚未回答任何题目');
    isSubmittingFinal.value = false; // 重置提交标志
    return;
  }

  // 构建提交数据
  const submitData = {
    courseId: courseId.value,
    assignmentReleaseId: assignmentId.value,
    questionAnswerItems: validAnswers,
    isSubmit: true // 最终提交
  };
  const res = await AssignmentApi.submitAssignmentAnswer(submitData);

  if (res.code === 0) {
    sheep.$helper.toast('提交成功');
    // 延迟后跳转到分数页面
    setTimeout(() => {
      sheep.$router.go(`/pages/group/detail/course/assignment-list/score?id=${assignmentId.value}&courseId=${courseId.value}&title=${encodeURIComponent(assignmentTitle.value)}`,{},{redirect: true});
    }, 1500);
  } else {
    sheep.$helper.toast(res.msg || '提交失败，请重试');
    isSubmittingFinal.value = false; // 重置提交标志
  }
};

// 获取题型对应的图标
const getQuestionTypeIcon = (type) => {
  const icons = {
    [QUESTION_TYPES.JUDGE]: sheep.$url.cdn('/group/judge.png'), // 判断题
    [QUESTION_TYPES.SINGLE]: sheep.$url.cdn('/group/question-type.png'), // 单选题
    [QUESTION_TYPES.MULTIPLE]: sheep.$url.cdn('/group/multiple-choices.png'), // 多选题
    [QUESTION_TYPES.FILL]: sheep.$url.cdn('/group/blanks.png'), // 填空题
    [QUESTION_TYPES.SHORT]: sheep.$url.cdn('/group/short-answer.png'), // 简答题
    [QUESTION_TYPES.TRANSLATE]: sheep.$url.cdn('/group/translation.png') // 互译题
  };
  return icons[type] || icons[QUESTION_TYPES.SINGLE]; // 默认返回单选题图标
};

// 获取题目选项（处理判断题的特殊情况）
const getQuestionOptions = (question) => {
  // 如果是判断题且没有选项
  if (question.questionType === QUESTION_TYPES.JUDGE && (!question.optionList || question.optionList.length === 0)) {
    return getJudgeOptions();
  }
  return question.optionList || [];
};

// 组件挂载完成
onMounted(() => {
  // 移除重复的题目获取逻辑，onLoad中已经处理了
});

// 保存答案但不提交（退出时调用）
const saveAnswersWithoutSubmit = async () => {
  // 如果正在提交最终答案，则不执行自动保存
  if (isSubmittingFinal.value) {
    return;
  }

  // 保存当前题目的答案
  saveCurrentAnswer();

  // 过滤掉空答案
  const validAnswers = userAnswers.value.filter(answer => answer && answer.questionId && answer.userAnswer);

  if (validAnswers.length === 0) {
    return; // 没有有效答案，不需要保存
  }

  // 构建提交数据，但isSubmit为false
  const submitData = {
    courseId: courseId.value,
    assignmentReleaseId: assignmentId.value,
    questionAnswerItems: validAnswers,
    isSubmit: false // 仅保存，不提交
  };
  await AssignmentApi.submitAssignmentAnswer(submitData);
};

// 页面卸载时保存答案
onUnload(() => {
  saveAnswersWithoutSubmit();
});

// 页面隐藏时保存答案（例如切换到其他tab）
onHide(() => {
  saveAnswersWithoutSubmit();
});

// 加载历史答案
const loadHistoricalAnswers = (questions) => {
  let lastAnsweredIndex = -1; // 记录最后一个有答案的题目索引

  questions.forEach((question, index) => {
    // 如果题目有userAnswer字段，表示有历史答案
    if (question.userAnswer !== undefined && question.userAnswer !== null && question.userAnswer !== '') {
      if (question.questionType === QUESTION_TYPES.JUDGE || question.questionType === QUESTION_TYPES.SINGLE || question.questionType === QUESTION_TYPES.MULTIPLE) {
        // 选择题、判断题、多选题，直接设置选项
        let answer = question.userAnswer;
        // 如果是判断题，将A/B转换为对/错
        if (question.questionType === QUESTION_TYPES.JUDGE) {
          answer = answer === 'A' ? '对' : '错';
        }
        // 单选题和多选题：如果历史答案是序号格式，需要转换为内容格式
        else if (question.questionType === QUESTION_TYPES.SINGLE || question.questionType === QUESTION_TYPES.MULTIPLE) {
          // 如果历史答案是序号格式（A、B、C等），转换为内容
          answer = convertSerialToContent(question, answer);
        }
        selectedOptions.value[index] = answer;
      } else if (question.questionType === QUESTION_TYPES.FILL || question.questionType === QUESTION_TYPES.SHORT || question.questionType === QUESTION_TYPES.TRANSLATE) {
        // 填空题、简答题、互译题，设置文本
        textAnswers.value[index] = question.userAnswer;
      }

      // 同时记录到userAnswers中
      userAnswers.value[index] = {
        questionId: question.id,
        userAnswer: question.userAnswer
      };

      // 更新最后一个有答案的题目索引
      lastAnsweredIndex = index;
    }
  });

  // 如果有历史答案，跳转到最后一个有答案的题目的下一题
  if (lastAnsweredIndex !== -1 && lastAnsweredIndex < questionList.value.length - 1) {
    currentIndex.value = lastAnsweredIndex + 1;
  } else if (lastAnsweredIndex !== -1) {
    // 如果最后一题有答案，就停留在最后一题
    currentIndex.value = lastAnsweredIndex;
  }
};

// 更新文本答案
const updateTextAnswer = (index) => {
  // 当文本输入框失去焦点时，更新对应题目的答案
  const question = questionList.value[index];
  if (question && (question.questionType === QUESTION_TYPES.FILL || question.questionType === QUESTION_TYPES.SHORT || question.questionType === QUESTION_TYPES.TRANSLATE)) {
    // 更新userAnswers中对应的答案
    userAnswers.value[index] = {
      questionId: question.id,
      userAnswer: textAnswers.value[index] || ''
    };
  }
};

// 将序号转换为内容的辅助函数
const convertSerialToContent = (question, serialAnswer) => {
  if (!question.optionList || question.optionList.length === 0) {
    return serialAnswer; // 如果没有选项列表，直接返回原答案
  }

  if (question.questionType === QUESTION_TYPES.JUDGE) {
    // 判断题：A/B -> 对/错
    return serialAnswer === 'A' ? '对' : '错';
  } else if (question.questionType === QUESTION_TYPES.MULTIPLE) {
    // 多选题：优先尝试解析JSON数组格式，失败则兼容管道符分隔格式
    let serials = [];
    try {
      serials = JSON.parse(serialAnswer);
    } catch (e) {
      // 如果JSON解析失败，尝试管道符分隔格式
      serials = serialAnswer.split('|');
    }
    const contents = serials.map(serial => {
      const option = question.optionList.find(opt => opt.serial === serial);
      return option ? option.content : serial;
    });
    return contents.join('|');
  } else {
    // 单选题：A -> 选项内容
    const option = question.optionList.find(opt => opt.serial === serialAnswer);
    return option ? option.content : serialAnswer;
  }
};

// 将内容转换为适当格式的辅助函数
const convertContentToSerial = (question, contentAnswer) => {
  if (!question.optionList || question.optionList.length === 0) {
    return contentAnswer; // 如果没有选项列表，直接返回原答案
  }

  // 判断题特殊处理：转换为A/B格式
  if (question.questionType === QUESTION_TYPES.JUDGE) {
    return contentAnswer === '对' ? 'A' : 'B';
  } else if (question.questionType === QUESTION_TYPES.SINGLE) {
    // 单选题：将选项内容转换为序号
    const option = question.optionList.find(opt => opt.content === contentAnswer);
    return option ? option.serial : contentAnswer;
  } else if (question.questionType === QUESTION_TYPES.MULTIPLE) {
    // 多选题：将多个选项内容转换为JSON数组格式
    const contents = contentAnswer.split('|');
    const serials = contents.map(content => {
      const option = question.optionList.find(opt => opt.content === content);
      return option ? option.serial : content;
    });
    return JSON.stringify(serials);
  } else {
    // 其他题型，直接返回内容
    return contentAnswer;
  }
};
</script>

<style scoped lang="scss">
.answer-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  z-index: -1;
}

.content-wrapper {
  flex: 1;
  padding-bottom: 120rpx;
}

.swiper-container {
  height: 85vh;
  width: 100%;
}

.debug-info {
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10rpx;
  position: absolute;
  top: 200rpx;
  left: 0;
  z-index: 100;
  color: #fff;
  font-size: 24rpx;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

.question-container {
  min-height: 720rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 25rpx;
  margin: 50rpx 32rpx;
  padding: 32rpx 30rpx 35rpx 30rpx;
  box-sizing: border-box;
  position: relative;
  overflow: visible;
}

.question-type {
  display: inline-block;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  color: #333;
  font-size: 28rpx;
  position: absolute;
  top: 33rpx;
  left: -20rpx;
  z-index: 10;
}

.question-type-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
  border-radius: 20rpx;
}

.question-type text {
  color: #333;
  position: relative;
  z-index: 5;
  font-weight: 500;
}

.question-progress {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 32rpx;
}

.current-page {
  color: #2CB5FF;
  font-size: 40rpx;
  font-weight: bold;
}

.total-page {
  color: #999;
  font-size: 32rpx;
}

.question-title {
  margin-top: 95rpx;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.5;
}

.options-list {
  margin-top: 40rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 31rpx;

  height: 80rpx;
  background: #F8F8F8;
  border-radius: 40rpx;
  border: 2px solid #F8F8F8;
}

.option-selected {
  background: #EEF8FF;
  border: 2px solid #29B1F3;
}

.option-circle {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(140deg, rgba(79,224,255,0.89), rgba(18,157,237,0.89));
  box-shadow: 1rpx 2rpx 6rpx 0rpx rgba(5,109,161,0.27);
  border-radius: 50%;

  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.option-circle text {
  color: #fff;
  font-size: 30rpx;
}

.option-text {
  font-size: 32rpx;
  color: #333;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 88rpx 0;
  z-index: 10;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
}

.next-btn {
  width: 690rpx;
  height: 82rpx;
  background: linear-gradient(270deg, rgba(0,222,255,0.89), rgba(70,173,240,0.89));
  border-radius: 41rpx;

  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
}

.text-input-container {
  margin-top: 40rpx;
  width: 100%;
  margin-bottom: 40rpx;
}

.text-input {
  width: 100%;
  height: 400rpx;
  background-color: #F9F9F9;
  border-radius: 20rpx;
  padding: 20rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  border: 1px solid #EEEEEE;
}
</style>
