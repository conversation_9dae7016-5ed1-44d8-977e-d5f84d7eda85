<template>
  <view class="container">
    <!-- 顶部导航栏 -->

    <view class="nav" :style="{ paddingTop: `${statusBarHeight}px` }">
      <view class="nav-content">
        <image class="back-icon" mode="widthFix" :src="sheep.$url.cdn('/common/back.png')" @click="goBack" />

        <view class="translate-scarf">

          <!-- 语言切换和翻译按钮 -->
          <view class="language-bar">
            <view class="language-text" @tap="toggleTranslationMode">
              <text class="from-language">{{ fromLanguage }}</text>
              <text class="direction-icon">⇌</text>
              <text class="to-language">{{ toLanguage }}</text>
            </view>
          </view>

        </view>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 导航栏占位，防止内容被遮挡 -->
    <view class="nav-placeholder" :style="{ height: `${navHeight}px` }"></view>



    <!-- PDF内容区域 -->
    <view class="content-area">
      <!-- 选项卡切换 -->
      <view class="tabs">
        <!-- 左侧 SVG 背景，用于原文 tab 激活时 -->
         <image class="tab-svg original-svg" src="/static/svg/left-active.svg" mode="widthFix"
         :class="{ 'svg-active': activeTab === 'original' }" >
         </image>
         <image class="tab-svg translation-svg" src="/static/svg/right-active.svg" mode="widthFix"
         :class="{ 'svg-active': activeTab === 'translation' }" >
         </image>
        
        <view class="tab" :class="{ active: activeTab === 'original' }" @tap="switchTab('original')">
          原文
        </view>
        <view class="tab" :class="{ active: activeTab === 'translation' }" @tap="switchTab('translation')">
          翻译
        </view>
      </view>

      <!-- 翻译结果 -->
      <view class="translation-result">
        <scroll-view scroll-y class="result-scroll" :scroll-top="scrollTop">
          <!-- 原文内容 -->
          <view v-if="activeTab === 'original'">
            <view class="result-content">
              <template v-for="(item, index) in originalTextList" :key="index">
                <view class="text-content">
                  <view class="text-item">
                    <text selectable>{{ item }}</text>
                    <image
                      class="play-icon"
                      :src="getPlayIconSource(PlayType.SINGLE, index, false)"
                      mode="widthFix"
                      @click="debouncedPlayTextSingle(item, index)"
                    />
                  </view>
                </view>
              </template>
            </view>
          </view>

          <!-- 翻译内容 -->
          <view v-else>
            <view class="result-content">
              <view class="text-content translation-text">
                <template v-if="typing">
                  <text selectable>{{ translationText }}<text class="cursor"></text></text>
                </template>
                <template v-else>
                  <text selectable>{{ translationText }}</text>
                </template>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 底部操作区域 -->
        <view class="bottom-controls">
          <!-- 一键复制和播放全文 -->
          <view class="left-icons" :class="{ 'hidden': !(activeTab === 'original' ? originalTextList.length > 0 : translationText) }">
            <image
              class="action-icon"
              :src="getPlayIconSource(activeTab === 'original' ? PlayType.ALL_SOURCE : PlayType.ALL_TRANSLATION)"
              mode="widthFix"
              @click="handlePlayAll"
            />
            <image
              class="action-icon copy-icon"
              :src="sheep.$url.cdn(ImagePath.COPY_ICON_GRAY)"
              mode="widthFix"
              @click="copyText"
            />
          </view>
          
          <!-- 翻页控制 -->
          <view class="page-control">
            <view class="page-btn prev-btn" @tap="prevPage" :class="{ disabled: currentPage <= 1 }">
              <image class="arrow-icon" :src="sheep.$url.cdn(currentPage <= 1 ? ImagePath.ARROW_LEFT_GRAY : ImagePath.ARROW_LEFT_BLACK)" mode="widthFix" />
            </view>
            <view class="page-info">
              <input type="number" class="page-input" v-model="pageInputValue" @blur="handlePageInput"
                @keypress.enter="handlePageInput" /> <text class="slash">/</text> {{ totalPages }}
            </view>
            <view class="page-btn next-btn" @tap="nextPage" :class="{ disabled: currentPage >= totalPages }">
              <image class="arrow-icon" :src="sheep.$url.cdn(currentPage >= totalPages ? ImagePath.ARROW_RIGHT_GRAY : ImagePath.ARROW_RIGHT_BLACK)" mode="widthFix" />
            </view>
          </view>
        </view>
      </view>


      <!-- 功能按钮区 -->
      <view class="function-area">
        <view class="btn translate-btn" @tap="translate">
          翻译
        </view>
      </view>
    </view>


  </view>
</template>

<script setup>
  import { ref, onMounted, computed, watch, nextTick } from 'vue';
  import { onUnload, onHide } from '@dcloudio/uni-app';
  import OcrApi from '@/sheep/api/ocr/index';
  import sheep from '@/sheep';
  import TranslateApi from '@/sheep/api/translate';
  import {
    getLanguageCodeByLabel,
    LanguageEnum,
    PlayType,
    ImagePath,
    getTtsConfig, TtsTypeEnum, getTextLanguage,
  } from '@/sheep/util/language-detector';
  import { AiModelEnum } from '@/sheep/util/const';
  import ConversationApi from '@/sheep/api/text-optimizer/conversation';
  import { createStreamHandler } from '@/sheep/util/stream-parser';
  import NlsApi from '@/sheep/api/voice/nls';
  import { debounce } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common';

  const buttonColorBg = sheep.$url.css('/uniapp/translate-pdf/button-color.png');
  const activeLineBg = sheep.$url.css('/uniapp/translate-pdf/active-line.png');
  const pageBg = sheep.$url.css('/uniapp/translate-pdf/bg.png');
  
  // 用户信息
  const userInfo = computed(() => sheep.$store('user').userInfo);
  
  // 导航栏安全距离相关
  const statusBarHeight = ref(0);
  const navHeight = ref(0);
  
  // 页面状态
  const loading = ref(false);
  const translationText = ref('');
  const pendingText = ref(''); // 新增
  const typing = ref(false); // 新增
  const originalText = ref('');
  const originalTextList = ref([]); // 存储原文句子列表
  const translationTextList = ref([]); // 存储翻译句子列表
  const currentPage = ref(1);
  const totalPages = ref(1);
  const pageInputValue = ref('1'); // 页码输入值
  const pdfBase64 = ref('');
  const pdfPath = ref('');
  const isTranslating = ref(false);
  // 选项卡状态
  const activeTab = ref('original'); // 默认显示原文
  // 语言设置
  const fromLanguage = ref(LanguageEnum.ZH.label); // 源语言
  const toLanguage = ref(LanguageEnum.TH.label); // 目标语言
  // 会话ID
  const conversationId = ref('');
  // 存储每页的识别结果缓存
  const pageResults = ref({});
  // 存储每页的翻译结果缓存
  const pageTranslations = ref({});
  
  // 音频播放相关状态
  const currentPlayingIndex = ref(null); // 当前播放的单句索引
  const currentPlayingType = ref(null); // 当前播放类型
  const isTranslated = ref(false); // 是否为翻译文本
  const lastPlayedText = ref(''); // 记录上次播放的文本内容
  let audioContext = null; // 音频上下文
  let typingTimer = null; // 打字机定时器
  
  // 节流控制变量
  const lastTranslateTime = ref(0);
  
  // 切换选项卡
  const switchTab = (tab) => {
    activeTab.value = tab;
    // 如果切换到“翻译”tab，且翻译内容为空，则自动触发翻译
    if (tab === 'translation' && !translationText.value) {
      translate();
    }
  };
  
  // 页面卸载时处理
  onUnload(() => {
    // 不清除缓存，允许返回后继续查看
    if (typingTimer) {
      clearInterval(typingTimer);
      typingTimer = null;
    }
  });

  onHide(() => {
    // 页面隐藏时的处理
  });

  // 加载PDF缓存数据
  const loadCache = () => {
    try {
      const cacheData = uni.getStorageSync('pdf_cache');
      if (!cacheData) return false;

      const data = JSON.parse(cacheData);
      // 检查缓存是否过期 (60分钟)
      if (Date.now() - data.timestamp > 60 * 60 * 1000) {
        clearCache();
        return false;
      }

      currentPage.value = data.currentPage || 1;
      totalPages.value = data.totalPages || 1;
      pageInputValue.value = String(currentPage.value);
      pdfBase64.value = data.pdfBase64 || '';
      pdfPath.value = data.pdfPath || '';

      // 加载页面结果缓存
      if (data.pageResults) {
        pageResults.value = data.pageResults;
      }
      
      // 加载翻译结果缓存
      if (data.pageTranslations) {
        pageTranslations.value = data.pageTranslations;
      }

      // 如果有当前页的缓存结果，直接展示
      if (pageResults.value[currentPage.value]) {
        displayOcrResult(pageResults.value[currentPage.value]);
        // 恢复翻译内容
        if (pageTranslations.value[currentPage.value]) {
          translationText.value = pageTranslations.value[currentPage.value];
        }
      }

      return true;
    } catch (e) {
      console.error('加载缓存失败:', e);
      return false;
    }
  };

  // 更新缓存
  const updateCache = (data = {}) => {
    try {
      const cacheData = uni.getStorageSync('pdf_cache');
      if (!cacheData) return;

      const cache = JSON.parse(cacheData);
      const newCache = {
        ...cache,
        ...data,
        timestamp: Date.now()
      };

      uni.setStorageSync('pdf_cache', JSON.stringify(newCache));
    } catch (e) {
      console.error('更新缓存失败:', e);
    }
  };

  // 清除缓存
  const clearCache = () => {
    uni.removeStorageSync('pdf_cache');
  };

  // 处理OCR结果显示
  const displayOcrResult = (data) => {
    if (!data) return;

    // 更新总页数
    if (data.pdfPageSize) {
      totalPages.value = data.pdfPageSize;
      updateCache({ totalPages: data.pdfPageSize });
    }

    // 根据返回数据结构提取文本
    if (data.textDetections && Array.isArray(data.textDetections)) {
      // 提取每个句子作为列表
      originalTextList.value = data.textDetections
        .map(item => item.detectedText || item.text || '')
        .filter(text => text);

      // 同时维护原始文本字段
      originalText.value = originalTextList.value.join('\n');
      // 不清空翻译内容，保持现有的翻译结果
      // translationText.value = '';
      translationTextList.value = [];
    } else if (Array.isArray(data)) {
      originalTextList.value = data
        .map(item => item.text || item.detectedText || '')
        .filter(text => text);

      originalText.value = originalTextList.value.join('\n');
      // 不清空翻译内容，保持现有的翻译结果
      // translationText.value = '';
      translationTextList.value = [];
    }
  };

  // 停止音频播放
  const stopAudio = () => {
    console.log('停止音频播放');
    if (audioContext) {
      try {
        console.log('停止并销毁音频上下文');
        audioContext.stop();
        audioContext.destroy();
        audioContext = null;
      } catch (error) {
        console.error('停止音频时出错:', error);
      }
    }
    currentPlayingIndex.value = null;
    currentPlayingType.value = null;
    isTranslated.value = false;
    lastPlayedText.value = ''; // 清空上次播放的文本记录
    console.log('重置播放状态完成');
  };

  // 播放单句
  const playTextSingle = async (text, index, translation = false) => {
    if (!text) return;

    // 如果当前正在播放这句话，则暂停
    if (
      currentPlayingType.value === PlayType.SINGLE &&
      currentPlayingIndex.value === index &&
      isTranslated.value === translation &&
      audioContext
    ) {
      audioContext.pause();
      currentPlayingType.value = null;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      lastPlayedText.value = text; // 记录暂停的文本
      return;
    }
    
    // 如果已经创建了音频上下文但是暂停了，且要播放的是同一段文本，则继续播放
    if (audioContext && !currentPlayingType.value && lastPlayedText.value === text) {
      currentPlayingType.value = PlayType.SINGLE;
      currentPlayingIndex.value = index;
      isTranslated.value = translation;
      audioContext.play();
      return;
    }
    
    // 如果是新文本或者没有暂停的音频，则创建新的播放
    stopAudio();
    currentPlayingType.value = PlayType.SINGLE;
    currentPlayingIndex.value = index;
    isTranslated.value = translation;
    lastPlayedText.value = text; // 记录当前播放的文本

    await commonPlayText(text); // 播放文本
  };
  
  // 使用防抖包装播放单句函数
  const debouncedPlayTextSingle = debounce(playTextSingle, 500, true);

  // 播放原文全文
  const _playAllSource = async () => {
    if (!originalTextList.value.length) return;
    
    const sourceFullText = originalTextList.value.join('\n');

    // 如果当前正在播放原文全文，则暂停
    if (currentPlayingType.value === PlayType.ALL_SOURCE && audioContext) {
      audioContext.pause();
      currentPlayingType.value = null;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      lastPlayedText.value = sourceFullText; // 记录暂停的文本
      return;
    }
    
    // 如果已经创建了音频上下文但是暂停了，且要播放的是同一段文本，则继续播放
    if (audioContext && !currentPlayingType.value && lastPlayedText.value === sourceFullText) {
      currentPlayingType.value = PlayType.ALL_SOURCE;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      audioContext.play();
      return;
    }
    
    // 如果是新文本或者没有暂停的音频，则创建新的播放
    stopAudio();
    currentPlayingType.value = PlayType.ALL_SOURCE;
    currentPlayingIndex.value = null;
    isTranslated.value = false;
    lastPlayedText.value = sourceFullText; // 记录当前播放的文本

    await playFullText(sourceFullText); // 播放全文
  };

    // 播放翻译全文
    const _playAllTranslation = async () => {
    console.log('播放翻译全文函数开始执行');
    if (!translationText.value) return;
    console.log('播放翻译文本:', translationText.value.substring(0, 30) + '...');

    // 如果当前正在播放翻译全文，则暂停
    if (currentPlayingType.value === PlayType.ALL_TRANSLATION && audioContext) {
      audioContext.pause();
      currentPlayingType.value = null;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      lastPlayedText.value = translationText.value; // 记录暂停的文本
      return;
    }
    
    // 如果已经创建了音频上下文但是暂停了，且要播放的是同一段文本，则继续播放
    if (audioContext && !currentPlayingType.value && lastPlayedText.value === translationText.value) {
      currentPlayingType.value = PlayType.ALL_TRANSLATION;
      currentPlayingIndex.value = null;
      isTranslated.value = true;
      audioContext.play();
      return;
    }
    
    // 如果是新文本或者没有暂停的音频，则创建新的播放
    stopAudio();
    currentPlayingType.value = PlayType.ALL_TRANSLATION;
    currentPlayingIndex.value = null;
    isTranslated.value = true;
    lastPlayedText.value = translationText.value; // 记录当前播放的文本

    await playFullText(translationText.value); // 播放全文
  };


  // 创建debounce版本的播放函数
  const playAllSource = debounce(_playAllSource, 500, true);
  const playAllTranslation = debounce(_playAllTranslation, 500, true);

  // 处理播放全文（兼容微信小程序）
  const handlePlayAll = () => {
    console.log('handlePlayAll被调用, 当前activeTab:', activeTab.value);
    if (activeTab.value === 'original') {
      console.log('准备调用原文debounce版本播放函数');
      playAllSource(); // 调用debounce包装后的函数
    } else {
      console.log('准备调用翻译debounce版本播放函数');
      playAllTranslation(); // 调用debounce包装后的函数
    }
  };


  // 识别当前页面
  const recognizePage = async () => {
    // 检查是否已有当前页的识别结果缓存
    if (pageResults.value[currentPage.value]) {
      console.log(`使用缓存的第${currentPage.value}页识别结果`);
      displayOcrResult(pageResults.value[currentPage.value]);
      return;
    }

    if (!pdfBase64.value) {
      sheep.$helper.toast('PDF文件不存在');
      return;
    }

    loading.value = true;

    try {
      console.log(`开始识别PDF，当前页: ${currentPage.value}`);
      const res = await OcrApi.pdfRecognize({
        fileBase64: pdfBase64.value,
        languageType: 'mix',
        isPdf: true,
        pdfPageNumber: currentPage.value,
        isWords: false,
      });

      console.log('PDF识别结果:', JSON.stringify({
        code: res.code,
        pdfPageSize: res.data?.pdfPageSize,
        currentPage: currentPage.value
      }));

      loading.value = false;

      if (res.code !== 0) {
        sheep.$helper.toast('识别失败');
        return;
      }

      // 显示识别结果
      displayOcrResult(res.data);

      // 更新总页数
      if (res.data.pdfPageSize) {
        console.log(`更新总页数: ${res.data.pdfPageSize}`);
        totalPages.value = res.data.pdfPageSize;
      } else {
        console.log('API返回数据中没有pdfPageSize字段');
      }

      // 检测识别文本的语言并设置翻译方向
      if (originalText.value) {
        detectLanguage(originalText.value);
      }

      // 保存当前页的识别结果到缓存
      pageResults.value = {
        ...pageResults.value,
        [currentPage.value]: res.data
      };

      // 更新缓存
      updateCache({
        currentPage: currentPage.value,
        totalPages: totalPages.value,
        pageResults: pageResults.value,
        pageTranslations: pageTranslations.value
      });

    } catch (e) {
      loading.value = false;
      sheep.$helper.toast('识别过程出错');
      console.error('识别失败:', e);
    }
  };

  // 加载最新的会话
  const loadLatestConversation = async () => {
    const { code, data } = await ConversationApi.getConversationPageByModelName({
      pageNo: 1,
      pageSize: 10,
      userId: userInfo.value.id,
      modelName: AiModelEnum.TEXT_TRANSLATION.name,
    });

    const conversationData = data.list || [];

    if (conversationData.length > 0) {
      const latestConversation = conversationData[0];
      conversationId.value = latestConversation.id.toString();
    } else {
      // 创建会话
      const res = await ConversationApi.createConversationByName(AiModelEnum.TEXT_TRANSLATION.name);
      if (res.code !== 0) {
        return;
      }
      conversationId.value = res.data.toString();
    }

    // 保存到缓存
    uni.setStorageSync('translate_conversation_id', conversationId.value);
  };

  // 创建真正的翻译函数
  const doTranslate = async () => {
    if (!originalText.value) {
      sheep.$helper.toast('没有可翻译的文本');
      return;
    }

    // 确保有会话ID
    if (!conversationId.value) {
      await loadLatestConversation();
    }

    // 准备翻译参数
    const params = {
      sourceText: originalText.value,
      sourceLang: getLanguageCodeByLabel(fromLanguage.value),
      targetLang: getLanguageCodeByLabel(toLanguage.value),
      conversationId: conversationId.value,
    };

    // 切换到翻译选项卡
    activeTab.value = 'translation';

    // 清空已有的翻译文本
    translationText.value = '';
    pendingText.value = ''; // 清空待显示的文本
    typing.value = false; // 关闭打字机
    isTranslating.value = true;
    loading.value = true;
    
    // 显示加载提示
    uni.showLoading({
      title: '翻译中...',
      mask: true
    });
    
    // 用于标记是否已收到第一个数据块
    let firstChunkReceived = false;

    try {
      // 使用流式翻译API
      await TranslateApi.textTranslateStream(params, {
        enableChunked: true,
        onChunkReceived: createStreamHandler((content) => {
          if (!firstChunkReceived) {
            uni.hideLoading();
            firstChunkReceived = true;
          }
          // 不直接加到translationText，而是加到pendingText
          pendingText.value += content;
          startTyping();
          // 实时保存翻译结果到缓存
          pageTranslations.value = {
            ...pageTranslations.value,
            [currentPage.value]: translationText.value + pendingText.value
          };
          updateCache({ pageTranslations: pageTranslations.value });
        }, {
          // 配置选项
          silent: false,
          contentPath: 'data.receive.content',
          successCode: 0
        })
      });
    } catch (e) {
      console.error('翻译失败:', e);
      uni.hideLoading(); // 确保在失败时也隐藏加载提示
    } finally {
      loading.value = false;
      isTranslating.value = false;
    }
  };

  // 节流控制的翻译函数
  const translate = () => {
    const now = Date.now();
    // 如果距离上次点击不到1秒，则不执行
    if (now - lastTranslateTime.value < 1000) {
      return;
    }
    lastTranslateTime.value = now;
    doTranslate();
  };

  // 切换翻译模式
  const toggleTranslationMode = () => {
    const tmp = fromLanguage.value;
    fromLanguage.value = toLanguage.value;
    toLanguage.value = tmp; // 交换源语言和目标语言
  };

  // 检测文本语言
  const detectLanguage = (text) => {
    const detectedLang = getTextLanguage(text);

    if (!detectedLang) return;

    // 设置中译泰
    if (detectedLang === LanguageEnum.ZH) {
      fromLanguage.value = LanguageEnum.ZH.label;
      toLanguage.value = LanguageEnum.TH.label;
    }
    // 设置泰译中
    else if (detectedLang === LanguageEnum.TH) {
      fromLanguage.value = LanguageEnum.TH.label;
      toLanguage.value = LanguageEnum.ZH.label;
    }
  };

  // 音频播放相关函数

  // 获取播放图标路径
  const getPlayIconSource = (type, index = null, isTranslation = false) => {
    // 单句播放中图标
    if (
      type === PlayType.SINGLE &&
      currentPlayingType.value === PlayType.SINGLE &&
      currentPlayingIndex.value === index &&
      isTranslated.value === isTranslation
    ) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.BLUE_ANIMATED);
    }
    // 原文全文播放中图标
    if (type === PlayType.ALL_SOURCE && currentPlayingType.value === PlayType.ALL_SOURCE) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.BLUE_ANIMATED);
    }
    // 翻译全文播放中图标
    if (type === PlayType.ALL_TRANSLATION && currentPlayingType.value === PlayType.ALL_TRANSLATION) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.BLUE_ANIMATED);
    }
    // 默认图标
    if (type === PlayType.SINGLE) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.BLUE_STATIC);
    } else {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.GRAY_STATIC);
    }
  };

  // 通用播放文本函数
  const commonPlayText = async (text) => {
    try {
      console.log('开始播放文本:', text.substring(0, 30) + '...');
      let ttsConfig = getTtsConfig(text, TtsTypeEnum.ALIYUN);

      if (!ttsConfig) {
        console.error('无法检测文本语言');
        sheep.$helper.toast('无法检测文本语言');
        stopAudio();
        return;
      }

      const params = {
        text,
        speaker: ttsConfig.speaker,
        speechRate: ttsConfig.speechRate,
        pitchRate: ttsConfig.pitchRate,
        displayCaptions: false,
      };
      
      console.log('调用TTS API, 参数:', JSON.stringify({
        speaker: params.speaker,
        speechRate: params.speechRate,
        length: text.length
      }));

      const res = await NlsApi.ttsAliyun(params); // 文本转语音

      if (res?.msg) {
        stopAudio();
        return;
      }

      console.log('TTS API返回成功, 数据长度:', res.length || '未知');

      const manager = uni.getFileSystemManager();
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;
      console.log('准备写入临时文件:', tempFilePath);
      
      try {
        await new Promise((resolve, reject) => {
          manager.writeFile({
            filePath: tempFilePath,
            data: res,
            encoding: 'binary',
            success: () => {
              console.log('临时文件写入成功');
              resolve();
            },
            fail: (err) => {
              console.error('临时文件写入失败:', err);
              reject(err);
            },
          });
        });

        await playFromUrl(tempFilePath); // 播放音频文件
      } catch (error) {
        console.error('播放音频失败:', error);
        sheep.$helper.toast('播放失败: ' + (error.message || '未知错误'));
        stopAudio();
      }
    } catch (error) {
      console.error('音频播放过程发生异常:', error);
      sheep.$helper.toast('播放出错');
      stopAudio();
    }
  };

  // 播放全文函数（使用OpenAI TTS）
  const playFullText = async (text) => {
    try {
      let ttsConfig = getTtsConfig(text, TtsTypeEnum.OPENAI);

      if (!ttsConfig) {
        sheep.$helper.toast('无法检测文本语言');
        stopAudio();
        return;
      }

      const params = {
        text,
        speaker: ttsConfig.speaker,
        pitchRate: ttsConfig.pitchRate,
        displayCaptions: false,
      };

      const res = await NlsApi.ttsOpenAI(params); // 文本转语音

      if (res?.msg) {
        stopAudio();
        return;
      }

      const manager = uni.getFileSystemManager();
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;

      try {
        await new Promise((resolve, reject) => {
          manager.writeFile({
            filePath: tempFilePath,
            data: res,
            encoding: 'binary',
            success: () => {
              console.log('临时文件写入成功');
              resolve();
            },
            fail: (err) => {
              console.error('临时文件写入失败:', err);
              reject(err);
            },
          });
        });
        
        console.log('开始播放音频文件');
        await playFromUrl(tempFilePath); // 播放音频文件
      } catch (error) {
        console.error('播放音频失败:', error);
        sheep.$helper.toast('播放失败: ' + (error.message || '未知错误'));
        stopAudio();
      }
    } catch (error) {
      console.error('音频播放过程发生异常:', error);
      sheep.$helper.toast('播放出错');
      stopAudio();
    }
  };

  // 从URL播放音频
  const playFromUrl = (audioUrl) => {
    return new Promise((resolve, reject) => {
      try {
        console.log('创建音频上下文');
        audioContext = uni.createInnerAudioContext();

        if (!audioContext) {
          console.error('创建音频上下文失败');
          sheep.$helper.toast('创建音频上下文失败');
          reject(new Error('无法创建音频上下文'));
          return;
        }

        console.log('设置音频源:', audioUrl);
        audioContext.src = audioUrl;

        audioContext.onPlay(() => {
          console.log('音频开始播放');
        });

        audioContext.onEnded(() => {
          console.log('音频播放结束');
          stopAudio(); // 播放结束
          resolve();
        });

        audioContext.onError((err) => {
          console.error('音频播放错误:', err);
          sheep.$helper.toast('音频播放失败: ' + (err.errMsg || '未知错误'));
          stopAudio(); // 播放错误
          reject(new Error('音频播放失败: ' + (err.errMsg || '')));
        });

        console.log('开始播放音频');
        audioContext.play(); // 开始播放
      } catch (error) {
        console.error('播放音频异常:', error);
        sheep.$helper.toast('播放异常');
        stopAudio();
        reject(error);
      }
    });
  };

  // 复制文本
  const copyText = () => {
    // 根据当前选中的选项卡决定复制原文还是翻译
    const textToCopy = activeTab.value === 'original' ? originalText.value : translationText.value;

    if (!textToCopy) {
      sheep.$helper.toast('没有可复制的文本');
      return;
    }

    uni.setClipboardData({
      data: textToCopy,
      success: () => {
        sheep.$helper.toast('复制成功');
      }
    });
  };

  // 上一页
  const prevPage = () => {
    if (currentPage.value > 1) {
      console.log(`切换到上一页: ${currentPage.value - 1}`);
      
      // 翻页时立即停止当前播放的音频
      if (audioContext && (currentPlayingType.value === PlayType.ALL_SOURCE || currentPlayingType.value === PlayType.ALL_TRANSLATION || currentPlayingType.value === PlayType.SINGLE)) {
        console.log('翻页时停止当前播放的音频');
        stopAudio();
      }
      
      currentPage.value--;
      pageInputValue.value = String(currentPage.value);
      updateCache({ currentPage: currentPage.value });
      // 恢复翻译内容
      if (pageTranslations.value[currentPage.value]) {
        translationText.value = pageTranslations.value[currentPage.value];
        // 如果有翻译内容，保持在翻译选项卡
        activeTab.value = 'translation';
      } else {
        // 没有翻译内容，切换到原文选项卡
        activeTab.value = 'original';
        translationText.value = '';
      }
      // recognizePage会先检查缓存，不会重复识别
      recognizePage();
    } else {
      console.log('已经是第一页，无法继续向前');
    }
  };

  // 下一页
  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      console.log(`切换到下一页: ${currentPage.value + 1}, 总页数: ${totalPages.value}`);
      
      // 翻页时立即停止当前播放的音频
      if (audioContext && (currentPlayingType.value === PlayType.ALL_SOURCE || currentPlayingType.value === PlayType.ALL_TRANSLATION || currentPlayingType.value === PlayType.SINGLE)) {
        console.log('翻页时停止当前播放的音频');
        stopAudio();
      }
      
      currentPage.value++;
      pageInputValue.value = String(currentPage.value);
      updateCache({ currentPage: currentPage.value });
      // 恢复翻译内容
      if (pageTranslations.value[currentPage.value]) {
        translationText.value = pageTranslations.value[currentPage.value];
        // 如果有翻译内容，保持在翻译选项卡
        activeTab.value = 'translation';
      } else {
        // 没有翻译内容，切换到原文选项卡
        activeTab.value = 'original';
        translationText.value = '';
      }
      // recognizePage会先检查缓存，不会重复识别
      recognizePage();
    } else {
      console.log(`已经是最后一页，无法继续向后，当前页: ${currentPage.value}, 总页数: ${totalPages.value}`);
    }
  };

  // 返回上一页
  const goBack = () => {
    sheep.$router.back();
  };

  // 处理页码输入
  const handlePageInput = () => {
    // 获取输入的页码
    let targetPage = parseInt(pageInputValue.value);

    // 验证输入是否为有效数字
    if (isNaN(targetPage)) {
      pageInputValue.value = String(currentPage.value);
      return;
    }

    // 确保页码在有效范围内
    if (targetPage < 1) {
      targetPage = 1;
    } else if (targetPage > totalPages.value) {
      targetPage = totalPages.value;
    }

    // 更新输入值为规范化后的页码
    pageInputValue.value = String(targetPage);

    // 如果页码实际发生了变化，则进行跳转
    if (targetPage !== currentPage.value) {
      // 页码跳转时立即停止当前播放的音频
      if (audioContext && (currentPlayingType.value === PlayType.ALL_SOURCE || currentPlayingType.value === PlayType.ALL_TRANSLATION || currentPlayingType.value === PlayType.SINGLE)) {
        console.log('页码跳转时停止当前播放的音频');
        stopAudio();
      }
      
      currentPage.value = targetPage;
      updateCache({ currentPage: currentPage.value });
      // 恢复翻译内容
      if (pageTranslations.value[currentPage.value]) {
        translationText.value = pageTranslations.value[currentPage.value];
        // 如果有翻译内容，保持在翻译选项卡
        activeTab.value = 'translation';
      } else {
        // 没有翻译内容，切换到原文选项卡
        activeTab.value = 'original';
        translationText.value = '';
      }
      // recognizePage会先检查缓存，不会重复识别
      recognizePage();
    }
  };

  // 获取状态栏高度
  const getStatusBarHeight = () => {
    // #ifdef MP-WEIXIN
    // 获取胶囊按钮信息
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    statusBarHeight.value = menuButtonInfo.top;
    navHeight.value = statusBarHeight.value + 66; // 66rpx是导航栏内容高度
    // #endif

    // #ifndef MP-WEIXIN
    // 获取状态栏高度
    const systemInfo = sheep.$helper.sys();
    statusBarHeight.value = systemInfo.marginTop;
    navHeight.value = statusBarHeight.value + 66; // 66rpx是导航栏内容高度
    // #endif
  };

  // 页面加载时执行
  onMounted(async () => {
    console.log('PDF翻译页面加载');
    // 获取状态栏高度
    getStatusBarHeight();

    const hasCacheData = loadCache();
    console.log('缓存加载状态:', hasCacheData);

    if (!hasCacheData) {
      sheep.$helper.toast('PDF文件不存在，请重新选择');
      setTimeout(() => {
        goBack();
      }, 1500);
      return;
    }

    console.log(`加载缓存数据 - 当前页: ${currentPage.value}, 总页数: ${totalPages.value}`);

    // 如果没有识别结果，则进行识别
    if (!translationText.value) {
      recognizePage();
    } else {
      console.log('使用缓存的识别结果');
    }

    // 读取缓存中的会话ID
    const cachedConversationId = uni.getStorageSync('translate_conversation_id');
    if (cachedConversationId) {
      conversationId.value = cachedConversationId;
    } else {
      // 没有缓存的会话ID，创建新会话
      await loadLatestConversation();
    }
  });

  // 添加打字机函数
  function getTypingInterval(len) {
    if (len >= 100) return 10;
    if (len <= 20) return 80;
    return 80 - Math.floor((len - 20) * (70 / 80));
  }

  function startTyping() {
    if (typingTimer) {
      clearInterval(typingTimer);
      typingTimer = null;
    }
    if (!pendingText.value || pendingText.value.length === 0) {
      typing.value = false;
      return;
    }
    typing.value = true;
    const interval = getTypingInterval(pendingText.value.length);
    typingTimer = setInterval(() => {
      if (pendingText.value.length > 0) {
        translationText.value += pendingText.value[0];
        pendingText.value = pendingText.value.slice(1);
        scrollToBottom();
        if (pendingText.value.length === 0) {
          typing.value = false;
          clearInterval(typingTimer);
          typingTimer = null;
        }
      }
    }, interval);
  }

  const scrollTop = ref(0);

  const scrollToBottom = async () => {
    await nextTick();
    setTimeout(() => {
      const query = uni.createSelectorQuery();
      query.select('.result-scroll').boundingClientRect();
      query.select('.text-content.translation-text').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const [scroll, content] = res;
          if (scroll && content) {
            scrollTop.value = Math.max(0, content.height - scroll.height)+100;
          }
        }
      });
    }, 50);
  };

</script>

<style lang="scss" scoped>

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: v-bind(pageBg) center/cover no-repeat;
}

.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: 0 24rpx;
  position: relative;
  z-index: 10;
}

.nav-placeholder {
  width: 100%;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon .iconfont {
  font-size: 40rpx;
  color: #333;
}

.title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

.top-bar {
  display: flex;
  align-items: center;
  height: 90rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  z-index: 10;
}

.back-btn {
  font-size: 36rpx;
  padding: 10rpx;
}

.title {
  flex: 1;
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
}

.content-area {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.translation-result {
  flex: 1;
  background-color: #ffffff;
  padding: 58rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211, 223, 230, 0.52);
  border-radius: 0rpx 0rpx 57rpx 57rpx;
  position: relative;
}

.result-scroll {
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 100rpx;
}

/* 结果内容样式 */
.result-content .text-content {
  font-size: 30rpx;
  margin-top: 22rpx;
  color: #000000;
  display: flex;
  align-items: center;
  position: relative;
  line-height: 1.8;
  word-break: break-all;
  user-select: text;
  -webkit-user-select: text;
}

/* 翻译文本特殊样式 */
.translation-text {
  display: block !important;
  white-space: pre-wrap;
  padding: 10rpx 0;
}

.text-content .text-item {
  display: flex;
  align-items: center;
}

.text-content .play-icon {
  width: 36rpx;
  margin-left: 10rpx;
  flex-shrink: 0;
  -webkit-tap-highlight-color: transparent; /* 禁用点击高亮 */
}

/* 底部控制区域 */
.bottom-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 58rpx;
  left: 58rpx;
  right: 58rpx;
  width: calc(100% - 116rpx);
}

/* 图标按钮区域 */
.left-icons {
  display: flex;
  height: 64rpx;
  align-items: center;
  gap: 30rpx;
  -webkit-tap-highlight-color: transparent; /* 禁用点击高亮 */
}

.left-icons .action-icon {
  width: 36rpx;
  padding: 10rpx;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent; /* 禁用点击高亮 */

  &.copy-icon {
    width: 33rpx;
  }
}

/* 隐藏元素的样式 - 保持占位但不可见 */
.hidden {
  visibility: hidden;
  pointer-events: none; /* 禁止点击 */
}

/* 复制和播放按钮区域 */
.action-buttons {
  display: flex;
}

.action-buttons .action-group {
  display: flex;
  gap: 30rpx;
  left: 58rpx;
  bottom: 58rpx;
}

/* 按钮样式 */
.action-group .copy-btn,
.action-group .play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 30rpx;
  border-radius: 35rpx;
  height: 64rpx;
}

.action-group .btn-icon {
  height: 24rpx;
  margin-right: 8rpx;
}

.action-group .btn-text {
  font-size: 24rpx;
}

.action-group .copy-btn {
  background-color: #ffffff;
  border: 1px solid #ddd;
  color: #000000;
}

.action-group .play-btn {
  background-color: #46ADF0;
  color: #ffffff;
}

/* 选项卡样式 */
.tabs {
  display: flex;
  background: #DDF2FF;
  border-radius: 53rpx 51rpx 0rpx 0rpx;
  position: relative;
  z-index: 9;
  top: 2px;
  height: 76rpx;
}

/* SVG 背景样式 */
.tab-svg {
  position: absolute;
  bottom: -1px;
  width: 394rpx;
  height: 115rpx;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
  aspect-ratio: 394/115;
}

.svg-active {
  opacity: 1;
  z-index: 10;
}

/* 调整左右 SVG 定位 */
.original-svg {
  left: 0;
}

.translation-svg {
  right: 0;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  z-index: 11;
}

.tab.active {
  font-weight: bold;
  font-size: 33rpx;
  color: #292828;
  top: -10rpx;
}


.tab.active:after {
    content: '';
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 146rpx;
    height: 29rpx;
    background: v-bind(activeLineBg) center/cover no-repeat;
    z-index: -1;
}

.function-area {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border-radius: 8rpx;
}



.copy-btn {
  background-color: #f0f0f0;
  color: #333;
  margin-right: 15rpx;
}

.translate-btn {
  background: v-bind(buttonColorBg) center/cover no-repeat;
  color: #fff;
  width: 690rpx;
  height: 99rpx;
  font-weight: bold;
  font-size: 38rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50rpx;
}

.translate-btn.btn-loading {
  background-color: #80c4ff;
  opacity: 0.8;
}

.page-control {
  display: flex;
  align-items: center;
}

.page-btn {
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 30rpx;
}

.page-btn .arrow-icon {
  width: 16rpx;
  padding: 0rpx 40rpx;
}

.next-btn{
  .arrow-icon {
    padding-right: 0rpx;
  }
}

.page-btn.disabled {
  opacity: 0.6;
}

.page-info {
  font-size: 33rpx;
  color: #848484;
  display: flex;
  align-items: center;

  .page-input {
  width: 52rpx;
  height: 52rpx;
  border: 2px solid #C1C1C1;
  text-align: center;
  margin-right: 8rpx;
  border-radius: 6rpx;
  font-size: 33rpx;
  background-color: transparent;
}

  .slash{
    margin: 0rpx 15rpx;
    font-size: 40rpx;
  }
}



/* 语言切换栏样式 */
.language-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20rpx 0;
}

.language-text {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  padding: 8rpx 18rpx;
  border-radius: 30rpx;
}

.from-language {
  font-size: 33rpx;
  color: #2D2D2D;
}

.direction-icon {
  color: #818181;
  margin: 0 20rpx;
  font-size: 34rpx;
}

.to-language {
  font-size: 33rpx;
  color: #2D2D2D;
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-content {
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #058bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}


.back-icon {
  width: 20rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  padding: 10rpx;
}

.cursor {
  display: inline-block;
  width: 6rpx;
  height: 36rpx;
  background: skyblue;
  vertical-align: bottom;
  margin-left: 2px;
  animation: blink 1.3s infinite;
}
@keyframes blink {
  0%, 100% { background: skyblue; }
  50% { background: transparent; opacity: 0; }
}
</style>
