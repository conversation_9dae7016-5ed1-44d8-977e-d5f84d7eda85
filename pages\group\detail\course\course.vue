<template>
  <view class="course-container">
    <scroll-view 
      scroll-y 
      refresher-enabled 
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      class="scroll-container"
    >
      <s-empty v-if="!sourceList?.length" text="暂无课程数据" />

      <view v-else class="course-list">
        <course-card
          v-for="(course, index) in sourceList"
          :key="index"
          :course="course"
          @click="goToStudy"
          @longpress="showActionSheet(course.id, $event)"
        />
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import sheep from '@/sheep';
  import Empty from '@/uni_modules/z-paging/components/z-paging/js/modules/empty';
  import LessonApi from '@/sheep/api/course/lesson';
  import CourseCard from '@/components/course-card/course-card.vue';
  import GroupApi from '@/sheep/api/group';

  // 接收班级ID参数
  const props = defineProps({
    classId: {
      type: String,
      default: '',
    },
  });

  // 课时列表
  const sourceList = ref([]);
  const loading = ref(false);
  const refreshing = ref(false);
  
  // 班级信息
  const classInfo = ref(null);
  
  // 是否为班级创建者
  const isClassCreator = ref(false);

  // 下拉刷新
  const onRefresh = async () => {
    refreshing.value = true;
    await loadLessons();
    refreshing.value = false;
  };

  // 跳转到课程资料详情页面
  const goToStudy = (course) => {
    sheep.$router.go(`/pages/group/detail/course/lesson-detail?id=${course.id}&tab=0&groupId=${props.classId}&isCreator=${isClassCreator.value}`);
  };

  // 加载班级课时数据
  const loadLessons = async () => {
    if (!props.classId) return;
    
    if (!refreshing.value) {
      sheep.$helper.toast('加载中...');
    }
    const res = await LessonApi.getLessonListByGroupId(props.classId);
    if (res.code === 0 && res.data) {
      sourceList.value = res.data.map(item => ({
        id: item.id,
        title: item.title || '未命名课程',
        subtitle: item.description || '暂无课程描述',
        date: formatDate(item.createTime),
        nickname: item.nickname || ''
      }));
    } else {
      sourceList.value = [];
      sheep.$helper.toast(res.msg || '获取课程列表失败');
    }
  };
  
  // 格式化日期
  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  };

  // 加载班级信息并判断是否为创建者
  const loadClassInfo = async () => {
    if (!props.classId) return;
    // 获取班级详情
    const { code, data } = await GroupApi.getGroup(props.classId);
    if (code === 0) {
      classInfo.value = data;

      // 获取当前用户信息
      const userStore = sheep.$store('user');
      let currentUserInfo = userStore.userInfo;
      if (!currentUserInfo || !currentUserInfo.id) {
        currentUserInfo = await userStore.updateUserData();
      }

      // 判断是否为班级创建者
      const currentUserId = currentUserInfo?.id;
      const classCreatorId = data.userId;
      isClassCreator.value = currentUserId && classCreatorId && Number(currentUserId) === Number(classCreatorId);
    }
  };
  
  // 长按显示操作菜单
  const showActionSheet = async (courseId, e) => {
    if (e) e.stopPropagation();
    
    // 只有班级创建者才能操作
    if (!isClassCreator.value) {
      sheep.$helper.toast('只有班级创建者可以移除课程');
      return;
    }

    // 显示操作菜单
    uni.showActionSheet({
      itemList: ['移除'],
      itemColor: '#000000',
      success: (res) => {
        if (res.tapIndex === 0) {
          // 移除课程
          confirmRemoveCourse(courseId);
        }
      },
    });
  };

  // 确认移除课程
  const confirmRemoveCourse = (courseId) => {
    uni.showModal({
      title: '确认移除',
      content: '确定要将此课程从班级中移除吗？移除后班级成员将无法访问此课程。',
      confirmText: '移除',
      confirmColor: '#ff0000',
      success: async (res) => {
        if (res.confirm) {
          try {
            uni.showLoading({
              title: '移除中...',
              mask: true,
            });
            
            // 调用批量删除接口
            const response = await LessonApi.batchDeleteClassCourses({
              courseIds: [courseId],
              classId: props.classId
            });
            
            uni.hideLoading();
            
            if (response.code === 0) {
              sheep.$helper.toast('移除成功');
              // 刷新课程列表
              loadLessons();
            } else {
              sheep.$helper.toast(response.msg || '移除失败');
            }
          } catch (error) {
            uni.hideLoading();
            sheep.$helper.toast('网络异常，请稍后再试');
            console.error('移除课程失败:', error);
          }
        }
      },
    });
  };

  // 监听班级ID变化
  watch(() => props.classId, (newVal) => {
    if (newVal) {
      loadLessons();
      loadClassInfo();
    }
  });

  // 组件挂载时加载数据
  onMounted(() => {
    if (props.classId) {
      loadLessons();
      loadClassInfo();
    }
  });
  
  // 向父组件暴露方法
  defineExpose({
    loadLessons
  });
</script>

<style lang="scss" scoped>
  .course-container {
    min-height: 80vh;
    padding: 20rpx 0;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .scroll-container {
    flex: 1;
    height: calc(100vh - 40rpx);
  }

  .loading-box {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    min-height: 300rpx;
  }

  .course-list {
    padding: 0 20rpx;
  }

  .course-item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 17rpx;
    margin-bottom: 30rpx;
    padding: 24rpx 32rpx 18rpx 32rpx;
    box-shadow: 0rpx 0rpx 13rpx 0rpx rgba(200,200,200,0.55);
    position: relative;
    transition: box-shadow 0.2s;
    cursor: pointer;
  }

  .course-main {
    display: flex;
    flex-direction: row;
    width: 100%;
    align-items: center;
    padding-right: 50rpx;
  }

  .course-icon {
    width: 85rpx;
    height: 85rpx;
    border-radius: 16rpx;
    margin-right: 24rpx;
    background: #f2f6fa;
    flex-shrink: 0;
    margin-top: 4rpx;
  }

  .course-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-width: 0;
    margin-top: 2rpx;
  }

  .course-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #222;
    margin-bottom: 6rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .course-nickname {
    font-size: 24rpx;
    color: #888;
    margin-bottom: 6rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .course-subtitle {
    font-size: 24rpx;
    color: #b0b0b0;
    line-height: 1.4;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-top: 20rpx;
    width: calc(100% - 109rpx - 14rpx);
  }

  .come-icon {
    width: 14rpx;
    height: 24rpx;
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    flex-shrink: 0;
  }
</style>
