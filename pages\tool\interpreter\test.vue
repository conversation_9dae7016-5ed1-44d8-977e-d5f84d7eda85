<template>
  <view>
    <view class="container">
    <easy-navbar title="同声传译" />

    <scroll-view class="chat-window" scroll-y="true" :scroll-top="scrollTop">
      <view v-for="(bubble, index) in chatBubbles" :key="bubble.id" :class="['chat-bubble', bubble.user ? 'user' : '']">
        <view class="translated-text-container">
          <text class="translated-text">{{ bubble.translatedText }}</text>
          <image
              v-if="!isAutoPlayEnabled && bubble.audioSrc"
              :src="getSpeakerIcon(bubble.id)"
              class="speaker-icon"
              @click="playBubbleAudio(bubble)"
          />
        </view>
        <hr v-if="bubble.originalText && bubble.translatedText" class="translation-divider">
        <view class="original-text">
          <text :class="{ 'placeholder': bubble.placeholder }">{{ bubble.originalText || '...' }}</text>
        </view>
      </view>
    </scroll-view>

    <view class="footer">
      <view class="language-controls">
        <picker :disabled="isRecording" :value="fromLanguageIndex" :range="languageOptions" range-key="label" @change="onFromLanguageChange" class="language-select">
          <view>{{ languageOptions[fromLanguageIndex].label }}</view>
        </picker>
        <button :disabled="isRecording" @click="swapLanguages" class="swap-button">⇄</button>
        <picker :disabled="isRecording" :value="toLanguageIndex" :range="languageOptions" range-key="label" @change="onToLanguageChange" class="language-select">
          <view>{{ languageOptions[toLanguageIndex].label }}</view>
        </picker>
        <button @click="openSettings" class="settings-button"></button>
      </view>
      <button :class="['control-button', { 'recording': isRecording }]" @click="toggleRecording">
        {{ isRecording ? '停止说话' : '开始说话' }}
      </button>
    </view>
  </view>

  <!-- 设置弹出层 -->
  <su-popup ref="settingsPopup" type="center" round="10">
    <view class="settings-panel">
      <view class="settings-header">
        <text>设置</text>
        <text class="close-button" @click="closeSettings">完成</text>
      </view>
      <view class="setting-item">
        <text>自动播放翻译结果</text>
        <switch :checked="isAutoPlayEnabled" @change="onAutoPlayChange" />
      </view>
    </view>
  </su-popup>
</view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import CryptoJS from '@/sheep/util/crypto.js';
import NlsApi from '@/sheep/api/voice/nls.js';
import {getTtsConfig, TtsTypeEnum, ImagePath} from "@/sheep/util/language-detector";
import sheep from "@/sheep";

// --- 持久化存储键 ---
const STORAGE_KEY_AUTO_PLAY = 'interpreter_auto_play';
const STORAGE_KEY_FROM_LANG = 'interpreter_from_language';
const STORAGE_KEY_TO_LANG = 'interpreter_to_language';

// --- 配置信息 ---
const APP_KEY = "60310af61cc10ed1";
const APP_SECRET = "bEfx49Wj0d1Un0doD7PfauVKWnghvxN9";

// --- 响应式状态 ---
const isRecording = ref(false);
const chatBubbles = ref([]);
const scrollTop = ref(0);
const audioQueue = ref([]); // 音频播放队列
const isAudioPlaying = ref(false); // 是否有音频正在自动播放
const isAutoPlayEnabled = ref(true); // 是否开启自动播放
const manuallyPlayingId = ref(null); // 正在手动播放的bubble id
const settingsPopup = ref(null); // 设置弹出层的引用

const languageOptions = ref([
  { value: 'zh-CHS', label: '中文' },
  { value: 'en', label: '英文' },
  { value: 'th', label: '泰语' },
  { value: 'vi', label: '越南语' },
  { value: 'id', label: '印尼语' },
  { value: 'ms', label: '马来语' },
  { value: 'km', label: '高棉语' },
  { value: 'lo', label: '老挝语' },
  { value: 'my', label: '缅甸语' },
  { value: 'tl', label: '菲律宾语' },
]);
const fromLanguageIndex = ref(0); // 默认中文
const toLanguageIndex = ref(2);   // 默认泰语

// --- 录音和 WebSocket ---
let recorderManager;
let socketTask;
let audioBuffer = [];
let isConnecting = ref(false);

onMounted(() => {
  recorderManager = uni.getRecorderManager();
  setupRecorderListeners();

  // 加载用户设置
  const savedAutoPlay = uni.getStorageSync(STORAGE_KEY_AUTO_PLAY);
  if (savedAutoPlay !== '' && savedAutoPlay !== null) {
    isAutoPlayEnabled.value = savedAutoPlay;
  }

  const savedFromLang = uni.getStorageSync(STORAGE_KEY_FROM_LANG);
  if (savedFromLang !== '' && savedFromLang !== null) {
    fromLanguageIndex.value = savedFromLang;
  }

  const savedToLang = uni.getStorageSync(STORAGE_KEY_TO_LANG);
  if (savedToLang !== '' && savedToLang !== null) {
    toLanguageIndex.value = savedToLang;
  }
});

onUnmounted(() => {
  if (isRecording.value) {
    recorderManager.stop();
  }
  if (socketTask) {
    socketTask.close();
  }
  // 清理音频资源
  stopAllAudio();
});

const getSign = (salt, curtime) => {
  const str = APP_KEY + salt + curtime + APP_SECRET;
  return CryptoJS.SHA256(str).toString();
};

const connectWebSocket = () => {
  const salt = Math.random().toString(36).substring(2);
  const curtime = Math.floor(Date.now() / 1000);
  const sign = getSign(salt, curtime);
  const from = languageOptions.value[fromLanguageIndex.value].value;
  const to = languageOptions.value[toLanguageIndex.value].value;
  
  const url = `wss://openapi.youdao.com/stream_speech_trans?appKey=${APP_KEY}&salt=${salt}&curtime=${curtime}&sign=${sign}&signType=v4&from=${from}&to=${to}&format=wav&channel=1&version=v1&rate=16000`;
  
  console.log('连接到 WebSocket:', url);
  
  socketTask = uni.connectSocket({
    url,
    success: () => {},
    fail: (err) => {
      console.error('WebSocket 连接失败:', err);
      uni.showToast({ title: '网络开小差了，请稍后重试', icon: 'none' });
    },
  });

  socketTask.onOpen(() => {
    console.log('WebSocket 已连接.');
    isConnecting.value = false;
    
    // 在连接成功后开始录音
    console.log('准备开始录音...');
    const options = {
      duration: 60000,
      sampleRate: 16000,
      numberOfChannels: 1,
      format: 'wav',
      frameSize: 1.28, // 按照有道文档建议，每 40ms 发送 1280 字节，16000*16/8*0.04 = 1280, 1280/1024=1.25k
    };
    recorderManager.start(options);

    // 发送缓存的音频数据
    if (audioBuffer.length > 0) {
      console.log(`发送 ${audioBuffer.length} 个缓冲的音频数据包.`);
      audioBuffer.forEach(buffer => {
        socketTask.send({ data: buffer });
      });
      audioBuffer = []; // 清空缓冲区
    }
  });

  socketTask.onMessage((res) => {
    console.log('收到消息:', res.data);
    const data = JSON.parse(res.data);
    if (data.errorCode === "0") {
      if (data.action === "started") {
        console.log("握手成功");
      } else {
        handleWsMessage(data);
      }
    } else if (data.errorCode !== "304") { // 304是会话闲置超时，不视为错误
      console.error('WebSocket 消息错误:', data);
      uni.showToast({ title: `服务开了个小差(${data.errorCode})，请稍后再试`, icon: 'none' });
    }
  });

  socketTask.onClose((res) => {
    console.log('WebSocket 已关闭:', res);
    if (isRecording.value) {
      // 如果 socket 关闭时仍在录音，则停止录音
      recorderManager.stop();
    }
    socketTask = null;
    isConnecting.value = false;
  });

  socketTask.onError((err) => {
    console.error('WebSocket 错误:', err);
    if (isRecording.value) {
      // 如果 socket 错误时仍在录音，则停止录音
      recorderManager.stop();
    }
    isConnecting.value = false;
    uni.showToast({ title: '网络似乎不太稳定，请重试', icon: 'none' });
  });
};

const setupRecorderListeners = () => {
  recorderManager.onStart(() => {
    console.log('录音开始');
    isRecording.value = true;
    createNewBubble();
  });

  recorderManager.onStop((res) => {
    console.log('录音停止', res);
    isRecording.value = false;
    if (socketTask) {
      const endData = JSON.stringify({ end: "true" });
      socketTask.send({
        data: endData,
        success: () => console.log('结束帧已发送.'),
        fail: (err) => console.error('发送结束帧失败:', err),
      });
    }
    // --- 如果没有识别到任何文本，则删除空气泡 ---
    const lastBubble = chatBubbles.value[chatBubbles.value.length - 1];
    if (lastBubble && !lastBubble.originalText) {
      chatBubbles.value.pop();
      return; // 直接返回，不进行后续TTS和播放处理
    }

    // --- 处理剩余未转换的翻译文本 ---
    if (lastBubble && lastBubble.translatedText) {
      const remainingText = lastBubble.translatedText.substring(lastBubble.fullTranslatedText.length).trim();
      if (remainingText) {
        console.log('停止录音，处理剩余翻译:', remainingText);
        synthesizeAndEnqueueSpeech(remainingText);
        // 更新已处理文本记录，防止重复
        lastBubble.fullTranslatedText = lastBubble.translatedText;
      }
    }

    // 检查并继续处理音频队列
    processAudioQueue();
  });

  recorderManager.onFrameRecorded((res) => {
    const { frameBuffer, isLastFrame } = res;
    if (!frameBuffer || frameBuffer.byteLength === 0) {
      return;
    }

    // 如果正在播放音频，则忽略当前录音帧，防止将播放的声音传回去
    if (isAudioPlaying.value) {
      return;
    }

    // 如果 WebSocket 正在连接或已连接，则发送数据，否则存入缓冲区
    if (socketTask && socketTask.readyState === 1) {
      socketTask.send({
        data: frameBuffer,
        fail: (err) => console.error('发送帧数据失败:', err),
      });
    } else {
      audioBuffer.push(frameBuffer);
    }

    if (isLastFrame) {
      const lastBubble = chatBubbles.value[chatBubbles.value.length - 1];
      if (lastBubble) {
        lastBubble.placeholder = false;
      }
    }
  });
  
  recorderManager.onError((err) => {
    console.error('录音错误', err);
    isRecording.value = false;
    uni.showToast({ title: '录音功能好像没准备好，请检查权限或稍后重试', icon: 'none' });
  });
};

const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording();
  } else {
    startRecording();
  }
};

const startRecording = () => {
  if (isRecording.value || isConnecting.value) {
    console.warn('录音或连接已在进行中.');
    return;
  }
  isConnecting.value = true;
  audioBuffer = []; // 清空缓冲区
  connectWebSocket();
};

const stopRecording = () => {
  console.log('准备停止录音...');
  recorderManager.stop();
};

const createNewBubble = () => {
  chatBubbles.value.push({
    id: Date.now() + Math.random(), // 唯一ID
    user: true,
    originalText: '',
    translatedText: '',
    placeholder: true,
    fullOriginalText: '',
    fullTranslatedText: '',
    audioSrc: null, // 用于存储TTS音频文件路径
  });
  scrollToBottom();
};

const handleWsMessage = (data) => {
  const lastBubble = chatBubbles.value[chatBubbles.value.length - 1];
  if (!lastBubble || !data.result) return;

  const isFinal = !data.result.partial;
  const sentence = data.result.context || '';
  const translation = data.result.tranContent || '';

  const displayOriginal = lastBubble.fullOriginalText + sentence;
  const displayTranslated = lastBubble.fullTranslatedText + translation;

  lastBubble.originalText = displayOriginal;
  lastBubble.translatedText = displayTranslated;
  lastBubble.placeholder = false;

  if (isFinal && sentence) {
      lastBubble.fullOriginalText += sentence + ' ';
      lastBubble.fullTranslatedText += translation + ' ';
      // 当收到完整句子时，触发TTS播放
      synthesizeAndEnqueueSpeech(translation);
    }
    
    scrollToBottom();
  };

const scrollToBottom = () => {
  nextTick(() => {
    scrollTop.value = chatBubbles.value.length * 1000; // A large number to scroll to bottom
  });
};

// --- 语言选择 ---
const onFromLanguageChange = (e) => {
  fromLanguageIndex.value = e.detail.value;
  uni.setStorageSync(STORAGE_KEY_FROM_LANG, fromLanguageIndex.value);
};

const onToLanguageChange = (e) => {
  toLanguageIndex.value = e.detail.value;
  uni.setStorageSync(STORAGE_KEY_TO_LANG, toLanguageIndex.value);
};

const swapLanguages = () => {
  const temp = fromLanguageIndex.value;
  fromLanguageIndex.value = toLanguageIndex.value;
  toLanguageIndex.value = temp;
  uni.setStorageSync(STORAGE_KEY_FROM_LANG, fromLanguageIndex.value);
  uni.setStorageSync(STORAGE_KEY_TO_LANG, toLanguageIndex.value);
};

// --- TTS 功能 ---
let autoPlayAudioContext = null; // 自动播放的音频上下文
let manualPlayAudioContext = null; // 手动播放的音频上下文

const openSettings = () => {
  settingsPopup.value.open();
};

const closeSettings = () => {
  settingsPopup.value.close();
};

const onAutoPlayChange = (e) => {
  isAutoPlayEnabled.value = e.detail.value;
  uni.setStorageSync(STORAGE_KEY_AUTO_PLAY, isAutoPlayEnabled.value);
  if (!isAutoPlayEnabled.value) {
    // 如果关闭自动播放，则停止当前所有自动播放
    stopAllAudio(true);
  }
};

const getSpeakerIcon = (bubbleId) => {
  if (manuallyPlayingId.value === bubbleId) {
    return sheep.$url.cdn(ImagePath.PLAY_ICON.BLUE_ANIMATED);
  }
  return sheep.$url.cdn(ImagePath.PLAY_ICON.BLUE_STATIC);
};

const playBubbleAudio = async (bubble) => {
  if (!bubble.translatedText) {
    console.warn('该气泡没有可播放的文本');
    return;
  }

  // 如果正在播放其他手动音频，先停止
  if (manualPlayAudioContext) {
    manualPlayAudioContext.stop();
    manualPlayAudioContext.destroy();
    manualPlayAudioContext = null;
  }

  // 如果点击的是正在播放的音频，则停止
  if (manuallyPlayingId.value === bubble.id) {
    manuallyPlayingId.value = null;
    return;
  }

  manuallyPlayingId.value = bubble.id;

  try {
    // 重新请求TTS并获取音频路径
    const audioSrc = await synthesizeAndStoreSpeech(bubble.translatedText, bubble.id, true);
    if (!audioSrc) {
      manuallyPlayingId.value = null;
      return;
    }

    manualPlayAudioContext = uni.createInnerAudioContext();
    manualPlayAudioContext.src = audioSrc;
    manualPlayAudioContext.play();

    manualPlayAudioContext.onEnded(() => {
      manuallyPlayingId.value = null;
      if (manualPlayAudioContext) {
        manualPlayAudioContext.destroy();
        manualPlayAudioContext = null;
      }
    });
    manualPlayAudioContext.onError(() => {
      manuallyPlayingId.value = null;
      if (manualPlayAudioContext) {
        manualPlayAudioContext.destroy();
        manualPlayAudioContext = null;
      }
    });
  } catch (error) {
    console.error('手动播放失败:', error);
    manuallyPlayingId.value = null;
  }
};

const stopAllAudio = (onlyAuto = false) => {
  if (autoPlayAudioContext) {
    autoPlayAudioContext.stop();
    autoPlayAudioContext.destroy();
    autoPlayAudioContext = null;
  }
  audioQueue.value = [];
  isAudioPlaying.value = false;

  if (!onlyAuto) {
    if (manualPlayAudioContext) {
      manualPlayAudioContext.stop();
      manualPlayAudioContext.destroy();
      manualPlayAudioContext = null;
    }
    manuallyPlayingId.value = null;
  }
};

/**
 * 处理音频队列播放
 */
const processAudioQueue = () => {
  if (!isAutoPlayEnabled.value || isAudioPlaying.value || audioQueue.value.length === 0) {
    return;
  }

  const audioFilePath = audioQueue.value.shift();
  if (!audioFilePath) return;

  isAudioPlaying.value = true;
  autoPlayAudioContext = uni.createInnerAudioContext();
  autoPlayAudioContext.src = audioFilePath;

  autoPlayAudioContext.onEnded(() => {
    isAudioPlaying.value = false;
    if (autoPlayAudioContext) {
      autoPlayAudioContext.destroy();
      autoPlayAudioContext = null;
    }
    processAudioQueue();
  });

  autoPlayAudioContext.onError((res) => {
    console.error('自动播放错误:', audioFilePath, res.errMsg);
    isAudioPlaying.value = false;
    if (autoPlayAudioContext) {
      autoPlayAudioContext.destroy();
      autoPlayAudioContext = null;
    }
    processAudioQueue();
  });

  autoPlayAudioContext.play();
};

watch(audioQueue, () => {
  processAudioQueue();
}, { deep: true });

const synthesizeAndStoreSpeech = async (text, bubbleId) => {
  if (!text || !bubbleId) return;

  try {
    const ttsConfig = getTtsConfig(text, TtsTypeEnum.ALIYUN);
    const params = {
      text,
      speaker: ttsConfig.speaker,
      speechRate: ttsConfig.speechRate,
      pitchRate: ttsConfig.pitchRate,
      displayCaptions: false,
    };

    const res = await NlsApi.ttsAliyun(params, { showLoading: false });
    if (res?.msg) {
      console.error('TTS 请求失败:', res.msg);
      return null;
    }

    const manager = uni.getFileSystemManager();
    const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;
    await new Promise((resolve, reject) => {
      manager.writeFile({
        filePath: tempFilePath,
        data: res,
        encoding: 'binary',
        success: resolve,
        fail: reject,
      });
    });

    const bubble = chatBubbles.value.find(b => b.id === bubbleId);
    if (bubble) {
      bubble.audioSrc = tempFilePath;
      console.log(`音频已生成并存储: ${tempFilePath}`);
      return tempFilePath;
    }
  } catch (error) {
    console.error('TTS 合成或文件保存失败:', error);
  }
  return null;
};

const synthesizeAndEnqueueSpeech = async (text) => {
  const lastBubble = chatBubbles.value[chatBubbles.value.length - 1];
  if (!lastBubble) return;

  const audioSrc = await synthesizeAndStoreSpeech(text, lastBubble.id);
  if (audioSrc && isAutoPlayEnabled.value) {
    audioQueue.value.push(audioSrc);
    console.log(`音频已加入自动播放队列: ${audioSrc}`);
  }
};

</script>

<style scoped>
/* 变量定义 */
:root {
  --primary-color: #007AFF;
  --secondary-color: #5AC8FA;
  --background-color: #F4F6F8;
  --surface-color: #FFFFFF;
  --text-color: #1D1D1F;
  --text-color-secondary: #8A8A8E;
  --bubble-background: #FFFFFF;
  --bubble-user-background: #DDEEFE;
  --border-color: #E5E5EA;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--surface-color);
}

.chat-window {
  flex-grow: 1;
  padding: 38rpx;
  background-color: var(--background-color);
  overflow-y: auto;
}

.chat-bubble {
  max-width: 85%;
  padding: 24rpx 36rpx;
  border-radius: 40rpx;
  margin-bottom: 36rpx;
  word-wrap: break-word;
  background-color: var(--bubble-background);
  border: 1px solid var(--border-color);
  position: relative;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.04);
}

.chat-bubble.user {
  margin-left: auto;
  background: linear-gradient(135deg, #eaf6ff, var(--bubble-user-background));
  border-color: transparent;
}

.original-text {
  font-size: 28rpx;
  color: var(--text-color-secondary);
  line-height: 1.5;
}

.placeholder {
  color: var(--text-color-secondary);
  opacity: 0.6;
}

.translated-text-container {
  line-height: 1.5;
}

.speaker-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

.translation-divider {
  border: 0;
  border-top: 1px dashed #D1D1D6;
  margin: 20rpx 0;
}

.translated-text {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-color);
  vertical-align: middle;
}

.footer {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding: 30rpx;
  background-color: var(--surface-color);
  border-top: 1px solid var(--border-color);
}

.language-controls {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 100%;
}

.settings-button {
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;
  border-radius: 24rpx;
  border: 1px solid var(--border-color);
  background-color: #F0F0F0;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238A8A8E'%3E%3Cpath d='M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.23c-0.04-0.24-0.24-0.41-0.48-0.41h-3.84 c-0.24,0-0.44,0.17-0.48,0.41L9.18,4.59C8.59,4.82,8.06,5.14,7.56,5.52L5.17,4.56C4.95,4.49,4.7,4.56,4.59,4.78L2.67,8.1 c-0.11,0.2-0.06,0.47,0.12,0.61l2.03,1.58C4.78,10.66,4.76,10.97,4.76,11.3c0,0.32,0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.42,2.36 c0.04,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.48-0.41l0.42-2.36c0.59-0.23,1.12-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0.01,0.59-0.22l1.92-3.32C21.36,13.35,21.31,13.08,21.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z'/%3E%3C/svg%3E");
  background-size: 50%;
  background-repeat: no-repeat;
  background-position: center;
}

.settings-panel {
  width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 34rpx;
  font-weight: 600;
  color: var(--text-color);
  padding-bottom: 20rpx;
  border-bottom: 1px solid var(--border-color);
}

.close-button {
  font-size: 30rpx;
  color: var(--primary-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  font-size: 30rpx;
  color: var(--text-color);
}

.language-select {
  flex: 1;
  width: 100%;
  padding: 20rpx;
  border-radius: 24rpx;
  border: 1px solid var(--border-color);
  background-color: #F0F0F0;
  color: var(--text-color);
  font-size: 28rpx;
  text-align: center;
}

.swap-button {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  background-color: #F0F0F0;
  color: var(--text-color-secondary);
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.control-button {
  width: 100%;
  padding: 24rpx 50rpx;
  border: none;
  border-radius: 50rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.25);
  margin-bottom: 20rpx;
}

.control-button.recording {
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.4);
}
</style>