<template>
  <view class="container">
    <easy-navbar title="拍照翻译" />

    <scroll-view scroll-y class="main-content" :scroll-top="scrollTop" scroll-with-animation>
      <!-- 图片预览区域 -->
      <view class="image-container">
        <movable-area class="movable-area">
          <movable-view
            class="movable-view"
            direction="all"
            :scale="true"
            :scale-min="1"
            :scale-max="4"
            :scale-value="1"
            out-of-bounds
          >
            <image v-if="previewImage" class="preview-image" :src="previewImage" mode="aspectFit" />
          </movable-view>
        </movable-area>
      </view>

      <!-- 识别结果区域 -->
      <view class="recognize-result">
        <text class="result-title">已识别图片内容：</text>
        <view class="result-content">
          <template v-for="(item, index) in recognizedTextList" :key="index">
            <view class="text-content">
              <view class="text-item">
                <text selectable>{{ item }}</text>
                <image
                  class="play-icon"
                  :src="getPlayIconSource(PlayType.SINGLE, index)"
                  mode="heightFix"
                  @click="playTextSingle(item, index)"
                />
              </view>
            </view>
          </template>
        </view>

        <!-- 复制和播放按钮 -->
        <view class="action-buttons">
          <view class="action-group">
            <button class="copy-btn" @click="handleCopySource">
              <image
                class="btn-icon"
                :src="sheep.$url.cdn(ImagePath.COPY_ICON)"
                mode="heightFix"
              />
              <text class="btn-text">一键复制</text>
            </button>
            <button class="play-btn" @click="playAllSource">
              <image
                class="btn-icon"
                :src="getPlayIconSource(PlayType.ALL_SOURCE)"
                mode="heightFix"
              />
              <text class="btn-text">{{ currentPlayingType === PlayType.ALL_SOURCE ? '暂停播放' : '播放全文' }}</text>
            </button>
          </view>
        </view>

        <!-- 语言切换和翻译按钮 -->
        <view class="language-bar">
          <view class="language-text" @click="toggleTranslationMode">
            <text>{{ fromLanguage }}</text>
            <text class="direction-icon">⇌</text>
            <text>{{ toLanguage }}</text>
          </view>
          <button class="translate-btn" @click="translate">翻译</button>
        </view>
      </view>

      <!-- 翻译结果区域 -->
      <view class="translate-result" v-if="showTranslation" id="translate-result-section">
        <text class="result-title">翻译结果：</text>
        <view class="result-content">
          <template v-for="(text, index) in translatedTextList" :key="index">
            <view class="text-content">
              <text selectable>
                {{ text }}
                <template v-if="typing && index === translatedTextList.length - 1"><text class="cursor"></text></template>
              </text>
            </view>
          </template>
          <view id="bottom-anchor"></view>
        </view>

        <!-- 复制和播放按钮 -->
        <view class="action-buttons">
          <view class="action-group">
            <button class="copy-btn" @click="handleCopyTranslation">
              <image
                class="btn-icon"
                :src="sheep.$url.cdn(ImagePath.COPY_ICON)"
                mode="heightFix"
              />
              <text class="btn-text">一键复制</text>
            </button>
            <button class="play-btn" @click="playAllTranslation">
              <image
                class="btn-icon"
                :src="getPlayIconSource(PlayType.ALL_TRANSLATION)"
                mode="heightFix"
              />
              <text class="btn-text">{{ currentPlayingType === PlayType.ALL_TRANSLATION ? '暂停播放' : '播放全文' }}</text>
            </button>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { computed, nextTick, ref } from 'vue';
  import { onHide, onLoad, onUnload } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import TranslateApi from '@/sheep/api/translate';
  import {
    getLanguageCodeByLabel,
    getTextLanguage,
    getTtsConfig,
    ImagePath,
    LanguageEnum,
    PlayType, TtsTypeEnum,
  } from '@/sheep/util/language-detector';
  import NlsApi from '@/sheep/api/voice/nls';
  import { AiModelEnum } from '@/sheep/util/const';
  import ConversationApi from '@/sheep/api/text-optimizer/conversation';
  import { createStreamHandler } from '@/sheep/util/stream-parser';
  import { debounce } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common';


  // 状态管理
  const userInfo = computed(() => sheep.$store('user').userInfo);
  const conversationId = ref(''); // 会话ID
  const recognizedTextList = ref([]); // 存储识别的文本列表
  const previewImage = ref(''); // 预览图片路径
  const translatedTextList = ref([]); // 存储翻译后的文本列表
  const pendingText = ref(''); // 打字机待显示内容
  const typing = ref(false); // 是否正在打字
  let typingTimer = null; // 打字机定时器
  let firstCharPrinted = false; // 是否已打印第一个字符
  const showTranslation = ref(false); // 是否显示翻译结果
  const fromLanguage = ref(LanguageEnum.ZH.label); // 源语言
  const toLanguage = ref(LanguageEnum.TH.label); // 目标语言
  const cacheKey = 'ocr_cache'; // 缓存键
  const currentPlayingIndex = ref(null); // 当前播放的单句索引
  const currentPlayingType = ref(null); // 当前播放类型
  const isTranslated = ref(false); // 是否为翻译文本
  const scrollTop = ref(0); // 滚动位置
  const lastPlayedText = ref(''); // 记录上次播放的文本内容
  let audioContext = null; // 音频上下文

  // 页面加载时执行
  onLoad(async () => {
    loadFromCache();
    
    // 读取缓存中的会话ID
    const cachedConversationId = uni.getStorageSync('translate_conversation_id');
    if (cachedConversationId) {
      conversationId.value = cachedConversationId;
    } else {
      // 没有缓存的会话ID，先获取会话列表
      await loadLatestConversation();
    }
  });

  // 加载最新的会话
  const loadLatestConversation = async () => {
    const { code, data } = await ConversationApi.getConversationPageByModelName({
      pageNo: 1,
      pageSize: 10,
      userId: userInfo.value.id,
      modelName: AiModelEnum.TEXT_TRANSLATION.name,
    });

    const conversationData = data.list || [];

    if (conversationData.length > 0) {
      const latestConversation = conversationData[0];
      conversationId.value = latestConversation.id.toString();
      // 保存会话ID到缓存
      uni.setStorageSync('translate_conversation_id', conversationId.value);
    } else {
      // 创建会话
      const res = await ConversationApi.createConversationByName(AiModelEnum.TEXT_TRANSLATION.name);
      if (res.code !== 0) {
        return;
      }
      conversationId.value = res.data.toString();
      // 保存会话ID到缓存
      uni.setStorageSync('translate_conversation_id', conversationId.value);
    }
  };

  // 页面卸载时清理
  onUnload(() => {
    uni.removeStorageSync(cacheKey); // 清除缓存
    stopAudio(); // 停止音频播放
    if (typingTimer) {
      clearInterval(typingTimer);
      typingTimer = null;
    }
  });
  onHide(() => {
    uni.removeStorageSync(cacheKey);
    stopAudio();
    if (typingTimer) {
      clearInterval(typingTimer);
      typingTimer = null;
    }
  });

  // 从缓存加载数据
  const loadFromCache = () => {
    const cacheData = uni.getStorageSync(cacheKey);
    if (!cacheData) {
      sheep.$helper.toast('加载失败，请重试');
      setTimeout(() => {
        sheep.$router.back(); // 返回上一页
      }, 1500);
      return;
    }

    const data = JSON.parse(cacheData);
    previewImage.value = data.imageUrl; // 设置预览图片
    recognizedTextList.value = data.OCRResponse?.textDetections || []; // 设置识别文本

    if (recognizedTextList.value.length > 0) {
      detectLanguage(recognizedTextList.value.join('\n')); // 检测语言
    }
  };

  // 复制识别文本
  const handleCopySource = () => {
    if (!recognizedTextList.value.length) return;

    const textToCopy = recognizedTextList.value.join('\n');
    sheep.$helper.copyText(textToCopy); // 复制文本到剪贴板
  };

  // 复制翻译文本
  const handleCopyTranslation = () => {
    if (!translatedTextList.value.length) return;

    const textToCopy = translatedTextList.value.join('\n');
    sheep.$helper.copyText(textToCopy); // 复制文本到剪贴板
  };

  // 获取播放图标路径
  const getPlayIconSource = (type, index = null, isTranslation = false) => {
    // 单句播放中图标
    if (
      type === PlayType.SINGLE &&
      currentPlayingType.value === PlayType.SINGLE &&
      currentPlayingIndex.value === index &&
      isTranslated.value === isTranslation
    ) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.GREEN_ANIMATED);
    }
    // 原文全文播放中图标
    if (type === PlayType.ALL_SOURCE && currentPlayingType.value === PlayType.ALL_SOURCE) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.WHITE_ANIMATED);
    }
    // 翻译全文播放中图标
    if (type === PlayType.ALL_TRANSLATION && currentPlayingType.value === PlayType.ALL_TRANSLATION) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.WHITE_ANIMATED);
    }
    // 默认图标
    if (type === PlayType.SINGLE) {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.GREEN_STATIC);
    } else {
      return sheep.$url.cdn(ImagePath.PLAY_ICON.WHITE_STATIC);
    }
  };

  // 停止音频播放
  const stopAudio = () => {
    if (audioContext) {
      audioContext.stop();
      audioContext.destroy();
      audioContext = null;
    }
    currentPlayingIndex.value = null;
    currentPlayingType.value = null;
    isTranslated.value = false;
    lastPlayedText.value = ''; // 清空上次播放的文本记录
  };

  // 播放单句
  const _playTextSingle = async (text, index, translation = false) => {
    if (!text) return;
    
    // 如果当前正在播放这句话，则暂停
    if (
      currentPlayingType.value === PlayType.SINGLE &&
      currentPlayingIndex.value === index &&
      isTranslated.value === translation &&
      audioContext
    ) {
      audioContext.pause();
      currentPlayingType.value = null;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      lastPlayedText.value = text; // 记录暂停的文本
      return;
    }
    
    // 如果已经创建了音频上下文但是暂停了，且要播放的是同一段文本，则继续播放
    if (audioContext && !currentPlayingType.value && lastPlayedText.value === text) {
      currentPlayingType.value = PlayType.SINGLE;
      currentPlayingIndex.value = index;
      isTranslated.value = translation;
      audioContext.play();
      return;
    }
    
    // 如果是新文本或者没有暂停的音频，则创建新的播放
    stopAudio();
    currentPlayingType.value = PlayType.SINGLE;
    currentPlayingIndex.value = index;
    isTranslated.value = translation;
    lastPlayedText.value = text; // 记录当前播放的文本

    await commonPlayText(text); // 播放文本
  };
  
  // 使用防抖包装播放单句函数
  const playTextSingle = debounce(_playTextSingle, 500, true);

  // 播放原文全文
  const _playAllSource = async () => {
    if (!recognizedTextList.value.length) return;
    const sourceFullText = recognizedTextList.value.join('\n');
    
    // 如果当前正在播放原文全文，则暂停
    if (currentPlayingType.value === PlayType.ALL_SOURCE && audioContext) {
      audioContext.pause();
      currentPlayingType.value = null;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      lastPlayedText.value = sourceFullText; // 记录暂停的文本
      return;
    }
    
    // 如果已经创建了音频上下文但是暂停了，且要播放的是同一段文本，则继续播放
    if (audioContext && !currentPlayingType.value && lastPlayedText.value === sourceFullText) {
      currentPlayingType.value = PlayType.ALL_SOURCE;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      audioContext.play();
      return;
    }
    
    // 如果是新文本或者没有暂停的音频，则创建新的播放
    stopAudio();
    lastPlayedText.value = sourceFullText; // 记录当前播放的文本

    await commonPlayText(sourceFullText); // 播放全文
  };
  
  // 使用防抖包装原文全文播放函数
  const playAllSource = debounce(_playAllSource, 500, true);

  // 播放翻译全文
  const _playAllTranslation = async () => {
    if (!translatedTextList.value.length) return;
    const translationFullText = translatedTextList.value.join('\n');
    
    // 如果当前正在播放翻译全文，则暂停
    if (currentPlayingType.value === PlayType.ALL_TRANSLATION && audioContext) {
      audioContext.pause();
      currentPlayingType.value = null;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
      lastPlayedText.value = translationFullText; // 记录暂停的文本
      return;
    }
    
    // 如果已经创建了音频上下文但是暂停了，且要播放的是同一段文本，则继续播放
    if (audioContext && !currentPlayingType.value && lastPlayedText.value === translationFullText) {
      currentPlayingType.value = PlayType.ALL_TRANSLATION;
      currentPlayingIndex.value = null;
      isTranslated.value = true;
      audioContext.play();
      return;
    }
    
    // 如果是新文本或者没有暂停的音频，则创建新的播放
    stopAudio();
    lastPlayedText.value = translationFullText; // 记录当前播放的文本

    await commonPlayText(translationFullText); // 播放全文
  };
  
  // 使用防抖包装翻译全文播放函数
  const playAllTranslation = debounce(_playAllTranslation, 500, true);

  // 通用播放文本函数
  const commonPlayText = async (text) => {
    let ttsConfig = getTtsConfig(text, TtsTypeEnum.OPENAI);

    if (!ttsConfig) {
      stopAudio();
      return;
    }

    const params = {
      text,
      speaker: ttsConfig.speaker,
      pitchRate: ttsConfig.pitchRate,
      displayCaptions: false,
    };

    const res = await NlsApi.ttsOpenAI(params); // 文本转语音

    if (res?.msg) {
      stopAudio();
      return;
    }

    // 接口返回数据后才设置播放状态
    // 判断当前播放的是原文还是翻译
    const isTranslationText = translatedTextList.value.join('\n') === text;
    if (isTranslationText) {
      currentPlayingType.value = PlayType.ALL_TRANSLATION;
      currentPlayingIndex.value = null;
      isTranslated.value = true;
    } else {
      currentPlayingType.value = PlayType.ALL_SOURCE;
      currentPlayingIndex.value = null;
      isTranslated.value = false;
    }

    const manager = uni.getFileSystemManager();
    const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;
    await new Promise((resolve, reject) => {
      manager.writeFile({
        filePath: tempFilePath,
        data: res,
        encoding: 'binary',
        success: resolve,
        fail: reject,
      });
    });
    await playFromUrl(tempFilePath); // 播放音频文件
  };

  // 从URL播放音频
  const playFromUrl = (audioUrl) => {
    return new Promise((resolve, reject) => {
      try {
        audioContext = uni.createInnerAudioContext();

        if (!audioContext) {
          reject(new Error('无法创建音频上下文'));
          return;
        }

        audioContext.src = audioUrl;

        audioContext.onEnded(() => {
          stopAudio(); // 播放结束
          resolve();
        });

        audioContext.onError(() => {
          stopAudio(); // 播放错误
          reject(new Error('音频播放失败'));
        });

        audioContext.play(); // 开始播放
      } catch (error) {
        stopAudio();
        reject(error);
      }
    });
  };

  // 切换翻译模式
  const toggleTranslationMode = () => {
    const tmp = fromLanguage.value;
    fromLanguage.value = toLanguage.value;
    toLanguage.value = tmp; // 交换源语言和目标语言
  };

  // 翻译状态
  const isTranslating = ref(false);

  // 打字机函数
  function getTypingInterval(len) {
    if (len >= 100) return 10;
    if (len <= 20) return 80;
    return 80 - Math.floor((len - 20) * (70 / 80));
  }

  function startTyping() {
    if (typingTimer) {
      clearInterval(typingTimer);
      typingTimer = null;
    }
    if (!pendingText.value || pendingText.value.length === 0) {
      typing.value = false;
      return;
    }
    typing.value = true;
    let tempText = translatedTextList.value.join('\n');
    typingTimer = setInterval(() => {
      if (pendingText.value.length > 0) {
        if (!firstCharPrinted) {
          uni.hideLoading();
          firstCharPrinted = true;
        }
        tempText += pendingText.value[0];
        pendingText.value = pendingText.value.slice(1);
        translatedTextList.value = tempText.split('\n');
        // 新增：每次内容有变化都滚动到底部（锚点法）
        nextTick(() => {
          uni.createSelectorQuery()
            .select('#bottom-anchor')
            .boundingClientRect((rect) => {
              if (rect) {
                // 这里直接设置一个较大值即可，scroll-view会滚到最底部
                scrollTop.value = scrollTop.value + 200;
              }
            })
            .exec();
        });
        if (pendingText.value.length === 0) {
          typing.value = false;
          clearInterval(typingTimer);
          typingTimer = null;
        }
      }
    }, getTypingInterval(pendingText.value.length));
  }

  // 执行翻译
  const translate = async () => {
    firstCharPrinted = false;
    if (!conversationId.value) {
      sheep.$helper.toast('创建会话失败，请重试');
      return;
    }

    if (!recognizedTextList.value.length) {
      sheep.$helper.toast('没有可翻译的内容');
      return;
    }

    const params = {
      sourceText: recognizedTextList.value.join('\\n'),
      sourceLang: getLanguageCodeByLabel(fromLanguage.value),
      targetLang: getLanguageCodeByLabel(toLanguage.value),
      conversationId: conversationId.value,
    };

    // 清空已有的翻译结果
    translatedTextList.value = [];
    pendingText.value = '';
    typing.value = false;
    // 显示翻译区域
    showTranslation.value = true;
    isTranslating.value = true;
    
    // 用于标记是否已收到第一个数据块
    let firstChunkReceived = false;
    
    // 显示加载提示
    uni.showLoading({
      title: '翻译中...',
      mask: true
    });
    
    // 使用流式翻译API
    TranslateApi.textTranslateStream(params, {
      enableChunked: true,
      onChunkReceived: createStreamHandler((content, jsonData) => {
        // 只在第一次收到数据时隐藏加载提示
        if (!firstChunkReceived) {
          firstChunkReceived = true;
        }

        if (jsonData?.data?.done) {
          isTranslating.value = false;
        }
        // 累加到pendingText
        pendingText.value += content;
        startTyping();
      }, {
        // 配置选项
        silent: false,
        contentPath: 'data.receive.content',
        successCode: 0
      })
    });

    await nextTick(() => {
      // 翻译后自动滚动到结果区域
      uni.createSelectorQuery()
        .select('#translate-result-section')
        .boundingClientRect()
        .select('.main-content')
        .boundingClientRect()
        .select('.main-content')
        .scrollOffset()
        .exec((res) => {
          if (res && res[0] && res[1] && res[2]) {
            // 计算滚动位置，显示标题
            const targetScrollTop = res[0].top - res[1].top + res[2].scrollTop - 80;

            // 延迟滚动，增强用户体验
            setTimeout(() => {
              scrollTop.value = targetScrollTop;
            }, 200);
          }
        });
    }, 300)
  };

  // 检测文本语言
  const detectLanguage = (text) => {
    const detectedLabel = getTextLanguage(text);

    if (!detectedLabel) {
      return;
    }

    // 设置中译泰
    if (detectedLabel === LanguageEnum.ZH) {
      fromLanguage.value = LanguageEnum.ZH.label;
      toLanguage.value = LanguageEnum.TH.label;
    }
    // 设置泰译中
    else if (detectedLabel === LanguageEnum.TH) {
      fromLanguage.value = LanguageEnum.TH.label;
      toLanguage.value = LanguageEnum.ZH.label;
    }
  };
</script>

<style scoped>
  /* 基础布局 */
  .container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #ffffff;
  }

  .main-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 60rpx;
  }

  /* 图片预览区域 */
  .image-container {
    width: 100%;
    height: 34%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background-color: #F8F8F8;
  }

  .movable-area {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
  }

  .movable-view {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  /* 结果区域通用样式 */
  .recognize-result,
  .translate-result {
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    background-color: #F8F8F8;
    border: 1px solid #e0e0e0;
    border-radius: 10rpx;
    margin: 20rpx;
  }

  .recognize-result {
    margin-bottom: 30rpx;
  }

  .translate-result {
    margin-top: 0;
    margin-bottom: 30rpx;
  }

  /* 结果内容样式 */
  .result-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #005Aff;
  }

  .result-content .text-content {
    font-size: 28rpx;
    margin-top: 22rpx;
    color: #000000;
    display: flex;
    align-items: center;
	  position:relative;
  }

  .text-content .text-item {
    display: flex;
    align-items: center;
  }
  
  .text-content .play-icon {
    height: 26rpx;
    margin-left: 10rpx;
    flex-shrink: 0;
  }

  /* 按钮区域 */
  .action-buttons {
    width: 100%;
    margin: 46rpx 0 20rpx;
    display: flex;
    justify-content: center;
  }

  .action-buttons .action-group {
    display: flex;
    gap: 30rpx;
  }

  /* 按钮样式 */
  .copy-btn,
  .play-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 35rpx;
  }

  .copy-btn .btn-icon,
  .play-btn .btn-icon {
    height: 24rpx;
    margin-right: 8rpx;
  }

  .copy-btn .btn-text,
  .play-btn .btn-text {
    font-size: 24rpx;
  }

  .copy-btn {
    background-color: #ffffff;
    border: 1px solid #ddd;
    color: #000000;
  }

  .play-btn {
    background-color: #46ADF0;
    color: #ffffff;
  }

  /* 语言切换和翻译按钮 */
  .language-bar {
    width: 100%;
    height: 48rpx;
    display: flex;
    gap: 30rpx;
    justify-content: center;
    align-items: center;
    margin: 20rpx 0 30rpx;
  }

  .language-bar .language-text {
    font-size: 26rpx;
    padding: 8rpx 18rpx;
    border-radius: 30rpx;
    background-color: #f0f0f0;
    color: #005Aff;
    display: flex;
    align-items: center;
  }

  .language-bar .language-text .direction-icon {
    margin: 0 6rpx;
  }

  .language-bar .translate-btn {
    font-size: 24rpx;
    height: 48rpx;
    line-height: 48rpx;
    width: 104rpx;
    background-color: #46ADF0;
    color: #ffffff;
    margin: 0;
  }

  .cursor {
    display: inline-block;
    width: 6rpx;
    height: 36rpx;
    background: skyblue;
    vertical-align: bottom;
    margin-left: 2px;
    animation: blink 1.3s infinite;
  }
  @keyframes blink {
    0%, 100% { background: skyblue; }
    50% { background: transparent; opacity: 0; }
  }
</style>
