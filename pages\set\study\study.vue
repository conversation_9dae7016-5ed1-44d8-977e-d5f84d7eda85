<template>
  <view class="study-container">
    <easy-navbar title="学习模式"/>

    <easy-progress
      mode="double"
      :currentProgress="progress"
      :masteredProgress="masteredProgress"
      :currentCount="correctCount"
      :totalCount="questions.length"
      :showElephant="true"
      :showNumbers="true"
    />

    <!-- 判断题 -->
    <view class="content-section" v-if="currentQuestion && currentQuestion.questionType === 0">
      <view class="title">完成判断</view>

      <view class="question">
        <text>{{ currentIndex + 1 }}.{{ currentQuestion.content }}</text>
      </view>

      <view v-if="showTip" class="tip-message" :class="{ 'tip-correct': isCorrect }">
        {{ isCorrect ? '努力就有回报，做得很好！' : '别担心，学习是一个过程！' }}
      </view>

      <view class="options-title" v-if="!hasAnswered">选择答案</view>

      <view class="options">
        <view
          v-for="(option, index) in currentQuestion.options"
          :key="index"
          class="option"
          :class="{
            'option-selected': hasAnswered && userSelectedAnswer === option.content,
            'option-correct':
              hasAnswered &&
              option.questionOption === currentQuestion.answer &&
              userSelectedAnswer === option.content,
            'option-wrong':
              hasAnswered &&
              option.questionOption !== currentQuestion.answer &&
              userSelectedAnswer === option.content,
            'option-right-answer':
              hasAnswered &&
              option.questionOption === currentQuestion.answer &&
              userSelectedAnswer !== option.content,
          }"
          @click="!hasAnswered && selectAnswer(option.questionOption, option.content)"
        >
          {{ option.content }}
          <text
            v-if="hasAnswered && option.questionOption === currentQuestion.answer"
            class="check-icon"
            >✓</text
          >
          <text
            v-if="
              hasAnswered &&
              option.questionOption !== currentQuestion.answer &&
              userSelectedAnswer === option.content
            "
            class="wrong-icon"
            >✕
          </text>
        </view>
      </view>

      <view v-if="hasAnswered && !autoJumping" class="bottom-button" @click="nextQuestion">
        继续
      </view>
    </view>

    <!-- 单选题 -->
    <view class="content-section" v-if="currentQuestion && currentQuestion.questionType === 1">
      <view class="title">完成选择</view>

      <view class="question">
        <text>{{ currentIndex + 1 }}.{{ currentQuestion.content }}</text>
      </view>

      <view v-if="showTip" class="tip-message" :class="{ 'tip-correct': isCorrect }">
        {{ isCorrect ? '努力就有回报，做得很好！' : '别担心，学习是一个过程！' }}
      </view>

      <view class="options-title" v-if="!hasAnswered">选择答案</view>

      <view class="options">
        <view
          v-for="(option, index) in currentQuestion.options"
          :key="index"
          class="option"
          :class="{
            'option-selected': hasAnswered && userSelectedAnswer === option.content,
            'option-correct':
              hasAnswered &&
              option.questionOption === currentQuestion.answer &&
              userSelectedAnswer === option.content,
            'option-wrong':
              hasAnswered &&
              option.questionOption !== currentQuestion.answer &&
              userSelectedAnswer === option.content,
            'option-right-answer':
              hasAnswered &&
              option.questionOption === currentQuestion.answer &&
              userSelectedAnswer !== option.content,
          }"
          @click="!hasAnswered && selectAnswer(option.questionOption, option.content)"
        >
          {{ option.content }}
          <text
            v-if="hasAnswered && option.questionOption === currentQuestion.answer"
            class="check-icon"
            >✓</text
          >
          <text
            v-if="
              hasAnswered &&
              option.questionOption !== currentQuestion.answer &&
              userSelectedAnswer === option.content
            "
            class="wrong-icon"
            >✕
          </text>
        </view>
      </view>

      <view v-if="hasAnswered && !autoJumping" class="bottom-button" @click="nextQuestion">
        继续
      </view>
    </view>

    <!-- 多选题 -->
    <view class="content-section" v-if="currentQuestion && currentQuestion.questionType === 2">
      <view class="title">完成多选</view>

      <view class="question">
        <text>{{ currentIndex + 1 }}.{{ currentQuestion.content }}</text>
      </view>

      <view v-if="showTip" class="tip-message" :class="{ 'tip-correct': isCorrect }">
        {{ isCorrect ? '努力就有回报，做得很好！' : '别担心，学习是一个过程！' }}
      </view>

      <view class="options-title" v-if="!hasAnswered">选择答案（可多选）</view>

      <view class="options">
        <view
          v-for="(option, index) in currentQuestion.options"
          :key="index"
          class="option"
          :class="{
            'option-selected': selectedOptions.includes(option.questionOption),
            'option-correct': hasAnswered && correctOptions.includes(option.questionOption),
            'option-wrong':
              hasAnswered &&
              selectedOptions.includes(option.questionOption) &&
              !correctOptions.includes(option.questionOption),
          }"
          @click="!hasAnswered && toggleMultipleChoice(option.questionOption)"
        >
          {{ option.content }}
          <text
            v-if="hasAnswered && correctOptions.includes(option.questionOption)"
            class="check-icon"
            >✓</text
          >
          <text
            v-if="
              hasAnswered &&
              selectedOptions.includes(option.questionOption) &&
              !correctOptions.includes(option.questionOption)
            "
            class="wrong-icon"
            >✕
          </text>
        </view>
      </view>

      <view v-if="!hasAnswered" class="bottom-button" @click="submitMultipleChoices">
        提交答案
      </view>

      <view v-if="hasAnswered && !autoJumping" class="bottom-button" @click="nextQuestion">
        继续
      </view>
    </view>

    <!-- 填空题 -->
    <view
      class="content-section"
      v-if="
        currentQuestion &&
        (currentQuestion.questionType === 3 ||
          (currentQuestion.questionType === 2 && currentQuestion.options.length === 0))
      "
    >
      <view class="title">完成填空</view>

      <view class="question">
        <text>{{ currentIndex + 1 }}.{{ currentQuestion.content }}</text>
      </view>

      <!-- 正确情况 -->
      <view v-if="hasAnswered && isCorrect" class="tip-message tip-correct">
        太棒了，答对了！
      </view>

      <!-- 错误情况 -->
      <view v-if="hasAnswered && !isCorrect" class="tip-message">
        <view class="error-message">{{ userAnswerMsg }}</view>
        
        <!-- 错误答案显示 -->
        <view v-if="userAnswer" class="wrong-answer-box">
          <view class="wrong-answer-content">{{ userAnswer }}</view>
          <view class="wrong-icon-container">
            <text class="wrong-answer-icon">✕</text>
          </view>
        </view>
        
        <!-- 正确答案显示 -->
        <view class="correct-answer-title">正确答案</view>
        <view class="correct-answer-box">
          <view class="correct-answer-content">{{ getCorrectFillinAnswer() }}</view>
          <view class="correct-icon-container">
            <text class="correct-icon">✓</text>
          </view>
        </view>
      </view>

      <!-- 未回答时显示"不知道"按钮 -->
      <view v-if="!hasAnswered" class="options-title" @click="showAnswer">不知道</view>

      <!-- 未回答时显示输入框 -->
      <view v-if="!hasAnswered" class="blank-answer">
        <input class="blank-input" v-model="userAnswer" placeholder="输入答案" />
      </view>

      <!-- 确认按钮 -->
      <view v-if="!hasAnswered" class="bottom-button" @click="submitAnswer">
        确认
      </view>

      <!-- 回答后显示用户输入的答案(错误时) -->
      <view v-if="hasAnswered && !autoJumping" class="bottom-button" @click="nextQuestion">
        继续
      </view>
    </view>
  </view>
</template>

<script setup>
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import SessionApi from '@/sheep/api/set/session'; // 直接导入SessionApi

  // 路由参数
  const setId = ref(0);
  const sessionId = ref(null);
  const isReviewMode = ref(false); // 是否为复习模式

  // 在onLoad中获取参数
  onLoad((options) => {
    setId.value = Number(options.setId) || 0;
    isReviewMode.value = options.mode === 'review'; // 检查是否为复习模式
    
    if (setId.value) {
      // 统一使用获取会话的方式，后端会自动创建会话（如果不存在）
      getStudySession();
    }
  });

  // 响应式状态
  const currentIndex = ref(0);
  const userAnswer = ref('');
  const userSelectedAnswer = ref('');
  const hasAnswered = ref(false);
  const showTip = ref(false);
  const isCorrect = ref(false);
  const autoJumping = ref(false);
  let autoJumpTimer = null;
  const userAnswerMsg = ref('');
  const questions = ref([]);
  const loading = ref(false);
  const sessionData = ref(null);

  // 进度相关
  const progressId = ref(null);
  const hasProgress = ref(false);

  // 多选题相关
  const selectedOptions = ref([]);
  const correctOptions = ref([]);

  // 成绩统计
  const correctCount = ref(0);
  const errorCount = ref(0);

  // 计算属性
  const currentQuestion = computed(() => {
    return questions.value[currentIndex.value] || null;
  });

  const progress = computed(() => {
    if (questions.value.length <= 1) return 0;
    return Math.round((currentIndex.value / (questions.value.length - 1)) * 100);
  });

  // 已掌握题目的进度
  const masteredProgress = computed(() => {
    if (questions.value.length === 0) return 0;
    return Math.round((correctCount.value / questions.value.length) * 100);
  });

  // 更新学习进度
  const updateProgress = async () => {
    if (!sessionId.value) return;
    const currentCount = currentIndex.value + 1;
    const allCount = questions.value.length;

    // 如果有已存在的进度记录，更新它
    if (hasProgress.value && progressId.value) {
      await SessionApi.updateSessionProgress({
        id: progressId.value,
        sessionId: sessionId.value,
        allCount: allCount,
        currentCount: currentCount,
        rightCount: correctCount.value
      });
    } else {
      // 否则创建新的进度记录
      const createRes = await SessionApi.createSessionProgress({
        sessionId: sessionId.value,
        allCount: allCount,
        currentCount: currentCount,
        rightCount: correctCount.value
      });

      if (createRes.code === 0 && createRes.data) {
        progressId.value = createRes.data;
        hasProgress.value = true;
      }
    }
  };

  // 恢复学习进度
  const restoreProgress = (progresses) => {
    if (!progresses || progresses.length === 0) return false;
    // 获取最新的进度记录
    const latestProgress = progresses.sort((a, b) =>
      new Date(b.updateTime) - new Date(a.updateTime)
    )[0];

    if (latestProgress) {
      progressId.value = latestProgress.id;
      hasProgress.value = true;

      // 恢复进度相关的状态
      currentIndex.value = Math.max(0, latestProgress.currentCount - 1);
      correctCount.value = latestProgress.rightCount || 0;

      // 如果恢复的索引大于题目数量，重置为0
      if (currentIndex.value >= questions.value.length) {
        currentIndex.value = 0;
      }

      return true;
    }
    return false;
  };

  // 筛选需要复习的题目
  const filterQuestionsForReview = (allQuestions, studyRecords) => {
    if (!studyRecords || studyRecords.length === 0) {
      return allQuestions; // 没有学习记录，返回全部题目
    }

    // 根据题目ID对学习记录进行分组，取每个题目的最新答题记录
    const latestRecords = {};
    studyRecords.forEach(record => {
      const questionId = record.questionId;
      if (!latestRecords[questionId] || 
          new Date(record.createTime) > new Date(latestRecords[questionId].createTime)) {
        latestRecords[questionId] = record;
      }
    });

    // 筛选出最近一次回答错误的题目
    const incorrectQuestionIds = Object.values(latestRecords)
      .filter(record => record.isCorrect === 0)
      .map(record => record.questionId);

    // 如果没有错误的题目，返回所有题目
    if (incorrectQuestionIds.length === 0) {
      return []; // 已全部掌握，返回空数组
    }

    // 从所有题目中筛选出需要复习的题目
    const questionsToReview = allQuestions.filter(question => 
      incorrectQuestionIds.includes(question.id)
    );

    return questionsToReview.length > 0 ? questionsToReview : [];
  };

  // 获取学习会话和题目
  const getStudySession = async () => {
    if (loading.value) return; // 防止重复调用
    loading.value = true;

    // 获取正在进行中的学习模式会话，如果不存在后端会自动创建
    const ongoingRes = await SessionApi.getOngoingSession({
      setId: setId.value,
      mode: 1, // 学习模式
    });

    // 检查返回的数据是否有效
    if (ongoingRes.code === 0 && ongoingRes.data) {
      // 获取到会话数据，直接使用
      sessionId.value = ongoingRes.data.id;
      sessionData.value = ongoingRes.data;

      // 如果是正常学习模式（不是复习模式）
      if (!isReviewMode.value) {
        // 检查是否需要重新开始（所有题目都学习过一遍）
        if (ongoingRes.data.studyRecords && ongoingRes.data.questions) {
          const allQuestionIds = new Set(ongoingRes.data.questions.map(q => q.id));
          const answeredQuestionIds = new Set(ongoingRes.data.studyRecords.map(r => r.questionId));

          // 如果所有题目都回答过，结束当前会话并重新获取新会话
          const allQuestionsAnswered = [...allQuestionIds].every(id => answeredQuestionIds.has(id));

          if (allQuestionsAnswered) {
            // 结束旧会话
            await SessionApi.completeSession(sessionId.value);

            // 添加延迟，确保服务器有足够时间处理会话状态
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 重新获取会话（后端会自动创建新会话）
            const newSessionRes = await SessionApi.getOngoingSession({
              setId: setId.value,
              mode: 1, // 学习模式
            });

            if (newSessionRes.code === 0 && newSessionRes.data) {
              sessionId.value = newSessionRes.data.id;
              sessionData.value = newSessionRes.data;
            } else {
              sheep.$helper.toast('网络异常，请刷新重试');
              loading.value = false;
              return;
            }
          }
        }
      }

      // 处理会话数据
      processSessionData();
    } else {
      sheep.$helper.toast('获取学习会话失败，请重试');
      loading.value = false;
    }
  };
  
  // 处理会话数据，提取题目并处理进度
  const processSessionData = () => {
    // 从questions中获取题目
    if (sessionData.value && sessionData.value.questions && sessionData.value.questions.length > 0) {
      let allQuestions = sessionData.value.questions.map((item) => {
        return {
          id: item.id,
          questionId: item.id,
          questionType: item.questionType, // 0-判断题, 1-单选题, 2-填空题, 3-填空题
          content: item.content,
          options: item.options || [],
          answer: item.answer,
        };
      });
      
      // 如果是复习模式，筛选出需要复习的题目
      if (isReviewMode.value && sessionData.value.studyRecords && sessionData.value.studyRecords.length > 0) {
        questions.value = filterQuestionsForReview(allQuestions, sessionData.value.studyRecords);
        
        // 刷新学习记录后复位计数器
        if (questions.value.length > 0) {
          correctCount.value = 0;
          errorCount.value = 0;
          currentIndex.value = 0;
          sheep.$helper.toast(`已为您筛选出${questions.value.length}道需要复习的题目`);
        } else {
          // 没有需要复习的题目，全部已掌握
          sheep.$helper.toast('恭喜！您已掌握全部题目');
          
          // 结束当前会话
          if (sessionId.value) {
            SessionApi.completeSession(sessionId.value);
          }
          
          // 回到学习完成页面
          setTimeout(() => {
            sheep.$router.go(`/pages/set/study/study-complete?setId=${setId.value}`, {}, { redirect: true });
          }, 1500);
          return;
        }
      } else {
        questions.value = allQuestions;
        
        // 尝试恢复学习进度 (仅在非复习模式下)
        if (!isReviewMode.value && sessionData.value.progresses && sessionData.value.progresses.length > 0) {
          const restored = restoreProgress(sessionData.value.progresses);
          if (restored) {
            sheep.$helper.toast('已恢复上次学习进度');
          }
        }
        
        // 根据学习记录统计正确和错误答案数量 (仅在非复习模式下且没有恢复进度时)
        if (!isReviewMode.value && sessionData.value.studyRecords && sessionData.value.studyRecords.length > 0 && !hasProgress.value) {
          sessionData.value.studyRecords.forEach(record => {
            if (record.isCorrect === 1) {
              correctCount.value++;
            } else {
              errorCount.value++;
            }
          });
        }
      }
    }
  };

  // 方法
  const selectAnswer = async (option, content) => {
    // 处理选择题和判断题答案
    userSelectedAnswer.value = content;
    hasAnswered.value = true;
    showTip.value = true;

    // 判断是否回答正确
    isCorrect.value = option === currentQuestion.value.answer;

    // 统计正确和错误的答案
    if (isCorrect.value) {
      correctCount.value++;
    } else {
      errorCount.value++;
    }

    // 创建学习记录
    if (sessionId.value && currentQuestion.value) {
      await SessionApi.createStudyRecord({
        setId: setId.value,
        sessionId: sessionId.value,
        questionId: currentQuestion.value.id,
        userAnswer: option.toString(), // 存储用户的选项
        isCorrect: isCorrect.value ? 1 : 0, // 1表示正确，0表示错误
      });
      
      // 更新学习进度
      await updateProgress();
    }

    // 如果回答正确，2秒后自动跳转
    if (isCorrect.value) {
      startAutoJump();
    }
  };

  // 处理多选题答案选择
  const toggleMultipleChoice = (option) => {
    const index = selectedOptions.value.indexOf(option);
    if (index === -1) {
      // 如果选项不在数组中，添加它
      selectedOptions.value.push(option);
    } else {
      // 如果选项已在数组中，移除它
      selectedOptions.value.splice(index, 1);
    }
  };

  // 提交多选题答案
  const submitMultipleChoices = async () => {
    hasAnswered.value = true;
    showTip.value = true;

    // 获取正确答案数组
    correctOptions.value = currentQuestion.value.answer.split(',');

    // 检查答案是否正确 (所有选项都对且没有多选或少选)
    const allCorrectSelected = correctOptions.value.every((option) =>
      selectedOptions.value.includes(option),
    );
    const noExtraSelection = selectedOptions.value.length === correctOptions.value.length;

    isCorrect.value = allCorrectSelected && noExtraSelection;

    // 统计正确和错误的答案
    if (isCorrect.value) {
      correctCount.value++;
    } else {
      errorCount.value++;
    }

    // 创建学习记录
    if (sessionId.value && currentQuestion.value) {
      await SessionApi.createStudyRecord({
        setId: setId.value,
        sessionId: sessionId.value,
        questionId: currentQuestion.value.id,
        userAnswer: selectedOptions.value.join(','), // 存储用户的多个选项，以逗号分隔
        isCorrect: isCorrect.value ? 1 : 0, // 1表示正确，0表示错误
      });
      
      // 更新学习进度
      await updateProgress();
    }

    // 如果回答正确，2秒后自动跳转
    if (isCorrect.value) {
      startAutoJump();
    }
  };

  // 填空题获取正确答案
  const getCorrectFillinAnswer = () => {
    if (!currentQuestion.value) return '';

    // 对于类型为3的填空题，可能有多个正确答案，用|分隔
    if (currentQuestion.value.questionType === 3 && currentQuestion.value.answer.includes('|')) {
      return currentQuestion.value.answer.split('|')[0];
    }

    return currentQuestion.value.answer;
  };

  const submitAnswer = async () => {
    // 处理填空题答案
    hasAnswered.value = true;
    showTip.value = true;

    // 判断是否回答正确 - 对于有多个可能答案的情况（用|分隔）
    const possibleAnswers = currentQuestion.value.answer.split('|');
    
    // 新的判断逻辑：用户输入的答案可能包含多个答案（用逗号、或、空格等分隔）
    // 只要用户输入的内容包含任一正确答案，就算正确
    const userAnswerTrimmed = userAnswer.value.trim();
    if (userAnswerTrimmed) {
      // 将用户输入按常见分隔符分割成多个可能的答案
      const userAnswerParts = userAnswerTrimmed.split(/[,，、；;或\s]+/).filter(Boolean);
      
      // 判断是否至少有一个部分匹配正确答案之一
      isCorrect.value = possibleAnswers.some(correctAns => {
        // 直接匹配完整答案（忽略大小写）
        if (userAnswerTrimmed.toLowerCase() === correctAns.trim().toLowerCase()) {
          return true;
        }
        
        // 检查用户输入的各部分是否包含任一正确答案
        return userAnswerParts.some(part => 
          part.toLowerCase() === correctAns.trim().toLowerCase()
        );
      });
    } else {
      isCorrect.value = false;
    }

    // 统计正确和错误的答案
    if (isCorrect.value) {
      correctCount.value++;
    } else {
      errorCount.value++;
    }

    // 设置错误提示信息
    if (!isCorrect.value) {
      if (userAnswer.value.trim() === '') {
        userAnswerMsg.value = '太遗憾了，就差一点！';
      } else {
        userAnswerMsg.value = '就差一点，您仍在学习！';
      }
    }

    // 创建学习记录
    if (sessionId.value && currentQuestion.value) {
      await SessionApi.createStudyRecord({
        setId: setId.value,
        sessionId: sessionId.value,
        questionId: currentQuestion.value.id,
        userAnswer: userAnswer.value, // 存储用户的填空答案
        isCorrect: isCorrect.value ? 1 : 0, // 1表示正确，0表示错误
      });
      
      // 更新学习进度
      await updateProgress();
    }
    // 如果回答正确，2秒后自动跳转
    if (isCorrect.value) {
      startAutoJump();
    }
  };

  const showAnswer = async () => {
    // 不知道答案，直接显示正确答案
    hasAnswered.value = true;
    isCorrect.value = false;
    userAnswerMsg.value = '太遗憾了，就差一点！';
    userAnswer.value = '';

    // 统计为错误答案
    errorCount.value++;

    // 创建学习记录 - 用户选择不知道
    if (sessionId.value && currentQuestion.value) {
      await SessionApi.createStudyRecord({
        setId: setId.value,
        sessionId: sessionId.value,
        questionId: currentQuestion.value.id,
        userAnswer: '不知道', // 用户跳过了这个问题
        isCorrect: 0, // 0表示错误
      });
      
      // 更新学习进度
      await updateProgress();
    }
  };

  const startAutoJump = () => {
    // 设置自动跳转状态
    autoJumping.value = true;

    // 清除可能存在的旧定时器
    if (autoJumpTimer) {
      clearTimeout(autoJumpTimer);
    }

    // 2秒后自动跳转
    autoJumpTimer = setTimeout(() => {
      nextQuestion();
    }, 2000);
  };

  const nextQuestion = () => {
    // 清除可能存在的定时器
    if (autoJumpTimer) {
      clearTimeout(autoJumpTimer);
    }

    // 重置状态
    hasAnswered.value = false;
    showTip.value = false;
    userSelectedAnswer.value = '';
    userAnswer.value = '';
    isCorrect.value = false;
    autoJumping.value = false;
    userAnswerMsg.value = '';
    selectedOptions.value = [];
    correctOptions.value = [];

    // 如果不是最后一题，前进到下一题
    if (currentIndex.value < questions.value.length - 1) {
      currentIndex.value++;
      
      // 更新进度（切换题目也要更新进度）
      updateProgress();
    } else {
      // 所有题目已完成，跳转到完成页面
      completeStudySession();
    }
  };

  // 完成学习会话
  const completeStudySession = async () => {
    if (sessionId.value) {
      // 获取总题目数量
      const total = questions.value.length;

      // 创建学习会话结果
      await SessionApi.createSessionResult({
        sessionId: sessionId.value,
        setId: setId.value,
        allCount: total, // 总题目数
        correctCount: correctCount.value, // 正确题目数
        errorCount: errorCount.value, // 错误题目数
      });

      // 完成会话前进行最后一次进度更新（设置为完成状态）
      if (hasProgress.value && progressId.value) {
        await SessionApi.updateSessionProgress({
          id: progressId.value,
          sessionId: sessionId.value,
          allCount: total,
          currentCount: total, // 设置为全部完成
          rightCount: correctCount.value
        });
      }

      // 更新完成状态
      const currentLearnSet = uni.getStorageSync('currentLearnSet') || {};
      const completedQuestionIds = currentLearnSet.completedQuestionIds || [];

      // 添加本次学习的问题ID到完成列表
      const currentQuestionIds = questions.value.map((q) => q.id);
      // 更新本地存储
      currentLearnSet.completedQuestionIds = [
        ...new Set([...completedQuestionIds, ...currentQuestionIds]),
      ];
      uni.setStorageSync('currentLearnSet', currentLearnSet);
    }

    // 跳转到完成页面
    sheep.$router.go(`/pages/set/study/study-complete?setId=${setId.value}`,{}, { redirect: true });
  };



  // 组件销毁时清除定时器
  onBeforeUnmount(() => {
    if (autoJumpTimer) {
      clearTimeout(autoJumpTimer);
    }
  });

  // 组件加载时获取数据
  onMounted(() => {
    if (setId.value) {
      getStudySession();
    } else {
      sheep.$helper.toast('缺少学习集...');
    }
  });
</script>

<style scoped>
  .study-container {
    background-color: #f5f7fa;
    min-height: 100vh;
    padding: 20rpx;
  }

  .content-section {
    padding: 20rpx 0;
  }

  .title {
    font-size: 40rpx;
    font-weight: bold;
  }

  .question {
    margin: 40rpx 0;
    font-size: 32rpx;
    line-height: 1.6;
  }

  .tip-message {
    color: #ff4d4f;
    font-size: 32rpx;
    margin: 20rpx 0;
  }

  .tip-correct {
    color: #52c41a;
  }

  .error-message {
    color: #ff4d4f;
    margin-bottom: 20rpx;
  }

  .wrong-answer-box {
    background-color: #fff1f0;
    border: 1px solid #ff4d4f;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    position: relative;
    overflow: hidden;
  }

  .wrong-answer-content {
    padding: 20rpx;
    text-align: center;
    font-size: 32rpx;
    color: #333;
  }

  .wrong-icon-container {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
  }

  .wrong-answer-icon {
    color: #ff4d4f;
    font-weight: bold;
    font-size: 36rpx;
  }

  .correct-answer-title {
    color: #52c41a;
    font-size: 32rpx;
    margin-bottom: 10rpx;
  }

  .correct-answer-box {
    background-color: #f6ffed;
    border: 1px solid #52c41a;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    position: relative;
    overflow: hidden;
  }

  .correct-answer-content {
    padding: 20rpx;
    text-align: center;
    font-size: 32rpx;
    color: #52c41a;
  }

  .correct-icon-container {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
  }

  .correct-icon {
    color: #52c41a;
    font-weight: bold;
    font-size: 36rpx;
  }

  .correct-answer .label {
    color: #52c41a;
    margin-right: 20rpx;
  }

  .correct-answer .answer {
    background-color: #f6ffed;
    border: 1px solid #52c41a;
    padding: 10rpx 30rpx;
    border-radius: 10rpx;
    color: #52c41a;
  }

  .wrong-icon {
    color: #ff4d4f;
    margin-right: 10rpx;
  }

  .options-title {
    margin: 40rpx 0 20rpx;
    color: #4b8bf4;
    font-size: 34rpx;
  }

  .options {
    margin-top: 20rpx;
  }

  .option {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    font-size: 32rpx;
    border: 1px solid #eaeaea;
    position: relative;
  }

  .option-selected {
    border: 1px solid #4b8bf4;
  }

  .option-correct {
    border: 1px solid #52c41a;
    background-color: #f6ffed;
  }

  .option-wrong {
    border: 1px solid #ff4d4f;
    background-color: #fff1f0;
  }

  .option-right-answer {
    border: 1px solid #52c41a;
    background-color: #f6ffed;
  }

  .check-icon {
    position: absolute;
    right: 30rpx;
    color: #52c41a;
    font-weight: bold;
  }

  .wrong-icon {
    position: absolute;
    right: 30rpx;
    color: #ff4d4f;
    font-weight: bold;
  }

  .blank-answer {
    margin-top: 30rpx;
    display: flex;
  }

  .blank-input {
    flex: 1;
    background-color: #fff;
    padding: 20rpx 30rpx;
    font-size: 32rpx;
    border-radius: 10rpx;
    border: 1px solid #e0e0e0;
  }

  .bottom-button {
    position: fixed;
    left: 30rpx;
    right: 30rpx;
    bottom: 50rpx;
    background-color: #40a9ff;
    color: #fff;
    text-align: center;
    padding: 30rpx 0;
    border-radius: 12rpx;
    font-size: 36rpx;
    margin-top: 0;
    z-index: 100;
  }

  @keyframes elephantWalk {
    0% {
      transform: rotate(-8deg);
    }

    50% {
      transform: rotate(8deg);
    }

    100% {
      transform: rotate(-8deg);
    }
  }
</style>
