<template>
  <view class="assignment-container" @tap="handleContainerClick">
    <!-- 顶部导航栏 -->
    <view
      class="navbar-area"
      :style="{ paddingTop: `${paddingTop}px`, paddingBottom: `${paddingBottom}px` }"
    >
      <!-- 左侧区域：返回按钮 -->
      <view class="left">
        <image
          class="back-icon"
          mode="widthFix"
          :src="sheep.$url.cdn('/common/back.png')"
          @click="goBack"
        />
      </view>

      <!-- 中间标题位置放置标签切换 -->
      <view class="title">
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'released' }"
            @tap="switchTab('released')"
            >已发放</view
          >
          <view
            class="tab-item"
            :class="{ active: activeTab === 'library' }"
            @tap="switchTab('library')"
            >作业库</view
          >
        </view>
      </view>

      <!-- 右侧区域 -->
      <view class="right">
        <view class="more-icon">
          <text class="iconfont icon-more-dot"></text>
        </view>
      </view>
    </view>

    <!-- 占位元素，防止内容被固定导航栏遮挡 -->
    <view class="navbar-placeholder" :style="{ height: `${navbarHeight + paddingBottom}px` }" />

    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input">
        <image class="search-icon" :src="searchIcon" mode="aspectFit"></image>
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="作业名" 
          placeholder-class="placeholder"
          @confirm="handleSearch"
          @blur="handleSearch"
        />
      </view>
      <text class="search-btn" @tap="handleSearch">搜索</text>
    </view>

    <!-- 作业列表 -->
    <view class="assignment-list" v-if="activeTab === 'released'">
      <view
        class="assignment-item-wrapper"
        v-for="(item, index) in releasedList"
        :key="index"
        @touchstart="handleSwipeStart(index, $event)"
        @touchend="handleSwipeEnd(index, $event)"
      >
        <view
          class="assignment-item"
          :style="{
            transform: currentSwipeIndex === index ? 'translateX(-90rpx)' : 'translateX(0)',
          }"
		   @tap="goToDetail(item, index)" 
        >
          <view class="assignment-info">
            <image class="assignment-icon" :src="jobLibraryIcon" mode="aspectFit"></image>
            <view class="assignment-content">
              <view class="assignment-title"> {{ item.title }} </view>
              <view class="assignment-time1">
                <image class="time-icon" :src="timelogo" mode="aspectFit"></image>
                作答时间:{{ item.answerTime }}
              </view>
            </view>
          </view>
          <view class="item-right">
            <text class="arrow-icon">›</text>
          </view>
        </view>
        <view class="delete-btn" v-if="currentSwipeIndex === index" @tap="handleDelete(index)">
          <image class="delete-icon" :src="deleteIcon" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <view class="assignment-list" v-else>
      <view
        class="assignment-item-wrapper"
        v-for="(item, index) in libraryList"
        :key="index"
        @touchstart="handleSwipeStart(index, $event)"
        @touchend="handleSwipeEnd(index, $event)"
      >
        <view
          class="assignment-item"
          :style="{
            transform: currentSwipeIndex === index ? 'translateX(-90rpx)' : 'translateX(0)',
          }"
          @tap="goToEdit(item)"
        >
          <view class="assignment-info">
            <image class="assignment-icon" :src="jobLibraryIcon" mode="aspectFit"></image>
            <view class="assignment-content">
              <view class="assignment-title">
                {{ item.title }}
                <text class="score-tag" v-if="item.score">满分:{{ item.score }}分</text>
              </view>
              <view class="assignment-time">创建时间:{{ item.createTime }}</view>
            </view>
          </view>
          <view class="release-btn" @tap.stop="goToRelease(item)">发布</view>
        </view>
        <view class="delete-btn" v-if="currentSwipeIndex === index" @tap="handleDelete(index)">
          <image class="delete-icon" :src="deleteIcon" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 添加按钮，只在作业库标签页显示 -->
    <view class="add-btn" @click="handleCreateAssignment" v-if="activeTab === 'library'">
      <image class="add-icon" :src="blueAddIcon" mode="aspectFit"></image>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import sheep from '@/sheep';
  import AssignmentApi from '@/sheep/api/course/assignment';

  // 图标资源
  const timelogo = sheep.$url.cdn('/course/shijian.png');
  const jobLibraryIcon = sheep.$url.cdn('/course/jobLibrary.png');
  const blueAddIcon = sheep.$url.cdn('/group/blue-add.png');
  const searchIcon = sheep.$url.cdn('/index/search-icon.png');
  const deleteIcon = sheep.$url.cdn('/set/delete.png');

  // 导航栏相关参数
  const paddingTop = ref(0);
  const navbarHeight = ref(0);
  const paddingBottom = ref(10);

  // 当前激活的标签页
  const activeTab = ref('released');

  // 当前滑动的项目索引
  const currentSwipeIndex = ref(-1);

  // 课程ID
  const courseId = ref('');

  // 搜索关键词
  const searchKeyword = ref('');

  // 作业列表数据
  const releasedList = reactive<any[]>([]);
  const libraryList = reactive<any[]>([]);

  // 记录起始触摸点
  const startX = ref(0);

  // 获取URL参数
  const getParams = () => {
    // @ts-ignore
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (currentPage && currentPage.options) {
      courseId.value = currentPage.options.courseId || '';
    }
  };

  // 加载已发放作业列表
  const loadReleasedAssignments = async () => {
    if (!courseId.value) return;

    const res = await AssignmentApi.getAssignmentReleasePage({
      courseId: courseId.value,
      page: 1,
      size: 50
    });
    // @ts-ignore
    if (res.code === 0 && res.data && res.data.list) {
      // 清空现有数据
      releasedList.length = 0;
      // 添加新数据
      res.data.list.forEach((item) => {
        releasedList.push({
          id: item.id,
          title: item.title || '未命名作业',
          courseName: item.courseName || '',
          answerTime: formatTime(item.startTime || Date.now()),
        });
      });
    }
  };

  // 加载作业库列表
  const loadLibraryAssignments = async () => {
    if (!courseId.value) return;

    const res = await AssignmentApi.getAssignmentPage({
      courseId: courseId.value,
      page: 1,
      size: 50
    });
    // @ts-ignore
    if (res.code === 0 && res.data && res.data.list) {
      // 清空现有数据
      libraryList.length = 0;
      // 添加新数据
      res.data.list.forEach((item) => {
        libraryList.push({
          id: item.id,
          title: item.name || item.title || '未命名作业',
          score: item.fullMarks || item.score || '',
          createTime: formatTime(item.createTime || Date.now()),
          questionCount: item.questionCount || 0,
        });
      });
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number | string) => {
    if (!timestamp) return '';
    
    const date = new Date(Number(timestamp));
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${month}-${day} ${hours}:${minutes}`;
  };

  // 搜索作业
  const handleSearch = () => {
    if (activeTab.value === 'released') {
      searchReleasedAssignments();
    } else {
      searchLibraryAssignments();
    }
  };

  // 搜索已发放作业
  const searchReleasedAssignments = async () => {
    if (!courseId.value) return;

    const res = await AssignmentApi.getAssignmentReleasePage({
      courseId: courseId.value,
      title: searchKeyword.value.trim(),
      page: 1,
      size: 50
    });

    // @ts-ignore
    if (res.code === 0 && res.data && res.data.list) {
      // 清空现有数据
      releasedList.length = 0;
      // 添加新数据
      res.data.list.forEach((item) => {
        releasedList.push({
          id: item.id,
          title: item.title || '未命名作业',
          courseName: item.courseName || '',
          answerTime: formatTime(item.startTime || Date.now()),
        });
      });
    }
  };

  // 搜索作业库
  const searchLibraryAssignments = async () => {
    if (!courseId.value) return;

    const res = await AssignmentApi.getAssignmentPage({
      courseId: courseId.value,
      name: searchKeyword.value.trim(),
      page: 1,
      size: 50
    });

    // @ts-ignore
    if (res.code === 0 && res.data && res.data.list) {
      // 清空现有数据
      libraryList.length = 0;
      // 添加新数据
      res.data.list.forEach((item) => {
        libraryList.push({
          id: item.id,
          title: item.name || item.title || '未命名作业',
          score: item.fullMarks || item.score || '',
          createTime: formatTime(item.createTime || Date.now()),
          questionCount: item.questionCount || 0,
        });
      });
    }
  };

  // 切换标签页
  const switchTab = (tab: string) => {
    activeTab.value = tab;
    currentSwipeIndex.value = -1;
    
    // 切换标签页时重新加载数据
    if (tab === 'released') {
      loadReleasedAssignments();
    } else {
      loadLibraryAssignments();
    }
  };

  // 返回上一页
  const goBack = () => {
    sheep.$router.back();
  };

  // 处理滑动开始
  const handleSwipeStart = (index: number, event: any) => {
    // 记录起始触摸点
    const touch = event.touches[0];
    startX.value = touch.clientX;
  };

  // 处理滑动结束
  const handleSwipeEnd = (index: number, event: any) => {
    const touch = event.changedTouches[0];
    const endX = touch.clientX;
    const moveX = endX - startX.value;

    // 如果向左滑动超过30px，则显示删除按钮
    if (moveX < -30) {
      currentSwipeIndex.value = index;
    }
    // 如果向右滑动超过30px，则关闭删除按钮
    else if (moveX > 30) {
      currentSwipeIndex.value = -1;
    }
  };

  // 点击空白区域关闭删除按钮
  const handleContainerClick = () => {
    currentSwipeIndex.value = -1;
  };

  // 处理删除操作
  const handleDelete = async (index: number) => {
    let itemId;

    if (activeTab.value === 'released') {
      itemId = releasedList[index].id;
      // @ts-ignore
      const res = await AssignmentApi.deleteAssignmentRelease(itemId);
      // @ts-ignore
      if (res.code === 0) {
        releasedList.splice(index, 1);
        sheep.$helper.toast('删除成功');
      } else {
        sheep.$helper.toast('删除失败');
      }
    } else {
      itemId = libraryList[index].id;
      // @ts-ignore
      const res = await AssignmentApi.deleteAssignment(itemId);
      // @ts-ignore
      if (res.code === 0) {
        libraryList.splice(index, 1);
        sheep.$helper.toast('删除成功');
      } else {
        sheep.$helper.toast('删除失败');
      }
    }
    currentSwipeIndex.value = -1;
  };

  // 跳转到作业发放设置页面
  const goToRelease = (item: any) => {
    // 传递课程ID和作业ID到发放设置页面
    sheep.$router.go(`/pages/course/assignment/issuance-set?courseId=${courseId.value}&assignmentId=${item.id}&questionCount=${item.questionCount || 0}`);
  };

  // 添加作业发布
  const handleCreateAssignment = () => {
    // 跳转到作业创建页面
    sheep.$router.go(`/pages/course/assignment/create-assignment?courseId=${courseId.value}`);
  };

  // 获取胶囊按钮信息和状态栏高度
  onMounted(() => {
    // 获取URL参数
    getParams();
    
    // 加载初始数据
    loadReleasedAssignments();
    
    // #ifdef MP-WEIXIN
    // 获取胶囊按钮信息
    try {
      // @ts-ignore
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      // 调整导航栏高度，使其能容纳胶囊按钮下方的标题
      paddingTop.value = menuButtonInfo.top;
      // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
      navbarHeight.value = paddingTop.value + 35;
    } catch (e) {
      // 获取状态栏高度
      const systemInfo = sheep.$helper.sys();
      paddingTop.value = systemInfo.marginTop || 20;
      navbarHeight.value = paddingTop.value + 35;
    }
    // #endif

    // #ifndef MP-WEIXIN
    // 获取状态栏高度
    const systemInfo = sheep.$helper.sys();
    paddingTop.value = systemInfo.marginTop;
    // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
    navbarHeight.value = paddingTop.value + 35;
    // #endif
  });
  
  // 跳转课程作业编辑页
  const goToDetail = (item, index) => {
    // 跳转到发放设置页面，传递课程ID和作业ID
    sheep.$router.go(`/pages/course/assignment/issuance-set?courseId=${courseId.value}&assignmentId=${item.id}&isUpdate=true`);
  };
  
  // 跳转到作业编辑页面
  const goToEdit = (item) => {
    // 跳转到作业编辑页面，传递课程ID和作业ID
    sheep.$router.go(`/pages/course/assignment/edit-assignment?courseId=${courseId.value}&assignmentId=${item.id}`);
  };
</script>

<style scoped lang="scss">
  .assignment-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    position: relative;
    padding-bottom: 100rpx;
  }

  .navbar-area {
    display: flex;
    align-items: center;
    height: 35px;
    width: 100%;
    background-color: #ffffff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    transition: all 0.05s ease;
  }

  .left {
    position: absolute;
    left: 15px;
    display: flex;
    align-items: center;
    z-index: 101;

    .back-icon {
      width: 19rpx;
      padding: 10rpx;
    }
  }

  .right {
    position: absolute;
    right: 15px;
    display: flex;
    align-items: center;
    z-index: 101;
  }

  .title {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tab-container {
    display: flex;
    width: 264rpx;
    height: 65rpx;
    background: #46adf0;
    border-radius: 33rpx;

    .tab-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 33rpx;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 25rpx;
      color: #ffffff;
      border: 2px solid #46adf0;

      &.active {
        background: #ffffff;
        border-radius: 33rpx;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 25rpx;
        color: #46adf0;
      }
    }
  }

  .more-icon {
    width: 60rpx;
    text-align: right;
    font-size: 40rpx;
  }

  .search-box {
    display: flex;
    align-items: center;
    padding: 10rpx 30rpx 23rpx 30rpx;
    background-color: #fff;

    .search-input {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #f5f7fa;
      border-radius: 40rpx;
      padding: 15rpx 20rpx;
      margin-right: 20rpx;

      .search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }

      input {
        flex: 1;
        font-size: 28rpx;
      }

      .placeholder {
        color: #999;
      }
    }

    .search-btn {
      font-size: 28rpx;
      color: #333;
    }
  }

  .assignment-list {
    padding: 16rpx 31rpx 0 31rpx;
  }

  .assignment-item-wrapper {
    position: relative;
    margin-bottom: 20rpx;
    border-radius: 25rpx;
    overflow: hidden;
  }

  .assignment-item {
    height: 173rpx;
    background: #ffffff;
    box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211, 223, 230, 0.52);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 25rpx;
    padding: 0 30rpx;
    position: relative;
    transition: transform 0.3s ease;
    z-index: 1;

    .assignment-info {
      display: flex;
      align-items: center;
      flex: 1;

      .assignment-icon {
        width: 105rpx;
        height: 105rpx;
        margin-right: 20rpx;
      }

      .assignment-content {
        flex: 1;

        .assignment-title {
          font-size: 30rpx;
          color: #333;

          .score-tag {
            display: inline-block;
            color: #ff6b6b;
            font-size: 24rpx;
            padding: 4rpx 8rpx;
            margin-left: 10rpx;
            width: 122rpx;
            background: #ffe3e3;
            border-radius: 6rpx;
          }
        }
        .assignment-time {
          display: inline-block;
          border-radius: 6rpx;
          width: 255rpx;
          height: 24rpx;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 24rpx;
          color: #cbcbcb;
        }

        .assignment-time1 {
          display: inline-flex;
          align-items: center;
          border-radius: 7rpx;
          height: 40rpx;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 22rpx;
          color: #FF8F34;
          background: #FFEAD7;
          padding: 8rpx 12rpx;
          .time-icon {
            width: 24rpx;
            height: 24rpx;
            margin-right: 8rpx;
          }
        }
      }
    }

    .release-btn {
      width: 115rpx;
      height: 64rpx;
      background: linear-gradient(128deg, rgba(51, 229, 255, 0.89), rgba(70, 140, 255, 0.89));
      box-shadow: 1rpx 1rpx 5rpx 0rpx rgba(39, 142, 243, 0.52);
      border-radius: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 25rpx;
      color: #ffffff;
    }

    .item-right {
      display: flex;
      align-items: center;

      .arrow-icon {
        font-size: 40rpx;
        color: #999;
      }
    }
  }

  .delete-btn {
    position: absolute;
    right: 0;
    top: 0;
    width: 120rpx;
    height: 100%;
    background: #ff7069;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-radius: 0 25rpx 25rpx 0;
    z-index: 0;

    .delete-icon {
      width: 37rpx;
      height: 40rpx;
      margin-right: 26rpx;
    }
  }

  .add-btn {
    position: fixed;
    right: 40rpx;
    bottom: 291rpx;
    width: 100rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;

    .add-icon {
      width: 100%;
      height: 100%;
    }
  }
</style>
