<template>
	<view class="filter-popup" v-if="show">
		<view class="filter-content" :style="popupStyle">
			<view class="filter-categories">
				<view class="category" v-for="tab in tabs" :key="tab.key" :class="{ active: activeTab === tab.key }"
					@tap="$emit('switchTab', tab.key)">
					<text>{{ tab.label }}</text>
					<text class="arrow">{{ activeTab === tab.key ? '▴' : '▾' }}</text>
				</view>
			</view>
			<!-- 选项区 -->
			<view class="filter-options" v-for="tab in tabs" :key="tab.key" v-show="activeTab === tab.key">
				<view class="section">
					<view class="option-tag" v-for="item in filterData[tab.key]" :key="item.id" :class="{
  'all-tag': item.id === 'all' && (filterOptions[tab.key] === 'all' || filterOptions[tab.key] === ''),
  'active': filterOptions[tab.key] === item.id
}" @tap="$emit('selectOption', tab.key, item.id)">
						{{ item.name }}
					</view>
					<view v-if="tab.key === 'questionType'" class="option-tag more-tag">更多</view>
				</view>
			</view>
			<!-- 操作按钮 -->
			<view class="filter-actions">
				<view class="reset-btn" @tap="$emit('reset')">重置</view>
				<view class="confirm-btn" @tap="$emit('confirm')">确定</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	const props = defineProps({
		show: Boolean,
		activeTab: String,
		filterData: Object,
		filterOptions: Object,
		popupStyle: [String, Object]
	});
	const emit = defineEmits(['update:show', 'switchTab', 'selectOption', 'reset', 'confirm']);

	const tabs = [
		{
			key: 'source',
			label: '来源'
		},
		{
			key: 'questionType',
			label: '题型'
		}
	];
</script>

<style scoped lang="scss">
	// 筛选弹窗样式
	.filter-popup {
		position: fixed;
		top: 190rpx;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.1);
		z-index: 999;

		.filter-content {
			background-color: #FFFFFF;
			border-radius: 0 0 20rpx 20rpx;
			width: 100%;
			position: absolute;
			top: 0;

			.filter-categories {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 40rpx;

				.category {
					display: flex;
					align-items: center;
					font-size: 29.17rpx;
					color: #828282;
					/* 设置默认字体颜色 */
					position: relative;


					&.active {
						color: #3DB3F2;

						.arrow {
							color: #3DB3F2;
							/* 活跃状态下箭头颜色跟随文字颜色 */
						}
					}

					.arrow {
						margin-left: 14rpx;
						font-size: 24rpx;
						color: #828282;
						/* 箭头颜色 */
						text-align: center;
						line-height: 1;
						transform: scale3d(2.5, 1.5, 1)
					}
				}
			}

			.filter-options {
				padding: 20rpx 30rpx;

				.section {
					justify-content: flex-start;

					.option-tag {
						display: inline-block;
						width: calc((100% - 48rpx) / 4);
						/* 计算每个选项的宽度 */
						height: 64rpx;
						box-sizing: border-box;
						font-size: 25rpx;
						color: #525252;
						padding: 16rpx 0;
						background-color: #F5F5F5;
						border-radius: 8rpx;
						margin-right: 0;
						/* 移除右外边距 */
						margin-left: 0;
						/* 默认没有左外边距 */
						margin-bottom: 16rpx;
						text-align: center;
						border: 1px solid #F5F5F5;

						/* 从每行的第二个元素开始设置左外边距 */
						&:nth-child(4n+2),
						&:nth-child(4n+3),
						&:nth-child(4n+4) {
							margin-left: 16rpx;
						}

						&.active {
							background-color: #E3F5FF;
							color: #3DB3F2;
						}

						&.all-tag {
							&.active {
								background-color: #E3F5FF;
								color: #3DB3F2;
							}

							&:not(.active) {
								background-color: #F5F5F5;
								color: #333;
							}
						}

						&.more-tag {
							background-color: #FFFFFF;
							color: #3DB3F2;
							border: 1rpx solid #3DB3F2;
							box-sizing: border-box;
							width: 160rpx;
							height: 64rpx;
						}
					}
				}
			}

			.filter-actions {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 4rpx 30rpx 35rpx 30rpx;

				.reset-btn,
				.confirm-btn {
					height: 82rpx;
					line-height: 80rpx;
					text-align: center;
					border-radius: 40rpx;
					font-size: 34rpx;
				}

				.reset-btn {
					width: 234rpx;
					background-color: #F5F5F5;
					color: #525252;
				}

				.confirm-btn {
					width: 430rpx;
					background: linear-gradient(270deg, #46ADF0, #00DEFF);
					color: #FFFFFF;

				}
			}
		}
	}
</style>