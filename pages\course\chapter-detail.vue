<template>
  <view class="chapter-detail-container">
    <easy-navbar custom-class="chapter-title" />
    
    <!-- 顶部章节名称 -->
    <view class="chapter-title-text">{{ chapterInfo.parentName }}</view>
    
    <view class="chapter-content">
      <view class="attachments-block" v-if="dataReady && attachments.length > 0">
        <view 
          v-for="(attachment, index) in attachments" 
          :key="index" 
          class="attachment-section"
        >
          <!-- 绿色圆点+类型名称 -->
          <view class="attachment-type-row">
            <view class="status-dot" :class="{ 'green': attachment.completed }"></view>
            <view class="type-label">{{ attachment.attachmentContent }}</view>
          </view>

          <!-- 图片 -->
          <view class="attachment-media" v-if="attachment.type === 'image' && attachment.url">
            <image 
              :src="attachment.url" 
              mode="widthFix" 
              class="attachment-image" 
              @click="previewImage(attachment.url, attachment)"
            />
          </view>
          <!-- 视频 -->
          <view class="attachment-media" v-else-if="attachment.type === 'video' && attachment.url">
            <view class="video-container" :id="'video-container-' + attachment.id">
              <video 
                :id="'video-' + attachment.id"
                :src="attachment.url" 
                class="attachment-video"
                controls
                :initial-time="attachment.isPlayOriginal === 1 ? getCurrentTime(attachment) : 0"
                @ended="handleMediaComplete(attachment)"
                :enable-progress-gesture="attachment.isTaskPoint === 1 ? attachment.isDrag === 1 : true"
                :play-btn-position="attachment.isTaskPoint === 1 && attachment.isDrag === 0 ? 'center' : 'bottom'"
                :show-progress="attachment.isTaskPoint === 1 ? attachment.isDrag === 1 : true"
                :enable-play-gesture="attachment.isTaskPoint === 1 ? attachment.isDrag === 1 : true"
                @pause="handleMediaPause($event, attachment)"
                @timeupdate="handleMediaTimeUpdate($event, attachment)"
                @loadedmetadata="handleMediaLoadedMetadata($event, attachment)"
                @play="handleMediaPlay($event, attachment)"
                @error="handleMediaError($event, attachment)"
                @fullscreenchange="handleFullscreenChange($event, attachment)"
                :playback-rate="getPlaybackRate(attachment)"
              >
                <!-- 使用cover-view实现倍速按钮 -->
                <cover-view 
                  v-if="attachment.isPlaySpeed === 1" 
                  class="speed-btn" 
                  :style="getSpeedBtnStyle(attachment)"
                  @click.stop="toggleSpeedPopup(attachment)"
                >
                  {{ getPlaybackRate(attachment) }}x
                </cover-view>
                <!-- 使用cover-view实现倍速选择弹窗 -->
                <cover-view 
                  v-if="attachment.isPlaySpeed === 1 && showSpeedPopupMap[attachment.id]" 
                  class="speed-popup"
                  :style="getSpeedPopupStyle(attachment)"
                  @click.stop
                >
                  <cover-view 
                    v-for="(speed, idx) in playbackSpeeds" 
                    :key="idx"
                    class="speed-item" 
                    :class="{ 'active': getPlaybackRate(attachment) === speed }"
                    @click.stop="changePlaybackRate(attachment, speed)"
                  >
                    {{ speed }}x
                  </cover-view>
                </cover-view>
              </video>
            </view>
          </view>
          <!-- 音频 -->
          <view class="attachment-media audio-container" v-else-if="attachment.type === 'audio' && attachment.url">
            <view class="audio-wrapper">
              <audio 
                :id="'audio-' + attachment.id"
                :src="attachment.url" 
                controls 
                class="attachment-audio"
                :initial-time="attachment.isPlayOriginal === 1 ? getCurrentTime(attachment) : 0"
                @ended="handleMediaComplete(attachment)"
                @pause="handleMediaPause($event, attachment)"
                @timeupdate="handleMediaTimeUpdate($event, attachment)"
                @loadedmetadata="handleMediaLoadedMetadata($event, attachment)"
                @play="handleMediaPlay($event, attachment)"
                @error="handleAudioError($event, attachment)"
                :playback-rate="getPlaybackRate(attachment)"
              />
              <!-- 音频倍速按钮 -->
              <view 
                v-if="attachment.isPlaySpeed === 1" 
                class="audio-speed-btn"
                @click.stop="toggleSpeedPopup(attachment)"
              >
                {{ getPlaybackRate(attachment) }}x
              </view>
              <!-- 音频倍速选择弹窗 -->
              <view 
                v-if="attachment.isPlaySpeed === 1 && showSpeedPopupMap[attachment.id]" 
                class="audio-speed-popup"
                @click.stop
              >
                <view 
                  v-for="(speed, idx) in playbackSpeeds" 
                  :key="idx"
                  class="speed-item" 
                  :class="{ 'active': getPlaybackRate(attachment) === speed }"
                  @click.stop="changePlaybackRate(attachment, speed)"
                >
                  {{ speed }}x
                </view>
              </view>
            </view>
          </view>
          <!-- 其他类型：图标+文件名 -->
          <view v-else class="attachment-file-row" @click="openAttachment(attachment)">
            <image :src="getFileIcon(attachment.type)" class="file-icon" />
            <view class="file-name">{{ attachment.name }}</view>
          </view>
        </view>
      </view>
      <view v-else class="empty-attachments">
        <text>此章节暂无附件内容</text>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted, reactive, nextTick } from 'vue';
  import LessonChapterApi from '@/sheep/api/course/lessonchapter';
  import LessonChapterStudyRecordApi from '@/sheep/api/course/record';
  import sheep from '@/sheep';

  // 章节信息
  const chapterInfo = ref({
    id: '',
    title: '加载中...',
    content: '',
    completed: false,
    lessonId: '',
    parentName: '',
  });
  
  // 章节附件
  const attachments = ref([]);
  
  // 学习记录
  const studyRecords = ref([]);

  // 任务点重试计数
  const retryCount = ref({});
  
  // 用于临时存储每个视频/音频的当前播放进度
  const mediaCurrentTimeMap = ref({});
  
  // 添加一个标志位，防止重复调用
  const isSeekingMap = ref({});
  
  // 防止重复创建学习记录的标志位
  const creatingRecordMap = ref({});
  
  // 数据加载完成标志
  const dataReady = ref(false);
  
  // 播放速度选项
  const playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
  
  // 当前播放速度映射表
  const playbackSpeedMap = reactive({});
  
  // 显示倍速选择弹窗的状态
  const showSpeedPopupMap = reactive({});
  
  // 倍速按钮位置
  const speedBtnPositionMap = reactive({});
  
  // 格式化内容
  const formatContent = (content) => {
    if (content && content.includes('<') && content.includes('>')) {
      return String(content);
    }
    return content ? String(content.replace(/\n/g, '<br>')) : '';
  };
  
  // 预览图片
  const previewImage = async (url, attachment) => {
    uni.previewImage({
      urls: [url],
      current: url
    });
    
    if (attachment && attachment.isTaskPoint === 1) {
      await handleMediaComplete(attachment);
    }
  };
  
  // 加载章节信息
  const loadChapterInfo = async (chapterId) => {
    const res = await LessonChapterApi.getLessonChapter(chapterId);
    if (res.code === 0 && res.data) {
      chapterInfo.value = {
        id: res.data.id,
        title: String(res.data.title || '未命名章节'),
        content: String(res.data.content || '暂无内容'),
        completed: res.data.status === 'COMPLETED',
        lessonId: res.data.lessonId,
        parentName: res.data.parentName
      };

      // 先加载学习记录
      await loadStudyRecords(chapterId);

      // 再加载附件
      await loadAttachments(chapterId);

      // 数据全部加载完毕
      dataReady.value = true;
    }
  };
  
  // 加载学习记录
  const loadStudyRecords = async (chapterId) => {
    const res = await LessonChapterStudyRecordApi.getLessonChapterStudyRecordByChapterId(chapterId, pageParams.value.groupId);
    if (res.code === 0) {
      studyRecords.value = res.data.list || [];
    }
  };
  
  // 获取当前播放时间
  const getCurrentTime = (attachment) => {
    if (!attachment || !attachment.id) return 0;
    
    const record = studyRecords.value.find(r => r.attachmentId === attachment.id);
    if (record && typeof record.currentTimeSec === 'number' && record.currentTimeSec > 0) {
      return Math.floor(record.currentTimeSec);
    }
    return 0;
  };
  
  // 监听播放进度
  const handleMediaTimeUpdate = (event, attachment) => {
    if (!attachment || !attachment.id) return;
    
    if (attachment.isTaskPoint === 1 && !attachment.completed) {
      // 从event.detail.currentTime获取当前时间（标准属性名）
      let currentTime = 0;
      if (event?.detail?.currentTime) {
        currentTime = Math.floor(Number(event.detail.currentTime) || 0);
      }
      
      if (currentTime > 0) {
        mediaCurrentTimeMap.value[attachment.id] = currentTime;
        // 如果当前时间超过了总时长，更新总时长
        if (currentTime > attachment.totalTime) {
          attachment.totalTime = currentTime;
        }
      }
    }
  };
  
  // 处理媒体暂停事件
  const handleMediaPause = async (event, attachment) => {
    if (!attachment || !attachment.id) return;
    
    // 如果是由handleMediaPlay函数触发的暂停，则不更新进度
    if (isSeekingMap.value[attachment.id]) return;
    
    if (attachment.isTaskPoint === 1 && !attachment.completed) {
      let currentTimeSec = 0;
      // 尝试从mediaCurrentTimeMap获取
      if (mediaCurrentTimeMap.value[attachment.id]) {
        currentTimeSec = mediaCurrentTimeMap.value[attachment.id];
      } 
      // 从event获取，使用多种可能的属性名
      else{
        currentTimeSec = Math.floor(Number(event.detail.currentTime) || 0);
      }
      
      if (currentTimeSec > 0) {
        // 更新mediaCurrentTimeMap
        mediaCurrentTimeMap.value[attachment.id] = currentTimeSec;
        
        // 如果当前时间超过了总时长，更新总时长
        if (currentTimeSec > attachment.totalTime) {
          attachment.totalTime = currentTimeSec;
        }
        
        await updateStudyProgress(attachment, currentTimeSec);
      }
    }
  };
  
  // 更新学习进度
  const updateStudyProgress = async (attachment, currentTimeSec) => {
    if (!attachment || !attachment.id) return;
    currentTimeSec = Math.floor(Number(currentTimeSec) || 0);
    const record = studyRecords.value.find(r => r.attachmentId === attachment.id);

    if (!record) {
      // 检查是否正在创建记录，防止重复创建
      if (creatingRecordMap.value[attachment.id]) {
        return;
      }
      
      creatingRecordMap.value[attachment.id] = true;
      
      try {
        // 再次检查记录是否存在（可能在等待期间被其他调用创建了）
        const existingRecord = studyRecords.value.find(r => r.attachmentId === attachment.id);
        if (existingRecord) {
          return;
        }
        
        // 如果记录不存在，创建新记录
        const data = {
          chapterId: chapterInfo.value.id,
          attachmentId: attachment.id,
          status: 0,
          userId: sheep.$store('user').userId,
          progressPercentage: "0.00",
          currentTimeSec: currentTimeSec,
          allTime: Math.floor(Number(attachment.totalTime) || 0),
          groupId: pageParams.value.groupId || sheep.$store('user').groupId || null // 优先使用页面传入的班级ID，其次使用用户信息中的班级ID
        };

        const res = await LessonChapterStudyRecordApi.createLessonChapterStudyRecord(data);
        if (res.code === 0) {
          studyRecords.value.push({
            id: res.data,
            ...data
          });
        }
      } finally {
        // 无论成功还是失败，都要清除创建标志
        delete creatingRecordMap.value[attachment.id];
      }
    } else {
      // 如果记录存在，更新进度
      const progressPercentage = attachment.totalTime ?
        ((currentTimeSec / attachment.totalTime) * 100).toFixed(2) :
        "0.00";

      const data = {
        id: record.id,
        chapterId: chapterInfo.value.id,
        attachmentId: attachment.id,
        status: progressPercentage === "100.00" ? 1 : 0,
        userId: sheep.$store('user').userId,
        progressPercentage: progressPercentage,
        currentTimeSec: currentTimeSec,
        groupId: pageParams.value.groupId || sheep.$store('user').groupId || null // 优先使用页面传入的班级ID，其次使用用户信息中的班级ID
      };

      const res = await LessonChapterStudyRecordApi.updateLessonChapterStudyRecord(data);
      if (res.code === 0) {
        const index = studyRecords.value.findIndex(r => r.id === record.id);
        if (index !== -1) {
          studyRecords.value[index] = {
            ...studyRecords.value[index],
            ...data
          };
        }
      }
    }
  };
  
  // 修改创建学习记录方法
  const createStudyRecord = async (attachment) => {
    // 检查是否正在创建记录，防止重复创建
    if (creatingRecordMap.value[attachment.id]) {
      return false;
    }
    
    const existingRecord = studyRecords.value.find(r => r.attachmentId === attachment.id);
    const safeCurrentTime = Math.floor(Number(existingRecord?.currentTimeSec) || 0);
    const safeTotalTime = Math.floor(Number(attachment.totalTime) || 0);
    
    if (existingRecord) {
      const data = {
        id: existingRecord.id,
        chapterId: chapterInfo.value.id,
        attachmentId: attachment.id,
        status: 0,
        userId: sheep.$store('user').userId,
        progressPercentage: "0.00",
        currentTimeSec: safeCurrentTime,
        allTime: safeTotalTime,
        groupId: pageParams.value.groupId || sheep.$store('user').groupId || null // 优先使用页面传入的班级ID，其次使用用户信息中的班级ID
      };
      const res = await LessonChapterStudyRecordApi.updateLessonChapterStudyRecord(data);
      if (res.code === 0) {
        const index = studyRecords.value.findIndex(r => r.id === existingRecord.id);
        if (index !== -1) {
          studyRecords.value[index] = {
            ...studyRecords.value[index],
            ...data
          };
        }
        return true;
      }
      return false;
    }
    
    creatingRecordMap.value[attachment.id] = true;
    
    try {
      // 再次检查记录是否存在（可能在等待期间被其他调用创建了）
      const doubleCheckRecord = studyRecords.value.find(r => r.attachmentId === attachment.id);
      if (doubleCheckRecord) {
        return true;
      }
      
      const data = {
        chapterId: chapterInfo.value.id,
        attachmentId: attachment.id,
        status: 0,
        userId: sheep.$store('user').userId,
        progressPercentage: "0.00",
        currentTimeSec: 0,
        allTime: safeTotalTime,
        groupId: pageParams.value.groupId || sheep.$store('user').groupId || null // 优先使用页面传入的班级ID，其次使用用户信息中的班级ID
      };
      const res = await LessonChapterStudyRecordApi.createLessonChapterStudyRecord(data);
      if (res.code === 0) {
        studyRecords.value.push({
          id: res.data,
          ...data
        });
        return true;
      }
      return false;
    } finally {
      // 无论成功还是失败，都要清除创建标志
      delete creatingRecordMap.value[attachment.id];
    }
  };
  
  // 修改更新学习记录方法
  const updateStudyRecord = async (attachment, status, currentTimeSec = 0) => {
    let record = studyRecords.value.find(r => r.attachmentId === attachment.id);
    
    // 如果记录不存在，先创建
    if (!record) {
      const created = await createStudyRecord(attachment);
      if (!created) {
        return false;
      }
      // 重新获取创建后的记录
      record = studyRecords.value.find(r => r.attachmentId === attachment.id);
      if (!record) {
        return false;
      }
    }
    
    // 如果已经是完成状态且要设置为完成状态，直接返回
    if ((record.status === 1 || record.status === 'COMPLETED') && status === 'COMPLETED') {
      return true;
    }
    const safeCurrentTime = Math.floor(Number(currentTimeSec) || 0);
    const safeTotalTime = Math.floor(Number(attachment.totalTime) || 0);
    const data = {
      id: record.id,
      chapterId: chapterInfo.value.id,
      attachmentId: attachment.id,
      status: status === 'COMPLETED' ? 1 : 0,
      userId: sheep.$store('user').userId,
      progressPercentage: status === 'COMPLETED' ? "100.00" : record.progressPercentage,
      currentTimeSec: safeCurrentTime,
      allTime: safeTotalTime,
      groupId: pageParams.value.groupId || sheep.$store('user').groupId || null // 优先使用页面传入的班级ID，其次使用用户信息中的班级ID
    };
    const res = await LessonChapterStudyRecordApi.updateLessonChapterStudyRecord(data);
    if (res.code === 0) {
      const index = studyRecords.value.findIndex(r => r.id === record.id);
      if (index !== -1) {
        studyRecords.value[index] = {
          ...studyRecords.value[index],
          ...data
        };
      }
      return true;
    }
    return false;
  };
  
  // 加载附件
  const loadAttachments = async (chapterId) => {
    const res = await LessonChapterApi.getLessonChapterAttachmentList(chapterId, pageParams.value.groupId);
    if (res.code === 0 && res.data) {
      if (!Array.isArray(res.data)) {
        attachments.value = [];
        return;
      }

      const processedAttachments = res.data.map(item => {
        const record = studyRecords.value.find(r => r.attachmentId === item.id);
        const isCompleted = record ? (record.status === 1 || record.progressPercentage === "100.00") : false;
        const completed = item.isTaskPoint === 1 ? isCompleted : true;
        
        return {
          id: item.id,
          name: String(item.name || item.fileName || '附件' + item.id),
          url: String(item.attachmentUrl),
          type: String(getFileType(item.attachmentUrl)),
          size: item.size || 0,
          attachmentContent: String(item.attachmentContent || ''),
          attachmentType: String(item.attachmentType),
          isTaskPoint: item.isTaskPoint || 0,
          isPlayOriginal: item.isPlayOriginal || 0,
          isDrag: item.isDrag || 0,
          isPlaySpeed: item.isPlaySpeed || 0,
          completed,
          sort: item.sort || 0,
          totalTime: item.totalTime || 0
        };
      });

      // 按排序字段排序
      attachments.value = processedAttachments.sort((a, b) => a.sort - b.sort);
    } else {
      attachments.value = [];
    }
  };
  
  // 获取文件类型
  const getFileType = (fileUrl) => {
    if (!fileUrl) return 'other';
    
    const ext = fileUrl.split('.').pop().toLowerCase();
    
    if (['pdf'].includes(ext)) return 'pdf';
    if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) return 'doc';
    if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(ext)) return 'video';
    if (['mp3', 'wav', 'ogg', 'flac'].includes(ext)) return 'audio';
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) return 'image';
    
    return 'other';
  };
  
  // 打开附件
  const openAttachment = async (attachment) => {
    if (attachment.url) {
      if (attachment.isTaskPoint === 1) {
        // 直接调用updateStudyRecord，它内部会处理记录不存在的情况
        await updateStudyRecord(attachment, 'COMPLETED');
      }
      
      if (['pdf', 'doc'].includes(attachment.type)) {
        uni.downloadFile({
          url: attachment.url,
          success: (res) => {
            if (res.statusCode === 200) {
              uni.openDocument({
                filePath: res.tempFilePath,
                success: () => {
                  if (attachment.isTaskPoint === 1) {
                    handleMediaComplete(attachment);
                  }
                }
              });
            }
          }
        });
      } else {
        // #ifdef H5
        window.open(attachment.url);
        // #endif
        
        // #ifdef APP-PLUS
        plus.runtime.openURL(attachment.url);
        // #endif
        
        if (attachment.isTaskPoint === 1) {
          handleMediaComplete(attachment);
        }
      }
    }
  };
  
  // 媒体加载完成处理
  const handleMediaLoaded = async (attachment) => {
    if (attachment.isTaskPoint === 1 && attachment.type === 'image') {
      // 如果任务点已完成，直接返回
      if (attachment.completed) {
        return;
      }

      const record = studyRecords.value.find(r => r.attachmentId === attachment.id);
      
      // 如果已有完成的学习记录，直接返回
      if (record && (record.status === 1 || record.progressPercentage === "100.00")) {
        return;
      }

      if (record) {
        // 如果记录已存在且未完成，则更新状态
        if (record.status !== 1 && record.progressPercentage !== "100.00") {
          await updateStudyRecord(attachment, 'COMPLETED');
        }
      } else {
        // 如果记录不存在，创建新记录并更新状态
        await createStudyRecord(attachment);
        await updateStudyRecord(attachment, 'COMPLETED');
      }
      await handleMediaComplete(attachment);
    }
  };
  
  // 媒体播放完成处理
  const handleMediaComplete = async (attachment) => {
    if (!attachment) {
      return;
    }
    
    if (attachment.isTaskPoint === 1) {
      // 如果任务点已完成，直接返回
      if (attachment.completed) {
        return;
      }

      const record = studyRecords.value.find(r => r.attachmentId === attachment.id);

      if (record) {
        // 如果记录已存在且未完成，则更新状态
        if (record.status !== 1 && record.progressPercentage !== "100.00") {
          // 传入视频总时长作为当前时间
          await updateStudyRecord(attachment, 'COMPLETED', attachment.totalTime || 0);
        }
      } else {
        // 如果记录不存在，创建新记录并更新状态
        await createStudyRecord(attachment);
        await updateStudyRecord(attachment, 'COMPLETED', attachment.totalTime || 0);
      }
      await updateLearningStatus(attachment.id);
    }
  };
  
  // 媒体错误处理
  const handleMediaError = (event, attachment) => {
    if (attachment.type === 'video') {
      const videoContext = uni.createVideoContext('video-' + attachment.id);
      if (videoContext) {
        videoContext.stop();
        setTimeout(() => {
          videoContext.play();
        }, 1000);
      }
    }
  };
  
  // 音频错误处理
  const handleAudioError = (event, attachment) => {
    if (!retryCount.value[attachment.id]) {
      retryCount.value[attachment.id] = 0;
    }
    
    if (retryCount.value[attachment.id] < 3) {
      retryCount.value[attachment.id]++;
      setTimeout(() => {
        // #ifdef H5
        const audioElement = document.getElementById('audio-' + attachment.id);
        if (audioElement) {
          audioElement.load();
        }
        // #endif
        
        // #ifdef APP-PLUS
        const audioContext = plus.audio.createPlayer({
          src: attachment.url
        });
        audioContext.play();
        // #endif
      }, 1000);
    }
  };
  
  // 更新学习状态
  const updateLearningStatus = async (attachmentId) => {
    if (!attachmentId) return;
    const index = attachments.value.findIndex(item => item.id === attachmentId);
    if (index !== -1) {
      attachments.value[index].completed = true;
    }

    const allCompleted = attachments.value.every(item =>
      item.completed || item.isTaskPoint !== 1
    );

    if (allCompleted) {
      chapterInfo.value.completed = true;
    }
  };
  
  // 获取当前播放速度
  const getPlaybackRate = (attachment) => {
    if (!attachment || !attachment.id) return 1.0;
    return playbackSpeedMap[attachment.id] || 1.0;
  };
  
  // 切换倍速选择弹窗
  const toggleSpeedPopup = (attachment) => {
    if (!attachment || !attachment.id) return;
    
    // 切换显示状态
    showSpeedPopupMap[attachment.id] = !showSpeedPopupMap[attachment.id];
    
    // 如果是视频且显示弹窗，计算按钮位置
    if (attachment.type === 'video' && showSpeedPopupMap[attachment.id]) {
      nextTick(() => {
        updateSpeedBtnPosition(attachment);
      });
    }
  };
  
  // 更新倍速按钮位置
  const updateSpeedBtnPosition = (attachment) => {
    if (!attachment || !attachment.id || attachment.type !== 'video') return;
    
    // 获取全屏状态
    let isFullscreen = false;
    
    // #ifdef H5
    const videoElement = document.getElementById('video-' + attachment.id);
    if (videoElement) {
      isFullscreen = videoElement.webkitDisplayingFullscreen || document.fullscreenElement === videoElement;
    }
    // #endif
    
    // #ifdef APP-PLUS
    const videoContext = uni.createVideoContext('video-' + attachment.id);
    if (videoContext && videoContext.requestFullScreen) {
      isFullscreen = videoContext.requestFullScreen.fullScreen || false;
    }
    // #endif
    
    // 更新按钮位置信息
    speedBtnPositionMap[attachment.id] = {
      right: isFullscreen ? '20px' : '10px',
      bottom: isFullscreen ? '80px' : '50px',
      isFullscreen: isFullscreen
    };
  };
  
  // 处理全屏变化事件
  const handleFullscreenChange = (event, attachment) => {
    if (!attachment || !attachment.id) return;
    
    // 获取全屏状态
    let isFullscreen = false;
    
    // #ifdef H5
    const videoElement = document.getElementById('video-' + attachment.id);
    if (videoElement) {
      isFullscreen = videoElement.webkitDisplayingFullscreen || document.fullscreenElement === videoElement;
    }
    // #endif
    
    // #ifdef APP-PLUS
    const videoContext = uni.createVideoContext('video-' + attachment.id);
    if (videoContext && videoContext.requestFullScreen) {
      isFullscreen = videoContext.requestFullScreen.fullScreen || false;
    }
    // #endif
    
    // 更新按钮位置信息
    speedBtnPositionMap[attachment.id] = {
      right: isFullscreen ? '20px' : '10px',
      bottom: isFullscreen ? '80px' : '50px',
      isFullscreen: isFullscreen
    };
    
    // 如果显示了倍速弹窗，需要更新其位置
    if (showSpeedPopupMap[attachment.id]) {
      nextTick(() => {
        // 强制更新视图
        showSpeedPopupMap[attachment.id] = false;
        setTimeout(() => {
          showSpeedPopupMap[attachment.id] = true;
        }, 100);
      });
    }
  };
  
  // 获取倍速按钮样式
  const getSpeedBtnStyle = (attachment) => {
    if (!attachment || !attachment.id) return {};
    
    const position = speedBtnPositionMap[attachment.id] || { right: '20px', bottom: '80px', isFullscreen: false };
    
    // 对于cover-view，使用更简单的样式
    return {
      right: position.isFullscreen ? '20px' : '10px',
      bottom: position.isFullscreen ? '80px' : '50px'
    };
  };
  
  // 获取倍速弹窗样式
  const getSpeedPopupStyle = (attachment) => {
    if (!attachment || !attachment.id) return {};
    
    const position = speedBtnPositionMap[attachment.id] || { right: '20px', bottom: '80px', isFullscreen: false };
    
    // 对于cover-view，使用更简单的样式
    return {
      right: position.isFullscreen ? '20px' : '10px',
      bottom: position.isFullscreen ? '120px' : '80px'
    };
  };
  
  // 更改播放速度
  const changePlaybackRate = (attachment, speed) => {
    if (!attachment || !attachment.id || attachment.isPlaySpeed !== 1) return;
    
    // 更新播放速度映射表
      playbackSpeedMap[attachment.id] = speed;
      
      // 关闭倍速选择弹窗
      showSpeedPopupMap[attachment.id] = false;
    
    // 获取媒体上下文并设置播放速度
    let mediaContext;
    if (attachment.type === 'video') {
      mediaContext = uni.createVideoContext('video-' + attachment.id);
      if (mediaContext && typeof mediaContext.playbackRate === 'function') {
        mediaContext.playbackRate(speed);
      }
    } else if (attachment.type === 'audio') {
      // #ifdef H5
      const audioElement = document.getElementById('audio-' + attachment.id);
      if (audioElement) {
        audioElement.playbackRate = speed;
      }
      // #endif
      
      // #ifdef APP-PLUS
      // 注意：APP-PLUS环境下可能需要特殊处理
      const audioContext = plus.audio.createPlayer({
        src: attachment.url
      });
      // 某些平台的音频API可能不支持直接设置播放速度
      // 这里需要根据实际情况调整
      // #endif
    }
  };
  
  // 处理媒体元数据加载完成事件
  const handleMediaLoadedMetadata = (event, attachment) => {
    if (!attachment || !attachment.id) return;
    
    // 获取媒体文件的实际时长并设置到attachment.totalTime
    if (event?.detail?.duration) {
      const duration = Math.floor(Number(event.detail.duration) || 0);
      if (duration > 0) {
        attachment.totalTime = duration;
      }
    }
    
    // 如果正在seek中，不再处理
    if (isSeekingMap.value[attachment.id]) return;
    
    if (attachment.isPlayOriginal === 1) {
      const currentTimeSec = getCurrentTime(attachment);
      if (currentTimeSec > 0) {
        // 设置标志位，防止重复调用
        isSeekingMap.value[attachment.id] = true;
        
        let mediaContext;
        if (attachment.type === 'video') {
          mediaContext = uni.createVideoContext('video-' + attachment.id);
        } else if (attachment.type === 'audio') {
          // #ifdef H5
          mediaContext = document.getElementById('audio-' + attachment.id);
          // #endif
          
          // #ifdef APP-PLUS
          mediaContext = plus.audio.createPlayer({
            src: attachment.url
          });
          // #endif
        }
        
        if (mediaContext) {
          // 设置初始播放时间
          if (attachment.type === 'video') {
            mediaContext.seek(currentTimeSec);
          } else if (attachment.type === 'audio') {
            // #ifdef H5
            mediaContext.currentTimeSec = currentTimeSec;
            // #endif
            
            // #ifdef APP-PLUS
            mediaContext.seekTo(currentTimeSec);
            // #endif
          }
          
          // 延时后重置标志位
          setTimeout(() => {
            isSeekingMap.value[attachment.id] = false;
          }, 500);
        } else {
          // 如果没有获取到媒体上下文，也要重置标志位
          setTimeout(() => {
            isSeekingMap.value[attachment.id] = false;
          }, 500);
        }
      }
    }
  };
  
  // 处理媒体播放事件
  const handleMediaPlay = (event, attachment) => {
    if (!attachment || !attachment.id) return;
    
    // 如果正在seek中，不再处理
    if (isSeekingMap.value[attachment.id]) return;
    
    if (attachment.isPlayOriginal === 1) {
      const currentTimeSec = getCurrentTime(attachment);
      if (currentTimeSec > 0) {
        // 设置标志位，防止重复调用
        isSeekingMap.value[attachment.id] = true;
        
        let mediaContext;
        if (attachment.type === 'video') {
          mediaContext = uni.createVideoContext('video-' + attachment.id);
        } else if (attachment.type === 'audio') {
          // #ifdef H5
          mediaContext = document.getElementById('audio-' + attachment.id);
          // #endif
          
          // #ifdef APP-PLUS
          mediaContext = plus.audio.createPlayer({
            src: attachment.url
          });
          // #endif
        }
        
        if (mediaContext) {
          // 先暂停再跳转确保生效
          if (attachment.type === 'video') {
            mediaContext.pause();
            mediaContext.seek(currentTimeSec);
          } else if (attachment.type === 'audio') {
            // #ifdef H5
            mediaContext.pause();
            mediaContext.currentTimeSec = currentTimeSec;
            // #endif
            
            // #ifdef APP-PLUS
            mediaContext.pause();
            mediaContext.seekTo(currentTimeSec);
            // #endif
          }
          
          // 延迟恢复播放
          setTimeout(() => {
            if (attachment.type === 'video') {
              mediaContext.play();
            } else if (attachment.type === 'audio') {
              // #ifdef H5
              mediaContext.play();
              // #endif
              
              // #ifdef APP-PLUS
              mediaContext.play();
              // #endif
            }
            
            // 延时后重置标志位
            setTimeout(() => {
              isSeekingMap.value[attachment.id] = false;
            }, 500);
          }, 100);
        } else {
          // 如果没有获取到媒体上下文，也要重置标志位
          setTimeout(() => {
            isSeekingMap.value[attachment.id] = false;
          }, 500);
        }
      }
    }
  };
  
  // 附件图标
  const getFileIcon = (type) => {
    if (type === 'txt') return sheep.$url.cdn('/course/txt.png');
    if (type === 'word') return sheep.$url.cdn('/course/word.png');
    if (type === 'ppt') return sheep.$url.cdn('/course/ppt.png');
    if (type === 'pdf') return sheep.$url.cdn('/course/pdf.png');
    return sheep.$url.cdn('/course/file.png');
  };
  
  // 初始化视频上下文
  const videoContextMap = reactive({});
  
  // 页面参数
  const pageParams = ref({
    chapterId: null,
    groupId: null
  });

  // 组件挂载时加载数据
  onMounted(() => {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options || {};

    // 保存页面参数
    pageParams.value.chapterId = options.id;
    pageParams.value.groupId = options.groupId;

    if (options.id) {
      loadChapterInfo(options.id);
    } else {
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
    
    // 监听页面显示事件，用于处理从后台恢复时重新初始化视频上下文
    uni.$on('page-show', () => {
      nextTick(() => {
        // 重新初始化所有视频的上下文
        attachments.value.forEach(attachment => {
          if (attachment.type === 'video') {
            videoContextMap[attachment.id] = uni.createVideoContext('video-' + attachment.id);
          }
        });
      });
    });
  });
</script>

<style lang="scss" scoped>
  .chapter-detail-container {
    min-height: 100vh;
    background-color: #fff;
    position: relative;
  }
  
  .chapter-title {
    font-size: 18.75pt !important;
    color: #383838 !important;
    font-family: "黑体", "SimHei", "Heiti SC", sans-serif !important;
  }
  
  .chapter-content {
    padding: 30rpx;
  }
  
  .content-block {
    line-height: 1.8;
    font-size: 30rpx;
    color: #333;
    margin-bottom: 50rpx;
  }
  
  .attachments-block {
    margin-bottom: 50rpx;
  }
  
  .attachment-section {
    margin-bottom: 30rpx;
    position: relative;
  }
  
  .task-status {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
  }
  
  .status-dot {
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background-color: #cccccc;
    margin-right: 10rpx;
    
    &.completed {
      background-color: #4CD964;
    }
  }
  
  .status-text {
    font-size: 26rpx;
    color: #666;
  }
  
  .attachment-content {
    line-height: 1.8;
    font-size: 30rpx;
    color: #333;
    margin-bottom: 20rpx;
    padding: 0 10rpx;
  }
  
  .attachment-media {
    margin-bottom: 20rpx;
    width: 100%;
  }
  
  .attachment-image {
    width: 100%;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }
  
  .attachment-video {
    width: 100%;
    height: 400rpx;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }
  
  .audio-container {
    padding: 20rpx;
    background-color: #f8f8f8;
    border-radius: 10rpx;
  }
  
  .audio-player {
    width: 100%;
  }
  
  .audio-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
    font-weight: 500;
  }
  
  .attachment-audio {
    width: 100%;
    height: 80rpx;
  }
  
  .attachment-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: #f8f8f8;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
  }
  
  .attachment-icon {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
  }
  
  .attachment-info {
    flex: 1;
  }
  
  .attachment-name {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 6rpx;
  }
  
  .attachment-size {
    font-size: 24rpx;
    color: #999;
  }
  
  .download-icon {
    margin-left: 20rpx;
  }
  
  .empty-attachments {
    text-align: center;
    padding: 100rpx 0;
    color: #999;
    font-size: 28rpx;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .chapter-title-text {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin: 30rpx 0 20rpx 30rpx;
    text-align: left;
  }
  .attachment-type-row {
    display: flex;
    align-items: center;
    margin-bottom: 18rpx;
  }
  .status-dot.green {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #4CD964;
    margin-right: 12rpx;
  }
  .type-label {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
  }
  .attachment-file-row {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.04);
    padding: 20rpx 24rpx;
    margin-bottom: 20rpx;
  }
  .file-icon {
    width: 69rpx;
    height: 69rpx;
    margin-right: 18rpx;
  }
  .file-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }
.video-container {
  position: relative;
  width: 100%;
}

.speed-btn {
  position: absolute;
  right: 20px;
  bottom: 80px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 999999; /* 提高z-index确保在全屏模式下可见 */
}

.speed-popup {
  position: absolute;
  right: 20px;
  bottom: 120px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 5px;
  z-index: 999999; /* 提高z-index确保在全屏模式下可见 */
}

.audio-wrapper {
  position: relative;
  width: 100%;
}

.audio-speed-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  z-index: 10;
}

.audio-speed-popup {
  position: absolute;
  right: 10px;
  top: 0;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8rpx;
  padding: 10rpx;
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.speed-item {
  padding: 5px 10px;
  margin: 2px;
  font-size: 12px;
  color: #ffffff;
  border-radius: 4px;
  text-align: center;
}

.speed-item.active {
  background-color: #007aff;
  color: #ffffff;
}
</style>