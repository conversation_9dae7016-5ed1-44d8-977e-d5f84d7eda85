<template>
  <view class="container">
    <easy-navbar title="学习集社区" />

    <!-- 搜索框区域 -->
    <view class="search-box">
      <input 
        v-model="title" 
        class="search-input" 
        placeholder="搜索学习集名称"
        @input="onSearchInput"
      />
    </view>

    <view class="word-set-container">
      <scroll-view scroll-y :show-scrollbar="true" :enhanced="true" :bounces="true">
        <view class="word-set-item">
          <easy-card v-for="(item, index) in wordSetState.pagination.list" :key="index" :title="item.title"
            :cover-url="item.coverUrl" :nickname="item.nickname" :avatar="item.avatar" :is-store="item.isStore"
            :word-card-count="item?.wordCardCount" mode="word" @handleClick="goToSetDetail(item.id)" />
        </view>

        <uni-load-more icon-type="auto" v-if="wordSetState.pagination.total > 0" :status="wordSetState.loadStatus"
          :content-text="{ contentdown: '上拉加载更多' }" @tap="loadWordSetMore" />
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onReachBottom } from '@dcloudio/uni-app';
import { ref, reactive } from 'vue';
import sheep from '@/sheep';
import SetApi from '@/sheep/api/set';
import _ from 'lodash-es';

const wordSetState = reactive({
  loadStatus: 'more', // 加载状态：more-加载前，loading-加载中，noMore-没有更多了
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 8,
  },
});

// 搜索输入防抖
const onSearchInput = _.debounce(() => {
  wordSetState.pagination.pageNo = 1;
  getWordSets();
}, 500);

const title = ref('');

const initData = async () => {
  try {
    const { code, data } = await SetApi.getQualityWordSets({ 
      pageNo: 1, 
      pageSize: wordSetState.pagination.pageSize 
    });
    if (code !== 0 || !data) {
      sheep.$router.back();
      return;
    }
    wordSetState.pagination.list = data.list;
    wordSetState.pagination.total = data.total;
    wordSetState.loadStatus = wordSetState.pagination.list.length < wordSetState.pagination.total ? 'more' : 'noMore';
  } catch (err) {
    sheep.$helper.toast('获取数据失败');
    sheep.$router.back();
  }
}

// 跳转到学习集详情页
const goToSetDetail = async (id) => {
  if (!id) {
    return;
  }
  // 正常跳转到学习集详情页
  sheep.$router.go(`/pages/set/word-card?id=${id}`);
};

function loadWordSetMore() {
  if (wordSetState.loadStatus === 'noMore') {
    return;
  }
  wordSetState.pagination.pageNo++;
  getWordSets();
}

const getWordSets = async () => {
  wordSetState.loadStatus = 'loading';
  const { code, data } = await SetApi.getQualityWordSets({
    title: title.value,
    pageNo: wordSetState.pagination.pageNo,
    pageSize: wordSetState.pagination.pageSize,
  });
  if (code !== 0) {
    return;
  }
  if (wordSetState.pagination.pageNo === 1) {
    wordSetState.pagination.list = [];
  }
  if (wordSetState.pagination.pageNo === 1 && !data.list.length) {
    sheep.$helper.toast('无搜索结果');
    return;
  }
  
  // 合并列表
  wordSetState.pagination.list = [...wordSetState.pagination.list, ...data.list];
  wordSetState.pagination.total = data.total;
  wordSetState.loadStatus = wordSetState.pagination.list.length < wordSetState.pagination.total ? 'more' : 'noMore';
}

onLoad(() => {
  initData();
});

// 上拉加载更多
onReachBottom(() => {
  loadWordSetMore();
});
</script>

<style scoped lang="scss">
.container {
  background: #F8FCFF;
  min-height: 100vh;

  .search-box {
    padding: 20rpx 30rpx;
    background: #fff;
    
    .search-input {
      height: 80rpx;
      padding: 0 30rpx;
      background: #f5f5f5;
      border-radius: 40rpx;
      font-size: 28rpx;
    }
  }

  .word-set-container {
    padding: 30rpx;

    .word-set-item {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
}
</style>
