# 牛码

## 已移除的分包配置
原项目中的 commission 分包已被删掉，便于通过微信审核，如需要请将 commission.json 中的配置粘贴回pages.json

# 芋道

## ?? 新手必读

* 演示地址：<https://doc.iocoder.cn/mall-preview/>
* 启动文档：<https://doc.iocoder.cn/quick-start/>
* 视频教程：<https://doc.iocoder.cn/video/>

## ?? 商城简介

**语链**，基于 [芋道开发平台](https://github.com/YunaiV/ruoyi-vue-pro) 构建，以开发者为中心，打造中国第一流的 Java 开源商城系统，全部开源，个人与企业可 100% 免费使用。

> 有任何问题，或者想要的功能，可以在 Issues 中提给艿艿。
>
> ?? 给项目点点 Star 吧，这对我们真的很重要！

![功能图](/.image/common/mall-feature.png)

* 基于 uni-app + Vue3 开发，支持微信小程序、微信公众号、H5 移动端，未来会支持支付宝小程序、抖音小程序等
* 支持 SaaS 多租户，可满足商品、订单、支付、会员、优惠券、秒杀、拼团、砍价、分销、积分等多种经营需求

## ?? 后端架构

支持 Spring Boot、Spring Cloud 两种架构：

① Spring Boot 单体架构：<https://doc.iocoder.cn>

![架构图](/.image/common/ruoyi-vue-pro-architecture.png)

② Spring Cloud 微服务架构：<https://cloud.iocoder.cn>

![架构图](/.image/common/yudao-cloud-architecture.png)

## ?? 移动端预览

![移动端预览](/.image/common/mall-preview.png)

## ?? 管理端预览

![店铺装修](/.image/mall/店铺装修.png)

![会员详情](/.image/mall/会员详情.png)

![商品详情](/.image/mall/商品详情.png)

![订单详情](/.image/mall/订单详情.png)

![营销中心](/.image/mall/营销中心.png)

