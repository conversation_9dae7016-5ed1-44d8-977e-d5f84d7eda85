import $store from '@/sheep/store';
import $helper from '@/sheep/helper';
import dayjs from 'dayjs';
import { ref } from 'vue';
import test from '@/sheep/helper/test.js';
import AuthUtil from '@/sheep/api/member/auth';
import sheep from '@/sheep';

// 打开授权弹框
export function showAuthModal(type = 'customLogin') {
  if (type === 'customLogin') {
    // 跳转到自定义的登录页
    uni.navigateTo({ url: '/pages/user/login' });
    return;
  }
  const modal = $store('modal');
  if (modal.auth !== '') {
    // 注意：延迟修改，保证下面的 closeAuthModal 先执行掉
    setTimeout(() => {
      modal.$patch((state) => {
        state.auth = type;
      });
    }, 500);
    closeAuthModal();
  } else {
    modal.$patch((state) => {
      state.auth = type;
    });
  }
}

// 关闭授权弹框
export function closeAuthModal() {
  $store('modal').$patch((state) => {
    state.auth = '';
  });
}

// 打开分享弹框
export function showShareModal() {
  $store('modal').$patch((state) => {
    state.share = true;
  });
}

// 关闭分享弹框
export function closeShareModal() {
  $store('modal').$patch((state) => {
    state.share = false;
  });
}

// 打开快捷菜单
export function showMenuTools() {
  $store('modal').$patch((state) => {
    state.menu = true;
  });
}

// 关闭快捷菜单
export function closeMenuTools() {
  $store('modal').$patch((state) => {
    state.menu = false;
  });
}

// 发送短信验证码  60秒
export function getSmsCode(event, mobile) {
  const modalStore = $store('modal');
  const lastSendTimer = modalStore.lastTimer[event];
  if (typeof lastSendTimer === 'undefined') {
    $helper.toast('短信发送事件错误');
    return;
  }

  const duration = dayjs().unix() - lastSendTimer;
  const canSend = duration >= 60;
  if (!canSend) {
    $helper.toast('请稍后再试');
    return;
  }
  // 手机号为空时不发送请求
  if (!mobile) {
    $helper.toast('请输入手机号码');
    return;
  }
  // 只有 mobile 非空时才校验。因为部分场景（修改密码），不需要输入手机
  if (!test.mobile(mobile)) {
    $helper.toast('请输入正确的手机号');
    return;
  }

  // 发送验证码 + 更新上次发送验证码时间
  let scene = -1;
  switch (event) {
    case 'resetPassword':
      scene = 4;
      break;
    case 'changePassword':
      scene = 3;
      break;
    case 'changeMobile':
      scene = 2;
      break;
    case 'smsLogin':
      scene = 1;
      break;
  }
  AuthUtil.sendSmsCode(mobile, scene).then((res) => {
    if (res.code === 0) {
      modalStore.$patch((state) => {
        state.lastTimer[event] = dayjs().unix();
      });
    }
  });
}

// 获取短信验证码倒计时 -- 60秒
export function getSmsTimer(event, mobile = '') {
  const modalStore = $store('modal');
  const lastSendTimer = modalStore.lastTimer[event];

  if (typeof lastSendTimer === 'undefined') {
    $helper.toast('短信发送事件错误');
    return;
  }

  const duration = ref(dayjs().unix() - lastSendTimer - 60);
  const canSend = duration.value >= 0;

  if (canSend) {
    return '获取验证码';
  }

  if (!canSend) {
    setTimeout(() => {
      duration.value++;
    }, 1000);
    return -duration.value.toString() + ' 秒';
  }
}

// 记录广告弹框历史
export function saveAdvHistory(adv) {
  const modal = $store('modal');

  modal.$patch((state) => {
    if (!state.advHistory.includes(adv.imgUrl)) {
      state.advHistory.push(adv.imgUrl);
    }
  });
}
