import request from '@/sheep/request';
const TranslateApi = {
  textTranslate: (data) => {
    return request({
      url: `/ai/translate/text`,
      method: 'POST',
      data,
      custom: {
        loadingMsg: "翻译中...",
        showSuccess: true,
        successMsg: "翻译完成",
      },
    });
  },
  textTranslateStream: (data, config) => {
    return request({
      url: `/ai/translate/text-stream`,
      method: 'POST',
      data,
      custom: {
        showLoading: false,
      },
      ...config,
    });
  },
};

export default TranslateApi;
