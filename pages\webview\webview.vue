<template>
  <view class="webview">
    <web-view :src="viewerUrl" @message="handleMessage" @error="handleError"></web-view>
  </view>
</template>

<script>
  // 使用Options API代替Composition API
  export default {
    data() {
      return {
        pdfUrl: '',
        title: 'PDF文档',
        viewerUrl: '',
        PDF_VIEWER_BASE: 'https://pdf.ai.nnnmkj.com/web/viewer.html',
      };
    },

    // 使用标准的onLoad生命周期函数
    onLoad(options) {
      // 获取PDF文件链接
      if (options.url) {
        this.pdfUrl = decodeURIComponent(options.url || '');
        this.title = decodeURIComponent(options.title || 'PDF文档');

        // 设置页面标题
        uni.setNavigationBarTitle({
          title: this.title,
        });

        // 构建PDF查看器URL
        this.viewerUrl = `${this.PDF_VIEWER_BASE}?file=${encodeURIComponent(this.pdfUrl)}`;
        console.log(this.viewerUrl);
      } else {
        // 没有PDF链接时提示错误
        uni.showToast({
          title: 'PDF链接不存在',
          icon: 'none',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },

    methods: {
      // 处理web-view消息
      handleMessage(event) {
        console.log('PDF查看器消息:', event);
      },

      // 处理web-view错误
      handleError(error) {
        console.error('PDF查看器错误:', error);
        uni.showToast({
          title: 'PDF加载失败',
          icon: 'none',
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .pdf-viewer {
    width: 100%;
    height: 100vh;
  }
</style>