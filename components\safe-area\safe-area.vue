<template>
  <view class="safe-area" :style="areaStyle">
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import sheep from '@/sheep';

const areaStyle = ref('');

onMounted(() => {
  const systemInfo = sheep.$helper.sys();
  const { safeAreaInsets } = systemInfo;
  areaStyle.value = `padding-top: ${safeAreaInsets?.top || 0}px;`;
});
</script>

<style>
.safe-area {
  width: 100%;
  box-sizing: border-box;
}
</style>