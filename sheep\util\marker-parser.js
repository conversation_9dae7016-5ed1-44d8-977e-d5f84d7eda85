/**
 * 标记解析器工具
 * 用于解析带有特定标记的文本流，例如 [TAG]内容[/TAG]
 * 支持多个标记的解析，如 [O]原文[/O][R]润色[/R][E]说明[/E]
 */
import { ref } from 'vue';

/**
 * 创建一个标记解析器
 * @param {Object} markers - 标记定义，格式为 { KEY: { START: '[TAG]', END: '[/TAG]' } }
 * @param {Object} options - 配置选项
 * @param {boolean} options.realTimeUpdate - 是否实时更新内容，默认为true
 * @returns {Object} 返回标记解析器对象
 */
export function createMarkerParser(markers, options = {}) {
  const { realTimeUpdate = true } = options;
  
  // 存储解析结果的对象
  const parsedContent = {};
  const buffer = ref(''); // 用于累积内容
  const currentMarker = ref(null); // 当前正在处理的标记
  const contentBuffer = ref(''); // 当前标记的内容缓冲区

  // 为每个标记创建对应的响应式引用
  Object.keys(markers).forEach(key => {
    parsedContent[key] = ref('');
  });

  /**
   * 检查是否匹配到开始标记
   * @param {string} text - 要检查的文本
   * @returns {string|null} 匹配到的标记键名
   */
  const checkStartMarker = (text) => {
    for (const [key, marker] of Object.entries(markers)) {
      if (text.includes(marker.START)) {
        return key;
      }
    }
    return null;
  };

  /**
   * 检查是否匹配到结束标记
   * @param {string} text - 要检查的文本
   * @returns {boolean} 是否匹配到结束标记
   */
  const checkEndMarker = (text) => {
    if (!currentMarker.value) return false;
    return text.includes(markers[currentMarker.value].END);
  };

  /**
   * 实时更新内容
   * @param {string} content - 当前内容
   */
  const updateContent = (content) => {
    if (currentMarker.value && realTimeUpdate) {
      parsedContent[currentMarker.value].value = content;
    }
  };

  /**
   * 解析文本内容
   * @param {string} content - 要解析的文本内容
   */
  const parseContent = (content) => {
    // 累积内容
    buffer.value += content;

    // 如果当前没有在处理任何标记
    if (!currentMarker.value) {
      // 检查是否有新的开始标记
      const newMarker = checkStartMarker(buffer.value);
      if (newMarker) {
        currentMarker.value = newMarker;
        // 提取开始标记之后的内容
        const startPos = buffer.value.indexOf(markers[newMarker].START) + markers[newMarker].START.length;
        contentBuffer.value = buffer.value.substring(startPos);
        buffer.value = '';
        
        // 实时更新
        updateContent(contentBuffer.value);
      }
    } else {
      // 当前正在处理标记
      contentBuffer.value += content;

      // 检查是否遇到结束标记
      if (checkEndMarker(contentBuffer.value)) {
        // 提取结束标记之前的内容
        const endPos = contentBuffer.value.indexOf(markers[currentMarker.value].END);
        const finalContent = contentBuffer.value.substring(0, endPos);
        
        // 更新解析结果
        parsedContent[currentMarker.value].value = finalContent;

        // 重置状态
        currentMarker.value = null;
        contentBuffer.value = '';
        buffer.value = '';
      } else {
        // 实时更新
        updateContent(contentBuffer.value);
      }
    }
  };

  /**
   * 重置解析器状态
   */
  const reset = () => {
    buffer.value = '';
    currentMarker.value = null;
    contentBuffer.value = '';
    Object.keys(parsedContent).forEach(key => {
      parsedContent[key].value = '';
    });
  };

  /**
   * 获取所有已解析的内容
   * @returns {Object} 所有已解析的内容
   */
  const getAllParsedContent = () => {
    const result = {};
    Object.keys(parsedContent).forEach(key => {
      result[key] = parsedContent[key].value;
    });
    return result;
  };

  return {
    parseContent,
    reset,
    getAllParsedContent,
    parsedContent
  };
}

export default {
  createMarkerParser
};
