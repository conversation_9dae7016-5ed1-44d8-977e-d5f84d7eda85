import request from '@/sheep/request';

const UserApi = {
  // 获得基本信息
  getUserInfo: () => {
    return request({
      url: '/member/user/get',
      method: 'GET',
      custom: {
        showLoading: false,
        auth: true,
      },
    });
  },
  // 根据用户ID获取用户信息
  getUserById: (id) => {
    return request({
      url: '/member/user/get',
      method: 'GET',
      params: { id },
      custom: {
        showLoading: false,
      },
    });
  },
  // 修改基本信息
  updateUser: (data) => {
    return request({
      url: '/member/user/update',
      method: 'PUT',
      data,
      custom: {
        auth: true,
        showSuccess: true,
        successMsg: '更新成功'
      },
    });
  },
  // 修改用户手机
  updateUserMobile: (data) => {
    return request({
      url: '/member/user/update-mobile',
      method: 'PUT',
      data,
      custom: {
        loadingMsg: '验证中',
        showSuccess: true,
        successMsg: '修改成功'
      },
    });
  },
  // 基于微信小程序的授权码，修改用户手机
  updateUserMobileByWeixin: (code) => {
    return request({
      url: '/member/user/update-mobile-by-weixin',
      method: 'PUT',
      data: {
        code
      },
      custom: {
        showSuccess: true,
        loadingMsg: '获取中',
        successMsg: '修改成功'
      },
    });
  },
  // 修改密码
  updateUserPassword: (data) => {
    return request({
      url: '/member/user/update-password',
      method: 'PUT',
      data,
      custom: {
        loadingMsg: '验证中',
        showSuccess: true,
        successMsg: '修改成功'
      },
    });
  },
  // 重置密码
  resetUserPassword: (data) => {
    return request({
      url: '/member/user/reset-password',
      method: 'PUT',
      data,
      custom: {
        loadingMsg: '验证中',
        showSuccess: true,
        successMsg: '修改成功'
      }
    });
  },
  // 检查手机号是否已存在
  checkMobileExists: (mobile) => {
    return request({
      url: '/member/user/check-mobile',
      method: 'GET',
      params: { mobile },
      custom: {
        showLoading: false,
        showError: false,
        auth: true,
      },
    });
  },
  // 强制绑定手机号（合并账号）
  forceBindMobile: (data) => {
    return request({
      url: '/member/user/force-bind-mobile',
      method: 'PUT',
      data,
      custom: {
        loadingMsg: '绑定中',
        showSuccess: true,
        successMsg: '绑定成功'
      },
    });
  },
};

export default UserApi;
