import request from '@/sheep/request';

const SessionApi = {
  // 获得进行中的学习会话
  getOngoingSession: (data) => {
    return request({
      url: '/learning/session/getOngoingSession',
      method: 'POST',
      params: {
        id: data.id
      },
      data: {
        setId: data.setId,    // 学习集编号（必填）
        mode: data.mode,      // 学习模式（必填）
        userId: data.userId   // 用户编号（可选）
      },
      custom: {
        auth: true,
        showLoading: false
      }
    });
  },

  // 创建学习会话
  createSession: (data) => {
    return request({
      url: '/learning/session/create',
      method: 'POST',
      data: {
        setId: data.setId,
        mode: data.mode || 0,
        status: data.status || 1,
      },
      custom: {
        auth: true,
        showLoading: false
      }
    });
  },

  // 结束学习会话
  completeSession: (id) => {
    return request({
      url: '/learning/session/completed',
      method: 'POST',
      params: {
        id  // 会话编号（必填）
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },

  // 获得学习会话
  getSession: (id) => {
    return request({
      url: '/learning/session/get',
      method: 'GET',
      params: { id },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },

  // 更新学习会话
  updateSession: (data) => {
    return request({
      url: '/learning/session/update',
      method: 'PUT',
      data: {
        id: data.id,            // 编号（必填）
        setId: data.setId,      // 学习集编号（必填）
        userId: data.userId,    // 用户编号
        mode: data.mode,        // 学习模式
        status: data.status     // 会话状态
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },

  // 创建单词卡记录
  createSessionCardRecord: (data) => {
    return request({
      url: '/learning/session-card-record/create',
      method: 'POST',
      data: {
        setId: data.setId,
        sessionId: data.sessionId,
        wordCardId: data.wordCardId,
        status: data.status
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },
  
  // 创建配对记录
  createMatchRecord: (data) => {
    return request({
      url: '/learning/session-match-record/create',
      method: 'POST',
      data: {
        setId: data.setId,         // 学习集ID（必填）
        sessionId: data.sessionId, // 学习会话ID（必填）
        word: data.word,           // 词语（必填）
        definition: data.definition, // 定义（必填）
        isCorrect: data.isCorrect   // 是否正确
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },
  
  // 创建会话成绩
  createSessionResult: (data) => {
    return request({
      url: '/learning/session-result/create',
      method: 'POST',
      data: {
        sessionId: data.sessionId,    // 学习会话ID（必填）
        setId: data.setId,            // 学习集ID（必填）
        allCount: data.allCount,      // 总题目数
        timeTaken: data.timeTaken,    // 完成时间(秒)
        correctCount: data.correctCount, // 答对数量
        errorCount: data.errorCount     // 答错数量
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },
  
  // 创建学习记录
  createStudyRecord: (data) => {
    return request({
      url: '/learning/session-study-record/create',
      method: 'POST',
      data: {
        setId: data.setId,           // 学习集ID（必填）
        sessionId: data.sessionId,   // 学习会话ID（必填）
        questionId: data.questionId, // 题目ID（必填）
        userAnswer: data.userAnswer, // 用户答案（必填）
        isCorrect: data.isCorrect    // 是否正确
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },

  // 创建测试记录
  createTestRecord: (data) => {
    return request({
      url: '/learning/session-test-record/create',
      method: 'POST',
      data: {
        setId: data.setId,           // 学习集ID（必填）
        sessionId: data.sessionId,   // 学习会话ID（必填）
        questionId: data.questionId, // 题目ID（必填）
        userAnswer: data.userAnswer, // 用户答案（必填）
        isCorrect: data.isCorrect    // 是否正确
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },

  // 更新测试记录
  updateTestRecord: (data) => {
    return request({
      url: '/learning/session-test-record/update',
      method: 'PUT',
      data: {
        id: data.id,                 // 编号（可选）
        setId: data.setId,           // 学习集ID（必填）
        sessionId: data.sessionId,   // 学习会话ID（必填）
        questionId: data.questionId, // 题目ID（必填）
        userAnswer: data.userAnswer, // 用户答案（必填）
        isCorrect: data.isCorrect    // 是否正确
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },

  // 创建学习会话进度
  createSessionProgress: (data) => {
    return request({
      url: '/learning/session-progress/create',
      method: 'POST',
      data: {
        id: data.id,                   // 编号（可选）
        sessionId: data.sessionId,     // 学习会话ID（必填）
        allCount: data.allCount,       // 题目总数（可选）
        currentCount: data.currentCount, // 当前数量（可选）
        rightCount: data.rightCount    // 正确数量（可选）
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  },

  // 更新学习会话进度
  updateSessionProgress: (data) => {
    return request({
      url: '/learning/session-progress/update',
      method: 'PUT',
      data: {
        id: data.id,                   // 编号（可选）
        sessionId: data.sessionId,     // 学习会话ID（必填）
        allCount: data.allCount,       // 题目总数（可选）
        currentCount: data.currentCount, // 当前数量（可选）
        rightCount: data.rightCount    // 正确数量（可选）
      },
      custom: {
        auth: true,
        showLoading: false,
      }
    });
  }
};

export default SessionApi;
