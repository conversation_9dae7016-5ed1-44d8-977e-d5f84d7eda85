<template>
  <view class="container">
    <dragon-drag-button :autoDocking="true" :size="88" :xEdge="0" :yEdge="200">
      <view class="menu-btn" @click="openQuestionList">
        <text class="menu-icon-text">=</text>
      </view>
    </dragon-drag-button>

    <!-- 题目列表弹窗 -->
    <su-popup
      :show="showQuestionList"
      type="bottom"
      @maskClick="closeQuestionList"
      backgroundColor="#ffffff"
      :safeArea="true"
      :animation="true"
    >
      <view class="question-list-container">
        <view class="question-list-header">
          <text class="question-list-title">题目</text>
          <view class="close-btn" @click="closeQuestionList">×</view>
        </view>
        <scroll-view scroll-y class="question-list-content">
          <view 
            v-for="(item, index) in questions" 
            :key="index" 
            class="question-list-item"
            :class="{
              'question-current': index === currentIndex,
              'question-answered': isQuestionAnswered(index),
              'question-correct': isQuestionAnswered(index) && getQuestionAnswerStatus(index),
              'question-incorrect': isQuestionAnswered(index) && !getQuestionAnswerStatus(index),
              'question-filled': hasFilledAnswer(index)
            }"
            @click="switchQuestion(index)"
          >
            <text class="question-list-number" :class="{'number-filled': hasFilledAnswer(index)}">{{ index + 1 }}</text>
            <text class="question-list-type">{{ getQuestionTypeText(item.questionType) }}</text>
          </view>
        </scroll-view>
      </view>
    </su-popup>

    <easy-navbar :title="(currentIndex + 1) + '/' + questions.length" :transparent="true" @back="confirmBack" />



    <view class="question-container">

      <easy-progress
        mode="double"
        :currentProgress="progress"
        :masteredProgress="progress"
        :currentCount="currentIndex + 1"
        :totalCount="questions.length"
        :showElephant="false"
        :showNumbers="true"
      />

      <view class="question-content">
        <view class="question-number">
          {{ currentIndex + 1 }}. {{ currentQuestion ? currentQuestion.content : '' }}
        </view>
      </view>

      <view class="options-title">选择答案</view>

      <!-- 判断题 (questionType 0) -->
      <view class="options-container" v-if="currentQuestion && currentQuestion.questionType === 0">
        <view
          v-for="(option, index) in currentQuestion.options"
          :key="index"
          class="option-item"
          :class="{ 'option-selected': selectedOption === option.questionOption }"
          @click="selectOption(option.questionOption, option.content)"
        >
          <text class="option-text">{{ option.content }}</text>
        </view>
      </view>

      <!-- 单选题 (questionType 1) -->
      <view class="options-container" v-if="currentQuestion && currentQuestion.questionType === 1">
        <view
          v-for="(option, index) in currentQuestion.options"
          :key="index"
          class="option-item"
          :class="{ 'option-selected': selectedOption === option.questionOption }"
          @click="selectOption(option.questionOption, option.content)"
        >
          <text class="option-text">{{ option.content }}</text>
        </view>
      </view>

      <!-- 多选题 (questionType 2) -->
      <view class="options-container" v-if="currentQuestion && currentQuestion.questionType === 2">
        <view 
          v-for="(option, index) in currentQuestion.options" 
          :key="index" 
          class="option-item"
          :class="{ 'option-selected': selectedOptions.includes(option.questionOption) }"
          @click="toggleMultipleOption(option.questionOption, option.content)"
        >
          <text class="option-text">{{ option.content }}</text>
        </view>
      </view>

      <!-- 填空题 (questionType 3) -->
      <view class="fill-blank-container" v-if="currentQuestion && currentQuestion.questionType === 3">
        <textarea 
          class="fill-blank-input" 
          v-model="fillBlankAnswer"
          placeholder="请在此输入您的答案，可输入多个答案（用逗号、顿号或空格分隔）"
        ></textarea>
      </view>
    </view>

    <view class="button-container">
      <view class="bottom-button" @click="handleNextQuestion">
        {{ isLastQuestion ? '完成测试' : '下一题' }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import SessionApi from '@/sheep/api/set/session';
import sheep from '@/sheep';
import EasyNavbar from '@/components/easy-navbar/easy-navbar.vue';
import SuPopup from '@/sheep/ui/su-popup/su-popup';
import EasyProgress from '@/components/easy-progress/easy-progress.vue';

// 页面状态
const questions = ref([]);
const currentIndex = ref(0);
const startTime = ref(Date.now());
const currentSetId = ref('');
const loading = ref(false);
const sessionId = ref('');
const correctCount = ref(0);
const incorrectCount = ref(0);
const selectedOption = ref(null);
const selectedOptions = ref([]); // 多选题选项
const fillBlankAnswer = ref(''); // 填空题答案
const userSelectedAnswer = ref('');
const isCorrect = ref(false);
const answers = ref([]); // 存储用户的回答记录
const sessionData = ref(null); // 存储会话数据
const showTip = ref(false); // 显示提示信息
const autoJumping = ref(false); // 是否自动跳转中
const correctOptions = ref([]); // 多选题正确选项
const isReviewMode = ref(false); // 是否为复习模式
let autoJumpTimer = null; // 自动跳转定时器

// 进度相关
const progressId = ref(null);
const hasProgress = ref(false);

// 用于存储已填写但未提交的答案
const unsavedAnswers = ref({});

// 获取题目类型文本
const getQuestionTypeText = (type) => {
  switch (type) {
    case 0: return '判断题';
    case 1: return '单选题';
    case 2: return '多选题';
    case 3: return '填空题';
    default: return '未知题型';
  }
};

// 更新测试进度
const updateProgress = async () => {
  if (!sessionId.value) return;
  // 计算当前进度
  // 注意：currentCount代表已完成的题目数量，而非当前题目的索引
  // 对应的索引应该是currentIndex.value，而完成的数量应该是+1
  const completedCount = currentIndex.value + 1;
  const allCount = questions.value.length;

  // 如果有已存在的进度记录，更新它
  if (hasProgress.value && progressId.value) {
    await SessionApi.updateSessionProgress({
      id: progressId.value,
      sessionId: sessionId.value,
      allCount: allCount,
      currentCount: completedCount,  // 已完成的题目数量
      rightCount: correctCount.value
    });
  } else {
    // 否则创建新的进度记录
    const createRes = await SessionApi.createSessionProgress({
      sessionId: sessionId.value,
      allCount: allCount,
      currentCount: completedCount,  // 已完成的题目数量
      rightCount: correctCount.value
    });

    if (createRes.code === 0 && createRes.data) {
      progressId.value = createRes.data;
      hasProgress.value = true;
    }
  }
};

// 恢复测试进度
const restoreProgressData = (progresses) => {
  if (!progresses || progresses.length === 0) return false;
  
  try {
    // 获取最新的进度记录
    const latestProgress = progresses.sort((a, b) => 
      new Date(b.updateTime) - new Date(a.updateTime)
    )[0];
    
    if (latestProgress) {
      progressId.value = latestProgress.id;
      hasProgress.value = true;
      
      // 恢复进度相关的状态
      currentIndex.value = Math.max(0, latestProgress.currentCount - 1);
      correctCount.value = latestProgress.rightCount || 0;
      incorrectCount.value = (latestProgress.currentCount - latestProgress.rightCount) || 0;
      
      // 如果恢复的索引大于题目数量，重置为0
      if (currentIndex.value >= questions.value.length) {
        currentIndex.value = 0;
      }
      
      return true;
    }
  } catch (e) {
    console.error('恢复测试进度失败:', e);
  }
  
  return false;
};

// 格式化正确答案显示
const formatCorrectAnswer = (answer) => {
  if (!answer) return '';
  // 处理可能有多个正确答案的情况（用|分隔）
  return answer.split('|').join(' 或 ');
};

// 自定义返回事件，显示确认提示框
const confirmBack = () => {
  uni.showModal({
    title: '提示',
    content: '确定退出测试吗？当前测试进度将不会被保存。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack();
      }
    }
  });
};

// 计算属性
const currentQuestion = computed(() => questions.value[currentIndex.value] || null);
const isLastQuestion = computed(() => currentIndex.value === questions.value.length - 1);
const progress = computed(() => {
  if (questions.value.length <= 1) return 0;
  return Math.round((currentIndex.value / (questions.value.length - 1)) * 100);
});

// 获取测试会话和题目
const getTestSession = async (setId) => {
  loading.value = true;
  // 获取进行中的会话（接口现在会自动创建新会话）
  const ongoingRes = await SessionApi.getOngoingSession({
    setId: setId,
    mode: 2, // 测试模式
  });

  if (ongoingRes.data) {
    sessionId.value = ongoingRes.data.id;
    sessionData.value = ongoingRes.data;
  } else {
    loading.value = false;
    return false;
  }

  // 确保会话数据中包含题目
  if (!sessionData.value || !sessionData.value.questions || sessionData.value.questions.length === 0) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const finalAttempt = await SessionApi.getSession(sessionId.value);

    if (finalAttempt.code === 0 && finalAttempt.data && finalAttempt.data.questions) {
      sessionData.value = finalAttempt.data;
    } else {
      loading.value = false;
      return false;
    }
  }

  // 从questions中获取题目
  questions.value = sessionData.value.questions.map((item) => {
    return {
      id: item.id,
      questionId: item.id,
      questionType: item.questionType, // 0-判断题, 1-单选题, 2-多选题, 3-填空题
      content: item.content,
      options: item.options || [],
      answer: item.answer,
    };
  });

  // 尝试从进度记录中恢复状态
  if (sessionData.value.progresses && sessionData.value.progresses.length > 0) {
    const restored = restoreProgressData(sessionData.value.progresses);
    if (restored) {
      sheep.$helper.toast('已恢复上次测试进度');
    }
  }

  // 从测试记录中恢复详细的答题情况
  if (sessionId.value) {
    await restoreSessionState();
  }

  // 确保当前显示的题目已加载答案数据
  if (currentIndex.value >= 0 && currentIndex.value < questions.value.length) {
    loadQuestionAnswer(currentIndex.value);
  }

  loading.value = false;
  return true;
};



// 选择单选题/判断题答案
const selectOption = (option, content) => {
  // 更新选中的选项
  selectedOption.value = option;
  userSelectedAnswer.value = content;
  
  // 保存未提交的答案
  saveUnsavedAnswer();
};

// 多选题选择切换
const toggleMultipleOption = (option, content) => {
  const index = selectedOptions.value.indexOf(option);
  if (index === -1) {
    selectedOptions.value.push(option);
  } else {
    selectedOptions.value.splice(index, 1);
  }
  
  // 保存未提交的答案
  saveUnsavedAnswer();
};

// 保存未提交的答案
const saveUnsavedAnswer = () => {
  if (!currentQuestion.value) return;
  
  const questionId = currentQuestion.value.id;
  const questionType = currentQuestion.value.questionType;
  switch (questionType) {
    case 0: // 判断题
    case 1: // 单选题
      if (selectedOption.value !== null) {
        unsavedAnswers.value[questionId] = {
          type: questionType,
          answer: selectedOption.value,
          content: userSelectedAnswer.value
        };
      }
      break;
    case 2: // 多选题
      if (selectedOptions.value.length > 0) {
        unsavedAnswers.value[questionId] = {
          type: questionType,
          answer: [...selectedOptions.value]  // 创建一个副本
        };
      }
      break;
    case 3: // 填空题
      if (fillBlankAnswer.value.trim() !== '') {
        unsavedAnswers.value[questionId] = {
          type: questionType,
          answer: fillBlankAnswer.value.trim()
        };
      }
      break;
  }
};

// 监听填空题答案变化
watch(fillBlankAnswer, (newValue) => {
  if (newValue.trim() !== '') {
    saveUnsavedAnswer();
  }
});

// 处理下一题按钮点击
const handleNextQuestion = async () => {
  // 根据题目类型检查是否有答案
  if (!hasValidAnswer()) {
    sheep.$helper.toast('请先选择或填写答案');
    return;
  }

  // 如果是最后一题且点击了"完成测试"，先检查是否所有题目都已回答
  if (isLastQuestion.value) {
    // 先保存当前题目的答案
    await evaluateAndRecordAnswer();
    await updateProgress();

    // 检查是否有未回答的题目
    const unansweredIndex = findFirstUnansweredQuestion();
    if (unansweredIndex !== -1) {
      sheep.$helper.toast('还有未完成的题目');
      // 跳转到未回答的题目
      performSwitch(unansweredIndex);
      return;
    }

    // 所有题目都已回答，完成测试
    await finishTest();
    return;
  }

  // 不是最后一题，常规处理
  // 评估答案并记录，自动创建或更新测试记录
  await evaluateAndRecordAnswer();

  // 更新进度记录
  await updateProgress();

  // 不是最后一题，前进到下一题
  currentIndex.value++;

  // 加载下一题的答案状态（如果有）
  loadQuestionAnswer(currentIndex.value);

  // 清理UI状态
  showTip.value = false;
  userSelectedAnswer.value = '';
  isCorrect.value = false;
  autoJumping.value = false;

  // 清除可能存在的定时器
  if (autoJumpTimer) {
    clearTimeout(autoJumpTimer);
    autoJumpTimer = null;
  }
};

// 查找第一个未回答的题目
const findFirstUnansweredQuestion = () => {
  if (!sessionData.value || !sessionData.value.testRecords) {
    // 如果没有测试记录，认为所有题目都未回答，返回第一题
    return 0;
  }
  
  // 检查每个题目是否有回答记录
  for (let i = 0; i < questions.value.length; i++) {
    const question = questions.value[i];
    const isAnswered = sessionData.value.testRecords.some(
      record => record.questionId === question.id
    );
    
    if (!isAnswered) {
      return i; // 找到未回答的题目，返回索引
    }
  }
  
  return -1; // 所有题目都已回答
};

// 检查是否有有效答案
const hasValidAnswer = () => {
  const questionType = currentQuestion.value.questionType;
  
  switch (questionType) {
    case 0: // 判断题
    case 1: // 单选题
      return selectedOption.value !== null;
    case 2: // 多选题
      return selectedOptions.value.length > 0;
    case 3: // 填空题
      return fillBlankAnswer.value.trim() !== '';
    default:
      return false;
  }
};

// 评估答案并记录
const evaluateAndRecordAnswer = async () => {
  // 当前题目为空，无法评估答案
  if (!currentQuestion.value) {
    return;
  }
  
  const questionType = currentQuestion.value.questionType;
  let userAnswer = '';
  let correct = false;

  switch (questionType) {
    case 0: // 判断题
    case 1: // 单选题
      userAnswer = selectedOption.value;
      correct = selectedOption.value === currentQuestion.value.answer;
      break;

    case 2: // 多选题
      userAnswer = selectedOptions.value.join(',');
      // 获取正确答案数组
      const correctAnswers = currentQuestion.value.answer ? currentQuestion.value.answer.split(',') : [];
      // 判断是否完全正确
      correct = correctAnswers.every(ans => selectedOptions.value.includes(ans)) &&
                selectedOptions.value.every(selected => correctAnswers.includes(selected));

      // 保存正确选项供展示
      correctOptions.value = correctAnswers;
      break;

    case 3: // 填空题
      userAnswer = fillBlankAnswer.value.trim();
      // 检查填空题答案，可能有多个正确答案用|分隔
      const fillBlankCorrectAnswers = currentQuestion.value.answer ? currentQuestion.value.answer.split('|') : [];
      
      // 新的判断逻辑：用户输入的答案可能包含多个答案（用逗号、或、空格等分隔）
      // 只要用户输入的内容包含任一正确答案，就算正确
      if (userAnswer) {
        // 将用户输入按常见分隔符分割成多个可能的答案
        const userAnswerParts = userAnswer.split(/[,，、；;或\s]+/).filter(Boolean);
        
        // 判断是否至少有一个部分匹配正确答案之一
        correct = fillBlankCorrectAnswers.some(correctAns => {
          // 直接匹配完整答案（忽略大小写）
          if (userAnswer.toLowerCase() === correctAns.toLowerCase()) {
            return true;
          }
          
          // 检查用户输入的各部分是否包含任一正确答案
          return userAnswerParts.some(part => 
            part.toLowerCase() === correctAns.toLowerCase()
          );
        });
      }
      break;
  }

  // 更新计数
  if (correct) {
    correctCount.value++;
  } else {
    incorrectCount.value++;
  }

  // 获取用户选择的答案内容用于显示
  let userAnswerContent = '';

  if (questionType === 0 || questionType === 1) {
    if (currentQuestion.value.options && currentQuestion.value.options.length > 0) {
      const option = currentQuestion.value.options.find(
        opt => opt.questionOption === selectedOption.value
      );
      userAnswerContent = option ? option.content : '';
    }
  } else if (questionType === 2) {
    if (currentQuestion.value.options && currentQuestion.value.options.length > 0) {
      userAnswerContent = selectedOptions.value.map(opt => {
        const option = currentQuestion.value.options.find(o => o.questionOption === opt);
        return option ? option.content : '';
      }).filter(Boolean).join(', ');
    }
  } else if (questionType === 3) {
    userAnswerContent = userAnswer;
  }

  // 记录用户的答案
  const existingAnswerIndex = answers.value.findIndex(a => a.questionId === currentQuestion.value.id);
  if (existingAnswerIndex !== -1) {
    // 更新已有答案记录
    answers.value[existingAnswerIndex] = {
      questionId: currentQuestion.value.id,
      content: currentQuestion.value.content,
      userAnswer: userAnswerContent,
      correctAnswer: questionType === 2 ?
        getMultipleChoiceCorrectAnswerText() :
        (questionType === 3 ? formatCorrectAnswer(currentQuestion.value.answer) : getCorrectAnswerText()),
      isCorrect: correct
    };
  } else {
    // 添加新答案记录
    answers.value.push({
      questionId: currentQuestion.value.id,
      content: currentQuestion.value.content,
      userAnswer: userAnswerContent,
      correctAnswer: questionType === 2 ?
        getMultipleChoiceCorrectAnswerText() :
        (questionType === 3 ? formatCorrectAnswer(currentQuestion.value.answer) : getCorrectAnswerText()),
      isCorrect: correct
    });
  }

  // 检查是否已经回答过这个问题
  const existingRecordIndex = sessionData.value?.testRecords?.findIndex(
    r => r.questionId === currentQuestion.value.id
  );

  if (existingRecordIndex !== -1 && existingRecordIndex !== undefined) {
    // 已回答过，使用更新接口
    await updateAnswer(
      sessionData.value.testRecords[existingRecordIndex].id,
      currentQuestion.value.id,
      userAnswer,
      correct
    );
  } else {
    // 没回答过，使用创建接口
    const recordId = await recordAnswer(currentQuestion.value.id, userAnswer, correct);

    // 如果成功创建记录，添加到sessionData中方便后续查询
    if (recordId && sessionData.value) {
      if (!sessionData.value.testRecords) {
        sessionData.value.testRecords = [];
      }

      sessionData.value.testRecords.push({
        id: recordId,
        questionId: currentQuestion.value.id,
        userAnswer: userAnswer,
        isCorrect: correct ? 1 : 0
      });
    }
  }

  // 更新isCorrect状态
  isCorrect.value = correct;

  // 清除未保存的答案
  if (currentQuestion.value && currentQuestion.value.id) {
    delete unsavedAnswers.value[currentQuestion.value.id];
  }
};

// 更新答题结果
const updateAnswer = async (recordId, questionId, userAnswer, isCorrect) => {
  if (!sessionId.value) return;

  // 使用updateTestRecord更新已有的测试记录
  await SessionApi.updateTestRecord({
    id: recordId,
    setId: currentSetId.value,
    sessionId: sessionId.value,
    questionId: questionId,
    userAnswer: userAnswer,
    isCorrect: isCorrect ? 1 : 0
  });
  
  // 也更新sessionData中的记录，以便后续查询
  if (sessionData.value && sessionData.value.testRecords) {
    const recordIndex = sessionData.value.testRecords.findIndex(r => r.id === recordId);
    if (recordIndex !== -1) {
      sessionData.value.testRecords[recordIndex] = {
        ...sessionData.value.testRecords[recordIndex],
        userAnswer: userAnswer,
        isCorrect: isCorrect ? 1 : 0
      };
    }
  }
};

// 获取多选题正确答案文本
const getMultipleChoiceCorrectAnswerText = () => {
  if (!currentQuestion.value || !currentQuestion.value.options) return '';
  
  const correctAnswerOptions = currentQuestion.value.answer.split(',');
  const correctTexts = correctAnswerOptions.map(opt => {
    const option = currentQuestion.value.options.find(o => o.questionOption === opt);
    return option ? option.content : '';
  }).filter(Boolean);
  
  return correctTexts.join(', ');
};

// 获取正确答案的文本
const getCorrectAnswerText = () => {
  if (!currentQuestion.value || !currentQuestion.value.options) return '';
  
  const correctOption = currentQuestion.value.options.find(
    option => option.questionOption === currentQuestion.value.answer
  );
  
  return correctOption ? correctOption.content : '';
};

// 记录答题结果
const recordAnswer = async (questionId, userAnswer, isCorrect) => {
  if (!sessionId.value) return null;

  // 使用createTestRecord与测试模式保持一致
  const response = await SessionApi.createTestRecord({
    setId: currentSetId.value,
    sessionId: sessionId.value,
    questionId: questionId, // 题目ID
    userAnswer: userAnswer, // 用户选择的答案
    isCorrect: isCorrect ? 1 : 0 // 1表示正确，0表示错误
  });

  // 返回创建的记录ID，用于后续更新
  return response.code === 0 && response.data ? response.data : null;
};

// 完成测试
const finishTest = async () => {
  try {
    // 再次检查是否有未回答的题目
    const unansweredIndex = findFirstUnansweredQuestion();
    if (unansweredIndex !== -1) {
      sheep.$helper.toast('还有未完成的题目');
      // 跳转到未回答的题目
      performSwitch(unansweredIndex);
      return;
    }
    
    // 复习模式特殊处理：无论是否还有错题，都跳转到结果页面
    if (isReviewMode.value) {
      const incorrectAnswers = answers.value.filter(answer => !answer.isCorrect);
      
      if (incorrectAnswers.length > 0) {
        sheep.$helper.toast(`本轮复习完成，还有${incorrectAnswers.length}道错题`);
      } else {
        sheep.$helper.toast('复习完成！所有题目都答对了');
      }
      
      // 复习模式下也跳转到结果页面，让用户选择是否继续复习
    }
    
    // 计算完成时间
    const endTime = Date.now();
    const testTime = Math.floor((endTime - startTime.value) / 1000); // 转换为秒
    
    if (sessionId.value) {
      // 最后一次更新进度（设置为已完成状态）
      if (hasProgress.value && progressId.value) {
        await SessionApi.updateSessionProgress({
          id: progressId.value,
          sessionId: sessionId.value,
          allCount: questions.value.length,
          currentCount: questions.value.length, // 设置为全部完成
          rightCount: correctCount.value
        });
      }
      
      // 创建会话结果
      await SessionApi.createSessionResult({
        sessionId: sessionId.value,
        setId: currentSetId.value,
        allCount: questions.value.length,
        timeTaken: testTime,
        correctCount: correctCount.value,
        errorCount: incorrectCount.value
      });
      
      // 不在这里结束会话，会话将在测试结果页面处理
    }
    
    // 验证答案记录与计数一致
    if (answers.value.length > 0) {
      const countFromAnswers = {
        correct: answers.value.filter(a => a.isCorrect).length,
        incorrect: answers.value.filter(a => !a.isCorrect).length
      };
      
      // 如果记录中的正确/错误数与计数器不一致，以记录为准
      if (countFromAnswers.correct !== correctCount.value || 
          countFromAnswers.incorrect !== incorrectCount.value) {
        console.warn('答案记录与计数不一致，已自动修正');
        correctCount.value = countFromAnswers.correct;
        incorrectCount.value = countFromAnswers.incorrect;
      }
    }
    
    // 验证总数一致性
    if (correctCount.value + incorrectCount.value !== questions.value.length) {
      if (answers.value.length === questions.value.length) {
        // 如果答案数量正确，根据答案重新计算
        correctCount.value = answers.value.filter(a => a.isCorrect).length;
        incorrectCount.value = answers.value.filter(a => !a.isCorrect).length;
      } else {
        // 强制修正总数
        incorrectCount.value = questions.value.length - correctCount.value;
      }
    }
    
    // 准备结果数据
    const resultData = {
      totalCards: questions.value.length,
      correctCards: correctCount.value,
      incorrectCards: incorrectCount.value,
      testTime: endTime - startTime.value,
      setId: currentSetId.value,
      answers: answers.value,
      sessionId: sessionId.value,
      mode: isReviewMode.value ? 'review' : 'normal'
    };
    
    // 跳转到结果页面
    sheep.$router.go(`/pages/set/test/result?data=${encodeURIComponent(JSON.stringify(resultData))}`,{}, { redirect: true });
  } catch (error) {
    
    // 验证答案记录与计数一致
    if (answers.value.length > 0) {
      correctCount.value = answers.value.filter(a => a.isCorrect).length;
      incorrectCount.value = answers.value.filter(a => !a.isCorrect).length;
    }
    
    // 验证总数一致性
    if (correctCount.value + incorrectCount.value !== questions.value.length) {
      incorrectCount.value = questions.value.length - correctCount.value;
    }
    
    // 即使API调用失败，也尝试跳转到结果页面
    const resultData = {
      totalCards: questions.value.length,
      correctCards: correctCount.value,
      incorrectCards: incorrectCount.value,
      testTime: Date.now() - startTime.value,
      setId: currentSetId.value,
      answers: answers.value,
      sessionId: sessionId.value,
      mode: isReviewMode.value ? 'review' : 'normal'
    };

    sheep.$router.go(`/pages/set/test/result?data=${encodeURIComponent(JSON.stringify(resultData))}`,{}, { redirect: true })
  }
};

// 组件销毁时清除定时器
onBeforeUnmount(() => {
  if (autoJumpTimer) {
    clearTimeout(autoJumpTimer);
  }
});

// 生命周期钩子
onLoad(async (options) => {
  const { setId, mode, incorrectData } = options;
  
  // 标记是否为复习模式
  isReviewMode.value = mode === 'review';
  let incorrectAnswers = [];
  
  // 如果是复习模式，解析错题数据
  if (isReviewMode.value && incorrectData) {
    try {
      incorrectAnswers = JSON.parse(decodeURIComponent(incorrectData));
    } catch (e) {
      console.error('解析错题数据失败:', e);
    }
  }
  
  if (setId) {
    // 保存setId以供后续使用
    currentSetId.value = setId;
    
    // 获取测试会话和题目
    let success = await getTestSession(setId);
    
    // 如果第一次加载失败，尝试重试一次
    if (!success) {
      await new Promise(resolve => setTimeout(resolve, 1500));
      success = await getTestSession(setId);
    }
    
    if (!success) {
      // 延迟后再次尝试
      setTimeout(async () => {
        success = await getTestSession(setId);
        
        if (!success) {
          sheep.$helper.toast('网络异常，无法加载测试数据');
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      }, 2000);
    }
    
    // 如果是复习模式且有错题数据，筛选出错题
    if (isReviewMode.value && incorrectAnswers.length > 0 && questions.value.length > 0) {
      // 获取错题的ID列表
      const incorrectIds = incorrectAnswers.map(item => item.questionId);
      
      // 筛选出错题
      questions.value = questions.value.filter(question => 
        incorrectIds.includes(question.id)
      );
      
      // 如果没有错题，显示提示
      if (questions.value.length === 0) {
        sheep.$helper.toast('没有需要复习的错题');
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } else {
        // 重置当前索引和进度
        currentIndex.value = 0;
        // 更新页面标题
        uni.setNavigationBarTitle({
          title: '错题复习'
        });
      }
    }
  } else {
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 恢复会话状态并加载用户已有答案
const restoreSessionState = async () => {
  try {
    if (!sessionId.value || !sessionData.value) return;

    // 直接从sessionData中获取测试记录
    if (sessionData.value.testRecords && sessionData.value.testRecords.length > 0) {
      const records = sessionData.value.testRecords;

      // 处理已回答的题目
      let answeredCount = 0;
      let lastAnsweredIndex = -1;

      // 清空现有答案记录，避免重复
      answers.value = [];
      
      // 重置计数器
      correctCount.value = 0;
      incorrectCount.value = 0;

      // 找出已回答的题目并记录
      for (let i = 0; i < questions.value.length; i++) {
        const question = questions.value[i];
        const record = records.find(r => r.questionId === question.id);

        if (record) {
          // 这道题已回答
          answeredCount++;
          lastAnsweredIndex = i;

          // 统计正确/错误数
          if (record.isCorrect === 1) {
            correctCount.value++;
          } else {
            incorrectCount.value++;
          }

          // 临时将currentQuestion设置为当前问题以获取正确答案
          const tempCurrentIndex = currentIndex.value;
          currentIndex.value = i;

          // 添加到答案记录中
          answers.value.push({
            questionId: question.id,
            content: question.content,
            userAnswer: record.userAnswer,
            correctAnswer: question.questionType === 2 ?
              getMultipleChoiceCorrectAnswerText() :
              (question.questionType === 3 ? formatCorrectAnswer(question.answer) : getCorrectAnswerText()),
            isCorrect: record.isCorrect === 1
          });

          // 恢复currentIndex
          currentIndex.value = tempCurrentIndex;
        }
      }

      // 确定当前应该显示的题目索引
      if (answeredCount > 0) {
        // 策略1: 如果有进度记录，优先使用进度记录中的索引
        if (sessionData.value.progresses && sessionData.value.progresses.length > 0) {
          const latestProgress = sessionData.value.progresses.sort((a, b) => 
            new Date(b.updateTime) - new Date(a.updateTime)
          )[0];
          
          if (latestProgress && latestProgress.currentCount > 0) {
            // 进度记录中索引从1开始，需要减1
            currentIndex.value = Math.min(latestProgress.currentCount - 1, questions.value.length - 1);
          } else {
            // 显示最后一个已回答的题目
            currentIndex.value = lastAnsweredIndex;
          }
        } else if (answeredCount < questions.value.length) {
          // 策略2: 如果不是所有题目都回答完，显示第一个未回答的题目
          let firstUnansweredIndex = -1;
          for (let i = 0; i < questions.value.length; i++) {
            const question = questions.value[i];
            const answered = records.some(r => r.questionId === question.id);
            if (!answered) {
              firstUnansweredIndex = i;
              break;
            }
          }
          
          if (firstUnansweredIndex !== -1) {
            currentIndex.value = firstUnansweredIndex;
          } else {
            // 如果没有找到未回答的题目，显示最后一个已回答的题目
            currentIndex.value = lastAnsweredIndex;
          }
        } else {
          // 策略3: 如果所有题目都已回答，显示最后一个已回答的题目
          currentIndex.value = lastAnsweredIndex;
        }
      }

      // 加载当前题目的答案状态
      if (currentIndex.value >= 0 && currentIndex.value < questions.value.length) {
        loadQuestionAnswer(currentIndex.value);
      }
    }
  } catch (error) {
    // 出错时从第一题开始
    currentIndex.value = 0;
  }
};

// 切换到指定题目时自动加载已有答案
const switchQuestion = async (index) => {
  if (index < 0 || index >= questions.value.length) return;

  // 如果当前题目已填写但未保存，先保存当前答案
  if (hasUnsavedChanges()) {
    await saveCurrentQuestionAnswer();
  }

  performSwitch(index);
};

// 保存当前题目答案
const saveCurrentQuestionAnswer = async () => {
  if (!currentQuestion.value) return;
  
  // 检查是否有有效答案
  if (!hasValidAnswer()) return;
  
  const questionType = currentQuestion.value.questionType;
  let userAnswer = '';
  let correct = false;
  
  switch (questionType) {
    case 0: // 判断题
    case 1: // 单选题
      userAnswer = selectedOption.value;
      correct = selectedOption.value === currentQuestion.value.answer;
      break;
      
    case 2: // 多选题
      userAnswer = selectedOptions.value.join(',');
      // 获取正确答案数组
      const correctAnswers = currentQuestion.value.answer.split(',');
      // 判断是否完全正确
      correct = correctAnswers.every(ans => selectedOptions.value.includes(ans)) && 
                selectedOptions.value.every(selected => correctAnswers.includes(selected));
      
      // 保存正确选项供展示
      correctOptions.value = correctAnswers;
      break;
      
    case 3: // 填空题
      userAnswer = fillBlankAnswer.value.trim();
      // 检查填空题答案，可能有多个正确答案用|分隔
      const fillBlankCorrectAnswers = currentQuestion.value.answer.split('|');
      // 新的判断逻辑：用户输入的答案可能包含多个答案（用逗号、或、空格等分隔）
      // 只要用户输入的内容包含任一正确答案，就算正确
      if (userAnswer) {
        // 将用户输入按常见分隔符分割成多个可能的答案
        const userAnswerParts = userAnswer.split(/[,，、；;或\s]+/).filter(Boolean);
        
        // 判断是否至少有一个部分匹配正确答案之一
        correct = fillBlankCorrectAnswers.some(correctAns => {
          // 直接匹配完整答案（忽略大小写）
          if (userAnswer.toLowerCase() === correctAns.toLowerCase()) {
            return true;
          }
          
          // 检查用户输入的各部分是否包含任一正确答案
          return userAnswerParts.some(part => 
            part.toLowerCase() === correctAns.toLowerCase()
          );
        });
      }
      break;
  }
  
  // 获取用户选择的答案内容用于显示
  let userAnswerContent = '';
  
  if (questionType === 0 || questionType === 1) {
    const option = currentQuestion.value.options.find(
      opt => opt.questionOption === selectedOption.value
    );
    userAnswerContent = option ? option.content : '';
  } else if (questionType === 2) {
    userAnswerContent = selectedOptions.value.map(opt => {
      const option = currentQuestion.value.options.find(o => o.questionOption === opt);
      return option ? option.content : '';
    }).filter(Boolean).join(', ');
  } else if (questionType === 3) {
    userAnswerContent = userAnswer;
  }
  
  // 记录用户的答案
  const existingAnswerIndex = answers.value.findIndex(a => a.questionId === currentQuestion.value.id);
  if (existingAnswerIndex !== -1) {
    // 更新已有答案记录
    answers.value[existingAnswerIndex] = {
      questionId: currentQuestion.value.id,
      content: currentQuestion.value.content,
      userAnswer: userAnswerContent,
      correctAnswer: questionType === 2 ? 
        getMultipleChoiceCorrectAnswerText() : 
        (questionType === 3 ? formatCorrectAnswer(currentQuestion.value.answer) : getCorrectAnswerText()),
      isCorrect: correct
    };
  } else {
    // 添加新答案记录
    answers.value.push({
      questionId: currentQuestion.value.id,
      content: currentQuestion.value.content,
      userAnswer: userAnswerContent,
      correctAnswer: questionType === 2 ? 
        getMultipleChoiceCorrectAnswerText() : 
        (questionType === 3 ? formatCorrectAnswer(currentQuestion.value.answer) : getCorrectAnswerText()),
      isCorrect: correct
    });
  }
  
  // 更新计数
  if (correct) {
    // 检查是否已经计数过
    const existingRecord = sessionData.value?.testRecords?.find(
      r => r.questionId === currentQuestion.value.id
    );
    if (!existingRecord || existingRecord.isCorrect !== 1) {
      correctCount.value++;
      // 如果已存在记录且为错误，减少错误计数
      if (existingRecord && existingRecord.isCorrect === 0) {
        incorrectCount.value--;
      }
    }
  } else {
    // 检查是否已经计数过
    const existingRecord = sessionData.value?.testRecords?.find(
      r => r.questionId === currentQuestion.value.id
    );
    if (!existingRecord || existingRecord.isCorrect !== 0) {
      incorrectCount.value++;
      // 如果已存在记录且为正确，减少正确计数
      if (existingRecord && existingRecord.isCorrect === 1) {
        correctCount.value--;
      }
    }
  }
  
  // 检查是否已经回答过这个问题
  const existingRecordIndex = sessionData.value?.testRecords?.findIndex(
    r => r.questionId === currentQuestion.value.id
  );
  
  if (existingRecordIndex !== -1 && existingRecordIndex !== undefined) {
    // 已回答过，使用更新接口
    await updateAnswer(
      sessionData.value.testRecords[existingRecordIndex].id,
      currentQuestion.value.id, 
      userAnswer, 
      correct
    );
  } else {
    // 没回答过，使用创建接口
    const recordId = await recordAnswer(currentQuestion.value.id, userAnswer, correct);
    
    // 如果成功创建记录，添加到sessionData中方便后续查询
    if (recordId && sessionData.value && !sessionData.value.testRecords) {
      sessionData.value.testRecords = [];
    }
    
    if (recordId && sessionData.value && sessionData.value.testRecords) {
      sessionData.value.testRecords.push({
        id: recordId,
        questionId: currentQuestion.value.id,
        userAnswer: userAnswer,
        isCorrect: correct ? 1 : 0
      });
    }
  }
  
  // 清除未保存的答案
  if (currentQuestion.value && currentQuestion.value.id) {
    delete unsavedAnswers.value[currentQuestion.value.id];
  }
  
  // 更新进度
  await updateProgress();
};

// 执行题目切换
const performSwitch = (index) => {
  currentIndex.value = index;
  showQuestionList.value = false;
  
  // 加载该题目已有的答案（如果有）
  loadQuestionAnswer(index);
};

// 检查是否有未保存的更改
const hasUnsavedChanges = () => {
  const question = currentQuestion.value;
  if (!question) return false;
  
  // 检查是否已经选择或填写了答案
  switch (question.questionType) {
    case 0:
    case 1:
      return selectedOption.value !== null;
    case 2:
      return selectedOptions.value.length > 0;
    case 3:
      return fillBlankAnswer.value.trim() !== '';
    default:
      return false;
  }
};

// 加载题目已有答案
const loadQuestionAnswer = (index) => {
  const question = questions.value[index];
  if (!question) return;
  
  // 重置所有答案状态
  selectedOption.value = null;
  selectedOptions.value = [];
  fillBlankAnswer.value = '';
  userSelectedAnswer.value = '';
  
  // 先检查是否有已提交的记录（复习模式下不加载之前的答案）
  if (!isReviewMode.value && sessionData.value && sessionData.value.testRecords && sessionData.value.testRecords.length > 0) {
    const record = sessionData.value.testRecords.find(r => r.questionId === question.id);
    
    if (record && record.userAnswer) {
      // 如果找到记录，根据题目类型加载答案
      switch (question.questionType) {
        case 0:
        case 1:
          // 判断题和单选题
          selectedOption.value = record.userAnswer;
          // 找到对应的选项内容
          if (question.options && question.options.length > 0) {
            const option = question.options.find(opt => opt.questionOption === record.userAnswer);
            if (option) {
              userSelectedAnswer.value = option.content;
            }
          }
          break;
        case 2:
          // 多选题
          if (record.userAnswer.includes(',')) {
            selectedOptions.value = record.userAnswer.split(',');
          } else if (record.userAnswer) {
            // 单个答案，不需要分割
            selectedOptions.value = [record.userAnswer];
          }
          break;
        case 3:
          // 填空题
          fillBlankAnswer.value = record.userAnswer || '';
          break;
      }
      return;
    }
  }
  
  // 如果没有已提交记录，检查是否有未提交的答案
  const unsavedAnswer = question.id && unsavedAnswers.value[question.id];
  if (unsavedAnswer) {
    switch (unsavedAnswer.type) {
      case 0:
      case 1:
        // 判断题和单选题
        selectedOption.value = unsavedAnswer.answer;
        userSelectedAnswer.value = unsavedAnswer.content;
        break;
      case 2:
        // 多选题
        if (Array.isArray(unsavedAnswer.answer)) {
          selectedOptions.value = [...unsavedAnswer.answer];  // 创建一个副本
        }
        break;
      case 3:
        // 填空题
        fillBlankAnswer.value = unsavedAnswer.answer || '';
        break;
    }
  }
};

// 处理题目列表相关状态
const showQuestionList = ref(false);
const closeQuestionList = () => {
  showQuestionList.value = false;
};
const openQuestionList = () => {
  showQuestionList.value = true;
};
const isQuestionAnswered = (index) => {
  const question = questions.value[index];
  if (!question || !sessionData.value || !sessionData.value.testRecords) return false;
  
  const record = sessionData.value.testRecords.find(r => r.questionId === question.id);
  return record !== undefined;
};
const getQuestionAnswerStatus = (index) => {
  const question = questions.value[index];
  if (!question || !sessionData.value || !sessionData.value.testRecords) return false;
  
  const record = sessionData.value.testRecords.find(r => r.questionId === question.id);
  return record && record.isCorrect === 1;
};

// 检查题目是否有已填写但未提交的答案
const hasFilledAnswer = (index) => {
  const question = questions.value[index];
  if (!question) return false;
  
  // 当前正在操作的题目，根据当前输入状态判断
  if (index === currentIndex.value) {
    return hasUnsavedChanges();
  }
  
  // 检查是否有已提交的答案记录
  if (isQuestionAnswered(index)) {
    return true;
  }
  
  // 检查是否有未提交的答案
  return !!unsavedAnswers.value[question.id];
};
</script>

<style>
.container {
  height: 100vh;
  background-color: #f2f7fc;
  display: flex;
  flex-direction: column;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: #e0e0e0;
  position: relative;
  margin-top: -6rpx;
}

.progress {
  height: 100%;
  background-color: #4169e2;
  transition: width 0.3s ease;
}

.question-container {
  padding: 25rpx 32rpx 73rpx 32rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question-content {
  margin: 48rpx 0 40rpx 0;
}

.question-number {
  font-size: 34rpx;
  color: #333;
  line-height: 1.4;
}

.options-container {
  margin-top: 20rpx;
}

.option-item {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.option-selected {
  border: 2rpx solid #4169e2;
  background-color: #f0f4ff;
}

.option-text {
  font-size: 32rpx;
  color: #333;
}

.fill-blank-container {
  margin-top: 30rpx;
}

.fill-blank-input {
  background-color: #fff;
  width: 100%;
  min-height: 200rpx;
  border-radius: 16rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.button-container {

  position: fixed;
  left: 30rpx;
  right: 30rpx;
  bottom: 50rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  z-index: 100;
}

.bottom-button {
  height: 95rpx;
  background-color: #40a9ff;
  color: #fff;
  text-align: center;
  border-radius: 12rpx;
  font-size: 36rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.options-title {
  margin: 0 0 20rpx;
  color: #003A7F ;
  font-size: 34rpx;
}

.menu-btn {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
}

.menu-icon {
  width: 44rpx;
  height: 44rpx;
}

.question-list-container {
  width: 100%;
  height: 50vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
}

.question-list-header {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  height: 88rpx;
}

.question-list-title {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: #999;
}

.question-list-content {
  flex: 1;
  padding: 20rpx 0;
  height: calc(50vh - 88rpx);
}

.question-list-item {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
}

.question-current {
  background-color: #f0f4ff;
  border-left: 8rpx solid #4169e2;
}

.question-answered {
  position: relative;
}

.question-answered::after {
  content: '';
  position: absolute;
  right: 30rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #52c41a;
}

.question-incorrect::after {
  background-color: #ff4d4f;
}

.question-list-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: #666;
  margin-right: 20rpx;
}

.question-current .question-list-number {
  background-color: #4169e2;
  color: #fff;
}

.question-list-type {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

.question-status {
  font-size: 32rpx;
  margin-left: 20rpx;
}

.question-correct .question-status {
  color: #52c41a;
}

.question-incorrect .question-status {
  color: #ff4d4f;
}

.menu-btn {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
}

.menu-icon-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.question-filled::after {
  content: '';
  position: absolute;
  right: 30rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #1890ff;
}

.number-filled {
  background-color: #1890ff !important;
  color: #fff !important;
}
</style>
