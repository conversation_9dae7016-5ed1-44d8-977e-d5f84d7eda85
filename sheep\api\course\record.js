import request from '@/sheep/request';

const LessonChapterStudyRecordApi = {
  // 创建课程章节学习记录
  createLessonChapterStudyRecord: (data) => {
    return request({
      url: '/course/lesson-chapter-study-record/create',
      method: 'POST',
      data
    });
  },

  // 更新课程章节学习记录
  updateLessonChapterStudyRecord: (data) => {
    return request({
      url: '/course/lesson-chapter-study-record/update',
      method: 'PUT',
      data
    });
  },

  // 根据章节ID获取学习记录
  getLessonChapterStudyRecordByChapterId: (chapterId, groupId) => {
    return request({
      url: '/course/lesson-chapter-study-record/page',
      method: 'GET',
      params: {
        chapterId,
        groupId
      }
    });
  }
};

export default LessonChapterStudyRecordApi;
