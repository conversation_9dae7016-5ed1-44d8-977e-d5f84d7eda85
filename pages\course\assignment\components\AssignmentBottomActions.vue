<template>
  <view class="bottom-actions">
    <view class="select-all" @tap="$emit('toggleSelectAll')">
      <view class="checkbox" :class="{ 'checked': isAllSelected }">
        <view class="check-icon" v-if="isAllSelected">
          <image :src="selectedIcon" mode=""></image>
        </view>
      </view>
      <text>全选</text>
    </view>
    <view class="complete-btn" @tap="$emit('complete')">{{ completeText }}</view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import sheep from '@/sheep';

const props = defineProps({
  isAllSelected: Boolean,
  completeText: {
    type: String,
    default: '完成创建'
  }
});
const selectedIcon=sheep.$url.cdn('/course/dagou.png')
</script>

<style scoped lang="scss">
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 168rpx;
  display: flex;
  justify-content: space-between;
  padding: 25rpx 30rpx 0 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 3;

  .select-all {
    display: flex;
    padding-top: 22rpx;

    text {
      font-size: 30rpx;
      color: #3D3D3D;
      margin-left: 16rpx;
    }
  }

  .complete-btn {
    width: 546rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: linear-gradient(270deg, #46ADF0,#00DEFF);
    border-radius: 40rpx;
    color: #fff;
    font-size: 30rpx;
    font-weight: 500;
  }
}

.checkbox {
  width: 35rpx;
  height: 35rpx;
  border-radius: 50%;
  border: 2rpx solid #D0D0D0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.checked {
    border-color: #3DB3F2;
    background-color: #3DB3F2;
  }

  .check-icon {
    width: 22rpx;
    height: 22rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    image {
      width: 19rpx;
      height: 16rpx;
    }
  }
}
</style>