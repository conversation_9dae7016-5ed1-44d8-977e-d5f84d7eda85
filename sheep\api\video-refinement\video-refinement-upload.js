import FileApi from '@/sheep/api/infra/file';
import request from '@/sheep/request';

const ERR_MSG_FAIL = 'chooseAndUploadFile:fail';

const VideoRefinementUploadApi = {
  //选择视频文件
  chooseVideo(opts) {
    const DEFAULT_OPTIONS = {
      camera: 'back',
      compressed: false,
      maxDuration: 60,
      sourceType: ['album', 'camera', 'messageFile'],
      extension: ['mp4','mp3'],
      sizeLimit: 100 * 1024 * 1024, // 100MB
    };

    const options = { ...DEFAULT_OPTIONS, ...opts };
    const { camera, compressed, maxDuration, sourceType, extension, sizeLimit } = options;

    return new Promise((resolve, reject) => {
      // 检查是否为微信小程序环境且包含messageFile选项
      if (typeof wx !== 'undefined' && wx.chooseMessageFile && sourceType.includes('messageFile')) {
        uni.showActionSheet({
          itemList: ['从相册选择', '拍摄视频', '微信音频', '微信视频'],
          success: (res) => {
            if (res.tapIndex === 2) {
              // 从聊天记录选择音频
              wx.chooseMessageFile({
                type: 'file',  // 使用file类型选择音频文件
                count: 1,
                extension: ['mp3'],
                success: (res) => {
                  console.log('选择的音频文件:', res.tempFiles);
                  const file = res.tempFiles[0];
                  const filePath = file.path;
                  // 提取文件扩展名并转为小写
                  const fileExt = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase();
                  
                  if (fileExt !== 'mp3') {
                    uni.showToast({
                      title: '请选择mp3格式的音频文件',
                      icon: 'none',
                      duration: 2000,
                    });
                    return;
                  }
                  
                  // 转换返回格式以匹配结果格式
                  const result = {
                    tempFilePath: filePath,
                    size: file.size,
                    duration: 0, // messageFile API 不返回时长
                    tempFile: {
                      name: file.name,
                      path: filePath,
                      type: 'audio',
                    },
                  };
                  this.handleVideoSuccess(result, sizeLimit, resolve, reject);
                },
                fail: (res) => this.handleVideoFail(res, reject),
              });
            } else if (res.tapIndex === 3) {
              // 从聊天记录选择视频
              wx.chooseMessageFile({
                type: 'video',  // 使用video类型只选择视频文件
                count: 1,
                success: (res) => {
                  const file = res.tempFiles[0];
                  const filePath = file.path;
                  // 提取文件扩展名并转为小写
                  const fileExt = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase();
                  
                  if (fileExt !== 'mp4') {
                    uni.showToast({
                      title: '请选择mp4格式的视频文件',
                      icon: 'none',
                      duration: 2000,
                    });
                    return;
                  }
                  
                  // 转换返回格式以匹配结果格式
                  const result = {
                    tempFilePath: filePath,
                    size: file.size,
                    duration: 0, // messageFile API 不返回时长
                    width: 0, // 视频宽度
                    height: 0, // 视频高度
                    tempFile: {
                      name: file.name,
                      path: filePath,
                      type: 'video',
                    },
                  };
                  this.handleVideoSuccess(result, sizeLimit, resolve, reject);
                },
                fail: (res) => this.handleVideoFail(res, reject),
              });
            } else {
              // 相册或拍摄
              uni.chooseVideo({
                camera,
                compressed,
                maxDuration,
                sourceType: [res.tapIndex === 0 ? 'album' : 'camera'],
                extension,
                success: (res) => this.handleVideoSuccess(res, sizeLimit, resolve, reject),
                fail: (res) => this.handleVideoFail(res, reject),
              });
            }
          },
          fail: (res) => this.handleVideoFail(res, reject),
        });
      } else {
        // 非微信小程序环境或不包含messageFile选项，使用默认选择器
        uni.chooseVideo({
          camera,
          compressed,
          maxDuration,
          sourceType: sourceType.filter((s) => s !== 'messageFile'),
          extension,
          success: (res) => this.handleVideoSuccess(res, sizeLimit, resolve, reject),
          fail: (res) => this.handleVideoFail(res, reject),
        });
      }
    });
  },

  handleVideoSuccess(res, sizeLimit, resolve, reject) {
    const { tempFilePath, duration, size, height, width, tempFile } = res;
    const isAudio = tempFile && tempFile.type === 'audio';

    if (this.isExceedSizeLimit(size, sizeLimit)) {
      const sizeMB = Math.floor(sizeLimit / 1024 / 1024);
      this.showSizeLimitError(sizeMB, reject);
      return;
    }

    const normalizedResult = {
      errMsg: isAudio ? 'chooseFile:ok' : 'chooseVideo:ok',
      tempFilePaths: [tempFilePath],
      tempFiles: [
        {
          name: (tempFile && tempFile.name) || '',
          path: tempFilePath,
          size,
          type: (tempFile && tempFile.type) || '',
          width,
          height,
          duration,
          fileType: isAudio ? 'audio' : 'video',
          cloudPath: '',
        },
      ],
    };

    resolve(normalizeChooseAndUploadFileRes(normalizedResult, isAudio ? 'audio' : 'video'));
  },

  handleVideoFail(res, reject) {
    console.log('handleVideoFail', res);
    if (res.errMsg.includes('cancel')) {
      uni.showToast({
        title: `用户未选择视频`,
        icon: 'none',
        duration: 2000,
      });
    } else {
      reject({
        errMsg: res.errMsg.replace('chooseVideo:fail', ERR_MSG_FAIL),
      });
    }
  },

  isExceedSizeLimit(fileSize, sizeLimit) {
    return sizeLimit && fileSize > sizeLimit;
  },

  showSizeLimitError(sizeMB, reject) {
    uni.showToast({
      title: `视频大小不能超过${sizeMB}MB`,
      icon: 'none',
      duration: 2000,
    });

    reject({
      errMsg: `chooseVideo:fail file size limit ${sizeMB}MB`,
    });
  },

  async uploadFiles(choosePromise, { onChooseFile }) {
    try {
      // 获取选择的文件
      const res = await choosePromise;
      // 处理文件选择回调
      let files = res.tempFiles || [];
      if (onChooseFile) {
        const customChooseRes = onChooseFile(res);
        if (typeof customChooseRes !== 'undefined') {
          const resolvedFiles = await Promise.resolve(customChooseRes);
          files = resolvedFiles || res.tempFiles || []; // Fallback
        }
      }

      // 为上传创建一组 Promise
      const uploadPromises = files.map(async (file) => {
        try {
          // 1.1 获取文件预签名地址
          const { data: presignedInfo } = await FileApi.getFilePresignedUrl(file.name);
          // 1.2 获取二进制文件对象
          const fileBuffer = await convertToArrayBuffer(file);
          // 返回上传的 Promise
          return new Promise((resolve, reject) => {
            //上传中
            uni.showLoading({
              title: '上传中',
              mask: true,
            });
            uni.request({
              url: presignedInfo.uploadUrl,
              method: 'PUT',
              header: {
                'Content-Type':
                  file.fileType + '/' + file.name.substring(file.name.lastIndexOf('.') + 1),
              },
              data: fileBuffer,
              success: (res) => {
                // 1.4. 记录文件信息到后端
                createFile(presignedInfo, file);
                // 1.5. 重新赋值
                file.url = presignedInfo.url;
                console.log('上传成功:', res);
                resolve(file);
              },
              fail: (err) => {
                console.error('上传失败:', err);
                reject(err);
              },
            });
          });
        } catch (error) {
          console.error('上传失败：', error);
          throw error;
        }
      });
      // 上传完成后隐藏loading
      uni.hideLoading();
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('文件处理失败：', error);
      uni.hideLoading();
      throw error;
    }
  },
};

function convertToArrayBuffer(uniFile) {
  return new Promise((resolve, reject) => {
    const fs = uni.getFileSystemManager();

    fs.readFile({
      filePath: uniFile.path, // 确保路径正确
      success: (fileRes) => {
        try {
          // 将读取的内容转换为 ArrayBuffer
          const arrayBuffer = new Uint8Array(fileRes.data).buffer;
          resolve(arrayBuffer);
        } catch (error) {
          reject(new Error(`转换为 ArrayBuffer 失败: ${error.message}`));
        }
      },
      fail: (error) => {
        reject(new Error(`读取文件失败: ${error.errMsg}`));
      },
    });
  });
}

function createFile(vo, file) {
  const fileVo = {
    configId: vo.configId,
    url: vo.url,
    path: file.name,
    name: file.name,
    type: file.fileType,
    size: file.size,
  };
  return request({
    url: '/infra/file/create', // 请求的 URL
    method: 'POST', // 请求方法
    data: fileVo, // 要发送的数据
  });
}

function normalizeChooseAndUploadFileRes(res, fileType) {
  res.tempFiles.forEach((item, index) => {
    if (!item.name) {
      item.name = item.path.substring(item.path.lastIndexOf('/') + 1);
    }
    if (fileType) {
      item.fileType = fileType;
    }
    item.cloudPath = Date.now() + '_' + index + item.name.substring(item.name.lastIndexOf('.'));
  });
  if (!res.tempFilePaths) {
    res.tempFilePaths = res.tempFiles.map((file) => file.path);
  }
  return res;
}

export default VideoRefinementUploadApi;
