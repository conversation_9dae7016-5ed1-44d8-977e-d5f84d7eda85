<template>
  <view class="progress-section">
    <!-- 单进度条模式 -->
    <template v-if="mode === 'single'">
      <view class="progress-wrapper-text">
        <view class="progress-container-single flex-grow" :style="{ marginRight: showRightText ? '28rpx' : '0' }">
          <view class="progress-bar" :style="{ backgroundColor: barBgColor }">
            <view class="progress" :style="{ width: `${percentage || 0}%`, backgroundColor: progressColor }" />
          </view>
          <view
            v-if="showElephant"
            class="elephant-container"
            :style="{ left: `${percentage || 0}%` }"
          >
            <image
              :src="sheep.$url.cdn('/index/Elephant-copy%203%403x.png')"
              class="elephant"
              mode="widthFix"
            />
          </view>
        </view>
        <!-- 右侧文本 -->
        <view class="right-text" v-if="showRightText">
          <text class="current">{{ currentCount }}</text>
          <text class="separator">/</text>
          <text class="total">{{ totalCount }}{{ rightTextSuffix }}</text>
        </view>
      </view>
    </template>

    <!-- 双进度条模式 -->
    <template v-else-if="mode === 'double'">
      <view class="progress-wrapper">
        <text v-if="showNumbers" class="left-number">{{ currentCount }}</text>
        <view class="progress-container">
          <view class="progress-bar" :style="{ backgroundColor: barBgColor }">
            <view class="current-progress" :style="{ width: `${currentProgress}%`, backgroundColor: '#e6f0fa' }"></view>
            <view class="mastered-progress"
                  :style="{ width: `${masteredProgress}%`, backgroundColor: progressColor }"></view>
          </view>
          <view
            v-if="showElephant"
            class="elephant-container"
            :style="{ left: `${masteredProgress}%` }"
          >
            <image
              :src="sheep.$url.cdn('/index/Elephant-copy%203%403x.png')"
              class="elephant"
              mode="widthFix"
            />
          </view>
        </view>
        <text v-if="showNumbers" class="right-number">{{ totalCount }}</text>
      </view>
    </template>
  </view>
</template>

<script setup>
  import sheep from '@/sheep';

  defineProps({
    // 进度条模式：'single' - 单进度条模式，'double' - 双进度条模式
    mode: {
      type: String,
      default: 'single',
    },
    // 单进度条模式下的进度百分比（0-100）
    percentage: {
      type: Number,
      default: 0,
    },
    // 双进度条模式下的当前学习进度百分比（0-100）
    currentProgress: {
      type: Number,
      default: 0,
    },
    // 双进度条模式下的已掌握进度百分比（0-100）
    masteredProgress: {
      type: Number,
      default: 0,
    },
    // 是否显示大象图标
    showElephant: {
      type: Boolean,
      default: true,
    },
    // 是否显示进度数字（仅在双进度条模式下生效）
    showNumbers: {
      type: Boolean,
      default: true,
    },
    // 当前完成的数量
    currentCount: {
      type: Number,
      default: 0,
    },
    // 总数量
    totalCount: {
      type: Number,
      default: 0,
    },
    // 是否在单进度条模式下显示右侧文本
    showRightText: {
      type: Boolean,
      default: false,
    },
    // 右侧文本后缀（例如"词"）
    rightTextSuffix: {
      type: String,
      default: '词',
    },
    // 进度条背景颜色
    barBgColor: {
      type: String,
      default: '#fff',
    },
    // 进度条填充颜色
    progressColor: {
      type: String,
      default: '#4a90e2',
    },
  });
</script>

<style lang="scss" scoped>
  .progress-section {
    width: 100%;
  }

  // 单进度条样式
  .progress-container-single {
    position: relative;
    margin: 5rpx 0;

    .progress-bar {
      height: 20rpx;
      border-radius: 10rpx;
      position: relative;
      overflow: hidden;
      /* 确保没有背景色在这里设置 */

      .progress {
        height: 100%;
        border-radius: 10rpx;
        position: relative;
        transition: width 0.3s ease;
        /* 确保没有背景色在这里设置 */
      }
    }

    .elephant-container {
      position: absolute;
      top: -1.5vw;
      transform: translateX(-40%);
      transition: left 0.3s ease;
      z-index: 10;

      .elephant {
        width: 6vw;
        height: 4.6vw;
        animation: elephantWalk 1.8s ease-in-out infinite;
        transform-origin: bottom center;
        filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
      }
    }
  }

  // 单进度条容器样式
  .progress-wrapper-text {
    display: flex;
    align-items: center;
    width: 100%;

    .flex-grow {
      flex: 1;
    }

    .right-text {
      display: flex;
      align-items: center;
      justify-content: center;

      .current {
        font-size: 36rpx;
        color: #2D2D2D;
      }

      .separator {
        margin: 0 8rpx;
      }

      .separator, .total {
        font-size: 28rpx;
        color: #777777;
      }
    }
  }

  // 双进度条样式
  .progress-wrapper {
    display: flex;
    align-items: center;
    width: 100%;

    .left-number, .right-number {
      width: 60rpx;
      background-color: #f0f0f0;
      padding: 10rpx 20rpx;
      border-radius: 50rpx;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .progress-container {
      position: relative;
      flex: 1;
      height: 4vw;
      margin: 0 10rpx;

      .progress-bar {
        height: 2.5vw;
        border-radius: 1.25vw;
        position: relative;
        overflow: hidden;
        width: 100%;
        margin-top: 0.75vw;

        .current-progress {
          position: absolute;
          height: 100%;
          border-radius: 1.25vw;
          transition: width 0.3s ease;
        }

        .mastered-progress {
          position: absolute;
          height: 100%;
          border-radius: 1.25vw;
          transition: width 0.3s ease;
        }
      }

      .elephant-container {
        position: absolute;
        top: -0.5vw;
        transform: translateX(-40%);
        transition: left 0.3s ease;
        z-index: 10;

        .elephant {
          width: 6vw;
          height: 4.6vw;
          filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
          animation: elephantWalk 1.8s ease-in-out infinite;
          transform-origin: bottom center;
        }
      }
    }
  }

  @keyframes elephantWalk {
    0%, 100% {
      transform: rotate(-8deg);
    }
    50% {
      transform: rotate(8deg);
    }
  }
</style>
