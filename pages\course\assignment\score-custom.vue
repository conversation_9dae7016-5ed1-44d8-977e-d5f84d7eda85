<template>
	<easy-navbar :title="title" />
	<AssignmentLayout v-model:searchKeyword="searchKeyword" :showFilter="showFilter" :activeFilterTab="activeFilterTab"
		:filterData="filterData" :filterOptions="filterOptions" :isAllSelected="isAllSelected" :completeText="completeText" @search="handleSearch"
		@filter="showFilterPopup" @update:showFilter="showFilter = $event" @switchTab="switchFilterTab" @selectOption="selectFilterOption"
		@reset="resetFilter" @confirm="confirmFilter" @toggleSelectAll="toggleSelectAll" @complete="completeSelection">
		<!-- 题目列表等内容插在这里 -->
		<view class="topic-list">
			<!-- 题目卡片 -->
			<view class="topic-item" v-for="(item, index) in topicList" :key="index">
				<text class="type-tag" :style="getQuestionTypeStyle(item.questionType)">
					{{ getQuestionTypeName(item.questionType) }}
				</text>
				<view class="item-header">
					<view class="left">
						<view class="checkbox" :class="{'checked': item.selected}" @tap="toggleSelect(item)">
							<view class="check-icon" v-if="item.selected">
								<image :src="selectedIcon" mode=""></image>
							</view>
						</view>
					</view>
					<view class="topic-info">
						<!-- 题目标题区域 -->
						<view class="question-title">
							<view class="title-row">
								<text class="question-index">{{ index + 1 }}. </text>
								<view class="question-content">{{ item.questionStem }}
                  <view class="score-tag">
                  <input type="number" v-model="item.score" maxlength="3" />
                  <text>分</text>
                </view>
              </view>
							</view>
						</view>

						<!-- 选项列表 - 单选题、多选题和判断题 -->
						<view class="options-list" v-if="item.questionType <= 2">
							<view class="option-item" v-for="(option, optIndex) in getQuestionOptions(item)" :key="optIndex">
								<text class="option-label">{{ option.serial }}.</text>
								<text class="option-text">{{ option.content }}</text>
							</view>
						</view>
						
						<!-- 填空题、简答题和互译题 -->
						<view class="text-input-container" v-if="item.questionType > 2">
							<view class="text-input-content" v-if="item.answer">
								{{ item.answer }}
							</view>
							<view class="text-input-placeholder" v-else>
								{{ getQuestionTypePlaceholder(item.questionType) }}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</AssignmentLayout>
</template>

<script setup lang="ts">
	import { ref, reactive, onMounted, computed, } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	import AssignmentLayout from './components/AssignmentLayout.vue';
	import AssignmentApi from '@/sheep/api/course/assignment.js';
	
	// 图标资源
	const selectedIcon = sheep.$url.cdn('/course/dagou.png')
	
	const assignmentId=ref()  //作业id
	const courseId	=ref()  //课程id
	const isEdit = ref(false)  //是否为编辑模式

	// 导航栏相关参数
	const paddingTop = ref(0);
	const paddingBottom = ref(10);

	const title = ref("选题创建");

	// 根据是否为编辑模式设置标题和按钮文字
	const completeText = ref("完成创建");
	const updateTitle = () => {
		if (isEdit.value) {
			title.value = "添加题目";
			completeText.value = "完成添加";
		} else {
			title.value = "选题创建";
			completeText.value = "完成创建";
		}
	};
	const searchKeyword = ref('');

	// 筛选弹窗相关
	const showFilter = ref(false);
	const activeFilterTab = ref('questionType'); // 当前激活的筛选标签：课程(course)、来源(source)、题型(questionType)

	const filterOptions = reactive({
		source: 'all',
		questionType: 'all'
	});
	

	// 定义类型
	interface ApiResponse {
		code: number;
		data: any;
		msg?: string;
	}

	// 筛选选项数据
	const filterData = reactive({
		source: [
			{ id: 'all', name: '全部来源' },
			{ id: 'teacher', name: '老师题库' },
			{ id: 'public', name: '公共题库' },
		],
		questionType: [
			{ id: 'all', name: '全部题型' },
			{ id: '0', name: '判断题' },
			{ id: '1', name: '单选题' },
			{ id: '2', name: '多选题' },
			{ id: '3', name: '填空题' },
			{ id: '4', name: '简答题' },
			{ id: '5', name: '互译题' },
		]
	});

	// 切换筛选标签
	const switchFilterTab = (tab) => {
		activeFilterTab.value = tab;
	};

	// 选择筛选选项
	const selectFilterOption = (tab, id) => {
		// 如果选中了非"全部"选项，则取消"全部"的选中状态
		if (id !== 'all') {
			filterOptions[tab] = id;
		} else {
			// 如果选中了"全部"，则只选择"全部"
			filterOptions[tab] = 'all';
		}
	};

	const showFilterPopup = () => {
		showFilter.value = true;
	};

	const hideFilterPopup = () => {
		showFilter.value = false;
	};

	const resetFilter = () => {
		filterOptions.source = 'all';
		filterOptions.questionType = 'all';
	};

	const confirmFilter = () => {
		// 无论选择什么选项，都重新请求数据
		getTopicData();
		hideFilterPopup();
	};

	// 题目列表数据
	const topicList = ref([]);

	// 是否全选
	const isAllSelected = computed(() => {
		return topicList.value.every(item => item.selected);
	});

	// 切换选择状态
	const toggleSelect = (item) => {
		item.selected = !item.selected;
	};

	// 切换全选状态
	const toggleSelectAll = () => {
		const newStatus = !isAllSelected.value;
		topicList.value.forEach(item => {
			item.selected = newStatus;
		});
	};

	// 完成选题创建
	const completeSelection = async () => {
		const selectedTopics = topicList.value.filter(item => item.selected);
		if (selectedTopics.length === 0) {
			sheep.$helper.toast('请至少选择一道题目');
			return;
		}

		// 检查是否所有选中的题目都设置了分数
		const hasInvalidScore = selectedTopics.some(item => !item.score || item.score <= 0);
		if (hasInvalidScore) {
			sheep.$helper.toast('请为所有选中的题目设置分数');
			return;
		}

    // 构建提交数据
    const questionItems = selectedTopics.map(item => ({
      questionId: item.id,
      score: Number(item.score) // 确保分数为数字类型
    }));

    const res = await AssignmentApi.submitTopic({
      assignmentId: assignmentId.value,
      courseId: courseId.value,
      questionItems
    }) as unknown as ApiResponse;

    if (res.code === 0 && res.data) {
      sheep.$helper.toast(isEdit.value ? '更新成功' : '创建成功');
      // 根据是否为编辑模式决定跳转页面
      if (isEdit.value) {
        // 编辑模式：触发刷新事件，然后返回上一页
        uni.$emit('refreshAssignmentData'); // 发送事件通知编辑页面刷新数据
        sheep.$router.back(); // 返回上一页
      } else {
        // 创建模式：跳转到作业列表页面
        sheep.$router.go('/pages/course/assignment/index', { courseId: courseId.value },{},{redirect: true});
      }
    }
	};

	// 返回上一页
	const goBack = () => {
		sheep.$router.back();
	};
	
	// 获取题库题目
	const getTopicData = async () => {
    const params: any = {
      pageNo: 1,
      pageSize: 50,
      sourceType: 3 // 默认为公共题库
    };

    // 根据来源筛选设置sourceType
    if (filterOptions.source !== 'all') {
      // teacher对应本人题库(2)，public对应公共题库(1)
      params.sourceType = filterOptions.source === 'teacher' ? 2 : 1;
    }

    // 如果有搜索关键词，添加到参数中
    if (searchKeyword.value) {
      params.questionStem = searchKeyword.value;
    }

    // 如果选择了具体题型，添加到参数中
    if (filterOptions.questionType !== 'all') {
      params.questionType = Number(filterOptions.questionType);
    }

    // 如果是编辑模式，先获取现有的作业题目
    let existingQuestions = [];
    if (isEdit.value && assignmentId.value) {
      const questionsRes = await AssignmentApi.getAssignmentQuestionList(assignmentId.value);
      if (questionsRes.code === 0 && questionsRes.data) {
        existingQuestions = questionsRes.data;
      }
    }

    const res = await AssignmentApi.getTopicPage(params) as unknown as ApiResponse;
    if (res.code === 0 && res.data) {
      // 创建现有题目的映射，用于快速查找
      const existingQuestionsMap = new Map();
      existingQuestions.forEach(q => {
        existingQuestionsMap.set(q.id, q);
      });

      topicList.value = res.data.list.map(item => {
        const existingQuestion = existingQuestionsMap.get(item.id);
        return {
          ...item,
          selected: !!existingQuestion, // 如果存在于现有题目中，则标记为已选中
          score: existingQuestion ? existingQuestion.score || 0 : 0, // 设置现有分数
          // 确保有答案字段，尤其是对填空题、简答题和互译题
          answer: item.answer || (item.questionType > 2 ? getDefaultAnswer(item.questionType) : '')
        };
      });
    }
	};

	// 处理搜索
	const handleSearch = () => {
		getTopicData();
	};
	
	// 获取题型名称
	const getQuestionTypeName = (type) => {
		switch (type) {
			case 0:
				return '判断题';
			case 1:
				return '单选题';
			case 2:
				return '多选题';
			case 3:
				return '填空题';
			case 4:
				return '简答题';
			case 5:
				return '互译题';
			default:
				return '单选题';
		}
	};

	// 获取题型样式
	const getQuestionTypeStyle = (type) => {
		const styleMap = {
			0: { background: '#E7E9FF', color: '#9D65DB' }, // 判断题
			1: { background: '#E3F5FF', color: '#3DB3F2' }, // 单选题
			2: { background: '#E7FFF5', color: '#2DCF7D' }, // 多选题
			3: { background: '#FFF0FD', color: '#CF2DA3' }, // 填空题
			4: { background: '#FFFCF0', color: '#EEA931' }, // 简答题
			5: { background: '#FFF5F0', color: '#EE6231' }, // 互译题
		};
		return styleMap[type] || { background: '#E3F5FF', color: '#3DB3F2' }; // 默认样式
	};
	
	// 获取题目选项（处理判断题的特殊情况）
	const getQuestionOptions = (question) => {
		// 如果是判断题且没有选项
		if (question.questionType === 0 && (!question.questionBankOptions || question.questionBankOptions.length === 0)) {
			return [
				{ serial: '对', content: '正确' },
				{ serial: '错', content: '错误' }
			];
		}
		return question.questionBankOptions || [];
	};
	
	// 获取题型占位文本
	const getQuestionTypePlaceholder = (type) => {
		switch (type) {
			case 3:
				return '[ 填空题标准答案 ]';
			case 4:
				return '[ 简答题参考答案 ]';
			case 5:
				return '[ 互译题参考答案 ]';
			default:
				return '[答案区]';
		}
	};

	// 获取默认答案文本
	const getDefaultAnswer = (type) => {
		switch (type) {
			case 3:
				return '[ 填空题标准答案 ]';
			case 4:
				return '[ 简答题参考答案 ]';
			case 5:
				return '[ 互译题参考答案 ]';
			default:
				return '';
		}
	};

	onLoad((options) => {
		getTopicData();
		if (options) {
			assignmentId.value = options.assignmentId;
			courseId.value = options.courseId;
			isEdit.value = options.isEdit === 'true';
			updateTitle();
		}
	});

</script>

<style scoped lang="scss">
	// 题目列表
	.topic-list {
		background-color: #F7FDFF;
		min-height: calc(100vh - 150rpx);
		padding: 22rpx 30rpx 200rpx;

		.topic-item {
			background-color: #FFFFFF;
			border-radius: 25rpx;
			padding: 32rpx 26rpx;
			margin-bottom: 30rpx;
			position: relative;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			overflow: hidden;

			.item-header {
				display: flex;
				position: relative;

				.left {
					display: flex;
					align-items: flex-start;
				}

				.topic-info {
					flex: 1;
					margin-left: 10rpx;
				}
			}
		}
	}

	// 复选框样式
	.checkbox {
		width: 35rpx;
		height: 35rpx;
		border-radius: 50%;
		border: 2rpx solid #D0D0D0;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;

		&.checked {
			border-color: #3DB3F2;
			background-color: #3DB3F2;
		}

		.check-icon {
			width: 22rpx;
			height: 22rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 19rpx;
				height: 16rpx;
			}
		}
	}
	
	// 题目标题样式
	.question-title {
		margin-bottom: 25rpx;
		
		.title-row {
			display: flex;
			flex-wrap: wrap;
			
			.question-index {
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				margin-right: 10rpx;
				flex-shrink: 0;
			}
			
			.score-tag {
				color: #FF6263;
				margin-right: 10rpx;
				font-size: 24rpx;
				height: 43rpx;
				display: inline-flex;
				align-items: center;
				white-space: nowrap;
				flex-shrink: 0;
				
				input {
					width: 60rpx;
					height: 40rpx;
					text-align: center;
					border: 1px solid #FFE7E7;
					background-color: #FFE7E7;
					border-radius: 8rpx;
					margin: 0 6rpx 0 0;
					color: #FF7575;
					font-size: 24rpx;
				}
			}
			
			.question-content {
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				flex: 1;
			}
		}
	}
	
	// 题型标签样式
	.type-tag {
		padding: 0 12rpx;
		font-size: 24rpx;
		min-width: 80rpx;
		height: 43rpx;
		border-radius: 0rpx 25rpx 0rpx 25rpx;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		white-space: nowrap;
		position: absolute;
		top: 0;
		right: 0;
		z-index: 1;
	}
	
	// 选项列表样式
	.options-list {
		margin-top: 20rpx;
		
		.option-item {
			margin-bottom: 12rpx;
			display: flex;
			font-size: 30rpx;
			color: #333;
     		background: #F7FDFF;
			padding: 20rpx;
			border-radius: 20rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.option-label {
				width: 50rpx;
				font-weight: 500;
				color: #3D3D3D;
			}
			
			.option-text {
				flex: 1;
			}
		}
	}
	
	// 填空题、简答题和互译题样式
	.text-input-container {
		margin-top: 20rpx;
		width: 100%;
		
		.text-input-content {
			width: 100%;
			min-height: 120rpx;
      		background: #F7FDFF;
			border-radius: 20rpx;
			padding: 20rpx;
			font-size: 28rpx;
			color: #333;
			box-sizing: border-box;
			line-height: 1.5;
			word-break: break-all;
			white-space: pre-wrap;
		}
		
		.text-input-placeholder {
			width: 100%;
			height: 120rpx;
      		background: #F7FDFF;
			border-radius: 20rpx;
			padding: 20rpx;
			font-size: 28rpx;
			color: #999;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: center;
			text-align: center;
		}
	}
</style>