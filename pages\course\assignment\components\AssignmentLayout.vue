<template>
  <view>
    <!-- 顶部搜索栏 -->
    <AssignmentSearchBar
      :modelValue="searchKeyword"
      @update:modelValue="emit('update:searchKeyword', $event)"
      @search="emit('search', $event)"
      @filter="emit('filter')"
    />

    <!-- 筛选弹窗 -->
    <AssignmentFilterPopup
      :show="showFilter"
      :activeTab="activeFilterTab"
      :filterData="filterData"
      :filterOptions="filterOptions"
      @update:show="emit('update:showFilter', $event)"
      @switchTab="emit('switchTab', $event)"
      @selectOption="(tab, id) => emit('selectOption', tab, id)"
      @reset="emit('reset')"
      @confirm="emit('confirm')"
    />

    <!-- 中间内容插槽 -->
    <slot />

    <!-- 底部操作栏 -->
    <AssignmentBottomActions
      :isAllSelected="isAllSelected"
      :completeText="completeText"
      @toggleSelectAll="emit('toggleSelectAll')"
	  @selectOption="emit('selectOption', ...arguments)"
      @complete="emit('complete')"
    />
  </view>
</template>

<script setup>
import AssignmentSearchBar from './AssignmentSearchBar.vue'
import AssignmentFilterPopup from './AssignmentFilterPopup.vue'
import AssignmentBottomActions from './AssignmentBottomActions.vue'

const props = defineProps({
  searchKeyword: String,
  showFilter: Boolean,
  activeFilterTab: String,
  filterData: Object,
  filterOptions: Object,
  isAllSelected: Boolean,
  completeText: String
})
const emit = defineEmits([
  'update:searchKeyword', 'search', 'filter',
  'update:showFilter', 'switchTab', 'selectOption', 'reset', 'confirm',
  'toggleSelectAll', 'complete'
])

	import { watch } from 'vue';
	watch(() => props.filterOptions, (val) => {
	  console.log('filterOptions changed:', val);
	}, { deep: true });
</script>