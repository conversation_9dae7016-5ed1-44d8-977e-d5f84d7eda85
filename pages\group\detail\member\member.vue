<template>
  <view class="member-container">
    <!-- 下拉刷新组件 -->
    <scroll-view scroll-y class="member-scroll">
      <!-- 空状态提示 -->
      <s-empty v-if="!members?.length" />

      <!-- 成员列表 -->
      <view v-else class="member-list">
        <swipe-action
          v-for="(member, index) in members"
          :key="index"
          :buttons="[{ text: '移除班级', backgroundColor: '#ff5252', width: 150 }]"
          @button-click="confirmRemoveMember(member)"
          :autoClose="true"
        >
          <!-- 成员信息区 -->
          <view class="member-item">
            <!-- 成员头像 -->
            <view class="member-avatar">
              <image v-if="member.avatar" :src="member.avatar" mode="aspectFill" />
              <view v-else class="default-avatar-placeholder" />
            </view>

            <!-- 成员信息 -->
            <view class="member-info">
              <text class="member-name">{{ member.nickname }}</text>
            </view>
          </view>
        </swipe-action>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import GroupApi from '@/sheep/api/group';
  import SwipeAction from '@/components/swipe-action';

  // 声明组件
  defineOptions({
    components: {
      SwipeAction
    }
  });

  // 接收班级ID参数
  const props = defineProps({
    classId: {
      type: String,
      default: '',
    },
  });

  // 成员列表
  const members = ref([]);
  // 班级信息
  const classInfo = ref({});

  // 获取班级信息
  const loadClassInfo = async () => {
    const { code, data } = await GroupApi.getGroup(props.classId);

    if (code !== 0) {
      return;
    }

    classInfo.value = data;
  };

  // 加载成员数据
  const loadMembers = async () => {
    const { code, data } = await GroupApi.getGroupUserList(props.classId);
    if (code !== 0) {
      return;
    }

    members.value = data;
  };

  // 确认移除成员
  const confirmRemoveMember = (member) => {
    uni.showModal({
      title: '确认移除',
      content: `确定要将该成员移出班级吗？`,
      success: ({ confirm }) => {
        if (confirm) {
          removeMember(member);
        }
      },
    });
  };

  // 移除成员
  const removeMember = async (member) => {
    const { code } = await GroupApi.kickGroupMember(props.classId, member.id);
    if (code !== 0) {
      return;
    }
    await loadMembers();
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadMembers();
    loadClassInfo(); // 获取班级信息
  });
</script>

<style lang="scss" scoped>
  .member-container {
    min-height: 100vh;
    background-color: #f8fcff;
    position: relative;
  }

  .member-scroll {
    height: calc(100vh - 200rpx);
  }

  .member-list {
    padding: 20rpx 30rpx;
    
    /* 为swipe-action添加样式 */
    :deep(.swipe-action-container) {
      margin-bottom: 20rpx;
      border-radius: 12rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    }
  }

  .member-item {
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    border-radius: 12rpx;
    width: 100%;
  }

  .member-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20rpx;
    background-color: #f0f0f0;

    image {
      width: 100%;
      height: 100%;
    }

    .default-avatar-placeholder {
      width: 100%;
      height: 100%;
      background-color: #e0e0e0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .member-info {
    flex: 1;
  }

  .member-name {
    font-size: 32rpx;
    color: #333;
  }
</style>
