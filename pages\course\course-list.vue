<template>
  <view class="container">
    <easy-navbar title="课程" :transparent="true" />
    <view v-if="isFromGroupAddToClass" class="add-to-class-tip">
      <text>选择要在班级中使用的课程: {{ targetClassName }}</text>
    </view>
    <view class="tab-box" :class="{ 'three-tabs': isFromGroupAddToClass }">
      <view class="tab-item" :class="{ selected: selectedTab === 0 }" @click="selectTab(0)">
        我收藏的
      </view>
      <view class="tab-item" :class="{ selected: selectedTab === 1 }" @click="selectTab(1)">
        我创建的
      </view>
      <view v-if="isFromGroupAddToClass" class="tab-item" :class="{ selected: selectedTab === 2 }" @click="selectTab(2)">
        已分享的
      </view>
    </view>
    <view class="content-area">
      <view class="course-section" v-show="selectedTab === 0">
        <view v-show="selectedTab === 0 && favoriteCourses.length === 0">
          <s-empty text="暂无收藏的课程" />
        </view>

        <view class="card-list">
          <easy-card
            v-for="course in favoriteCourses"
            :key="course.id"
            :title="course.title"
            :coverUrl="course.coverImage || sheep.$url.cdn('/course/lesson.png')"
            :nickname="course.nickname"
            :avatar="course.avatar"
            :isStore="course.isStore"
            :isSelected="isFromGroupAddToClass && selectedItems.includes(course.id)"
            mode="course"
            :courseCount="course.chapterCount || 0"
            @handleClick="() => goToCourseDetail(course)"
          />
        </view>
      </view>
      
      <view class="course-section" v-show="selectedTab === 1">
        <view v-show="selectedTab === 1 && createdCourses.length === 0">
          <s-empty text="暂无创建的课程" />
        </view>

        <view class="card-list">
          <easy-card
            v-for="course in createdCourses"
            :key="course.id"
            :title="course.title"
            :coverUrl="course.coverImage || sheep.$url.cdn('/course/lesson.png')"
            :nickname="course.nickname"
            :avatar="course.avatar"
            :isStore="course.isStore"
            :isSelected="isFromGroupAddToClass && selectedItems.includes(course.id)"
            mode="course"
            :courseCount="course.chapterCount || 0"
            @handleClick="() => goToCourseDetail(course)"
          />
        </view>
      </view>
      
      <view class="course-section" v-show="selectedTab === 2">
        <view v-show="selectedTab === 2 && sharedCourses.length === 0">
          <s-empty text="暂无已分享的课程" />
        </view>

        <view class="card-list">
          <easy-card
            v-for="course in sharedCourses"
            :key="course.id"
            :title="course.title"
            :coverUrl="course.coverImage || sheep.$url.cdn('/course/lesson.png')"
            :nickname="course.nickname"
            :avatar="course.avatar"
            :isStore="course.isStore"
            :isSelected="isFromGroupAddToClass && selectedItems.includes(course.id)"
            mode="course"
            :courseCount="course.chapterCount || 0"
            @handleClick="() => goToCourseDetail(course)"
          />
        </view>
      </view>
    </view>
    <view class="create-btn" v-show="selectedTab === 1">
      <view class="btn-inner" @click="createNewCourse">
        <text class="icon">+</text>
        <text class="text">创建课程</text>
      </view>
    </view>
    <view class="batch-add-btn" v-show="isFromGroupAddToClass">
      <view class="btn-inner" @click="batchAddToClass">
        <text class="text">保存课程选择 {{ selectedItems.length > 0 ? `(${selectedItems.length})` : '' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import LessonApi from '@/sheep/api/course/lesson';
import sheep from '@/sheep';
import EasyCard from '@/components/easy-card/easy-card.vue';

// 使用索引来表示选中的标签
const selectedTab = ref(0);
// 是否来自班级添加课程
const isFromGroupAddToClass = ref(false);
// 目标班级ID
const targetClassId = ref('');
// 目标班级名称
const targetClassName = ref('');
// 已选择的课程列表
const selectedItems = ref([]);
// 班级原有的课程ID列表
const originalClassCourseIds = ref([]);

function selectTab(index) {
  selectedTab.value = index;
}

// 创建的课程列表
const createdCourses = ref([]);
// 收藏的课程列表
const favoriteCourses = ref([]);
// 已分享的课程列表
const sharedCourses = ref([]);
// 加载状态
const loading = ref(false);
// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: -1,
});

// 获取班级中已有的课程列表
const getClassCourses = async () => {
  if (!targetClassId.value) return;
  
  const params = {
    classId: targetClassId.value,
    pageNo: 1,
    pageSize: -1 // 获取所有数据
  };
  
  const res = await LessonApi.getLessonPageByClass(params);
  if (res.code === 0 && res.data && res.data.list) {
    // 保存班级中已有的课程ID列表
    originalClassCourseIds.value = res.data.list.map(item => item.id);
    // 将已有课程ID添加到选中列表
    selectedItems.value = [...originalClassCourseIds.value];
  }
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 格式化课程数据
const formatCourseData = (data = []) => {
  return data.map(item => ({
    id: item.id,
    title: item.title || '未命名课程',
    subtitle: item.description || '暂无课程描述',
    date: formatDate(item.createTime),
    nickname: item.nickname || item.creatorNickname || '',
    avatar: item.avatar || '',
    isStore: item.isStore ? 1 : 0, // easy-card组件需要数字类型
    coverImage: item.coverImage,
    userId: item.userId, // 创建者ID
    chapterCount: item.chapterCount || 0
  }));
};

// 获取用户创建的课程列表
const getCreatedCourses = async () => {
  loading.value = true;
  try {
    const res = await LessonApi.getCreatedLessonList();
    if (res.code === 0) {
      let listData = [];
      if (res.data && res.data.records) {
        listData = res.data.records;
      } else if (Array.isArray(res.data)) {
        listData = res.data;
      }
      createdCourses.value = formatCourseData(listData);
    } else {
      sheep.$helper.toast(res.msg || '获取数据失败');
    }
  } catch (error) {
    sheep.$helper.toast('网络异常，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 获取收藏的课程列表
const getFavoriteCourses = async () => {
  loading.value = true;
  try {
    const res = await LessonApi.getFavoriteLessonPage(queryParams);
    if (res.code === 0) {
      let listData = [];
      if (res.data && res.data.list) {
        listData = res.data.list;
      } else if (res.data && res.data.records) {
        listData = res.data.records;
      } else if (Array.isArray(res.data)) {
        listData = res.data;
      }
      favoriteCourses.value = formatCourseData(listData);
    } else {
      sheep.$helper.toast(res.msg || '获取数据失败');
    }
  } catch (error) {
    sheep.$helper.toast('网络异常，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 获取已分享的课程列表（班级中的课程）
const getSharedCourses = async () => {
  if (!targetClassId.value) return;
  
  loading.value = true;
  try {
    const params = {
      classId: targetClassId.value,
      pageNo: 1,
      pageSize: -1
    };
    
    const res = await LessonApi.getLessonPageByClass(params);
    if (res.code === 0) {
      let listData = [];
      if (res.data && res.data.list) {
        listData = res.data.list;
      } else if (res.data && res.data.records) {
        listData = res.data.records;
      } else if (Array.isArray(res.data)) {
        listData = res.data;
      }
      sharedCourses.value = formatCourseData(listData);
    } else {
      sheep.$helper.toast(res.msg || '获取数据失败');
    }
  } catch (error) {
    sheep.$helper.toast('网络异常，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 跳转到课程详情页或添加到班级
const goToCourseDetail = async (course) => {
  // 如果是从班级添加课程进入，则添加到班级
  if (isFromGroupAddToClass.value) {
    // 如果已选中，则取消选中；如果未选中，则选中
    const index = selectedItems.value.indexOf(course.id);
    if (index > -1) {
      selectedItems.value.splice(index, 1);
    } else {
      selectedItems.value.push(course.id);
    }
    return;
  }

  // 获取当前登录用户ID
  const currentUserId = sheep.$store('user').userInfo?.id;
  
  // 判断是否为创建者
  const isCreator = currentUserId && course.userId && Number(currentUserId) === Number(course.userId);
  
  if (isCreator) {
    // 创建者跳转到课程详情页
    sheep.$router.go(`/pages/course/course-detail?id=${course.id}`);
  } else {
    // 非创建者跳转到访客模式的课程详情页
    sheep.$router.go(`/pages/group/detail/course/lesson-detail?id=${course.id}&isVisitor=true`);
  }
};

// 批量添加课程到班级
const batchAddToClass = async () => {
  try {
    uni.showLoading({ title: '处理中...', mask: true });
    
    // 找出需要新增的课程ID（当前选中但不在原列表中的）
    const newCourseIds = selectedItems.value.filter(id => !originalClassCourseIds.value.includes(id));

    // 找出需要删除的课程ID（原列表中有但当前未选中的）
    const deleteCourseIds = originalClassCourseIds.value.filter(id => !selectedItems.value.includes(id));
    
    let successCount = 0;
    
    // 执行添加操作
    if (newCourseIds.length > 0) {
      const addResponse = await LessonApi.batchShareClassCourses({
        courseIds: newCourseIds,
        classId: targetClassId.value
      });

      if (addResponse.code !== 0) {
        throw new Error(addResponse.msg || '添加失败');
      }
      successCount += newCourseIds.length;
    }

    // 执行删除操作
    if (deleteCourseIds.length > 0) {
      const deleteResponse = await LessonApi.batchDeleteClassCourses({
        courseIds: deleteCourseIds,
        classId: targetClassId.value
      });

      if (deleteResponse.code !== 0) {
        throw new Error(deleteResponse.msg || '删除失败');
      }
      successCount += deleteCourseIds.length;
    }

    uni.hideLoading();
    
    // 显示成功消息（即使没有实际变化也提示）
    const message = successCount > 0 ? `成功更新${successCount}个课程` : '已保存当前选择';
    sheep.$helper.toast(message);

    // 返回班级编辑页面
    setTimeout(() => {
      uni.navigateBack();
    }, 1000);
    
  } catch (error) {
    uni.hideLoading();
    sheep.$helper.toast(error.message || '操作失败，请稍后重试');
  }
};

// 创建新课程
const createNewCourse = () => {
  sheep.$router.go('/pages/course/edit');
};

// 监听标签切换
watch(selectedTab, (newVal) => {
  if (newVal === 1) {
    getCreatedCourses();
  } else if (newVal === 2) {
    getSharedCourses();
  } else {
    getFavoriteCourses();
  }
});

onShow(() => {
  // 根据当前选中的标签加载数据
  if (selectedTab.value === 0) {
    getFavoriteCourses();
  } else if (selectedTab.value === 1) {
    getCreatedCourses();
  } else {
    getSharedCourses();
  }
});

onLoad((options) => {
  if (options.selectedTab) {
    selectedTab.value = parseInt(options.selectedTab);
  }

  // 检查是否来自班级添加课程
  if (options.from === 'group' && options.action === 'addToClass') {
    isFromGroupAddToClass.value = true;
    targetClassId.value = options.classId;
    targetClassName.value = options.className || '未知班级';
    // 默认切换到"我创建的"标签
    selectedTab.value = 1;

    // 获取班级已有的课程
    getClassCourses();
  }
});
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  margin: 0 auto;
  background-color: #f8fcff;
  min-height: 100vh;
  position: relative;
  padding-bottom: 120rpx;
}

.add-to-class-tip {
  width: 688rpx;
  padding: 20rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #239eed;
  background-color: rgba(35, 158, 237, 0.1);
  border-radius: 10rpx;
  margin: 20rpx auto 0;
}

.tab-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 688rpx;
  height: 100rpx;
  background: #ffffff;
  border-radius: 50rpx;
  margin: 20rpx auto;

  .tab-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 329rpx;
    height: 80rpx;
    background: #ffffff;
    border-radius: 40rpx;
    color: #3a3939;
    font-size: 32rpx;

    &.selected {
      background: #239eed;
      color: #ffffff;
    }
  }

  &.three-tabs {
    .tab-item {
      width: 216rpx;
      font-size: 28rpx;
    }
  }
}

.content-area {
  padding: 20rpx;
}

.course-section {
  margin-bottom: 30rpx;
}

.card-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.create-btn,
.batch-add-btn {
  position: fixed;
  bottom: 48rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 686rpx;
  height: 98rpx;

  .btn-inner {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 49rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

    .icon {
      font-size: 40rpx;
      color: #239eed;
      margin-right: 12rpx;
    }

    .text {
      font-size: 32rpx;
      color: #239eed;
      font-weight: 500;
    }
  }
}

.batch-add-btn {
  .btn-inner {
    background: #239eed;

    .text {
      color: #ffffff;
    }
  }
}
</style> 