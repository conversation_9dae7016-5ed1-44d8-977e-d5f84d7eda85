<template>
  <view class="container">
    <easy-navbar title="课程社区" />

    <!-- 搜索框区域 -->
    <view class="search-box">
      <input
        v-model="title"
        class="search-input"
        placeholder="搜索课程名称"
        @input="onSearchInput"
      />
    </view>

    <view class="word-set-container">
      <scroll-view scroll-y :show-scrollbar="true" :enhanced="true" :bounces="true">
        <view class="course-list" v-if="courseState.pagination.list && courseState.pagination.list.length">
          <view
            v-for="(course, index) in courseState.pagination.list"
            :key="index"
            class="course-item"
            @click="goToCourseDetail(course.id)"
          >
            <image class="course-cover" :src="course.coverImage || ''" mode="aspectFill"></image>
            <view class="course-info">
              <text class="course-title">{{ course.title }}</text>
              <text class="course-lessons">共{{ course.chapterCount || 0 }}课</text>
            </view>
          </view>
        </view>
        <s-empty v-else text="暂无搜索结果" />

        <uni-load-more
          icon-type="auto"
          v-if="courseState.pagination.total > 0"
          :status="courseState.loadStatus"
          :content-text="{ contentdown: '上拉加载更多' }"
          @tap="loadMore"
        />
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
  import { onLoad, onReachBottom } from '@dcloudio/uni-app';
  import { ref, reactive } from 'vue';
  import sheep from '@/sheep';
  import LessonApi from '@/sheep/api/course/lesson';
  import _ from 'lodash-es';

  const courseState = reactive({
    loadStatus: 'more', // 加载状态：more-加载前，loading-加载中，noMore-没有更多了
    pagination: {
      list: [],
      total: 0,
      pageNo: 1,
      pageSize: 10,
    },
  });

  // 搜索输入防抖
  const onSearchInput = _.debounce(() => {
    courseState.pagination.pageNo = 1;
    getCourses();
  }, 500);

  const title = ref('');

  const initData = async () => {
    try {
      const { code, data } = await LessonApi.getQualityLessons({
        pageNo: 1,
        pageSize: courseState.pagination.pageSize,
      });
      console.log('获取精品课程', data);
      if (code !== 0 || !data) {
        sheep.$router.back();
        return;
      }
      courseState.pagination.list = data.list;
      courseState.pagination.total = data.total;
      courseState.loadStatus =
        courseState.pagination.list.length < courseState.pagination.total ? 'more' : 'noMore';
    } catch (err) {
      sheep.$helper.toast('获取数据失败');
      sheep.$router.back();
    }
  };

  // 跳转到课程详情
  const goToCourseDetail = (id) => {
    if (!id) {
      return;
    }
    uni.navigateTo({
      url: `/pages/group/detail/course/lesson-detail?id=${id}&isVisitor=true`,
    });
  };

  function loadMore() {
    if (courseState.loadStatus === 'noMore') {
      return;
    }
    courseState.pagination.pageNo++;
    getCourses();
  }

  const getCourses = async () => {
    courseState.loadStatus = 'loading';
    const { code, data } = await LessonApi.getQualityLessons({
      title: title.value,
      pageNo: courseState.pagination.pageNo,
      pageSize: courseState.pagination.pageSize,
    });
    if (code !== 0) {
      return;
    }
    if (courseState.pagination.pageNo === 1) {
      courseState.pagination.list = [];
    }

    // 合并列表
    courseState.pagination.list = [...courseState.pagination.list, ...data.list];
    courseState.pagination.total = data.total;
    courseState.loadStatus =
      courseState.pagination.list.length < courseState.pagination.total ? 'more' : 'noMore';
  };

  onLoad(() => {
    initData();
  });

  // 上拉加载更多
  onReachBottom(() => {
    loadMore();
  });
</script>

<style scoped lang="scss">
  .container {
    background: #f8fcff;
    min-height: 100vh;

    .search-box {
      padding: 20rpx 30rpx;
      background: #fff;

      .search-input {
        height: 80rpx;
        padding: 0 30rpx;
        background: #f5f5f5;
        border-radius: 40rpx;
        font-size: 28rpx;
      }
    }

    .word-set-container {
      padding: 30rpx;

      .word-set-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      .course-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 10rpx;

      .course-item {
        width: 48%;
        margin-bottom: 20rpx;

        .course-cover {
          width: 100%;
          height: 350rpx;
          border-radius: 12rpx;
          margin-bottom: 10rpx;
        }

        .course-info {
          .course-title {
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 8rpx;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .course-lessons {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
    }
    }
  }
</style>
