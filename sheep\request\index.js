/**
 * Shopro-request
 * @description api模块管理，loading配置，请求拦截，错误处理
 */

import Request from './luch-request/index.js';
import { apiPath, baseUrl, tenantId } from '@/sheep/config';
import $store from '@/sheep/store';
import $platform from '@/sheep/platform';
import { showAuthModal } from '@/sheep/hooks/useModal';
import AuthUtil from '@/sheep/api/member/auth';
import { getTerminal } from '@/sheep/util/const';

const options = {
  // 显示操作成功消息 默认不显示
  showSuccess: false,
  // 成功提醒 默认使用后端返回值
  successMsg: '',
  // 显示失败消息 默认显示
  showError: true,
  // 失败提醒 默认使用后端返回信息
  errorMsg: '',
  // 显示请求时loading模态框 默认显示
  showLoading: true,
  // loading提醒文字
  loadingMsg: '加载中',
  // 需要授权才能请求 默认放开
  auth: false,
  // ...
};

// Loading全局实例
let LoadingInstance = {
  target: null,
  count: 0,
};

/**
 * 关闭loading
 */
function closeLoading() {
  if (LoadingInstance.count > 0) LoadingInstance.count--;
  if (LoadingInstance.count === 0) uni.hideLoading();
}

/**
 * @description 请求基础配置 可直接使用访问自定义请求
 */
const http = new Request({
  baseURL: baseUrl + apiPath,
  timeout: 80000, // TODO 待调整
  method: 'GET',
  header: {
    Accept: 'text/json',
    'Content-Type': 'application/json;charset=UTF-8',
    platform: $platform.name,
  },
  // #ifdef APP-PLUS
  sslVerify: false,
  // #endif
  // #ifdef H5
  // 跨域请求时是否携带凭证（cookies）仅H5支持（HBuilderX 2.6.15+）
  withCredentials: false,
  // #endif
  custom: options,
});

/**
 * @description 请求拦截器
 */
http.interceptors.request.use(
  (config) => {
    // 自定义处理【auth 授权】：必须登录的接口，则跳出 AuthModal 登录弹窗
    if (config.custom.auth && !$store('user').isLogin) {
      showAuthModal();
      return Promise.reject();
    }

    // 自定义处理【loading 加载中】：如果需要显示 loading，则显示 loading
    if (config.custom.showLoading) {
      LoadingInstance.count++;
      LoadingInstance.count === 1 &&
      uni.showLoading({
        title: config.custom.loadingMsg,
        mask: true,
        fail: () => {
          uni.hideLoading();
        },
      });
    }

    // 增加 token 令牌、terminal 终端、tenant 租户的请求头
    const token = getAccessToken();
    if (token) {
      config.header['Authorization'] = token;
    }
    config.header['terminal'] = getTerminal();

    config.header['Accept'] = '*/*';
    config.header['tenant-id'] = tenantId;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

/**
 * @description 响应拦截器
 * 当流式chunk返回时，不会走响应拦截器
 * 需要为流式请求单独处理
 */

http.interceptors.response.use(
  (response) => {

    // 自定处理【loading 加载中】：如果需要显示 loading，则关闭 loading
    response.config.custom.showLoading && closeLoading();

    // 约定：如果是 /auth/ 下的 URL 地址，并且返回了 accessToken 说明是登录相关的接口，则自动设置登录令牌
    if (response.config.url.indexOf('/member/auth/') >= 0 && response.data?.data?.accessToken) {
      $store('user').setToken(response.data.data.accessToken, response.data.data.refreshToken);
    }

    // 流数据响应处理
    if (response.config.responseType === 'arraybuffer') {
      if (response.header && response.header['Content-Type']) {
        const contentType = response.header['Content-Type'].toLowerCase();
        if (contentType.includes('application/json') || contentType.includes('text/json')) {
          // 如果是JSON类型，将arraybuffer转换为文本并解析为JSON对象
          try {
            // const textDecoder = new TextDecoder('utf-8');
            // const jsonText = textDecoder.decode(response.data);
            
            // 兼容写法
            const uint8Array = new Uint8Array(response.data);
            const jsonText = decodeURIComponent(escape(String.fromCharCode(...uint8Array)));
            
            response.data = JSON.parse(jsonText);
          } catch (error) {
            console.error('解析JSON响应失败:', error);
            // 如果解析失败，返回原始数据
            return Promise.resolve(response.data);
          }
        } else {
          // 如果不是JSON类型，直接返回原始数据
          return Promise.resolve(response.data);
        }
      }
    }

    // 只有非流式请求才检查 response.data.code
    if (response.data && response.data.code !== 0) {
      // 特殊：如果 401 错误码，则跳转到登录页 or 刷新令牌
      if (response.data.code === 401) {
        console.error('登录过期，正在刷新令牌...');

        return refreshToken(response.config);
      }
      // 特殊：处理分销用户绑定失败的提示
      if ((response.data.code + '').includes('1011007')) {
        console.error(`分销用户绑定失败，原因：${response.data.msg}`);
      } else if (response.config.custom.showError) { // 错误提示
        uni.showToast({
          title: response.data.msg || '服务器开小差啦,请稍后再试~',
          icon: 'none',
          mask: true,
        });
      }
    }

    if (response.config.responseType === 'text') {
      return Promise.resolve(response.data);
    }

    // 自定义处理【showSuccess 成功提示】：如果需要显示成功提示，则显示成功提示
    if (
      response.config.custom.showSuccess &&
      response.config.custom.successMsg !== '' &&
      response.data && response.data.code === 0
    ) {
      uni.showToast({
        title: response.config.custom.successMsg,
        icon: 'none',
      });
    }

    // 返回结果：包括 code + data + msg
    return Promise.resolve(response.data);
  },
  (error) => {
    const userStore = $store('user');
    const isLogin = userStore.isLogin;
    let errorMessage = '网络请求出错';
    if (error !== undefined) {
      switch (error.statusCode) {
        case 400:
          errorMessage = '请求错误';
          break;
        case 401:
          errorMessage = isLogin ? '您的登录已过期' : '请先登录';
          // 正常情况下，后端不会返回 401 错误，所以这里不处理 handleAuthorized
          break;
        case 403:
          errorMessage = '拒绝访问';
          break;
        case 404:
          errorMessage = '请求出错';
          break;
        case 408:
          errorMessage = '请求超时';
          break;
        case 429:
          errorMessage = '请求频繁, 请稍后再访问';
          break;
        case 500:
          errorMessage = '服务器开小差啦,请稍后再试~';
          break;
        case 501:
          errorMessage = '服务未实现';
          break;
        case 502:
          errorMessage = '网络错误';
          break;
        case 503:
          errorMessage = '服务不可用';
          break;
        case 504:
          errorMessage = '网络超时';
          break;
        case 505:
          errorMessage = 'HTTP 版本不受支持';
          break;
      }
      if (error.errMsg.includes('timeout')) errorMessage = '请求超时';
      // #ifdef H5
      if (error.errMsg.includes('Network'))
        errorMessage = window.navigator.onLine ? '服务器异常' : '请检查您的网络连接';
      // #endif
    }

    if (error && error.config) {
      if (error.config.custom.showError === false) {
        uni.showToast({
          title: error.data?.msg || errorMessage,
          icon: 'none',
          mask: true,
        });
      }
      error.config.custom.showLoading && closeLoading();
    }

    return false;
  },
);

// Axios 无感知刷新令牌，参考 https://www.dashingdog.cn/article/11 与 https://segmentfault.com/a/1190000020210980 实现
// 全局的刷新令牌 Promise
let refreshTokenPromise = null;
const refreshToken = (config) => {
  // 如果当前已经是 refresh-token 的 URL 地址，并且还是 401 错误，说明是刷新令牌失败了，直接返回 Promise.reject(error)
  if (config.url.indexOf('/member/auth/refresh-token') >= 0) {
    console.error('刷新令牌失败，正在登出...');
    return Promise.reject('error');
  }

  // 如果 refreshTokenPromise 存在，说明正在刷新中，返回同一个 Promise
  if (refreshTokenPromise) {
    return refreshTokenPromise.then(() => {
      config.header.Authorization = getAccessToken();
      return request(config);
    });
  }

  // 创建新的刷新令牌 Promise
  refreshTokenPromise = new Promise(async (resolve, reject) => {
    console.error('正在刷新令牌...');
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      console.error('无刷新令牌，执行登出...');
      handleAuthorized();
      return reject('no refresh token');
    }

    try {
      const refreshTokenResult = await AuthUtil.refreshToken(refreshToken);
      if (refreshTokenResult.code !== 0) {
        return reject(new Error('刷新令牌失败'));
      }
      console.error('刷新令牌成功...');
      resolve();
    } catch (e) {
      console.error('刷新令牌接口异常，执行登出...');
      handleAuthorized();
      reject(e);
    } finally {
      // 无论成功或失败，都重置 refreshTokenPromise
      refreshTokenPromise = null;
    }
  });

  return refreshTokenPromise.then(() => {
    config.header.Authorization = getAccessToken();
    return request(config);
  });
};

/**
 * 处理 401 未登录的错误
 */
const handleAuthorized = () => {
  const userStore = $store('user');
  userStore.logout(true);
  showAuthModal();
  // 登录超时
  return Promise.reject({
    code: 401,
    msg: userStore.isLogin ? '您的登录已过期' : '请先登录',
  });
};

/** 获得访问令牌 */
export const getAccessToken = () => {
  return uni.getStorageSync('token');
};

/** 获得刷新令牌 */
export const getRefreshToken = () => {
  return uni.getStorageSync('refresh-token');
};

const request = (config) => {
  return http.middleware(config);
};

export default request;
