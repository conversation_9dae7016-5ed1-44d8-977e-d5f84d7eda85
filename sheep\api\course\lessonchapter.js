import request from '@/sheep/request';

const LessonChapterApi = {
  // 获取课程章节详情
  getLessonChapter: (id) => {
    return request({
      url: '/course/lesson-chapter/get',
      method: 'GET',
      params: { id },
    });
  },
  
  // 获取课程章节分页
  getLessonChapterPage: (params) => {
    return request({
      url: '/course/lesson-chapter/page',
      method: 'GET',
      params,
    });
  },
  
  // 获取课程章节附件列表
  getLessonChapterAttachmentList: (chapterId, groupId) => {
    return request({
      url: '/course/lesson-chapter/lesson-chatper-attachment/list-by-chapter-id',
      method: 'GET',
      params: { chapterId, groupId },
    });
  },
  
  // 根据课程ID获取章节列表
  getLessonChapterListByCourseId: (courseId) => {
    return request({
      url: '/course/lesson-chapter/list-by-course-id',
      method: 'GET',
      params: { courseId },
    });
  },
  
  // 获取顶级章节分页
  getParentLessonChapterPage: (params) => {
    return request({
      url: '/course/lesson-chapter/parent-page',
      method: 'GET',
      params,
    });
  },
  
  // 根据父章节ID获取子章节列表
  getChildLessonChapterListByParentId: (parentId, groupId) => {
    return request({
      url: '/course/lesson-chapter/list-by-parent-id',
      method: 'GET',
      params: { parentId, groupId },
    });
  },
  
  // 搜索课程章节
  searchLessonChapters: (keyword, courseId) => {
    return request({
      url: '/course/lesson-chapter/search',
      method: 'GET',
      params: { keyword, courseId },
    });
  },
};

export default LessonChapterApi;