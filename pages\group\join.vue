<template>
  <view class="join-container">
    <easy-navbar title="加入班级" backPath='/pages/my/index'/>

    <view class="content">
      <view v-if="error" class="error">
        <image class="error-icon" :src="sheep.$url.cdn('/group/error.png')"/>
        <text class="error-text">{{ errorMessage }}</text>
        <button class="back-btn" @click="sheep.$router.backOrGo('/pages/group/list')">返回</button>
      </view>

      <view v-else class="confirm">
        <view class="confirm-title">
          <text>邀请您加入班级</text>
        </view>
        <view class="confirm-content" v-if="classInfo">
          <image class="class-icon" :src="sheep.$url.cdn('/group/Faculty.png')"/>
          <view class="class-info">
            <text class="class-name">{{ classInfo.name }}</text>
            <text class="class-desc">{{ classInfo.remark || '暂无班级说明' }}</text>
          </view>
        </view>
        <view class="confirm-buttons">
          <button class="cancel-btn" @click="sheep.$router.backOrGo('/pages/group/list')">取消</button>
          <button class="join-btn" :disabled="joining" @click="joinClass">
            <text>加入班级</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import GroupApi from '@/sheep/api/group/index';
import sheep from '@/sheep';

// 页面状态
const error = ref(false);
const errorMessage = ref('');
const className = ref('');
const groupId = ref('');
const classInfo = ref(null);
const joining = ref(false); // 加入中状态

// 从URL参数中获取邀请参数
const getInviteParamsFromUrl = () => {
  // 方法1: getCurrentPages获取当前页面参数（适用于各端）
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];

  // 不同端获取参数的方式可能不同
  const options = currentPage?.$page?.options || currentPage?.options || {};

  let params = {
    groupId: '',
    timestamp: '',
    sign: ''
  };

  if (options.groupId) {
    params.groupId = options.groupId;
    params.timestamp = options.timestamp || '';
    params.sign = options.sign || '';
    return params;
  }

  // 方法2: 微信小程序场景
  // #ifdef MP-WEIXIN
  let query = currentPage?.query;
  if (query && query.groupId) {
    params.groupId = query.groupId;
    params.timestamp = query.timestamp || '';
    params.sign = query.sign || '';
    return params;
  }

  // 如果是朋友圈打开，可能参数在scene中
  if (query && query.scene) {
    // scene通常是被编码的，需要解码
    const scene = decodeURIComponent(query.scene);
    const groupIdMatch = scene.match(/groupId=([^&]+)/);
    const timestampMatch = scene.match(/timestamp=([^&]+)/);
    const signMatch = scene.match(/sign=([^&]+)/);
    
    if (groupIdMatch && groupIdMatch[1]) {
      params.groupId = groupIdMatch[1];
      params.timestamp = timestampMatch ? timestampMatch[1] : '';
      params.sign = signMatch ? signMatch[1] : '';
      return params;
    }
  }
  // #endif

  // 方法3: H5场景，从URL中解析
  // #ifdef H5
  const fullPath = window.location.href;
  const groupIdMatch = fullPath.match(/[\?&]groupId=([^&]+)/);
  const timestampMatch = fullPath.match(/[\?&]timestamp=([^&]+)/);
  const signMatch = fullPath.match(/[\?&]sign=([^&]+)/);
  
  if (groupIdMatch && groupIdMatch[1]) {
    params.groupId = decodeURIComponent(groupIdMatch[1]);
    params.timestamp = timestampMatch ? decodeURIComponent(timestampMatch[1]) : '';
    params.sign = signMatch ? decodeURIComponent(signMatch[1]) : '';
    return params;
  }
  // #endif

  return params;
};

// 兼容旧版本的函数
const getGroupIdFromParams = () => {
  const params = getInviteParamsFromUrl();
  return params.groupId;
};

// 加载班级信息
const loadClassInfo = async (id) => {
  const res = await GroupApi.getGroup(id);

  if (res.code !== 0) {
    error.value = true;
    errorMessage.value = res.msg;
    return;
  }

  classInfo.value = res.data;
  className.value = res.data.name;
  error.value = false;
};

// 加入班级
const joinClass = async () => {
  // 设置加入中状态
  joining.value = true;
  
  let res;
  // 获取邀请参数
  const inviteParams = getInviteParamsFromUrl();
  
  // 如果有签名参数，使用带签名验证的接口
  if (inviteParams.timestamp && inviteParams.sign) {
    res = await GroupApi.joinGroupByInvite(groupId.value, inviteParams.timestamp, inviteParams.sign);
  } else {
    // 兼容旧版本，使用原接口
    res = await GroupApi.joinGroup(groupId.value);
  }

  if (res.code !== 0) {
    error.value = true;
    errorMessage.value = res.msg;
    joining.value = false;
    return
  }

  sheep.$helper.toast('加入成功');
  // 将班级ID保存到本地存储
  uni.setStorageSync('last_group_id', groupId.value);
  
  // 延迟跳转到班级详情页
  setTimeout(() => {
    sheep.$router.go(`/pages/group/index?id=${groupId.value}`,{},{redirect: true});
  }, 1500);
};

// 页面加载时处理
onMounted(async () => {
  // 获取班级ID
  const id = getGroupIdFromParams();
  groupId.value = id;

  if (!id) {
    error.value = true;
    errorMessage.value = '无法获取班级信息，请检查邀请链接';
    return;
  }

  // 加载班级信息
  await loadClassInfo(id);
});
</script>

<style lang="scss" scoped>
.join-container {
  min-height: 100vh;
  background-color: #F8FCFF;
}

.content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

/* 确认加入界面 */
.confirm {
  width: 100%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

  .confirm-title {
    text-align: center;
    margin-bottom: 40rpx;

    text {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .confirm-content {
    display: flex;
    align-items: center;
    margin-bottom: 60rpx;

    .class-icon {
      width: 100rpx;
      height: 100rpx;
      margin-right: 30rpx;
    }

    .class-info {
      flex: 1;
      display: flex;
      flex-direction: column;

      .class-name {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 10rpx;
      }

      .class-desc {
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  .confirm-buttons {
    display: flex;
    justify-content: space-between;

    button {
      width: 45%;
      height: 80rpx;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30rpx;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    .join-btn {
      background-color: #2196f3;
      color: #fff;

      &:disabled {
        background-color: #a0d5f9;
        color: #fff;
      }
    }
  }
}

/* 错误状态 */
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

  .error-icon {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 30rpx;
  }

  .error-text {
    font-size: 30rpx;
    color: #ff5252;
    margin-bottom: 40rpx;
    text-align: center;
  }

  .back-btn {
    background-color: #f5f5f5;
    color: #666;
    font-size: 30rpx;
    padding: 16rpx 60rpx;
    border-radius: 40rpx;
  }
}
</style>
