<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <easy-navbar title="创建班级" />

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 班级名称 -->
      <view class="form-item">
        <input
          class="input-field"
          type="text"
          v-model="className"
          placeholder="班级名称"
          placeholder-class="placeholder"
          focus
          maxlength="20"
        />
        <view class="divider"></view>
      </view>

      <!-- 班级描述 -->
      <view class="form-item">
        <input
          class="input-field"
          type="text"
          v-model="classDescription"
          placeholder="班级描述"
          placeholder-class="placeholder"
        />
        <view class="divider"></view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn-wrapper" @click="submitForm">
      <text>确认</text>
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue';
  import GroupApi from '@/sheep/api/group/index';
  import sheep from '@/sheep';

  // 表单数据
  const className = ref('');
  const classDescription = ref('');

  // 提交表单
  const submitForm = async () => {
    // 校验班级名称
    if (!className.value.trim()) {
      uni.showToast({
        title: '班级名称不能为空',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
 
    // 构建表单数据
    const formData = {
      name: className.value,
      remark: classDescription.value,
    };

    // 调用创建班级接口
    const res = await GroupApi.createGroup(formData);

    if (res.code !== 0) {
      return;
    }

    sheep.$router.back();
  };
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vh;
    background-color: #f8fcff;
    display: flex;
    flex-direction: column;
  }

  /* 顶部导航栏 */
  .navbar {
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    background-color: #fff;
    position: relative;
    z-index: 100;
  }

  .navbar-left,
  .navbar-right {
    width: 80rpx;
    display: flex;
    align-items: center;
  }

  .back-icon {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .navbar-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #000;
    flex: 1;
    text-align: center;
  }

  .form-container {
    padding: 0;
    background-color: #f8fcff;
    margin-top: 20rpx;
  }

  .form-item {
    padding: 30rpx 40rpx;
  }

  .input-field {
    height: 90rpx;
    font-size: 34rpx;
    color: #333;
    font-weight: 400;
  }

  .placeholder {
    color: #999;
    font-size: 34rpx;
    font-weight: 300;
  }

  .divider {
    height: 1px;
    background-color: #eee;
    margin-top: 10rpx;
  }

  .permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 40rpx;
    margin-top: 20rpx;
    background-color: #f8fcff;
  }

  .permission-text {
    font-size: 30rpx;
    color: #333;
    flex: 1;
    padding-right: 20rpx;
    font-weight: 400;
  }

  .permission-switch {
    transform: scale(0.9);
  }

  .bottom-btn-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 80%;
    height: 100rpx;
    color: #ffffff;
    background-color: #239eed !important;
    padding: 3rpx 40rpx;
    border-radius: 50rpx;
    margin-left: 40rpx;
    margin-right: 40rpx;
    margin-bottom: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(57, 166, 242, 0.68);
    z-index: 99;
    font-size: 40rpx;
  }
</style>
