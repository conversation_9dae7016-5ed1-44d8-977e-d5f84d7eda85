// 题目类型枚举
export const QUESTION_TYPES = {
  JUDGE: 0,     // 判断题
  SINGLE: 1,    // 单选题
  MULTIPLE: 2,  // 多选题
  FILL: 3,      // 填空题
  SHORT: 4,     // 简答题
  TRANSLATE: 5  // 互译题
};

// 判断题选项枚举
export const JUDGE_OPTIONS = [
  {
    serial: 'A',
    content: '对'
  },
  {
    serial: 'B', 
    content: '错'
  }
];

// 获取判断题选项
export const getJudgeOptions = () => {
  return [...JUDGE_OPTIONS];
};

// 根据序号获取判断题选项
export const getJudgeOptionBySerial = (serial) => {
  return JUDGE_OPTIONS.find(option => option.serial === serial);
};

// 根据内容获取判断题选项
export const getJudgeOptionByContent = (content) => {
  return JUDGE_OPTIONS.find(option => option.content === content);
};

// 根据值获取判断题选项（兼容A/B和对/错两种格式）
export const getJudgeOptionByValue = (value) => {
  return JUDGE_OPTIONS.find(option => 
    option.serial === value || option.content === value
  );
};