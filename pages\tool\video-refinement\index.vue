<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <easy-navbar title="重点提炼" />

    <!-- 输入框区域 -->
    <view :class="{ 'input-error': urlError }" class="input-box">
		<!-- TODO可以粘贴抖音b站视频链接进行解析成.mp4mp3直链 与python程序员配合-->
      <textarea
        v-model="videoUrl"
        class="input-area"
        placeholder="请上传视频"
        placeholder-class="placeholder-style"
        @input="onUrlInput"
      ></textarea>
      <view v-if="urlError" class="error-message">{{ urlErrorMessage }}</view>
      <view class="upload-btn-container">
        <button class="upload-btn" @click="handleUpload">
          <image :src="sheep.$url.cdn('/live/upload_the_video.png')" class="upload-icon" />
        </button>
      </view>
    </view>

    <!-- 提炼按钮 -->
    <view class="btn-container">
      <button class="extract-btn" @click="extractKey">一键提炼重点</button>
    </view>

    <!-- 历史记录区域 -->
    <view class="history-section">
      <view class="history-header">
        <text class="history-title">历史记录</text>
        <view class="history-actions">
          <text class="clear-history" @tap="clearHistory">清空记录</text>
        </view>
      </view>

      <!-- 空状态显示 -->
      <s-empty
        v-if="historyList.length === 0"
        text="你还没有用过提炼功能"
      ></s-empty>

      <!-- 历史记录列表 -->
      <scroll-view 
        v-else 
        class="history-list" 
        scroll-y 
        :show-scrollbar="true" 
        :enhanced="true" 
        :bounces="true"
        :refresher-enabled="true" 
        :refresher-triggered="refreshing" 
        @refresherrefresh="onRefresh"
        @scrolltolower="onHistoryScrollToLower"
      >
        <swipe-action
          v-for="(item, index) in historyList"
          :key="index"
          :buttons="[{ text: '删除', backgroundColor: '#ff5252', width: 150 }]"
          :disabled="item.status === HISTORY_TYPE.PROCESSING"
          @button-click="deleteHistoryItem(item)"
        >
          <view
            :class="{ 'history-processing': item.status === HISTORY_TYPE.PROCESSING }"
            class="history-item"
            @tap="useHistoryItem(index)"
          >
            <view class="history-content">
              <text
                :class="{ 'text-processing': item.status === HISTORY_TYPE.PROCESSING }"
                class="history-text"
                >{{ item.text }}
              </text>
              <view class="history-info">
                <text class="history-date">{{ item.date }}</text>
                <view v-if="item.status === HISTORY_TYPE.PROCESSING" class="processing-indicator">
                  <view class="dot dot1"></view>
                  <view class="dot dot2"></view>
                  <view class="dot dot3"></view>
                </view>
                <text v-else-if="item.status === HISTORY_TYPE.FAILED" class="history-status failed"
                  >提炼失败
                </text>
              </view>
            </view>
          </view>
        </swipe-action>
        <!-- 加载更多 -->
        <uni-load-more 
          v-if="historyList.length > 0" 
          :status="loadStatus" 
          :content-text="{ 
            contentdown: '上拉加载更多' 
          }" 
          @clickLoadMore="loadMore" 
        />
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import {  onLoad, onShow, onUnload } from '@dcloudio/uni-app';
  import EasyNavbar from '@/components/easy-navbar/easy-navbar.vue';
  import SwipeAction from '@/components/swipe-action/swipe-action.vue';
  import ConversationApi from '@/sheep/api/text-optimizer/conversation';
  import SEmpty from '@/sheep/components/s-empty/s-empty.vue';
  import VideoRefinementApi from '@/sheep/api/video-refinement/video-refinement';
  import VideoRefinementUploadApi from '@/sheep/api/video-refinement/video-refinement-upload';
  import sheep from '@/sheep';
  import { useWebSocket } from '@/sheep/hooks/useWebSocket';
  import { formatDate, jsonParse } from '@/sheep/util';
  import { debounce } from 'lodash';
  import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';

  //历史记录常量
  const HISTORY_TYPE = {
    PROCESSING: 'processing',
    SUCCESS: 'success',
    FAILED: 'failed',
    STATUS_PROCESSING: '重点提炼中',
    STATUS_FAILED: '提炼失败',
  };

  // 定义视频提炼相关的WebSocket消息类型常量
  const VideoRefinementWebSocketMessageType = {
    VIDEO_REFINEMENT_STATUS_CHANGE: 'video_refinement_status_change', // 视频提炼状态变更
  };

  // 页面状态管理
  const pageState = reactive({
    loading: false,
    refreshing: false,
  });
  const videoUrl = ref('');
  const historyList = ref([]);
  const urlError = ref(false);
  const urlErrorMessage = ref('');
  const currentExtractingId = ref(null);
  const historyHasMore = ref(true); // 是否还有更多历史记录
  const loading = ref(false); // 加载状态
  const refreshing = ref(false); // 刷新状态
  const loadStatus = ref('more'); // 加载状态：more-加载前，loading-加载中，noMore-没有更多了
  
  // 历史记录请求参数
  const historyRequest = ref({
    pageNo: 1,
    pageSize: 5,
    userId: "",
    type: "",
    modelId: "",
    createTime: []
  });

  // 初始化WebSocket连接
  useWebSocket({
    // 连接成功回调
    onConnected: () => {},
    // 收到消息回调
    onMessage: async (data) => {
      const type = data.type;
      if (!type) {
        return;
      }

      // 处理视频提炼相关的消息
      if (type === VideoRefinementWebSocketMessageType.VIDEO_REFINEMENT_STATUS_CHANGE) {
        const messageContent = jsonParse(data.content);
        const messageId = messageContent.messageId;
        const status = messageContent.status;

        // 根据状态更新列表项
        if (status === HISTORY_TYPE.PROCESSING) {
          videoUrl.value = '';
          await loadHistoryData();
        } else if (status === HISTORY_TYPE.SUCCESS) {
          // 更新列表项状态
          uni.showToast({
            title: '重点提炼完成',
            icon: 'success',
            duration: 2000,
          });
          await loadHistoryData();
          // 如果是当前正在提炼的视频，重置状态
          if (messageId === currentExtractingId.value) {
            currentExtractingId.value = null;
          }
        } else if (status === HISTORY_TYPE.FAILED) {
          // 显示失败提示
          ShowModal(messageContent.dateTime, messageContent.errorMessage, 1);
        }
      }
    },
  });

  // 当链接输入发生变化时进行校验
  const onUrlInput = debounce(() => {
    // 如果输入框为空，清除错误提示
    if (!videoUrl.value) {
      urlError.value = false;
      urlErrorMessage.value = '';
      return;
    }
    // 如果输入的内容超过一定长度，才进行校验
    if (videoUrl.value.length > 5) {
      const { valid, message } = validateVideoUrl(videoUrl.value);
      urlError.value = !valid;
      urlErrorMessage.value = message;
    }
  }, 1000);

  // 页面显示时，加载历史记录
  onShow(async () => {
    await loadHistoryData();
  });

  // 页面卸载时，清理上传状态和其他状态
  onUnload(() => {
    // 重置当前提炼 ID
    currentExtractingId.value = null;
  });

  // 页面加载时读取历史记录
  onLoad(async () => {
    await loadHistoryData();
  });

  // 加载历史数据
  const loadHistoryData = async () => {
    pageState.loading = true;
    // 重置分页参数
    historyRequest.value.pageNo = 1;
    loadStatus.value = 'loading';
    
    try {
      const { code: historyCode, data } = await VideoRefinementApi.getResultPage(historyRequest.value);

      if (historyCode === 0 && Array.isArray(data?.list)) {
        // 处理历史记录列表
        const processedList = data.list.map((item) => {
          const summarize = item.summarize || '';
          const baseItem = {
            ...item,
            date: formatDate(item.createTime || new Date()),
          };

          // 状态判断和文本处理
          switch (true) {
            case summarize.startsWith(HISTORY_TYPE.STATUS_PROCESSING):
              return {
                ...baseItem,
                status: HISTORY_TYPE.PROCESSING,
                text: '重点提炼中...',
              };

            case summarize.startsWith(HISTORY_TYPE.STATUS_FAILED):
              const errorMessage =
                summarize.substring(HISTORY_TYPE.STATUS_FAILED.length).trim() || '未知错误';
              return {
                ...baseItem,
                status: HISTORY_TYPE.FAILED,
                text: `提炼失败，${errorMessage}`,
                errorMessage,
              };

            default:
              return {
                ...baseItem,
                status: HISTORY_TYPE.SUCCESS,
                text: summarize.length > 45 ? `${summarize.slice(0, 45)}...` : summarize,
              };
          }
        });

        // 先按时间降序排序
        const sortedByDate = processedList.sort(
          (a, b) => new Date(b.createTime || 0) - new Date(a.createTime || 0),
        );

        // 然后将处理中的记录移到前面
        const processingItems = sortedByDate.filter(item => item.status === HISTORY_TYPE.PROCESSING);
        const otherItems = sortedByDate.filter(item => item.status !== HISTORY_TYPE.PROCESSING);

        historyList.value = [...processingItems, ...otherItems];
        historyHasMore.value = data.total > data.list.length;
        loadStatus.value = historyHasMore.value ? 'more' : 'noMore';

        // 检查失败项目并显示提醒
        const failedItem = processedList.find((item) => item.status === HISTORY_TYPE.FAILED);
        if (failedItem) {
          const failedCount = processedList.filter(
            (item) => item.status === HISTORY_TYPE.FAILED,
          ).length;
          ShowModal(failedItem.createTime, failedItem.errorMessage, failedCount);
        }
      } else {
        historyList.value = [];
        historyHasMore.value = false;
        loadStatus.value = 'noMore';
      }
    } catch (error) {
      console.error('获取历史记录失败:', error);
      sheep.$helper.toast('获取历史记录失败');
      loadStatus.value = 'more';
    } finally {
      pageState.loading = false;
      pageState.refreshing = false;
      refreshing.value = false;
      loading.value = false;
    }
  };
  
  // 加载更多历史记录
  const loadMoreHistory = async () => {
    if (!historyHasMore.value || loading.value) return;
    
    try {
      loading.value = true;
      loadStatus.value = 'loading';
      historyRequest.value.pageNo++;
      
      const { code, data } = await VideoRefinementApi.getResultPage(historyRequest.value);
      
      if (code === 0 && data?.list?.length) {
        // 处理新加载的历史记录
        const processedList = data.list.map((item) => {
          const summarize = item.summarize || '';
          const baseItem = {
            ...item,
            date: formatDate(item.createTime || new Date()),
          };

          // 状态判断和文本处理
          switch (true) {
            case summarize.startsWith(HISTORY_TYPE.STATUS_PROCESSING):
              return {
                ...baseItem,
                status: HISTORY_TYPE.PROCESSING,
                text: '重点提炼中...',
              };

            case summarize.startsWith(HISTORY_TYPE.STATUS_FAILED):
              const errorMessage =
                summarize.substring(HISTORY_TYPE.STATUS_FAILED.length).trim() || '未知错误';
              return {
                ...baseItem,
                status: HISTORY_TYPE.FAILED,
                text: `提炼失败，${errorMessage}`,
                errorMessage,
              };

            default:
              return {
                ...baseItem,
                status: HISTORY_TYPE.SUCCESS,
                text: summarize.length > 45 ? `${summarize.slice(0, 45)}...` : summarize,
              };
          }
        });
        
        // 合并新旧数据
        historyList.value = [...historyList.value, ...processedList];
        // 判断是否还有更多数据
        historyHasMore.value = data.list.length >= historyRequest.value.pageSize;
        loadStatus.value = historyHasMore.value ? 'more' : 'noMore';
      } else {
        historyHasMore.value = false;
        loadStatus.value = 'noMore';
      }
    } catch (error) {
      console.error('加载更多历史记录失败:', error);
      sheep.$helper.toast('加载更多失败');
      loadStatus.value = 'more';
    } finally {
      loading.value = false;
    }
  };
  
  // 点击加载更多按钮触发
  const loadMore = () => {
    if (historyHasMore.value && !loading.value) {
      loadMoreHistory();
    }
  };
  
  // 滚动到底部时触发加载更多
  const onHistoryScrollToLower = () => {
    if (historyHasMore.value && !loading.value) {
      loadStatus.value = 'loading';
      loadMoreHistory();
    }
  };
  
  // 下拉刷新
  const onRefresh = async () => {
    refreshing.value = true;
    await loadHistoryData();
  };

  //提醒弹窗
  const ShowModal = (dateTime, errorMessage, num) => {
    let DateTime = formatDate(dateTime);
    let errContent = `${DateTime} \n\n有${num}个视频提炼失败`;
    if (errorMessage == null || errorMessage === '') {
      errorMessage = '未知错误';
    }
    errContent += `\n\n错误信息: ${errorMessage}`;

    uni.showModal({
      title: '提炼失败提醒',
      content: errContent,
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 用户点击确认后，自动清除失败记录
        executeFailedHistoryClear();
      },
    });
  };

  // 清空历史记录
  const clearHistory = async () => {
    uni.showModal({
      title: '提示',
      content: '确定要清空所有历史记录吗？',
      success: async (res) => {
        if (res.confirm) {
          // 调用的API清空历史记录
          await VideoRefinementApi.clearHistory();
          await loadHistoryData();
        }
      },
    });
  };

  // 执行清除失败的记录
  const executeFailedHistoryClear = async () => {
    // 调用API清除失败历史
    await VideoRefinementApi.clearFailedHistory();
    // 更新列表
    await loadHistoryData();
  };

  // 使用历史记录项
  const useHistoryItem = async (index) => {
    if (historyList.value[index]) {
      const item = historyList.value[index];

      if (item.status === HISTORY_TYPE.PROCESSING) {
        sheep.$helper.toast('正在提炼中，请稍后查看');
        return;
      }

      if (item.status === HISTORY_TYPE.FAILED) {
        sheep.$helper.toast('该视频重点提炼失败，请重新提交');

        // 可以选择将链接填入输入框，方便用户重新提交
        videoUrl.value = item.url || '';
        return;
      }
      if (item.status === HISTORY_TYPE.SUCCESS) {
        sheep.$router.go(`/pages/tool/video-refinement/result?videoUrl=${encodeURIComponent(item.url)}&messageId=${item.id}`)
      }
    }
};

  // 删除单条历史记录
  const deleteHistoryItem = async (item) => {
    try {
      // 调用删除API
      const { code } = await ConversationApi.deleteHistory(item.id);

      if (code === 0) {
        // 从本地列表中移除该项
        historyList.value = historyList.value.filter(record => record.id !== item.id);

        // 如果当前显示的列表为空，且还有更多数据，则加载更多
        if (historyList.value.length === 0 && historyHasMore.value) {
          await loadMoreHistory();
        }

        // 如果没有更多数据且当前列表为空，显示空状态
        if (historyList.value.length === 0) {
          historyHasMore.value = false;
          loadStatus.value = 'noMore';
        }
      }
    } catch (error) {
      console.error('删除历史记录失败:', error);
      sheep.$helper.toast('删除失败');
    }
  };

  // 验证视频链接
  const validateVideoUrl = (url) => {
    // 检查是否为空
    if (!url || url.trim() === '') {
      return {
        valid: false,
        message: '链接不能为空',
      };
    }

    // 文件类型验证 - 检查是否以.mp4或.mp3结尾（忽略查询参数）
    const mainUrl = url.split('?')[0].toLowerCase();
    const isVideoFile = mainUrl.endsWith('.mp4') || mainUrl.endsWith('.mp3');

    if (!isVideoFile) {
      return {
        valid: false,
        message: '不支持的视频链接或格式',
      };
    }

    return {
      valid: true,
      message: '',
    };
  };

  // 重点提炼按钮点击事件
  const extractKey = async () => {
    if (!videoUrl.value) {
      sheep.$helper.toast('请先粘贴链接')
      return;
    }
    // 验证视频链接
    const validation = validateVideoUrl(videoUrl.value);
    if (!validation.valid) {
      sheep.$helper.toast(validation.message);
      return;
    }
    // 发送异步提炼请求
    VideoRefinementApi.processVideo(videoUrl.value);
    sheep.$helper.toast('重点提炼中，结果将更新在历史记录',3000);
  };

  // 上传视频方法
  const handleUpload = async () => {
    try {
      // 检查相册权限
      await checkWxPermission('scope.writePhotosAlbum', '相册', '需要访问您的相册才能选择视频');

      // 检查相机权限
      await checkWxPermission('scope.camera', '相机', '需要访问您的相机才能拍摄视频');

      // 如果权限都已获取，继续执行上传逻辑
      let opts = {
        camera: 'back',
        compressed: false,
        maxDuration: 60,
        sourceType: ['album', 'camera', 'messageFile'],
        extension: ['mp4', 'mp3'],
        sizeLimit: 100 * 1024 * 1024,
      };

      const choosePromise = VideoRefinementUploadApi.chooseVideo(opts);
      const [{ url }] = await VideoRefinementUploadApi.uploadFiles(choosePromise, opts);
      videoUrl.value = url;
      uni.showToast({
        title: '文件上传成功',
        icon: 'success',
        duration: 2000,
      });
    } catch (error) {
      sheep.$helper.toast('文件上传失败，请重试');
    }
  };

  // 检查微信小程序权限
  const checkWxPermission = (scope, permissionName, message) => {
    return new Promise((resolve, reject) => {
      // 获取权限状态
      uni.getSetting({
        success: (res) => {
          // 如果已经授权
          if (res.authSetting[scope]) {
            resolve();
          } else {
            // 未授权，请求授权
            uni.authorize({
              scope: scope,
              success: () => {
                // 用户同意授权
                resolve();
              },
              fail: () => {
                // 用户拒绝授权，显示设置对话框
                uni.showModal({
                  title: '权限提醒',
                  content: `${message}，是否前往设置页面开启？`,
                  confirmText: '去设置',
                  cancelText: '取消',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      // 打开设置页面
                      uni.openSetting({
                        success: (settingRes) => {
                          if (settingRes.authSetting[scope]) {
                            // 用户在设置页面开启了权限
                            resolve();
                          } else {
                            // 用户在设置页面仍然拒绝权限
                            reject(`未获取${permissionName}权限`);
                          }
                        },
                        fail: () => {
                          reject('打开设置页面失败');
                        }
                      });
                    } else {
                      // 用户取消，不打开设置页面
                      reject(`用户拒绝授予${permissionName}权限`);
                    }
                  }
                });
              }
            });
          }
        },
        fail: (error) => {
          reject(`获取设置失败: ${error}`);
        }
      });
    });
  };
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    min-height: 100vh;
    background-color: #f8fcff;
  }

  .input-box {
    position: relative;
    height: 220rpx;
    margin: 40rpx 20rpx;
    background: #ffffff;
    border: 3.8rpx solid #4184ff;
    border-radius: 16rpx;
    overflow: visible;

    &.input-error {
      border-color: #ff4d4f;
    }

    .error-message {
      position: absolute;
      bottom: -40rpx;
      left: 0;
      font-size: 24rpx;
      color: #ff4d4f;
    }
  }

  .input-area {
    width: 100%;
    height: 200rpx;
    padding: 20rpx;
    box-sizing: border-box;
    font-size: 28rpx;
  }

  .upload-btn-container {
    position: absolute;
    right: 10rpx;
    bottom: 10rpx;
    z-index: 10;
  }

  .upload-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    padding: 0;
    margin: 0;
    background: transparent;
    border: none;
    outline: none;

    &::after {
      border: none;
    }

    .upload-icon {
      width: 58rpx;
      height: 58rpx;
    }
  }

  .placeholder-style {
    color: #999;
    font-size: 28rpx;
  }

  .btn-container {
    padding: 0 20rpx;
    margin-bottom: 40rpx;
  }

  .extract-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 95rpx;
    font-family: PingFang-SC-Bold, sans-serif;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    background-color: #46adf0;
    border: none;
    border-radius: 16rpx;

    &:disabled {
      color: #ffffff;
      background-color: #a8d6f5;
    }
  }

  .history-section {
    margin-top: 40rpx;
    padding: 0 30rpx;

    .history-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .history-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .history-actions {
        display: flex;
        align-items: center;
      }

      .clear-history {
        font-size: 28rpx;
        color: #999;
      }
    }

    .history-list {
      height: calc(100vh - 500rpx);
      max-height: 800rpx;
      
      .history-item {
        display: flex;
        padding: 25rpx 0;
        position: relative;

        &:not(:last-child)::after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 1rpx;
          background-color: #eaeaea;
        }

        &.history-processing {
          padding-left: 16rpx;
          border-left: 4rpx solid #46adf0;
        }
      }

      .history-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .history-text {
        margin-bottom: 20rpx;
        font-family: SourceHanSansCN-Normal, sans-serif;
        font-weight: normal;
        font-size: 25rpx;
        line-height: 1.5;
        color: #575757;

        &.text-processing {
          color: #46adf0;
        }
      }

      .history-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .history-date {
        font-size: 25rpx;
        color: #575757 ;
      }

      .history-status {
        font-size: 26rpx;
        color: #46adf0;

        &.failed {
          color: #ff4d4f;
        }
      }

      .processing-indicator {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 80rpx;
        height: 20rpx;

        .dot {
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          background-color: #46adf0;
          margin: 0 2rpx;
        }

        .dot1 {
          animation: blink 1s infinite;
        }

        .dot2 {
          animation: blink 1s infinite 0.2s;
        }

        .dot3 {
          animation: blink 1s infinite 0.4s;
        }

        @keyframes blink {
          0% {
            opacity: 0;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0;
          }
        }
      }
    }
  }
</style>
