<template>
  <view class="document-list">
    <!-- 调试信息 -->
    <view class="debug-info" v-if="false">
      <text>课程ID: {{ props.courseId }}</text>
      <text>资料数量: {{ documentList.length }}</text>
    </view>
    
    <!-- 空数据状态 -->
    <s-empty v-if="documentList.length === 0" text="暂无资料数据" />
    
    <!-- 资料列表 -->
    <view v-else class="list-content">
      <swipe-action 
        v-for="(item, index) in documentList" 
        :key="index"
        :buttons="[
          { text: '重命名', backgroundColor: '#FF8E51', color: '#FFFFFF', width: 106 },
          { text: '删除', backgroundColor: '#FF5757', color: '#FFFFFF', width: 106 }
        ]"
        @button-click="handleSwipeAction(item, $event)"
      >
        <view 
          class="document-item"
          @click="handleDocument(item)"
        >
          <!-- 资料图标 -->
          <view class="document-icon-wrapper">
            <image class="document-icon" :src="getDocumentIcon(item)" mode="aspectFill" />
          </view>
          
          <!-- 资料信息 -->
          <view class="document-info">
            <!-- 从URL中提取文件名 -->
            <text class="document-title">{{ item.title || '未命名文件' }}</text>
            <view class="document-meta">
              <text class="document-size">{{ formatFileSize(item.fileSize) }}</text>
            </view>
          </view>
        </view>
      </swipe-action>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import sheep from '@/sheep';
import LessonApi from '@/sheep/api/course/lesson';
import SwipeAction from '@/components/swipe-action/swipe-action.vue';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  },
  keyword: {
    type: String,
    default: ''
  }
});

// 文档列表数据
const documentList = ref([]);

// 获取资料列表
const getDocumentList = async () => {
  // 调用课程附件接口获取资料数据
  const res = await LessonApi.getLessonAttachmentList(props.courseId);

  if (res.code === 0 && res.data) {
    // 处理接口返回的附件数据
    const attachments = res.data || [];
    
    // 确保数据是数组
    const dataArray = Array.isArray(attachments) ? attachments : [attachments];
    
    // 处理每个附件数据，为其添加额外信息
    const processedData = await Promise.all(
      dataArray.map(async (item) => {
        // 添加文件类型描述
        if (item.attachmentType !== undefined) {
          switch (item.attachmentType) {
            case 0: item.typeDesc = '图片'; break;
            case 1: item.typeDesc = '视频'; break;
            case 2: item.typeDesc = '音频'; break;
            case 3: item.typeDesc = 'PDF'; break;
            default: item.typeDesc = '其他';
          }
        }
        
        return item;
      })
    );
    
    // 如果有搜索关键词，过滤数据
    if (props.keyword) {
      const keyword = props.keyword.toLowerCase();
      // 从URL提取的文件名也纳入搜索范围
      documentList.value = processedData.filter(item => {
        const fileName = getFileName(item).toLowerCase();
        return fileName.includes(keyword) ||
               (item.typeDesc && item.typeDesc.toLowerCase().includes(keyword));
      });
    } else {
      // 没有搜索关键词时，显示所有附件
      documentList.value = processedData;
    }
  } else {
    // 如果接口返回错误或没有数据，设置为空数组
    documentList.value = [];
  }
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '未知大小';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = Number(size);
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`;
};

// 获取文件图标
const getDocumentIcon = (document) => {
  const attachmentType = document.attachmentType;
  
  // 根据附件类型返回对应图标
  switch (attachmentType) {
    case 0: return sheep.$url.cdn('/course/blue_photo.png'); // 图片
    case 1: return sheep.$url.cdn('/course/blue_vedio.png'); // 视频
    case 2: return sheep.$url.cdn('/course/blue_audio.png'); // 音频
    case 3: return sheep.$url.cdn('/course/blue_pdf.png');   // PDF
    default: return sheep.$url.cdn('/course/blue_pdf.png');  // 默认使用PDF图标
  }
};

// 从URL或对象属性中获取文件名
const getFileName = (item) => {
  return item.title || item.fileName || item.name || '未命名文件';
};

// 处理文档点击
const handleDocument = (document) => {
  // 获取文件URL
  const fileUrl = document.attachmentUrl || document.url || document.fileUrl || document.downloadUrl || '';
  
  if (!fileUrl) {
    sheep.$helper.toast('资料链接不存在');
    return;
  }
  
  // 使用API返回的文件名或从URL中提取的文件名
  const fileName = getFileName(document);
  
  // 根据attachmentType判断文件类型
  const attachmentType = document.attachmentType;
  
  // 根据文件类型处理预览
  switch (attachmentType) {
    case 0: // 图片
      uni.previewImage({
        urls: [fileUrl]
      });
      break;
    case 1: // 视频
      // 跳转到视频播放页面，传递视频URL和标题
      sheep.$router.go('/pages/webview/video-player', {
        url: fileUrl,
        title: fileName,
        showBack: true
      });
      break;
    case 2: // 音频
      // 使用背景音频管理器播放音频
      const bgAudioManager = wx.getBackgroundAudioManager();
      // 设置音频源及元数据
      bgAudioManager.src = fileUrl;
      bgAudioManager.title = fileName || '音频播放';
      bgAudioManager.coverImgUrl = sheep.$url.cdn('/course/blue_audio.png'); // 使用音频图标作为封面
      // 播放音频
      bgAudioManager.play();
      break;
    case 3: // PDF
      sheep.$router.go(`/pages/webview/webview?url=${encodeURIComponent(fileUrl)}`);
      break;
    default:
      // 其他类型文件，询问是否下载
      showDownloadConfirm(fileName, fileUrl);
      break;
  }
};

// 显示下载确认对话框
const showDownloadConfirm = (fileName, fileUrl) => {
  uni.showModal({
    title: '下载提示',
    content: `是否下载文件"${fileName}"？`,
    confirmText: '下载',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        downloadFile(fileName, fileUrl);
      }
    }
  });
};

// 下载文件
const downloadFile = (fileName, fileUrl) => {
  uni.downloadFile({
    url: fileUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        // 下载成功，打开文件
        uni.openDocument({
          filePath: res.tempFilePath
        });
      }
    }
  });
};

// 监听关键词变化
watch(() => props.keyword, () => {
  if (props.keyword) {
    // 使用搜索API进行搜索
    searchDocuments();
  } else {
    // 没有关键词时加载全部资料
    getDocumentList();
  }
});

// 使用搜索API搜索资料
const searchDocuments = async () => {
  if (!props.keyword || !props.courseId) return;
  const res = await LessonApi.searchLessonAttachments(props.keyword, props.courseId);

  if (res.code === 0 && res.data) {
    // 处理接口返回的附件数据
    const attachments = res.data || [];

    // 确保数据是数组
    const dataArray = Array.isArray(attachments) ? attachments : [attachments];

    // 处理每个附件数据，为其添加额外信息
    const processedData = await Promise.all(
      dataArray.map(async (item) => {
        // 添加文件类型描述
        if (item.attachmentType !== undefined) {
          switch (item.attachmentType) {
            case 0: item.typeDesc = '图片'; break;
            case 1: item.typeDesc = '视频'; break;
            case 2: item.typeDesc = '音频'; break;
            case 3: item.typeDesc = 'PDF'; break;
            default: item.typeDesc = '其他';
          }
        }

        return item;
      })
    );

    documentList.value = processedData;
  } else {
    documentList.value = [];
  }
};

// 监听课程ID变化
watch(() => props.courseId, (newVal, oldVal) => {
  if (newVal) {
    getDocumentList();
  } else {
    documentList.value = [];
  }
}, { immediate: true });


// 处理左滑操作按钮
const handleSwipeAction = (item, event) => {
  const { index } = event;
  
  if (index === 0) {
    // 重命名
    handleRename(item);
  } else if (index === 1) {
    // 删除
    handleDelete(item);
  }
};

// 处理重命名
const handleRename = (item) => {
  uni.showModal({
    title: '重命名文件',
    editable: true,
    placeholderText: '请输入新的文件名',
    content: getFileName(item),
    success: async (res) => {
      if (res.confirm && res.content) {
        // 调用重命名接口
        const result = await LessonApi.updateAttachmentTitle(item.id, res.content);
        if (result.code === 0 && result.data) {
          sheep.$helper.toast('重命名成功');
          // 更新本地数据
          item.title = res.content;
        }
      }
    }
  });
};

// 处理删除
const handleDelete = (item) => {
  uni.showModal({
    title: '删除确认',
    content: `确定要删除文件"${getFileName(item)}"吗？`,
    confirmColor: '#FF5757',
    success: async (res) => {
      if (res.confirm) {
        // 调用删除接口
        const result = await LessonApi.deleteAttachment(item.id);
        if (result.code === 0 && result.data) {
          sheep.$helper.toast('删除成功');
          // 从列表中移除已删除的文件
          const index = documentList.value.findIndex(doc => doc.id === item.id);
          if (index !== -1) {
            documentList.value.splice(index, 1);
          }
        }
      }
    }
  });
};
</script>

<style scoped lang="scss">
.document-list {
  width: 100%;
}

.list-content {
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

/* 确保swipe-action包裹的文档项目宽度正确 */
:deep(.swipe-action-container) {
  width: 690rpx !important;
  margin-bottom: 20rpx;
  border-radius: 25rpx !important;
}

/* 确保滑动内容区域边角圆滑 */
:deep(.swipe-action-content) {
  border-radius: 25rpx !important;
}

/* 设置按钮高度 */
:deep(.swipe-action-btn) {
  height: 155rpx !important;
  font-size: 29rpx !important;
  font-weight: 500;
  font-family: 'PingFang SC', sans-serif;
}

/* 设置按钮文本垂直居中 */
:deep(.swipe-action-btn text) {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.document-item {
  width: 690rpx;
  height: 155rpx;
  background: #FFFFFF;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border-radius: 25rpx;
  opacity: 0.8;
  display: flex;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
}

.document-icon-wrapper {
  min-width: 96rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.document-icon {
  width: 96rpx;
  height: 96rpx;
}

.document-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.document-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-meta {
  display: flex;
  font-size: 24rpx;
  color: #999999;
  white-space: nowrap;
  overflow: hidden;
}

.document-size {
  margin-right: 20rpx;
}

.document-date {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.debug-info {
  padding: 10rpx;
  font-size: 24rpx;
  color: #999;
  display: flex;
  flex-direction: column;
}

/* 确保swipe-action按钮样式与设计一致 */
:deep(.swipe-action-buttons) {
  height: 155rpx !important;
}
</style> 