<template>
  <view class="container">
    <safe-area>
      <view class="header">
        <view class="header-title">学习完成</view>
      </view>
    </safe-area>

    <view class="result-container">
      <view class="stats-card">
        <view class="stats-row">
          <view class="stat-item">
            <text class="stat-value">{{ result.totalCards }}</text>
            <text class="stat-label">总单词</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ result.learningCards }}</text>
            <text class="stat-label">在学习</text>
          </view>
        </view>
        <view class="stats-row">
          <view class="stat-item">
            <text class="stat-value">{{ result.masteredCards }}</text>
            <text class="stat-label">已掌握</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ formatTime(result.reviewTime) }}</text>
            <text class="stat-label">学习时长</text>
          </view>
        </view>
      </view>

      <view class="progress-card">
        <view class="progress-title">掌握进度</view>
        <view class="progress-bar">
          <view
            class="progress-fill"
            :style="{ width: `${progressPercentage}%` }"
          ></view>
        </view>
        <text class="progress-text">已掌握 {{ progressPercentage }}%</text>
      </view>

      <view class="action-buttons">
        <button class="btn-primary" @click="handleRestart">重新学习</button>
        <button v-if="!isAllMastered" class="btn-primary" @click="handleReview">复习</button>
        <button class="btn-quaternary" @click="handleBackToList">返回</button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { formatTime } from '@/sheep/util/date';
import sheep from '@/sheep';

// 添加必要的声明
declare const uni: any;
declare const getCurrentPages: () => any[];

interface StudyResult {
  totalCards: number;
  learningCards: number;
  masteredCards: number;
  reviewTime: number;
  setId?: string;
}

const result = ref<StudyResult>({
  totalCards: 0,
  learningCards: 0,
  masteredCards: 0,
  reviewTime: 0,
  setId: ''
});

const progressPercentage = computed(() => {
  return Math.round((result.value.masteredCards / result.value.totalCards) * 100);
});

// 判断是否所有单词都已掌握
const isAllMastered = computed(() => {
  return result.value.masteredCards === result.value.totalCards && result.value.totalCards > 0;
});

onMounted(async () => {
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  if (page?.options?.data) {
    try {
      const data = JSON.parse(decodeURIComponent(page.options.data));
      result.value = data;
    } catch (e) {
      sheep.$helper.toast('数据加载失败');
    }
  }
});

const handleRestart = () => {
  // 跳转到学习页面，设置currentReset=true以重置学习状态
  sheep.$router.go('/pages/set/car/car', { setId: result.value.setId, reset: true },{
    redirect: true,
  });
};

const handleReview = () => {
  // 如果所有单词都已掌握，则不执行任何操作
  if (isAllMastered.value) {
    return;
  }
  
  // 跳转到复习模式，只复习未掌握的单词
  sheep.$router.go('/pages/set/car/car', {
    setId: result.value.setId,
    startMode: 'review'
  },{
    redirect: true,
  });
};

const handleBackToList = () => {
  // 返回到学习列表页面
  sheep.$router.back();
};
</script>

<style>
.container {
  height: 100%;
  background-color: #f4f3f8;
  display: flex;
  flex-direction: column;
}
.header {
  padding: 40rpx 32rpx;
}
.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}
.result-container {
  flex: 1;
  padding: 32rpx;
}
.stats-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.stats-row:last-child {
  margin-bottom: 0;
}
.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stat-label {
  font-size: 28rpx;
  color: #666;
}
.progress-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.progress-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}
.progress-bar {
  height: 16rpx;
  background: rgba(102, 255, 166, 0.2);
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.progress-fill {
  height: 100%;
  background: #4CAF50;
  transition: width 0.3s ease;
}
.progress-text {
  font-size: 28rpx;
  color: #666;
}
.action-buttons {
  padding: 32rpx 0;
}
.btn-primary,
.btn-secondary {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-bottom: 24rpx;
}
.btn-primary {
  background: #3B82F6;
  color: #fff;
}
.btn-secondary {
  background: #EFF6FF;
  color: #3B82F6;
  border: 1px solid #3B82F6;
}
.btn-secondary-disabled {
  opacity: 0.5;
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #999;
}
.btn-quaternary {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: #fff;
  color: #707070b8;
  border: 1px solid #d0d0d0;
}
</style>
