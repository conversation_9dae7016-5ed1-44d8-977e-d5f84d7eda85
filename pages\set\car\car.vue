<template>
  <view class="container">
    <safe-area>
      <view class="header">
        <view class="header-left" @click="handleBack">
          <uni-icons type="close" size="24" color="#666" />
        </view>
        <view class="header-center">{{ currentIndex + 1 }} / {{ wordCards.length }}</view>
        <view class="header-right">
        </view>
      </view>
    </safe-area>

    <view class="score-container">
      <view class="score-item wrong" :style="{
        backgroundColor: isLearningZone
          ? `rgba(255, 166, 102, ${colorOpacity})`
          : 'rgba(255, 166, 102, 0.2)'
      }">
        <text class="score-text">{{ isLearningZone ?  '+1' : learningCount }}</text>
      </view>
      <view class="score-item correct" :style="{
        backgroundColor: isMasteredZone
          ? `rgba(102, 255, 166, ${colorOpacity})`
          : 'rgba(102, 255, 166, 0.2)'
      }">
        <text class="score-text">{{ isMasteredZone ?  '+1' : masteredCount }}</text>
      </view>
    </view>

    <view class="card-wrapper">
      <view v-if="currentCard" class="card" :class="{
        'card-flipped': isFlipped,
        'card-learning': isLearningZone,
        'card-mastered': isMasteredZone,
        'transition': !moving
      }" :style="{
        left: `${left}px`,
        top: `${top}px`,
        transform: `translate(${left}px, ${top}px) rotateZ(${rotation}deg)`,
        borderColor: isLearningZone
          ? `rgba(255, 166, 102, ${colorOpacity})`
          : isMasteredZone
            ? `rgba(102, 255, 166, ${colorOpacity})`
            : 'transparent'
      }" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
        <view class="card-inner">
          <view class="card-front" v-show="!isFlipped" :class="{ 'blur-content': hasMoved && (isLearningZone || isMasteredZone) }">
            <view class="card-content">
              <view class="card-top">
                <view class="icon-wrapper" @touchstart.stop="playAudio" @touchmove.stop=""
                    @touchend.stop="" @click.stop="playAudio">
                  <image
                    class="sound-image"
                    :src="
                      playingIndex === currentIndex
                        ? sheep.$url.cdn('/video-refinement/playing.gif')
                        : sheep.$url.cdn('/video-refinement/play.png')
                    "
                    mode="aspectFit"
                  ></image>
                </view>
                
                <view class="placeholder"></view>
              </view>
              
              <view class="main-content">
                <view class="word-image-container" v-if="currentCard && currentCard.imageUrl">
                  <image 
                    :src="currentCard.imageUrl" 
                    class="word-image" 
                    mode="aspectFill"
                  ></image>
                  <!-- 添加预览按钮 -->
                  <view class="preview-button" @click.stop="previewImage">
                    <image 
                      :src="sheep.$url.cdn('/set/fullscreen.png')" 
                      class="preview-icon" 
                      mode="aspectFit"
                    ></image>
                  </view>
                </view>
                
                <view class="word-container" :class="{'no-image': !currentCard || !currentCard.imageUrl}">
                  <text class="word">{{ currentCard ? currentCard.word : '' }}</text>
                  <!-- 音标显示 -->
                  <text v-if="currentCard && currentCard.phoneticSymbol" class="phonetic-symbol">{{ currentCard.phoneticSymbol }}</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="card-back" v-show="isFlipped" :class="{ 'blur-content': hasMoved && (isLearningZone || isMasteredZone) }">
            <view class="card-content">
              <view class="card-top">
                <view class="icon-wrapper" @touchstart.stop="playAudio" @touchmove.stop=""
                    @touchend.stop="" @click.stop="playAudio">
                  <image
                    class="sound-image"
                    :src="
                      playingIndex === currentIndex
                        ? sheep.$url.cdn('/video-refinement/playing.gif')
                        : sheep.$url.cdn('/video-refinement/play.png')
                    "
                    mode="aspectFit"
                  ></image>
                </view>
                
                <view class="placeholder"></view>
              </view>
              
              <view class="main-content">
                <view class="definition-container">
                  <text class="definition">{{ currentCard ? currentCard.definition : '' }}</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 状态文字 -->
          <view class="status-text" v-if="currentCard && (isLearningZone || isMasteredZone)" :style="{
            color: isLearningZone
              ? `rgba(255, 166, 102, ${colorOpacity})`
              : isMasteredZone
                ? `rgba(102, 255, 166, ${colorOpacity})`
                : 'transparent'
          }">
            {{ isLearningZone ? '在学习' : isMasteredZone ? '已掌握' : '' }}
          </view>
        </view>
      </view>
    </view>

    <view class="bottom-nav">
      <view class="nav-button" @click="handleUndo">
        <uni-icons type="undo" size="24" color="#666" />
      </view>
      <view class="nav-button" @click="">
        <!-- <uni-icons type="forward" size="24" color="#666" /> -->
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import SetApi from '@/sheep/api/set';
import SessionApi from '@/sheep/api/set/session';
import sheep from '@/sheep';
import NlsApi from '@/sheep/api/voice/nls';
import { getTtsConfig, TtsTypeEnum } from '@/sheep/util/language-detector';

const currentSetId = ref(''); // 添加状态变量存储当前setId
const currentStartMode = ref(''); // 添加一个状态标记是否是复习模式
const currentReset = ref(false); // 添加一个状态标记是否重置

// 页面状态
const wordCards = ref([]);
const currentIndex = ref(0);
const isFlipped = ref(false);
const startTime = ref(Date.now());
const playingIndex = ref(-1); // 当前正在播放的卡片索引
const audioContext = ref(null); // 音频上下文对象
const previewingImage = ref(false); // 标记是否正在预览图片

const sessionId = ref(''); // 存储学习会话的ID
// 添加直接使用的状态变量而非计算属性
const initialMasteredCount = ref(0);
const initialLearningCount = ref(0);

// 触摸状态
const touchStartX = ref(0);
const touchStartY = ref(0);
const startLeft = ref(0);
const startTop = ref(0);
const left = ref(0);
const top = ref(0);
const rotation = ref(0);
const moving = ref(false);
const hasMoved = ref(false);
const isLearningZone = ref(false);
const isMasteredZone = ref(false);
const colorOpacity = ref(0);



const currentCard = computed(() => wordCards.value[currentIndex.value]);

const learningCount = computed(() =>
  wordCards.value.filter(card => card.status === 'learning').length
);

const masteredCount = computed(() =>
  wordCards.value.filter(card => card.status === 'mastered').length
);

// 生命周期钩子
onLoad(async (options) => {
  const { setId, startMode, reset } = options;

  if (!setId) {
    sheep.$helper.toast('参数错误');
    setTimeout(() => uni.navigateBack(), 2000);
    return;
  }

  await SetApi.setTopWordSet(setId); //置顶
  currentSetId.value = setId; // 保存setId以供后续使用
  currentStartMode.value = startMode; // 保存 review||continue||undefined
  currentReset.value = reset === 'true'; // 保存是否重置的状态

  await initData();//初始化数据
});

// 初始化加载数据
const initData = async () => {
  try {
    // 设置学习的初始时间
    startTime.value = Date.now();

    // 无论是否重置，都检查是否有进行中的会话
    const ongoingRes = await SessionApi.getOngoingSession({
      setId: currentSetId.value,
      mode: 0 // 单词卡学习模式编号为0
    });

    // 重新开始
    // 强制完成旧的会话进度，创建新会话，重置词卡状态
    if (currentReset.value) {
      
      //如果存在旧的会话，强制结束
      if (ongoingRes.code === 0 && ongoingRes.data && ongoingRes.data.id) {
        await SessionApi.completeSession(ongoingRes.data.id);
      }

      // 重新获取新会话
      const newSessionRes = await SessionApi.getOngoingSession({
        setId: currentSetId.value,
        mode: 0
      });

      if (newSessionRes.code === 0 && newSessionRes.data && newSessionRes.data.id) {
        sessionId.value = newSessionRes.data.id;
      } else {
        console.error('重置模式获取新会话失败:', newSessionRes);
        sheep.$helper.toast('服务繁忙，请稍后再试');
        setTimeout(() => uni.navigateBack(), 2000);
        return;
      }

      // 重置模式下，直接加载所有卡片并重置状态
      await loadAllCardsFromAPI(currentSetId.value);
      // 处理完成后再重置标志
      currentReset.value = false;
      
      uni.hideLoading();
      return;
    }

    // 非重新开始
    if (ongoingRes.code === 0 && ongoingRes.data && ongoingRes.data.id) {
      sessionId.value = ongoingRes.data.id;

      // 使用会话数据处理卡片
      // 更新wordCards
      await processSessionCards(currentSetId.value, ongoingRes.data);

    } else {
      // 加载所有卡片
      // 更新wordCards
      await loadAllCardsFromAPI(currentSetId.value);
    }

    uni.hideLoading();

  } catch (error) {
    console.error('初始化失败:', error);
    sheep.$helper.toast('服务繁忙，请稍后再试');
    setTimeout(() => uni.navigateBack(), 2000);
  }

}

// 保存当前卡片状态到会话
const saveCardStatusToSession = async (cardId, status, retryCount = 0) => {
  if (!sessionId.value || !cardId || !currentSetId.value) {
    sheep.$helper.toast('参数错误，无法保存状态');
    return false;
  }
  // 状态：1是学习中，2是已掌握
  const recordStatus = status === 'mastered' ? 2 : 1;

  const response = await SessionApi.createSessionCardRecord({
    setId: currentSetId.value,
    sessionId: sessionId.value,
    wordCardId: cardId,
    status: recordStatus
  });

  if (response && response.code === 0) {
    return true;
  } else {

    // 如果是会话不存在的错误，尝试重新获取会话
    if (response && (response.code === 404 || response.message?.includes('会话') || response.message?.includes('session'))) {
      const newSessionRes = await SessionApi.getOngoingSession({
        setId: currentSetId.value,
        mode: 0
      });
      if (newSessionRes.code === 0 && newSessionRes.data && newSessionRes.data.id) {
        sessionId.value = newSessionRes.data.id;
        // 重试保存
        if (retryCount < 2) {
          return await saveCardStatusToSession(cardId, status, retryCount + 1);
        }
      }
    }

    // 网络错误重试
    if (retryCount < 2 && (response?.code === 500 || !response)) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒后重试
      return await saveCardStatusToSession(cardId, status, retryCount + 1);
    }

    return false;
  }
};

// 更新卡片状态
const updateCardStatus = async (cardIndex, newStatus) => {
  if (cardIndex < 0 || cardIndex >= wordCards.value.length) {
    console.error('无效的卡片索引:', cardIndex);
    return false;
  }

  const card = wordCards.value[cardIndex];

  // 保存到会话
  const success = await saveCardStatusToSession(card.id, newStatus);

  if (success) {
    // 更新本地卡片状态
    card.status = newStatus;
    return true;
  }

  return false;
};

// 处理会话中的单词卡数据
// 更新wordCards
const processSessionCards = async (setId, sessionData) => {
  try {
    // 首先从API加载所有单词卡
    const res = await SetApi.getWordSet(setId);
    if (res.code !== 0 || !res.data) {
      console.error('无法从API加载单词卡数据');
      return false;
    }

    // 处理API返回的数据结构
    let setData = res.data;
    if (res.data.list && res.data.list.length > 0) {
      setData = res.data.list[0];
    }

    if (!setData || !setData.wordCards || !Array.isArray(setData.wordCards)) {
      console.error('API返回的数据结构无效');
      return false;
    }

    // 将API数据转换为标准格式
    const apiCards = setData.wordCards.map(item => {
      // 存储所有定义
      let definitions = [];
      if (item.definitions && Array.isArray(item.definitions)) {
        // 过滤掉空定义得到数组
        definitions = item.definitions.map(def => def.definition).filter(def => def && def.trim() !== '');
      }

      return {
        id: item.id,
        word: item.word || '',
        phoneticSymbol: item.phoneticSymbol || '',
        definition: definitions.join('\n'),
        definitions: definitions,
        imageUrl: item.imageUrl || '',
        audioUrl: item.audioUrl || '',
        status: 'undefined', // 初始状态
      };
    });

    // 没有找到具体的单词卡会话记录，使用所有卡片
    if (!sessionData || !sessionData.cardRecords || !Array.isArray(sessionData.cardRecords) || sessionData.cardRecords.length === 0) {
      wordCards.value = [...apiCards];
      initialLearningCount.value = 0;
      initialMasteredCount.value = 0;
      currentIndex.value = 0;
      return true;
    }

    // 创建卡片记录映射
    const cardRecordMap = {};
    sessionData.cardRecords.forEach(record => {
      cardRecordMap[record.wordCardId] = record;
    });

    // 用于统计的变量
    let masteredCountLocal = 0;
    let learningCountLocal = 0;

    // 先统一处理所有卡片的状态和统计数量
    apiCards.forEach(card => {
      if (cardRecordMap[card.id]) {
        if (cardRecordMap[card.id].status === 1) {
          card.status = 'learning';
          learningCountLocal++;
        } else if (cardRecordMap[card.id].status === 2) {
          card.status = 'mastered';
          masteredCountLocal++;
        }
      }
    });

    // 已经学完了，开启新会话
    if (masteredCountLocal === apiCards.length) {
      let modalRes = await new Promise((resolve) => {
        uni.showModal({
          title: '提示',
          content: '所有单词都已掌握，是否开启一轮学习？',
          success: (res) => resolve(res)
        });
      });
      if (modalRes.cancel) {
        setTimeout(() => uni.navigateBack(), 2000);
        return false;
      } else if (modalRes.confirm) {
        // 结束当前学习会话，代码使用 await
        let completeResult = await SessionApi.completeSession(sessionId.value);
        if (completeResult.code === 0) {
          // 重新调用getOngoingSession获取新会话
          const newOngoingRes = await SessionApi.getOngoingSession({
            setId: currentSetId.value,
            mode: 0
          });
          if (newOngoingRes.code === 0 && newOngoingRes.data && newOngoingRes.data.id) {
            sessionId.value = newOngoingRes.data.id;
            // 加载所有卡片并重置状态
            await loadAllCardsFromAPI(currentSetId.value);
            return true;
          } else {
            sheep.$helper.toast('服务繁忙，请稍后再试');
            setTimeout(() => uni.navigateBack(), 2000);
            return false;
          }
        } else {
          console.error('会话完成失败:', completeResult);
          sheep.$helper.toast('服务繁忙，请稍后再试');
          setTimeout(() => uni.navigateBack(), 2000);
          return false;
        }
      }
    }

    // 没有新单词可学，强制进入复习模式
    // 相等说明所有单词在当前会话都已经学习过了
    if (sessionData.cardRecords.length === apiCards.length) {
      currentStartMode.value = 'review';
    }

    // 没有掌握完
    // 根据模式处理卡片
    if (currentStartMode.value === 'review') {
      // 复习模式：只加载状态为学习中的卡片

      // 获取学习状态的卡片并重置状态
      const cardsToLearnCards = apiCards.filter(card => {
        if (card.status === 'learning') {
          card.status = 'undefined'; // 同时重置状态
          return true;
        }
        return false;
      });
      // 复习模式：没有需要复习的卡片
      if (cardsToLearnCards.length === 0) {
        sheep.$helper.toast('没有需要复习的单词');
        setTimeout(() => uni.navigateBack(), 2000);
        return false;
      }

      wordCards.value = cardsToLearnCards;//初始化完成
      currentIndex.value = 0; // 复习模式从第一张卡片开始
      // 更新初始计数值
      initialMasteredCount.value = 0;
      initialLearningCount.value = 0;

    } else {
      // 继续学习模式：过滤掉已掌握的卡片，只保留需要学习的
      const unMasteredCards = apiCards.filter(card => card.status !== 'mastered');
      
      if (unMasteredCards.length === 0) {
        // 所有卡片都已掌握，提示用户
        sheep.$helper.toast('所有单词都已掌握！');
        setTimeout(() => uni.navigateBack(), 2000);
        return false;
      }
      
      wordCards.value = unMasteredCards;

      // 确定继续学习的位置
      if (sessionData.lastCardIndex !== undefined && sessionData.lastCardIndex !== null && sessionData.lastCardIndex >= 0) {
        // 需要将原始索引映射到过滤后的数组中
        const originalCard = apiCards[sessionData.lastCardIndex];
        if (originalCard && originalCard.status !== 'mastered') {
          const newIndex = unMasteredCards.findIndex(card => card.id === originalCard.id);
          currentIndex.value = newIndex !== -1 ? newIndex : 0;
        } else {
          currentIndex.value = 0;
        }
      } else {
        // 查找第一个未学习的卡片位置
        const firstUndefinedIndex = unMasteredCards.findIndex(card => card.status === 'undefined');
        if (firstUndefinedIndex !== -1) {
          currentIndex.value = firstUndefinedIndex;
        } else {
          // 如果所有未掌握的卡片都是learning状态，从第一个开始
          currentIndex.value = 0;
        }
      }

      // 更新初始计数值 - 基于过滤后的卡片
      const filteredMasteredCount = 0; // 过滤后不包含已掌握的卡片
      const filteredLearningCount = unMasteredCards.filter(card => card.status === 'learning').length;
      initialMasteredCount.value = filteredMasteredCount;
      initialLearningCount.value = filteredLearningCount;
    }
    return true;
  } catch (error) {
    console.error('处理会话卡片时出错:', error);
    return false;//返回失败状态
  }
};



// 从API加载所有单词卡并处理状态
const loadAllCardsFromAPI = async (setId) => {
  // 初始状态都为0，只有"普通模式"拿到所有单词卡时，从单词卡学习记录更新学习中和已掌握的数量
  initialMasteredCount.value = 0;
  initialLearningCount.value = 0;

  try {
    // 从API获取学习集数据
    const res = await SetApi.getWordSet(setId);
    if (res.code !== 0 || !res.data) {
      console.error('无法从API加载单词卡数据2');
      return false;
    }

    uni.hideLoading();

    if (res.code !== 0 || !res.data) {
      console.error('API返回错误:', res);
      sheep.$helper.toast('加载单词卡失败');
      return false;
    }

    // 处理API返回的数据结构
    let setData = res.data;
    if (res.data.list && res.data.list.length > 0) {
      setData = res.data.list[0];
    }

    if (!setData || !setData.wordCards || !Array.isArray(setData.wordCards) || setData.wordCards.length === 0) {
      console.error('API返回的数据结构无效或无单词卡');
      sheep.$helper.toast('没有找到单词卡');
      return false;
    }

    // 将API返回的单词卡数据转换为本地格式
    const apiCards = setData.wordCards.map(item => {
      let definitions = [];
      if (item.definitions && Array.isArray(item.definitions)) {
        definitions = item.definitions.map(def => def.definition).filter(def => def && def.trim() !== '');
      }

      return {
        id: item.id,
        word: item.word || '',
        phoneticSymbol: item.phoneticSymbol || '',
        definition: definitions.join('\n'),
        definitions: definitions,
        imageUrl: item.imageUrl || '',
        audioUrl: item.audioUrl || '',
        status: 'undefined' //初始状态
      };
    });

    if (apiCards.length === 0) {
      console.error('没有可用的单词卡');
      sheep.$helper.toast('没有可用的单词卡');
      return false;
    }

    console.log('从API加载单词卡成功，数量:', apiCards.length);

    // 如果是复习模式
    if (currentStartMode.value === "review") {
      // 你还没有要复习的单词，是否开始普通模式新学

      uni.showModal({
        title: '提示',
        content: '没有要复习的单词，是否开始新学？',
        success: function (res) {
          if (res.confirm) {
            wordCards.value = [...apiCards]; // 使用所有单词卡
          } else if (res.cancel) {
            setTimeout(() => uni.navigateBack(), 2000);
          }
        }
      });

      const res = await new Promise((resolve) => {
        uni.showModal({
          title: '提示',
          content: '没有要复习的单词，是否新学单词？',
          success: (result) => resolve(result)
        });
      });

      if (res.cancel) {
        setTimeout(() => uni.navigateBack());
        return false;
      }

    }

    // 普通模式使用所有单词卡
    wordCards.value = [...apiCards];
    currentIndex.value = 0; // 从第一张卡片开始
    return true;

  } catch (error) {
    console.error('加载单词卡时出错:', error);
    sheep.$helper.toast('加载单词卡失败');
    return false;
  }
};

// 优化音频播放函数
const playAudio = async () => {
  if (!currentCard.value) return;

  // 如果有正在播放的音频，先停止
  if (audioContext.value) {
    audioContext.value.stop();
    // 如果点击的是当前正在播放的，则仅停止播放
    if (playingIndex.value === currentIndex.value) {
      playingIndex.value = -1;
      audioContext.value = null;
      return;
    }
  }

  try {
    // 设置当前播放索引，显示加载动画
    playingIndex.value = currentIndex.value;
    
    // 根据卡片面获取要播放的内容
    const playText = isFlipped.value ? currentCard.value.definition : currentCard.value.word;

    // 检测文本语言
    const ttsConfig = getTtsConfig(playText, TtsTypeEnum.ALIYUN);
    if (!ttsConfig) {
      sheep.$helper.toast('无法识别文本语言');
      playingIndex.value = -1;
      return;
    }

    // 准备文本转语音的参数
    const ttsParams = {
      text: playText,
      speaker: ttsConfig.speaker,
      speechRate: ttsConfig.speechRate,
      pitchRate: ttsConfig.pitchRate,
      displayCaptions: false
    };

    // 调用API获取音频数据
    const res = await NlsApi.ttsAliyun(ttsParams);

    // 如果请求失败
    if (res?.msg) {
      playingIndex.value = -1;
      return;
    }

    // 使用文件系统将音频数据保存为临时文件
    const manager = uni.getFileSystemManager();
    const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`;

    await new Promise((resolve, reject) => {
      manager.writeFile({
        filePath: tempFilePath,
        data: res,
        encoding: 'binary',
        success: resolve,
        fail: (err) => {
          console.error('写入音频文件失败:', err);
          reject(err);
        }
      });
    });

    // 创建音频上下文并播放
    const innerAudioContext = uni.createInnerAudioContext();
    audioContext.value = innerAudioContext;
    innerAudioContext.autoplay = true;
    innerAudioContext.src = tempFilePath;

    // 播放结束
    innerAudioContext.onEnded(() => {
      playingIndex.value = -1;
      audioContext.value = null;

      // 删除临时文件
      manager.unlink({
        filePath: tempFilePath
      });
    });

    innerAudioContext.onStop(() => {
      // 删除临时文件
      manager.unlink({
        filePath: tempFilePath
      });
    });

    innerAudioContext.onError((res) => {
      console.error('播放失败:', res);
      sheep.$helper.toast('播放失败');
      playingIndex.value = -1;
      audioContext.value = null;

      // 删除临时文件
      manager.unlink({
        filePath: tempFilePath
      });
    });
  } catch (error) {
    console.error('语音播放过程中出错:', error);
    sheep.$helper.toast('语音播放失败');
    playingIndex.value = -1;
    audioContext.value = null;
  }
};

// 事件处理方法
const handleBack = async () => {
  // 返回上一页
  sheep.$router.back();
};

// 预览图片
const previewImage = () => {
  if (currentCard.value && currentCard.value.imageUrl) {
    previewingImage.value = true; // 标记正在预览
    uni.previewImage({
      urls: [currentCard.value.imageUrl],
      current: currentCard.value.imageUrl,
      complete: () => {
        // 预览结束后，始终显示卡片正面
        isFlipped.value = false;
        
        // 为了防止预览结束后立即触发卡片翻转，设置一个短暂的延时
        setTimeout(() => {
          previewingImage.value = false; // 预览结束
        }, 800); // 足够的延时确保状态已经恢复
      }
    });
  }
};

const handleTouchStart = (e) => {
  const touch = e.touches[0] || e.changedTouches[0];
  touchStartX.value = touch.clientX || touch.pageX;
  touchStartY.value = touch.clientY || touch.pageY;
  startLeft.value = left.value;
  startTop.value = top.value;
  moving.value = true;
  hasMoved.value = false;
  isLearningZone.value = false;
  isMasteredZone.value = false;
};

const handleTouchMove = (e) => {
  if (!moving.value) return;

  const touch = e.touches[0] || e.changedTouches[0];
  const currentX = touch.clientX || touch.pageX;
  const currentY = touch.clientY || touch.pageY;
  const deltaX = currentX - touchStartX.value;
  const deltaY = currentY - touchStartY.value;

  if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
    hasMoved.value = true;
    e.preventDefault?.();

    const windowInfo = uni.getWindowInfo();
    const screenWidth = windowInfo.windowWidth || 375;
    left.value = startLeft.value + deltaX;
    top.value = startTop.value + deltaY;

    const slideRatio = Math.max(-1, Math.min(1, deltaX / (screenWidth / 10)));
    rotation.value = slideRatio * 10;

    const threshold = screenWidth / 10;
    const reachedThreshold = Math.abs(deltaX) > threshold;

    if (reachedThreshold) {
      colorOpacity.value = Math.abs(slideRatio) * 0.7 + 0.2;
      isLearningZone.value = deltaX < 0;
      isMasteredZone.value = deltaX > 0;
    } else {
      colorOpacity.value = 0;
      isLearningZone.value = false;
      isMasteredZone.value = false;
    }
  }
};

const handleTouchEnd = (e) => {
  if (!moving.value || !hasMoved.value) {
    if (!hasMoved.value && !previewingImage.value) {
      handleCardClick();
    }
    return;
  }

  const touch = e.changedTouches[0] || e.touches[0];
  const endX = touch.clientX || touch.pageX;
  const deltaX = endX - touchStartX.value;
  const windowInfo = uni.getWindowInfo();
  const screenWidth = windowInfo.windowWidth || 375;
  const threshold = screenWidth / 10;
  const shouldTrigger = Math.abs(deltaX) > threshold;

  if (shouldTrigger) {
    // 确保 currentCard 存在且 currentIndex 在有效范围内
    if (currentCard.value && currentIndex.value >= 0 && currentIndex.value < wordCards.value.length) {
      // 设置卡片状态
      const newStatus = deltaX < 0 ? 'learning' : 'mastered';

      // 使用更新函数更新卡片状态
      updateCardStatus(currentIndex.value, newStatus).then(success => {
        if (success) {
          if (currentIndex.value < wordCards.value.length - 1) {
            moveToNextCard();
          } else {
            checkCompletion();
          }
        } else {
          // 状态更新失败，显示错误并重置卡片位置
          uni.showModal({
            title: '保存失败',
            content: '网络连接异常，状态保存失败。请检查网络后重试。',
            showCancel: true,
            cancelText: '取消',
            confirmText: '重试',
            success: (res) => {
              if (res.confirm) {
                // 重试保存
                uni.showLoading({
                  title: '重试中...',
                  mask: false
                });
                updateCardStatus(currentIndex.value, newStatus).then(retrySuccess => {
                  uni.hideLoading();
                  if (retrySuccess) {
                    if (currentIndex.value < wordCards.value.length - 1) {
                      moveToNextCard();
                    } else {
                      checkCompletion();
                    }
                  } else {
                    sheep.$helper.toast('保存仍然失败，请稍后再试');
                    resetCardPosition();
                  }
                });
              } else {
                resetCardPosition();
              }
            }
          });
        }
      });
    } else {
      // 重置卡片位置
      resetCardPosition();
    }
  } else {
    resetCardPosition();
  }
};

// 添加重置卡片位置的函数
const resetCardPosition = () => {
  left.value = 0;
  top.value = 0;
  rotation.value = 0;
  moving.value = false;
  isLearningZone.value = false;
  isMasteredZone.value = false;
  colorOpacity.value = 0;
};

const handleCardClick = () => {
  // 如果正在预览图片或刚刚结束预览，则不触发翻转
  if (previewingImage.value) {
    return;
  }
  isFlipped.value = !isFlipped.value;
};

const moveToNextCard = async () => {
  // 重置所有状态
  moving.value = false;
  left.value = 0;
  top.value = 0;
  startLeft.value = 0;
  startTop.value = 0;
  rotation.value = 0;
  colorOpacity.value = 0;
  isLearningZone.value = false;
  isMasteredZone.value = false;

  if (currentIndex.value < wordCards.value.length - 1) {
    currentIndex.value++;
    isFlipped.value = false;

  } else {
    checkCompletion();
  }
};

const handleUndo = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    const card = wordCards.value[currentIndex.value];
    card.status = 'undefined';
    isFlipped.value = false;
  }
};

// 检查学习完成状态
const checkCompletion = async () => {
  try {
    const endTime = Date.now();
    const reviewTime = Math.floor((endTime - startTime.value) / 1000);

    // 计算状态统计 - 使用本地状态而非服务器状态
    const totalCards = wordCards.value.length;
    const masteredCount = wordCards.value.filter(card => card.status === 'mastered').length;
    const learningCount = wordCards.value.filter(card => card.status === 'learning').length;
    const undefinedCount = totalCards - masteredCount - learningCount;

    // 保存学习记录
    new Date(startTime.value).toISOString();
    new Date(endTime).toISOString();

    // 处理会话状态 - 只有当所有卡片都已掌握时才结束会话
    if (sessionId.value) {
      const allMastered = masteredCount === totalCards;
      
      if (allMastered) {
        await SessionApi.completeSession(sessionId.value);
      }
    }

    // 准备结果数据，包含所有必要信息供结果页面判断
    const result = {
      totalCards: totalCards,
      learningCards: learningCount,
      masteredCards: masteredCount,
      undefinedCards: undefinedCount,
      reviewTime,
      completedAt: new Date(endTime).toISOString(),
      setId: currentSetId.value,
      sessionId: sessionId.value
    };

    const resultData = encodeURIComponent(JSON.stringify(result));
    // 直接前往结果页面，由结果页面判断完成状态
    sheep.$router.go(`/pages/set/car/result?data=${resultData}`,{},{redirect: true});

    return true;
  } catch (error) {
    console.error('保存学习进度时出错:', error);
    sheep.$helper.toast('保存学习进度失败');
    return false;
  }
};
</script>

<style lang="scss">
page {
  height: 100%;
}

.container {
  min-height: 100%;
  background-color: #f4f3f8;
  display: flex;
  flex-direction: column;
  padding: 0 32rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 0;
}

.header-center {
  font-size: 16px;
  color: #666;
}

.score-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.score-item {
  padding: 12rpx 40rpx;
  border-radius: 100rpx;
}

.wrong {
  background-color: rgba(255, 166, 102, 0.2);
}

.correct {
  background-color: rgba(102, 255, 166, 0.2);
}

.score-text {
  font-size: 16px;
  color: #666;
}

.card-wrapper {
  position: relative;
  background-color: #fff;
  border-radius: 32rpx;
  width: 90%;
  height: 800rpx;
  margin: 32rpx auto;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  flex: 1;
}

.card {
  background-color: transparent;
  border-radius: 32rpx;
  border-width: 2px;
  border-style: solid;
  border-color: transparent;
  width: 100%;
  height: 100%;
  position: absolute;
  perspective: 2000px;
  -webkit-perspective: 2000px;
  will-change: transform;
  transform-origin: center center;
  touch-action: none;
  user-select: none;
  cursor: grab;
  left: 0;
  top: 0;

  &.transition {
    transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1);
  }

  &:active {
    cursor: grabbing;
  }
}

.card-inner {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  -webkit-transform-style: preserve-3d;
}

.card-flipped .card-inner {
  transform: rotateY(180deg);
}

.card-learning {
  border-color: rgba(255, 166, 102, 0.2);
}

.card-mastered {
  border-color: rgba(102, 255, 166, 0.2);
}

.card-front,
.card-back {
  width: 86%;
  height: 92.5%;
  position: absolute;
  top: 0;
  left: 0;
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
  display: flex;
  flex-direction: column;
  border-radius: 32rpx;
  padding: 40rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: filter 0.3s ease;
  
  &.blur-content {
    filter: blur(8px);
    -webkit-filter: blur(8px);
  }
}

.card-back {
  transform: rotateY(180deg);
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  z-index: 1;
  position: relative;
}

.card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.sound-image {
  width: 36rpx;
  height: 36rpx;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 48px;
}

.status-text {
  font-size: 60rpx;
  font-weight: bold;
  text-align: center;
  flex: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.word-image-container {
  width: 200px;
  height: 200px;
  margin: 0 auto 20px auto;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* 防止图片溢出容器 */
  position: relative; /* 添加相对定位以支持预览按钮的绝对定位 */

  .word-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 裁剪图片以填充容器，可能会裁剪掉部分图片 */
  }
  
  .preview-button {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 32px;
    height: 32px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }
  
  .preview-icon {
    width: 18px;
    height: 18px;
  }
}

.word-container,
.definition-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.word,
.definition {
  font-size: 32px;
  color: #333;
  font-weight: 500;
  text-align: center;
}

.phonetic-symbol {
  font-size: 24px;
  color: #888;
  font-style: italic;
  margin-top: 8rpx;
  text-align: center;
}

.definition {
  white-space: pre-wrap;
  /* 保留换行符 */
  line-height: 1.4;
  /* 增加行高 */
  padding: 0 20rpx;
  max-height: 500rpx;
  overflow-y: auto;
  /* 内容太多时可滚动 */
}

.bottom-nav {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 0;
}

.nav-button {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder {
  width: 48px;
  /* 与icon-wrapper相同宽度，用于平衡布局 */
}
</style>
