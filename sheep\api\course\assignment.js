import request from '@/sheep/request';

const AssignmentApi = {
  // 获取作业库分页
  getAssignmentPage: (params) => {
    return request({
      url: '/course/assignment/page',
      method: 'GET',
      params,
    });
  },
  
  // 创建课程作业
  createAssignment: (data) => {
    return request({
      url: '/course/assignment/create',
      method: 'POST',
      data,
    });
  },

  // 获取作业详情
  getAssignment: (id) => {
    return request({
      url: '/course/assignment/get',
      method: 'GET',
      params: { id },
    });
  },

  // 更新课程作业
  updateAssignment: (data) => {
    return request({
      url: '/course/assignment/update',
      method: 'PUT',
      data,
    });
  },

  // 删除课程作业
  deleteAssignment: (id) => {
    return request({
      url: '/course/assignment/delete',
      method: 'DELETE',
      params: { id },
    });
  },
  
  // 获取作业发布分页
  getAssignmentReleasePage: (params) => {
    return request({
      url: '/course/assignment-release/page',
      method: 'GET',
      params,
    });
  },
  
  // 创建课程作业发布
  createAssignmentRelease: (data) => {
    return request({
      url: '/course/assignment-release/create',
      method: 'POST',
      data,
    });
  },
  
  // 删除课程作业发布
  deleteAssignmentRelease: (id) => {
    return request({
      url: '/course/assignment-release/delete',
      method: 'DELETE',
      params: { id },
    });
  },
  
  // 获取当前用户的课程作业发布分页
  getMyAssignmentReleasePage: (params) => {
    return request({
      url: '/course/assignment-release/page/my',
      method: 'GET',
      params,
    });
  },

  // 学生提交作业答题
  submitAssignmentAnswer: (data) => {
    return request({
      url: '/course/assignment-release/answer/submit',
      method: 'POST',
      data,
    });
  },
  
  // 学生查看答题报告
  getAssignmentAnswerReport: (id = {}) => {
    return request({
      url: '/course/assignment-release/answer/report',
      method: 'GET',
      params: { id },
    });
  },
  // 老师查看答题报告
  getAssignmentAnswerReportForTeacher: (assignmentId, userId) => {
    return request({
      url: '/course/assignment-release/answer/teacher-get',
      method: 'GET',
      params: {
        id: assignmentId,  // 使用传入的assignmentId参数
        userId: userId     // 使用传入的userId参数
      }
    });
  },


  // 教师查看答题统计报告
  getAssignmentAnswerStatistics: (params = {}) => {
    return request({
      url: '/course/assignment-release/answer/statistics',
      method: 'GET',
      params,
    });
  },
  
  // 获取作业完成情况
  getAssignmentCompletionStatus: (params = {}) => {
    return request({
      url: '/course/assignment-answer-paper/get/completion-status',
      method: 'GET',
      params,
    });
  },

  // 题库选题
  createAssignmentQuestionFromBank: (data) => {
    return request({
      url: '/course/assignment-question/create/from-bank',
      method: 'POST',
      data,
    });
  },

  // 删除课程作业题目
  deleteAssignmentQuestion: (id) => {
    return request({
      url: '/course/assignment-question/delete',
      method: 'DELETE',
      params: { id },
    });
  },

  // 获得课程作业题目详情
  getAssignmentQuestion: (id) => {
    return request({
      url: '/course/assignment-question/get',
      method: 'GET',
      params: { id },
    });
  },

  // 获得作业题目列表
  getAssignmentQuestionList: (assignmentId) => {
    return request({
      url: '/course/assignment-question/list',
      method: 'GET',
      params: { assignmentId },
    });
  },

  // 获得课程作业题目选项列表
  getAssignmentQuestionOptionList: (questionId) => {
    return request({
      url: '/course/assignment-question/assignment-question-option/list-by-question-id',
      method: 'GET',
      params: { questionId },
    });
  },

  // 学生查看作业题目
  viewAssignmentReleaseQuestion: (params = {}) => {
    return request({
      url: '/course/assignment-release-question/view',
      method: 'GET',
      params,
    });
  },
  
  // 获取题目分页
  getTopicPage: (params) => {
    return request({
      url: '/quiz/question-bank/page',
      method: 'GET',
      params,
    });
  },

  // 提交选题
  submitTopic: (data) => {
    return request({
      url: '/course/assignment-question/create/from-bank',
      method: 'POST',
      data,
    });
  },

  // 更新题目分数
  updateQuestionScore: (data) => {
    return request({
      url: '/course/assignment-question/update-score',
      method: 'PUT',
      data,
    });
  },

  // 更新课程作业题目
  updateAssignmentQuestion: (data) => {
    return request({
      url: '/course/assignment-question/update',
      method: 'PUT',
      data,
    });
  },

  // 根据id获取当前用户发布的作业
  getAssignmentReleaseById: (params) => {
    return request({
      url: '/course/assignment-release/get/my',
      method: 'GET',
      params
    });
  },

  // 更新课程作业发布
  updateAssignmentReleaseById: (data) => {
    return request({
      url: '/course/assignment-release/update',
      method: 'PUT',
      data
    });
  },

};

export default AssignmentApi;