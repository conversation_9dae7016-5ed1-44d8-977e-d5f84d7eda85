/**
 * 流式响应解析工具
 * 用于处理从服务器接收的流式数据，特别是ArrayBuffer类型的数据
 */

/**
 * 解析ArrayBuffer类型的流式响应数据
 * @param {ArrayBuffer|Uint8Array} data - 接收到的二进制数据
 * @param {Function} onJsonData - 处理解析后的JSON数据的回调函数，参数为(jsonData)
 * @param {Function} onError - 错误处理回调函数，参数为(error, rawData)
 * @param {Object} options - 配置选项
 * @param {boolean} options.silent - 是否静默处理错误，默认为false
 * @param {string} options.dataPrefix - 数据前缀，默认为'data:'
 * @returns {void}
 */
export function parseStreamData(data, onJsonData, onError = null, options = {}) {
  const { silent = false, dataPrefix = 'data:' } = options;

  try {
    // 确保数据是Uint8Array类型
    let binaryData = data;
    if (data instanceof ArrayBuffer) {
      binaryData = new Uint8Array(data);
    }


    //微信小程序不支持 TextDecoder，使用自定义的utf8Decode函数进行解码
    const text = utf8Decode(binaryData);

    // 分割多个data对象
    const chunks = text.split(dataPrefix).filter(chunk => chunk.trim());

    // 处理每个数据块
    for (const chunk of chunks) {
      try {
        // 处理可能的不完整JSON
        let trimmedChunk = chunk.trim();

        // 跳过空字符串
        if (!trimmedChunk) continue;

        // 尝试修复常见的JSON格式问题
        if (!trimmedChunk.startsWith('{')) {
          // 尝试找到JSON开始的位置
          const jsonStartIndex = trimmedChunk.indexOf('{');
          if (jsonStartIndex > -1) {
            trimmedChunk = trimmedChunk.substring(jsonStartIndex);
          } else {
            if (!silent) {
              console.warn('无法找到JSON开始位置:', trimmedChunk);
            }
            continue;
          }
        }

        // 确保JSON格式完整
        if (trimmedChunk.endsWith('}') || trimmedChunk.endsWith('"}')) {
          try {
            const jsonData = JSON.parse(trimmedChunk);
            // 调用回调函数处理JSON数据
            onJsonData(jsonData);
          } catch (parseError) {
            if (onError) {
              onError(parseError, trimmedChunk);
            } else if (!silent) {
              console.error('JSON解析失败:', parseError.message, '原始数据:', trimmedChunk);
            }
          }
        } else if (!silent) {
          console.warn('跳过不完整的JSON数据:', trimmedChunk);
        }
      } catch (e) {
        if (onError) {
          onError(e, chunk);
        } else if (!silent) {
          console.error('处理数据块错误:', e.message);
        }
      }
    }
  } catch (e) {
    if (onError) {
      onError(e, data);
    } else if (!silent) {
      console.error('解析流式数据错误:', e.message);
    }
  }
}

/**
 * 创建一个处理流式响应的回调函数
 * @param {Function} dataHandler - 处理解析后的数据的回调函数，参数为(content, jsonData)
 * @param {Object} options - 配置选项
 * @param {boolean} options.silent - 是否静默处理错误，默认为false
 * @param {string} options.dataPrefix - 数据前缀，默认为'data:'
 * @param {string} options.contentPath - 内容在JSON数据中的路径，默认为'data.receive.content'
 * @returns {Function} 返回一个可以直接用于onChunkReceived的回调函数
 */
export function createStreamHandler(dataHandler, options = {}) {
  const {
    silent = false,
    dataPrefix = 'data:',
    contentPath = 'data.receive.content',
    successCode = 0
  } = options;

  return ({ data }) => {
    parseStreamData(
      data,
      (jsonData) => {
        if (jsonData.code === successCode) {
          // 根据contentPath提取内容
          let content = '';
          try {
            // 解析路径，例如 'data.receive.content'
            const pathParts = contentPath.split('.');
            let value = jsonData;
            
            console.log('jsonData流式数据:', jsonData);

            for (const part of pathParts) {
              value = value?.[part];
              if (value === undefined) break;
            }
            content = value || '';
          } catch (e) {
            if (!silent) {
              console.error('提取内容失败:', e.message);
            }
            content = '';
          }

          // 即使内容为空也调用处理函数，因为可能是标记的一部分
          dataHandler(content || '', jsonData);
        }
      },
      (error, rawData) => {
        if (!silent) {
          console.error('处理流式数据错误:', error.message, '原始数据:', rawData);
        }
      },
      { silent, dataPrefix }
    );
  };
}



/**
 * 将一个 UTF-8 字符串转换为 JavaScript 字符串
 * @param {Uint8Array} utf8 UTF-8 字符串
 * @return {string} JavaScript 字符串
 */
const utf8Decode = function (utf8) {
  var str = '';
  var i = 0;

  while (i < utf8.length) {
    var value = utf8[i++];

    // 1. 处理单字节（ASCII）字符
    if (value < 0x80) {
      str += String.fromCharCode(value);
    }
    // 2. 处理双字节序列 (0xC2-0xDF)
    else if (value > 0xBF && value < 0xE0) {
      // 检查是否有另一个字节可用
      if (i >= utf8.length) {
        // 处理错误：数据意外结束
        console.error("无效的 UTF-8 序列：输入意外结束（双字节序列）");
        break;
      }
      var nextValue = utf8[i++];
      if ((nextValue & 0xC0) !== 0x80) {
        // 处理错误：无效的连续字节
        console.error("无效的 UTF-8 序列：无效的连续字节（双字节序列）");
        continue;
      }
      str += String.fromCharCode((value & 0x1F) << 6 | nextValue & 0x3F);
    }
    // 3. 处理三字节序列 (0xE0-0xEF)
    else if (value > 0xDF && value < 0xF0) {
      if (i + 1 >= utf8.length) {
        // 处理错误：数据意外结束
        console.error("无效的 UTF-8 序列：输入意外结束（三字节序列）");
        break;
      }
      var nextValue1 = utf8[i++];
      var nextValue2 = utf8[i++];
      if ((nextValue1 & 0xC0) !== 0x80 || (nextValue2 & 0xC0) !== 0x80) {
        // 处理错误：无效的连续字节
        console.error("无效的 UTF-8 序列：无效的连续字节（三字节序列）");
        continue;
      }
      str += String.fromCharCode((value & 0x0F) << 12 | (nextValue1 & 0x3F) << 6 | nextValue2 & 0x3F);
    }
    // 4. 处理四字节序列 (0xF0-0xF7)
    else if (value > 0xEF && value < 0xF8) {
      if (i + 2 >= utf8.length) {
        // 处理错误：数据意外结束
        console.error("无效的 UTF-8 序列：输入意外结束（四字节序列）");
        break;
      }
      var nextValue1 = utf8[i++];
      var nextValue2 = utf8[i++];
      var nextValue3 = utf8[i++];
      if ((nextValue1 & 0xC0) !== 0x80 || (nextValue2 & 0xC0) !== 0x80 || (nextValue3 & 0xC0) !== 0x80) {
        // 处理错误：无效的连续字节
        console.error("无效的 UTF-8 序列：无效的连续字节（四字节序列）");
        continue;
      }
      var charcode = ((value & 0x07) << 18 | (nextValue1 & 0x3F) << 12 | (nextValue2 & 0x3F) << 6 | nextValue3 & 0x3F) - 0x010000;
      str += String.fromCharCode(0xD800 + (charcode >> 10), 0xDC00 + (charcode & 0x03FF));
    } 
    // 5. 处理意外的首字节值
    else {
      // 处理错误：无效的首字节
      console.error("无效的 UTF-8 序列：无效的首字节");
    }
  }

  return str;
}

export default {
  parseStreamData,
  createStreamHandler
};
