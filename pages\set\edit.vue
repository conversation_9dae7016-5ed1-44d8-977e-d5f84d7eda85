<template>
  <view class="container">
    <!-- 顶部导航 -->
    <easy-navbar title="编辑学习集" />

    <!-- 表单区域 -->
    <view class="form-area">
      <!-- 学习集标题和封面 -->
      <view class="form-item title-input">
        <view class="title-cover-container">
          <input type="text" v-model="formData.title" placeholder="请输入标题、说明等" class="input-field" />
          <view class="cover-upload-inline" @click="uploadCover">
            <image v-if="!formData.coverUrl" :src="sheep.$url.cdn('/set/image.png')" class="upload-icon-img"
              mode="aspectFit"></image>
            <image v-else :src="formData.coverUrl" class="upload-image" mode="aspectFill"></image>
            <text v-if="!formData.coverUrl" class="upload-text">点击上传</text>
            <text v-else class="upload-text">更换封面</text>
          </view>
        </view>
      </view>

      <!-- 可见范围 -->
      <view class="form-item visibility">
        <text class="label">可见范围</text>
        <view class="radio-group">
          <view class="radio-item" @click="formData.visibility = 0">
            <view class="radio-circle" :class="{ active: formData.visibility === 0 }">
              <view class="radio-inner" v-if="formData.visibility === 0"></view>
            </view>
            <text>所有人</text>
          </view>
          <view class="radio-item" @click="formData.visibility = 2">
            <view class="radio-circle" :class="{ active: formData.visibility === 2 }">
              <view class="radio-inner" v-if="formData.visibility === 2"></view>
            </view>
            <text>分享班级</text>
          </view>
          <view class="radio-item" @click="formData.visibility = 1">
            <view class="radio-circle" :class="{ active: formData.visibility === 1 }">
              <view class="radio-inner" v-if="formData.visibility === 1"></view>
            </view>
            <text>仅自己</text>
          </view>
        </view>
      </view>

      <!-- 分享到班级 -->
      <view class="form-item share-class" v-if="formData.visibility !== 1" @click="goSelectClass">
        <text class="label">分享到班级</text>
        <view class="share-value">
          <text v-if="selectedClasses.length === 0">请选择班级</text>
          <text v-else>已选择{{ selectedClasses.length }}个班级</text>
          <uni-icons type="right" size="14" color="#999999"></uni-icons>
        </view>
      </view>

      <!-- 扫描文件区域 -->
      <view class="file-section">
        <view class="scan-btn" @click="scanFiles">
          <image :src="sheep.$url.cdn('/set/scan.png')" class="scan-icon-img" mode="aspectFit"></image>
          <text class="scan-text">扫描文件</text>
        </view>
      </view>

      <!-- 单词卡区域 -->
      <view class="word-cards-section">
        <swipe-action v-for="(card, cardIndex) in formData.wordCards" :key="cardIndex"
          :buttons="[{ text: '删除', backgroundColor: '#ff5252', width: 150 }]"
          @button-click="handleCardDelete($event, cardIndex)" :disabled="formData.wordCards.length <= 1"
          :autoClose="true">
          <view class="word-card">
            <view class="word-input">
              <input type="text" v-model="card.word" placeholder="词语" class="card-input" />
              <view class="word-image-upload" @click="uploadWordImage(cardIndex)">
                <image v-if="!card.imageUrl" :src="sheep.$url.cdn('/set/image.png')" class="upload-icon-img"
                  mode="aspectFit"></image>
                <image v-else :src="card.imageUrl" class="upload-word-image" mode="aspectFill"></image>
                <text v-if="!card.imageUrl" class="upload-text">点击上传</text>
                <text v-else class="upload-text">更换图片</text>
              </view>
            </view>

            <!-- 音标输入区域 -->
            <view class="phonetic-input">
              <view class="input-container">
                <input 
                  type="text" 
                  v-model="card.phoneticSymbol" 
                  placeholder="音标（可选）" 
                  class="card-input"
                />
              </view>
            </view>

            <view v-for="(def, defIndex) in card.definitions" :key="defIndex" class="definition-input">
              <view class="input-with-delete">
                <input type="text" v-model="def.definition" placeholder="定义" class="card-input" />
                <view class="def-delete" v-if="card.definitions.length > 1"
                  @click.stop.prevent="removeDefinition(cardIndex, defIndex)">
                  <text class="delete-icon">×</text>
                </view>
              </view>
            </view>

            <view class="add-def-btn" @click.stop="addDefinition(cardIndex)">
              <text>+ 添加更多定义</text>
            </view>
          </view>
        </swipe-action>
        
        <!-- 加载状态指示器 -->
        <uni-load-more :status="loadStatus" :content-text="{
          contentdown: '上拉加载更多',
          contentrefresh: '加载中...',
          contentnomore: '没有更多了'
        }" @click="loadMore" />
      </view>

      <!-- 添加按钮 -->
      <view class="add-btn-container">
        <view class="add-btn" @click="addWordCard">
          <image :src="sheep.$url.cdn('/group/blue-add.png')" class="add-icon" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 底部确认按钮 -->
    <view class="confirm-btn" @click="submitForm">
      <text>确认</text>
    </view>
  </view>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { onLoad, onReachBottom, onShow } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import SetApi from '@/sheep/api/set';
import FileApi from '@/sheep/api/infra/file';
import SwipeAction from '@/components/swipe-action/swipe-action.vue';
import { PAGE_SIZE_NONE } from '@/sheep/util/const';

// 定义组件
defineOptions({
  components: {
    SwipeAction
  }
});

// 编辑模式的设置ID
const setId = ref(0);
const isEdit = ref(false);

// 表单数据
const formData = reactive({
  id: undefined,
  title: '',
  coverUrl: '',
  visibility: 2, // 0: 所有人，1: 仅自己，2: 分享班级
  wordCards: [
    // 初始添加一张空的单词卡
    {
      id: undefined,
      word: '',
      phoneticSymbol: '',
      imageUrl: '',
      audioUrl: '',
      definitions: [{ id: undefined, definition: '' }]
    }
  ]
});

// 单词卡操作跟踪
const cardsToDelete = ref([]); // 要删除的单词卡ID
const cardsToUpdate = ref([]); // 要更新的单词卡
const cardsToAdd = ref([]); // 要新增的单词卡
const originalCardIds = ref([]); // 记录原始单词卡ID，用于判断是更新还是新增

// 加载状态
const loading = ref(false);
// 标记是否已经加载过全部数据
const hasLoadedAllData = ref(false);

// 分页相关
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
  hasMore: true
});

// 加载状态
const loadStatus = ref('more'); // 'more', 'loading', 'noMore'

// 选中的班级
const selectedClasses = ref([]);
const selectedClassIds = ref([]);

// 跳转到班级选择页面
const goSelectClass = () => {
  // 传递已选择的班级ID和学习集ID
  const selectedIdsJson = JSON.stringify(selectedClassIds.value);
  sheep.$router.go(`/pages/set/select-group?setId=${formData.id}&isCreated=true&selectedIds=${encodeURIComponent(selectedIdsJson)}`);
};

// 获取学习集数据
const fetchWordSetData = async (id, isLoadMore = false) => {
  if (!isLoadMore) {
    // 第一次加载或刷新，重置分页
    pagination.pageNo = 1;
    pagination.hasMore = true;
    loadStatus.value = 'more';
    // 清空现有单词卡
    formData.wordCards = [];
    // 重置操作跟踪数组
    cardsToDelete.value = [];
    cardsToUpdate.value = [];
    cardsToAdd.value = [];
    originalCardIds.value = [];
    // 重置全部加载状态
    hasLoadedAllData.value = false;
  } else {
    // 加载更多，页码+1
    loadStatus.value = 'loading';
  }

  // 判断是否是不分页模式（获取全部数据）
  const isNoPageMode = pagination.pageSize === PAGE_SIZE_NONE;

  // 使用分页API获取学习集和单词卡
  const res = await SetApi.getWordSetWithPagedCards({
    id: id,
    pageNo: pagination.pageNo,
    pageSize: pagination.pageSize
  });

  if (res.code !== 0) {
    setTimeout(() => sheep.$router.back(), 1200);
  }

  // 提取学习集基本信息（仅在首次加载时设置）
  if (!isLoadMore) {
    formData.id = res.data.id;
    formData.title = res.data.title || '';
    formData.coverUrl = res.data.coverUrl || '';
    formData.visibility = typeof res.data.visibility === 'number' ? res.data.visibility : 0;
    
    // 处理学习集已分享的班级信息
    if (res.data.classIds && Array.isArray(res.data.classIds)) {
      // 更新已选班级ID
      selectedClassIds.value = res.data.classIds.filter(id => id !== null && id !== undefined);
      
      // 如果有班级ID，则需要获取班级详细信息
      if (selectedClassIds.value.length > 0) {
        // 用ID构建班级对象
        selectedClasses.value = selectedClassIds.value.map(id => ({ id }));
      } else {
        // 没有分享的班级
        selectedClasses.value = [];
      }
    }
  }

  // 检查是否存在分页数据结构
  const wordCardPage = res.data.wordCardPage || {};
  const wordCardsData = wordCardPage.list || [];

  // 更新分页信息
  pagination.total = wordCardPage.total || 0;

  // 处理单词卡数据
  if (wordCardsData && Array.isArray(wordCardsData) && wordCardsData.length > 0) {
    // 添加从服务器获取的单词卡
    wordCardsData.forEach(card => {
      // 记录原始单词卡ID
      if (card.id && !originalCardIds.value.includes(card.id)) {
        originalCardIds.value.push(card.id);
      }

      const newCard = {
        id: card.id,
        word: card.word || '',
        phoneticSymbol: card.phoneticSymbol || '',
        imageUrl: card.imageUrl || '',
        audioUrl: card.audioUrl || '',
        wordSetId: formData.id, // 确保设置wordSetId
        definitions: []
      };

      // 处理定义
      if (card.definitions && Array.isArray(card.definitions)) {
        card.definitions.forEach(def => {
          newCard.definitions.push({
            id: def.id,
            definition: def.definition || '',
            wordSetId: formData.id, // 确保设置wordSetId
            wordCardId: card.id // 确保设置wordCardId
          });
        });
      }

      // 如果没有定义，添加一个空的
      if (newCard.definitions.length === 0) {
        newCard.definitions.push({
          id: undefined,
          definition: '',
          wordSetId: formData.id, // 确保设置wordSetId
          wordCardId: newCard.id // 确保设置wordCardId
        });
      }

      formData.wordCards.push(newCard);
    });
  }

  // 如果是首次加载且没有单词卡，添加一个空的单词卡作为初始状态
  if (!isLoadMore && formData.wordCards.length === 0) {
    formData.wordCards.push({
      id: undefined,
      word: '',
      phoneticSymbol: '',
      imageUrl: '',
      audioUrl: '',
      wordSetId: formData.id,
      definitions: [{ id: undefined, definition: '', wordSetId: formData.id }]
    });
  }

  // 更新加载状态 - 判断是否还有更多数据
  pagination.hasMore = formData.wordCards.length < pagination.total;
  
  // 如果是不分页模式或者没有更多数据了，标记为已加载全部数据
  if (isNoPageMode || !pagination.hasMore) {
    hasLoadedAllData.value = true;
  }
  
  loadStatus.value = pagination.hasMore ? 'more' : 'noMore';
};

// 加载更多
const loadMore = () => {
  // 如果没有更多数据或正在加载中，则不执行操作
  if (loadStatus.value === 'noMore') return
  if (loadStatus.value === 'loading') return
  
  // 设置为加载中状态
  loadStatus.value = 'loading';

  // 页码加1并加载下一页数据
  pagination.pageNo++;
  fetchWordSetData(setId.value, true);
};

// 上传封面
const uploadCover = async () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'], // 压缩图片
    sourceType: ['album', 'camera'], // 从相册或相机
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0];

      // 先使用临时图片路径更新预览，提供即时反馈
      formData.coverUrl = tempFilePath;
      // 使用FileApi上传文件
      const uploadResult = await FileApi.uploadFile(tempFilePath);

      if (uploadResult && uploadResult.code === 0) {
        // 检查data是否直接是URL字符串
        if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
          // 直接使用字符串URL
          formData.coverUrl = uploadResult.data;
          sheep.$helper.toast('上传成功');
        }
        // 检查data是否包含url属性的对象
        else if (uploadResult.data && uploadResult.data.url) {
          formData.coverUrl = uploadResult.data.url;
          sheep.$helper.toast('上传成功');
        }
      }
    }
  });
};

// 添加单词卡
const addWordCard = () => {
  // 如果还有更多数据且还没有完整加载过，先加载所有数据
  if (pagination.hasMore && !hasLoadedAllData.value) {
    // 显示加载中提示
    uni.showLoading({
      title: '请稍候...',
      mask: true
    });

    // 保存原始分页大小
    const originalPageSize = pagination.pageSize;
    
    // 设置为不分页模式获取所有数据
    pagination.pageSize = PAGE_SIZE_NONE;
    
    // 加载所有数据
    fetchWordSetData(setId.value, true).then(() => {
      // 标记已加载所有数据
      hasLoadedAllData.value = true;

      // 恢复原始分页大小
      pagination.pageSize = originalPageSize;

      // 隐藏加载提示
      uni.hideLoading();

      // 添加新卡片
      addNewCard();
    });
  } else {
    // 已加载所有数据或没有更多数据，直接添加新卡片
    addNewCard();
  }
};

// 实际添加新卡片的函数
const addNewCard = () => {
  // 新增一张空白单词卡
  const newCard = {
    id: null, // 新卡片ID为null，表示需要新增
    word: '',
    phoneticSymbol: '',
    imageUrl: '',
    audioUrl: '',
    wordSetId: formData.id, // 确保设置wordSetId
    definitions: [{
      id: null,
      definition: '',
      wordSetId: formData.id, // 确保设置wordSetId
      wordCardId: null // 新卡片的定义暂时没有wordCardId
    }]
  };
  
  // 添加到界面显示的单词卡列表
  formData.wordCards.push(newCard);
  
  // 添加后滚动到新卡片位置
  setTimeout(() => {
    const query = uni.createSelectorQuery();
    query.selectAll('.word-card').boundingClientRect(data => {
      if (data && data.length > 0) {
        const lastCard = data[data.length - 1];
        uni.pageScrollTo({
          scrollTop: lastCard.bottom,
          duration: 300
        });
      }
    }).exec();
  }, 300);
};

// 处理卡片删除事件
const handleCardDelete = (event, index) => {
  // 如果只有一张卡片，不允许删除
  if (formData.wordCards.length <= 1) {
    sheep.$helper.toast('至少需要保留一张单词卡');
    return;
  }

  // 显示确认删除提示
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这张单词卡吗？',
    success: (res) => {
      if (res.confirm) {
        const cardToDelete = formData.wordCards[index];
        
        // 如果是已有的单词卡（有ID且ID > 0），记录要删除的ID
        if (cardToDelete.id && cardToDelete.id > 0) {
          // 添加到要删除的单词卡ID列表
          if (!cardsToDelete.value.includes(cardToDelete.id)) {
            cardsToDelete.value.push(cardToDelete.id);
          }
        }
        
        // 执行UI上的删除操作
        sheep.$helper.toast('已删除');
        formData.wordCards.splice(index, 1);
      }
    }
  });
};

// 添加定义
const addDefinition = (cardIndex) => {
  const card = formData.wordCards[cardIndex];
  formData.wordCards[cardIndex].definitions.push({
    id: null, // 新定义ID为null
    definition: '',
    wordSetId: formData.id, // 确保设置wordSetId
    wordCardId: card.id // 确保设置wordCardId
  });
};

// 删除定义
const removeDefinition = (cardIndex, defIndex) => {
  const definitions = formData.wordCards[cardIndex].definitions;
  if (definitions.length <= 1) {
    return;
  }
  definitions.splice(defIndex, 1);
};

// 添加扫描文件功能
const scanFiles = () => {
  sheep.$helper.inDevMsg();
};

// 上传单词卡图片
const uploadWordImage = async (cardIndex) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'], // 压缩图片
    sourceType: ['album', 'camera'], // 从相册或相机
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0];

      // 先使用临时图片路径更新预览，提供即时反馈
      formData.wordCards[cardIndex].imageUrl = tempFilePath;

      // 显示上传中提示
      sheep.$helper.toast("上传中...");

      // 使用FileApi上传文件
      const uploadResult = await FileApi.uploadFile(tempFilePath);

      if (uploadResult.code !== 0) {
        return;
      }
      // 检查data是否直接是URL字符串
      if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
        // 直接使用字符串URL
        formData.wordCards[cardIndex].imageUrl = uploadResult.data;
        sheep.$helper.toast('上传成功');
      }
      // 检查data是否包含url属性的对象
      else if (uploadResult.data && uploadResult.data.url) {
        formData.wordCards[cardIndex].imageUrl = uploadResult.data.url;
        sheep.$helper.toast('上传成功');
      }
    }
  });
};

// 验证表单
const validateForm = () => {
  // 验证标题
  if (!formData.title.trim()) {
    sheep.$helper.toast('请输入学习集标题');
    return false;
  }

  // 验证至少有一张有效的单词卡
  let hasValidCard = false;

  for (let i = 0; i < formData.wordCards.length; i++) {
    const card = formData.wordCards[i];
    if (!card.word.trim()) {
      sheep.$helper.toast(`第${i + 1}张卡片的词语不能为空`);
      return false;
    }

    // 检查定义
    let hasValidDefinition = false;
    for (const def of card.definitions) {
      if (def.definition.trim()) {
        hasValidDefinition = true;
        break;
      }
    }

    if (!hasValidDefinition) {
      sheep.$helper.toast(`第${i + 1}张卡片至少需要一个有效定义`);
      return false;
    }

    hasValidCard = true;
  }

  if (!hasValidCard) {
    sheep.$helper.toast('请至少添加一张单词卡');
    return false;
  }

  return true;
};

// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!validateForm()) {
    return;
  }

  // 避免重复提交
  if (loading.value) {
    return;
  }

  // 检查每个单词卡的图片URL是否是本地路径，如果是则需要上传
  for (let i = 0; i < formData.wordCards.length; i++) {
    const card = formData.wordCards[i];
    if (card.imageUrl && (card.imageUrl.startsWith('file://') || card.imageUrl.startsWith('/'))) {
      const uploadResult = await FileApi.uploadFile(card.imageUrl);
      if (uploadResult && uploadResult.code === 0) {
        // 检查data是否直接是URL字符串
        if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
          formData.wordCards[i].imageUrl = uploadResult.data;
        }
        // 检查data是否包含url属性的对象
        else if (uploadResult.data && uploadResult.data.url) {
          formData.wordCards[i].imageUrl = uploadResult.data.url;
        }
      }
    }
  }

  // 检查图片URL是否是本地路径，如果是则可能需要再次上传
  if (formData.coverUrl && (formData.coverUrl.startsWith('file://') || formData.coverUrl.startsWith('/'))) {
    const uploadResult = await FileApi.uploadFile(formData.coverUrl);
    if (uploadResult && uploadResult.code === 0) {
      // 检查data是否直接是URL字符串
      if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
        formData.coverUrl = uploadResult.data;
      }
      // 检查data是否包含url属性的对象
      else if (uploadResult.data && uploadResult.data.url) {
        formData.coverUrl = uploadResult.data.url;
      }
    }
  }

  // 准备要更新的单词卡和要新增的单词卡
  const updatedCards = [];
  const newCards = [];

  formData.wordCards.forEach(card => {
    // 过滤掉空的单词卡
    if (!card.word.trim() || !card.definitions.some(def => def.definition.trim())) {
      return;
    }

    // 处理单词卡数据，过滤掉空的定义
    const processedCard = {
      id: card.id,
      word: card.word.trim(),
      phoneticSymbol: card.phoneticSymbol || '',
      imageUrl: card.imageUrl || '',
      audioUrl: card.audioUrl || '',
      wordSetId: formData.id,
      definitions: card.definitions
        .filter(def => def.definition.trim())
        .map(def => ({
          id: def.id,
          definition: def.definition.trim(),
          wordSetId: formData.id,
          wordCardId: card.id
        }))
    };

    // 判断是更新还是新增
    if (card.id && originalCardIds.value.includes(card.id)) {
      updatedCards.push(processedCard);
    } else {
      // 新卡片的id设为null，让后端生成
      processedCard.id = null;
      // 新卡片的定义id也设为null
      processedCard.definitions.forEach(def => {
        def.id = null;
        def.wordCardId = null; // 新卡片的wordCardId也需要设为null
      });
      newCards.push(processedCard);
    }
  });

  // 准备提交数据，采用新的结构
  const submitData = {
    // 基本信息
    id: formData.id,
    title: formData.title.trim(),
    coverUrl: formData.coverUrl,
    visibility: formData.visibility,
    classIds: selectedClassIds.value,

    // 操作数据
    deleteCardIds: cardsToDelete.value,    // 要删除的单词卡ID列表
    updateCards: updatedCards,             // 要更新的单词卡列表
    addCards: newCards                     // 要新增的单词卡列表
  };

  // 调用API更新学习集
  const { code } = await SetApi.updateWordSet(submitData);

  if (code !== 0) {
    return
  }

  // 如果选择了班级并且非仅自己可见，进行班级分享
  if (selectedClasses.value.length > 0 && formData.visibility !== 1) {
    for (const classItem of selectedClasses.value) {
      await SetApi.shareToClass(formData.id, classItem.id);
    }
  }

  // 触发刷新事件，通知相关页面刷新数据
  uni.$emit('refreshSetInfo', formData.id);

  sheep.$helper.toast('更新成功');
  setTimeout(() => sheep.$router.back(), 1200);
};

// 页面加载时初始化
onLoad(async (options) => {
  // 检查是否有setId参数，有则为编辑模式
  if (options.id) {
    const id = parseInt(options.id);
    if (!isNaN(id) && id > 0) {
      setId.value = id;
      isEdit.value = true;
      // 获取学习集数据
      await fetchWordSetData(setId.value);
    }
  }
});

// 上拉加载
onReachBottom(() => {
  loadMore();
});

// 监听班级选择
onMounted(() => {
  // 监听班级选择事件
  uni.$on('classSelected', (data) => {
    selectedClasses.value = data.selectedClasses;
    selectedClassIds.value = data.selectedClasses.map(item => item.id);
  });

  return () => {
    // 组件卸载时移除监听
    uni.$off('classSelected');
  };
});

// 获取学习集已分享班级信息的函数
const fetchSharedClassInfo = async () => {
  // 只有当学习集ID存在且大于0时才获取
  if (!formData.id || formData.id <= 0) {
    return;
  }

  // 获取学习集详情，包含班级分享信息
  const res = await SetApi.getWordSet(formData.id);

  if (res.code === 0 && res.data) {
    // 检查是否存在classIds字段
    if (res.data.classIds && Array.isArray(res.data.classIds)) {
      // 更新已选班级ID
      selectedClassIds.value = res.data.classIds.filter(id => id !== null && id !== undefined);

      // 如果有班级ID，则需要获取班级详细信息
      if (selectedClassIds.value.length > 0) {
        // 这里简化处理，用ID构建班级对象
        selectedClasses.value = selectedClassIds.value.map(id => ({ id }));
      } else {
        // 没有分享的班级
        selectedClasses.value = [];
      }
    }
  }
};

// 添加onShow生命周期钩子
onShow(() => {
  // 页面显示时更新学习集已分享的班级信息
  if (formData.id && formData.id > 0) {
    fetchSharedClassInfo();
  }
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #F8FCFF;
  display: flex;
  flex-direction: column;
  padding-bottom: 180rpx;
}

.header {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;

  .header-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;

    .back-icon {
      font-size: 40rpx;
      color: #333;
    }
  }

  .header-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
  }

  .header-right {
    display: flex;
    align-items: center;

    .menu-icon {
      font-size: 30rpx;
      margin-right: 20rpx;
      letter-spacing: 2rpx;
      color: #999;
    }

    .circle-icon {
      font-size: 36rpx;
      color: #666;
    }
  }
}

.form-area {
  flex: 1;
  padding: 0;
}

.form-item {
  background-color: #F8FCFF;
  padding: 30rpx 30rpx 10rpx 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

  &.visibility,
  &.share-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.label {
  font-size: 26rpx;
  color: #A5A5A5;
  font-family: 'SimHei', sans-serif;
}

.title-cover-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 通用输入框样式 */
input {
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
  color: #171717;
}

.input-field {
  flex: 1;
  height: 60rpx;
  font-size: 26rpx;
  font-family: 'SimHei', sans-serif;
  color: #171717;
  background-color: transparent;
}

.cover-upload-inline {
  width: 80rpx;
  height: 100rpx;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .upload-icon-img {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 8rpx;
  }

  .upload-image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 4rpx;
    margin-bottom: 8rpx;
    object-fit: cover;
  }

  .upload-text {
    font-size: 18rpx;
    color: #999;
    text-align: center;
    line-height: 1;
  }
}

.radio-group {
  display: flex;
  align-items: center;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  margin-right: 10rpx;

  .radio-circle {
    width: 28rpx;
    height: 28rpx;
    border-radius: 50%;
    border: 2rpx solid #ccc;
    margin-right: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    &.active {
      border-color: #4CAF50;
    }

    .radio-inner {
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #4CAF50;
    }
  }
}

.file-section {
  display: flex;
  align-items: center;
  margin: 60rpx 0;
  padding: 0 30rpx;

  .scan-btn {
    display: flex;
    align-items: center;
    color: #2A7EEF;

    .scan-icon-img {
      width: 36rpx;
      height: 36rpx;
      margin-right: 8rpx;
    }

    text {
      color: #2A7EEF !important;
    }
  }
}

.word-cards-section {
  padding: 10rpx 30rpx;
  margin-top: 10rpx;

  /* 为swipe-action添加样式 */
  :deep(.swipe-action-container) {
    margin-bottom: 30rpx;
    border-radius: 16rpx;
    overflow: hidden;
  }

  :deep(.swipe-action-content) {
    border-radius: 16rpx;
    /* 移除此样式以避免冲突 */
  }

  :deep(.swipe-action-buttons) {
    height: 100%;
    /* 确保按钮在默认状态下不可见 */
    right: -1px;
  }

  /* 增加内容区边框宽度，遮挡潜在的按钮 */
  :deep(.word-card) {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    /* 增加右侧边框 */
    border-right: 2px solid #fff;
  }
}

.word-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;

  .word-input {
    margin-bottom: 30rpx;
    border-bottom: 1rpx solid #2A7EEF;
    padding-bottom: 10rpx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-input {
      width: calc(100% - 100rpx);
      /* 为上传图片按钮留出空间 */
      height: 48rpx;
      font-size: 26rpx;
      font-family: 'SimHei', sans-serif;
      color: #171717;
    }

    .word-image-upload {
      width: 80rpx;
      height: 100rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .upload-icon-img {
        width: 60rpx;
        height: 60rpx;
        margin-bottom: 8rpx;
      }

      .upload-word-image {
        width: 60rpx;
        height: 60rpx;
        border-radius: 4rpx;
        margin-bottom: 8rpx;
        object-fit: cover;
      }

      .upload-text {
        font-size: 18rpx;
        color: #999;
        text-align: center;
        line-height: 1;
      }
    }
  }

  .phonetic-input {
    margin-bottom: 30rpx;
    
    .input-container {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      border-bottom: 1rpx solid #2A7EEF;
      padding-bottom: 10rpx;
    }
    
    .card-input {
      width: 100%; /* 音标只有一个，不需要为删除按钮预留空间 */
      height: 48rpx;
      font-size: 26rpx;
      font-family: 'SimHei', sans-serif;
      color: #171717;
    }
  }

  .definition-input {
    margin-bottom: 30rpx;

    .input-with-delete {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      border-bottom: 1rpx solid #2A7EEF;
      padding-bottom: 10rpx;
    }

    .card-input {
      width: calc(100% - 50rpx);
      /* 为删除按钮留出空间 */
      height: 48rpx;
      font-size: 26rpx;
      font-family: 'SimHei', sans-serif;
      color: #171717;
      /* 移除独立的边框 */
    }

    .def-delete {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 46rpx;
      height: 46rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 50%;
      padding: 0;
      box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      z-index: 10;

      .delete-icon {
        font-size: 32rpx;
        color: #999;
        font-weight: bold;
        line-height: 1;
      }
    }
  }

  .add-def-btn {
    height: 40rpx;
    padding: 8rpx 0;
    display: flex;
    justify-content: center;

    text {
      font-size: 24rpx;
      color: #2A7EEF !important;
      font-family: 'SimHei', sans-serif;
    }
  }
}

.add-btn-container {
  width: 100rpx;
  height: 100rpx;
  position: fixed;
  right: 40rpx;
  bottom: 200rpx;
  z-index: 10;
  border-radius: 50%;
  background-color: #2A7EEF;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 16rpx rgba(42, 126, 239, 0.3);
}

.add-btn {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  .add-icon {
    width: 100rpx;
    height: 100rpx;
    font-size: 60rpx;
    color: #fff !important;
    margin-top: -6rpx;
  }
}

.confirm-btn {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  height: 90rpx;
  background-color: #239EED;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 16rpx rgba(57, 166, 242, 0.68);
  z-index: 10;

  text {
    color: #fff !important;
    font-size: 34rpx !important;
    font-family: 'SimHei', sans-serif;
  }
}

/* 页面文本和标签通用样式 */
text,
.label {
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
  color: #A5A5A5;
}

/* 词语和定义输入框专用样式 */
.card-input {
  font-size: 26rpx !important;
  font-family: 'SimHei', sans-serif;
  color: #171717;
}

/* placeholder样式 */
::-webkit-input-placeholder {
  color: #A5A5A5;
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
}

input::placeholder {
  color: #A5A5A5;
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
}

.share-class {
  background-color: #F8FCFF;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.share-value {
  display: flex;
  align-items: center;

  text {
    font-size: 26rpx;
    color: #666;
    margin-right: 10rpx;
  }
}
</style>
