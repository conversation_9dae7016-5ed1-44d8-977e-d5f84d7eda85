@import './mixins';

//颜色 ，渐变背景60%
$blue: #136FED;     // 海蓝
$red: #d10019;      // 中国红
$orange: #f37b1d;   // 桔橙
$gold: #fbbd08;     // 明黄
$green: #8dc63f;    // 橄榄绿
$cyan: #1cbbb4;     // 天青
$purple: #6739b6;   // 姹紫
$brightRed: #e54d42;  // 嫣红
$forestGreen: #39b54a; // 森绿
$mauve: #9c26b0;    // 木槿
$pink: #e03997;     // 桃粉
$brown: #a5673f;    // 棕褐
$grey: #8799a3;     // 玄灰
$gray: #aaaaaa;     // 草灰
$black: #333333;    // 墨黑

$colors: ();
$colors: map-merge(
  (
    'blue':$blue,
    'red':$red,
    'orange':$orange,
    'gold':$gold,
    'green':$green,
    'cyan':$cyan,
    'purple':$purple,
    'brightRed':$brightRed,
    'forestGreen':$forestGreen,
    'mauve':$mauve,
    'pink':$pink,
    'brown':$brown,
    'grey':$grey,
    'gray':$gray,
    'black':$black,
  ),
  $colors
);

//灰度
$bg-page: #f6f6f6; //这个变量控制了全局页面默认背景颜色
$white: #ffffff;
$gray-f: #f8f9fa;
$gray-e: #eeeeee;
$gray-d: #dddddd;
$gray-c: #cccccc;
$gray-b: #bbbbbb;
$gray-a: #aaaaaa;
$dark-9: #999999;
$dark-8: #888888;
$dark-7: #777777;
$dark-6: #666666;
$dark-5: #555555;
$dark-4: #484848; //ss-黑
$dark-3: #333333;
$dark-2: #222222;
$dark-1: #111111;
$black: #000000;

$grays: ();
$grays: map-merge(
  (
    'white': $white,
    'gray-f': $gray-f,
    'gray-e': $gray-e,
    'gray-d': $gray-d,
    'gray-c': $gray-c,
    'gray-b': $gray-b,
    'gray-a': $gray-a,
    'gray': $gray-a,
  ),
  $grays
);

$darks: ();
$darks: map-merge(
  (
    'dark-9': $dark-9,
    'dark-8': $dark-8,
    'dark-7': $dark-7,
    'dark-6': $dark-6,
    'dark-5': $dark-5,
    'dark-4': $dark-4,
    'dark-3': $dark-3,
    'dark-2': $dark-2,
    'dark-1': $dark-1,
    'black': $black,
  ),
  $darks
);

// 边框
$border-width: 1rpx !default; // 边框大小
$border-color: $gray-d !default; // 边框颜色

// 圆角
$radius: 10rpx !default; // 默认圆角大小
$radius-lg: 40rpx !default; // 大圆角
$radius-sm: 6rpx !default; // 小圆角
$round-pill: 1000rpx !default; // 半圆

// 动画过渡
$transition-base: all 0.2s ease-in-out !default; // 默认过渡
$transition-base-out: all 0.04s ease-in-out !default; // 进场过渡
$transition-fade: opacity 0.15s linear !default; // 透明过渡
$transition-collapse: height 0.35s ease !default; // 收缩过渡

// 间距
$spacer: 20rpx !default;
$spacers: () !default;
$spacers: map-merge(
  (
    0: 0,
    1: $spacer * 0.25,
    2: $spacer * 0.5,
    3: $spacer,
    4: $spacer * 1.5,
    5: $spacer * 3,
    6: $spacer * 5,
  ),
  $spacers
);
// 字形
$font-weight-lighter: lighter !default;
$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-bold: 700 !default;
$font-weight-bolder: 900 !default;
$fontsize: () !default;
$fontsize: map-merge(
  (
    xs: 20,
    sm: 24,
    df: 28,
    lg: 32,
    xl: 36,
    xxl: 44,
    sl: 80,
    xsl: 120,
  ),
  $fontsize
);
// 段落
$line-height-base: 1.5 !default;
$line-height-lg: 2 !default;
$line-height-sm: 1.25 !default;
// 图标
$iconsize: () !default;
$iconsize: map-merge(
  (
    xs: 0.5,
    sm: 0.75,
    df: 1,
    lg: 1.25,
    xl: 1.5,
    xxl: 2,
    sl: 6,
    xsl: 10,
  ),
  $iconsize
);
// 定义公共图片URL变量
$baseImgUrl: 'https://thai-nnnmkj.oss-cn-shenzhen.aliyuncs.com/uniapp';
