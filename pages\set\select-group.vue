<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<easy-navbar title="选择班级">
			<template #left>
				<view class="close-btn" @click="goBack">关闭</view>
			</template>
			<template #right>
				<view class="options">
					<text>...</text>
					<text class="circle-icon">◯</text>
				</view>
			</template>
		</easy-navbar>

		<!-- 标签栏 -->
		<view class="tabs-container">
			<view class="tabs-left">
				<text class="tab-text">所有班级</text>
			</view>
			<view class="tabs-right">
				<text class="create-text" @click="createNewClass">创建新班级</text>
				<text class="selected-text">已选 ({{ selectedCount }})</text>
			</view>
		</view>

		<!-- 班级列表 -->
		<view class="class-list">
			<view
				v-if="classes.length === 0"
				class="empty-state"
			>
				<text>暂无班级</text>
			</view>
			<view
				class="class-item"
				v-for="(item, index) in classes"
				:key="index"
				:class="{'selected': selectedIds.includes(item.id)}"
				@click="toggleSelect(item)"
			>
				<view class="item-content">
					<!-- 第一行 -->
					<view class="first-row">
						<view class="icon-container">
							<image class="class-icon" :src="sheep.$url.cdn('/group/Faculty.png')" mode="aspectFit" />
						</view>
						<view class="name-container">{{ item.name }}</view>
					</view>

					<!-- 第二行 -->
					<view class="second-row">
						<view class="count-container">
							<template v-if="shareType === 'course'">
								{{ item.lessonCount }}个课程
							</template>
							<template v-else>
								{{ item.studySetCount }}个学习集
							</template>
						</view>
						<view class="separator">|</view>
						<view class="desc-container">{{ item.remark || '班级说明班级说明' }}</view>
					</view>
				</view>
			</view>
		</view>

		<view class="finish-btn-container">
			<view class="finish-btn" @click="confirmSelection">
				<text>完成</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import GroupApi from '@/sheep/api/group/index';
import SetApi from '@/sheep/api/set';
import LessonApi from '@/sheep/api/course/lesson';
import sheep from '@/sheep';
import easyNavbar from '@/components/easy-navbar/easy-navbar.vue';

// 响应式数据
const classes = ref([]);
const selectedIds = ref([]);
const setId = ref(null); // 学习集ID
const courseId = ref(null); // 课程ID
const shareType = ref(''); // 分享类型：set或course
const isCreated = ref(true); // 学习集是否已创建
const shareToClass = ref(true);
const initialSelectedIds = ref([]); // 初始选中的ID列表，用于比较变更

// 计算已选择班级数量
const selectedCount = computed(() => selectedIds.value.length);

// 加载所有班级
const loadClasses = async () => {
  // 只加载我创建的班级
  const createdRes = await GroupApi.getMyCreatedGroups();

  let allClasses = [];

  if (createdRes.code === 0) {
    allClasses = [...createdRes.data];
  }

  // 排序：把有学习集的班级排前面
  allClasses.sort((a, b) => {
    // 优先按是否有学习集排序
    const aHasStudySets = (a.studySetCount || 0) > 0;
    const bHasStudySets = (b.studySetCount || 0) > 0;

    if (aHasStudySets && !bHasStudySets) return -1;
    if (!aHasStudySets && bHasStudySets) return 1;

    // 其次按创建时间排序，新创建的排前面
    return new Date(b.createTime) - new Date(a.createTime);
  });

  classes.value = allClasses;
};

// 切换选择状态
const toggleSelect = (item) => {
	const index = selectedIds.value.findIndex(id => id === item.id);
	if (index === -1) {
		selectedIds.value.push(item.id);
	} else {
		selectedIds.value.splice(index, 1);
	}
};

// 返回上一页
const goBack = () => {
	sheep.$router.back();
};

// 确认选择
const confirmSelection = async () => {
	if (selectedIds.value.length === 0 && shareType.value === 'course') {
		sheep.$helper.toast('请至少选择一个班级');
		return;
	}

	const selectedClasses = classes.value.filter(item => selectedIds.value.includes(item.id));

	// 如果是自己创建的学习集分享
	if (setId.value && isCreated.value) {
		// 只有当开启分享到班级时才执行分享
		if (shareToClass.value) {
      // 先获取学习集详情，确保有标题、封面和可见范围数据
      const setDetail = await SetApi.getWordSet(setId.value);

      if (setDetail.code !== 0 || !setDetail.data) {
        sheep.$helper.toast('获取学习集信息失败');
        uni.hideLoading();
        return;
      }

      // 使用更新学习集接口更新groupIds
      const updateResult = await SetApi.batchShareToGroup({
        setId: Number(setId.value),
     /*   title: setDetail.data.title || '',
        coverUrl: setDetail.data.coverUrl || '',
        visibility: 2, // 设置为分享班级可见*/
        groupIds: selectedIds.value
      });

      // 发送刷新事件
      uni.$emit('refreshSetClasses', { setId: setId.value });

      // 延迟返回上一页
      setTimeout(() => {
        goBack();
      }, 1500);
		} else {
			// 如果没有开启分享到班级，直接返回
			goBack();
		}
	} else if (shareType.value === 'course' && courseId.value) {
    // 获取需要添加和删除的班级ID
    const toAddIds = selectedIds.value.filter(id => !initialSelectedIds.value.includes(id));
    const toRemoveIds = initialSelectedIds.value.filter(id => !selectedIds.value.includes(id));

    // 批量添加班级
    if (toAddIds.length > 0) {
      await LessonApi.batchCreateGroupsInCourse({
        courseId: courseId.value,
        groupIds: toAddIds
      });
    }

    // 批量删除班级
    if (toRemoveIds.length > 0) {
      await LessonApi.batchDeleteGroupsInCourse({
        courseId: courseId.value,
        groupIds: toRemoveIds
      });
    }

    uni.hideLoading();
    sheep.$helper.toast('操作成功');

    // 发送刷新课程班级列表的事件
    uni.$emit('refreshCourseClasses', { courseId: courseId.value });

    // 延迟返回上一页
    setTimeout(() => {
      goBack();
    }, 1500);
	} else {
		// 如果是未创建的学习集，先将选择结果和分享状态保存到本地缓存
		const resultData = {
			selectedClasses,
			shareToClass: shareToClass.value
		};

		// 使用uni.$emit传递数据到上一页
		uni.$emit('classSelected', resultData);

		// 返回上一页
		goBack();
	}
};

// 创建新班级
const createNewClass = () => {
	sheep.$router.go('/pages/group/add');
};

// 获取课程已分享的班级
const fetchCourseSharedClasses = async (courseId) => {
    const res = await LessonApi.getClassesByCourseId(courseId);
    if (res.code === 0 && res.data) {
      // 将课程已分享的班级ID加入已选数组
      const classIds = res.data.map(item => item.groupId).filter(id => id !== null && id !== undefined);
      selectedIds.value = classIds;
      initialSelectedIds.value = [...classIds]; // 保存初始状态，用于后续比较

      // 如果班级数据还没加载完成，需要等待班级数据
      if (classes.value.length === 0) {
        await loadClasses();
      }

      // 确认选择的班级ID都存在于加载的班级列表中
      selectedIds.value = selectedIds.value.filter(id =>
        classes.value.some(classItem => classItem.id === id)
      );
      initialSelectedIds.value = [...selectedIds.value]; // 更新初始状态
    }
};

// 获取学习集已分享的班级
const fetchSetSharedClasses = async (id) => {
  const res = await SetApi.getWordSet(id);

  if (res.code === 0 && res.data) {
    // 检查是否存在classIds字段
    if (res.data.classIds && Array.isArray(res.data.classIds)) {
      // 将已分享的班级ID加入已选数组
      selectedIds.value = res.data.classIds.filter(id => id !== null && id !== undefined);
      initialSelectedIds.value = [...selectedIds.value]; // 保存初始状态

      // 如果班级数据还没加载完成，需要等待班级数据
      if (classes.value.length === 0) {
        await loadClasses();
      }

      // 确认选择的班级ID都存在于加载的班级列表中
      selectedIds.value = selectedIds.value.filter(id =>
        classes.value.some(classItem => classItem.id === id)
      );
      initialSelectedIds.value = [...selectedIds.value]; // 更新初始状态
    }
  }
};

// 页面加载时获取班级信息
onShow(() => {
	loadClasses();

	// 获取页面参数
	const pages = getCurrentPages();
	const currentPage = pages[pages.length - 1];
	const options = currentPage?.$page?.options || currentPage?.options || {};

	// 处理分享类型
	if (options.shareType) {
    shareType.value = options.shareType;

    // 处理课程分享
    if (options.shareType === 'course' && options.courseId) {
      courseId.value = options.courseId;
      fetchCourseSharedClasses(options.courseId);
    }
    // 处理学习集分享
    else if (options.shareType === 'set' && options.setId) {
      setId.value = options.setId;
      // 判断学习集是否已创建
      isCreated.value = options.isCreated === 'true' || options.isCreated === true;

      // 如果是已创建的学习集，获取已分享的班级
      if (isCreated.value) {
        fetchSetSharedClasses(options.setId);
      }
    }
	}
	// 兼容原有逻辑
	else if (options.setId) {
    shareType.value = 'set';
		setId.value = options.setId;
		// 判断学习集是否已创建
		isCreated.value = options.isCreated === 'true' || options.isCreated === true;

		// 如果是已创建的学习集，获取已分享的班级
		if (isCreated.value) {
			fetchSetSharedClasses(options.setId);
		}
	}

	// 处理已选班级IDs
	if (options.selectedIds) {
    selectedIds.value = JSON.parse(decodeURIComponent(options.selectedIds));
    initialSelectedIds.value = [...selectedIds.value]; // 保存初始状态
	}
});
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #FFFFFF;
	display: flex;
	flex-direction: column;
}

/* 自定义关闭按钮 */
.close-btn {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	padding: 10rpx;
}

.options {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 32rpx;
	color: #333;
}

.circle-icon {
	font-size: 40rpx;
}

/* 标签栏 */
.tabs-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1px solid #EFEFEF;
}

.tabs-left {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.tabs-right {
	display: flex;
	align-items: center;
}

.create-text {
	color: #4184FF;
	font-size: 32rpx;
	margin-right: 30rpx;
}

.selected-text {
	color: #4184FF;
	font-size: 32rpx;
}

.tab-text {
	font-size: 32rpx;
	color: #000000;
}

/* 班级列表 */
.class-list {
	flex: 1;
	padding: 0 20rpx;
	padding-bottom: 160rpx; /* 为底部按钮预留空间 */
	overflow-y: auto;
	box-sizing: border-box;
	height: calc(100vh - 200rpx); /* 限制高度，减去导航栏和标签栏的高度 */
}

.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200rpx;
	color: #999;
	font-size: 28rpx;
}

.class-item {
	margin: 20rpx 0;
	padding: 15rpx 20rpx;
	background-color: #FFFFFF;
	border: 1px solid #D8D8D8;
	border-radius: 12rpx;
	position: relative;
	height: 90rpx;
}

.class-item.selected {
	border: 1px solid #46ADF0;
}

.item-content {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

/* 第一行 */
.first-row {
	display: flex;
	align-items: center;
}

.icon-container {
	margin-right: 10rpx;
}

.class-icon {
	width: 60rpx;
	height: 56rpx;
}

.name-container {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

/* 第二行 */
.second-row {
	display: flex;
	align-items: center;
}

.count-container {
	font-size: 24rpx;
	color: #999;
	white-space: nowrap;
}

.separator {
	font-size: 24rpx;
	color: #999;
	padding: 0 6rpx;
}

.desc-container {
	font-size: 24rpx;
	color: #999;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 分享到班级选项 */
.share-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: #fff;
	border-top: 1px solid #EFEFEF;
	margin-bottom: 120rpx;
}

.share-label {
	font-size: 28rpx;
	color: #333;
}

/* 完成按钮容器 */
.finish-btn-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
  padding-bottom: 40rpx;
	background-color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
}

/* 完成按钮 */
.finish-btn {
	width: 90%;
	height: 86rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #46ADF0;
	color: #FFFFFF;
	font-size: 34rpx;
	border-radius: 45rpx;
	box-shadow: 0 4rpx 16rpx rgba(70, 173, 240, 0.3);
}
</style>
