<template>
	<view>
		<view class="content-container">
			<!-- 使用动态类实现淡入淡出 -->
			<scroll-view
				ref="mainScrollView"
				class="component-wrapper"
				:class="{'fade-out': isAnimating}"
				scroll-y="true"
				@scroll="handleContainerScroll"
				:style="'height: calc(100vh - 158rpx);'"
			>
				<!-- 使用v-if和v-show的混合方案 -->
				<!-- v-if确保组件只在第一次需要时创建，v-show控制后续的显示/隐藏 -->
				<IndexComponent v-if="loadedTabs.includes(0)" v-show="activeTab === 0" :hideBottomNav="true" ref="indexComponent"/>
				<CommunityComponent v-if="loadedTabs.includes(1)" v-show="activeTab === 1" :hideBottomNav="true" ref="communityComponent"/>
				<ToolComponent v-if="loadedTabs.includes(2)" v-show="activeTab === 2" :hideBottomNav="true" ref="toolComponent"/>
				<PhoneticComponent v-if="loadedTabs.includes(3)" v-show="activeTab === 3" :hideBottomNav="true" ref="phoneticComponent"/>
				<MyComponent v-if="loadedTabs.includes(4)" v-show="activeTab === 4" :hideBottomNav="true" ref="myComponent"/>
			</scroll-view>
		</view>
		
		<view class="bottom-nav">
			<image
			  class="tabbar-top-bg"
			  mode="widthFix"
			  :src="sheep.$url.static('/uniapp/tabbar/tabbar-top-bg.png')"
			/>
			
			
			<view class="nav-item home-nav" :class="{'show-active': activeTab === 0 }" @click="switchTab(0)">
				<view class="icon">
					<image class="normal" src="/static/icon/index.png" mode="heightFix" />
					<image class="active" src="/static/icon/index-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					首页
				</view>

			</view>
			
			<view class="nav-item community-nav" :class="{ 'show-active': activeTab === 1 }" @click="switchTab(1)">
				<view class="icon">
					<image class="normal" src="/static/icon/community.png" mode="heightFix" />
					<image class="active" src="/static/icon/community-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					社区
				</view>

			</view>
			
			
			<view class="nav-item tool-item tool-nav" :class="{ 'show-active': activeTab === 2 }" @click="switchTab(2)">
				<view class="tool-circle-container">
					<view class="tool-circle">
						<image class="tool-icon" src="/static/icon/tool.png" mode="heightFix" />
					</view>
				</view>
				<view class="nav-title tool-title">
					AI工具
				</view>
			</view>
			
			
			<view class="nav-item phonetic-nav" :class="{ 'show-active': activeTab === 3 }" @click="switchTab(3)">

				<view class="icon">
					<image class="normal" src="/static/icon/phonetic.png" mode="heightFix" />
					<image class="active" src="/static/icon/phonetic-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					音标
				</view>

			</view>
			
			
			
			<view class="nav-item my-nav" :class="{'show-active': activeTab === 4 }" @click="switchTab(4)">
				<view class="icon">
					<image class="normal" src="/static/icon/my.png" mode="heightFix" />
					<image class="active" src="/static/icon/my-active.png" mode="heightFix" />
				</view>
				<view class="nav-title">
					我的
				</view>
			</view>
		</view>
	</view>

</template>

<script setup>
	import {
		ref,
		onMounted,
		onBeforeUnmount,
		nextTick,
		watch,
		getCurrentInstance
	} from 'vue'
	import { onShow, onHide } from '@dcloudio/uni-app';
	import sheep from '@/sheep';
	
	// 直接导入组件
	import IndexComponent from '@/pages/home/<USER>'
	import CommunityComponent from '@/pages/community/index.vue'
	import ToolComponent from '@/pages/tool-list/index.vue'
	import PhoneticComponent from '@/pages/phonetic/index.vue'
	import MyComponent from '@/pages/my/index.vue'

	const tabBars=[
		{
        "pagePath": "pages/index/index",
        "text": "主页",
        "iconPath": "static/icon/index.png",
        "selectedIconPath": "static/icon/index-active.png"
      },
      {
        "pagePath": "pages/community/index",
        "text": "社区",
        "iconPath": "static/icon/community.png",
        "selectedIconPath": "static/icon/community-active.png"
      },
      {
        "pagePath": "pages/tool/index",
        "text": "工具",
        "iconPath": "static/icon/tool.png",
        "selectedIconPath": "static/icon/tool.png"
      },
      {
        "pagePath": "pages/phonetic/index",
        "text": "音标",
        "iconPath": "static/icon/phonetic.png",
        "selectedIconPath": "static/icon/phonetic-active.png"
      },
      {
        "pagePath": "pages/my/index",
        "text": "我的",
        "iconPath": "static/icon/my.png",
        "selectedIconPath": "static/icon/my-active.png"
      }
	]
	
	// 组件引用
	const indexComponent = ref(null);
	const communityComponent = ref(null);
	const toolComponent = ref(null);
	const phoneticComponent = ref(null);
	const myComponent = ref(null);
	
	// 当前激活的标签页
	const activeTab = ref(0);
	// 动画状态
	const isAnimating = ref(false);
	// 上一个激活的标签页
	const prevActiveTab = ref(0);
	// 是否已初始化
	const isInitialized = ref(false);
	// 已加载的标签页
	const loadedTabs = ref([0]); // 初始只加载首页
	
	const { proxy } = getCurrentInstance();
	const mainScrollView = ref(null);
	let cachedClientHeight = 0;
	
	function updateScrollViewHeight() {
		uni.createSelectorQuery()
			.in(proxy)
			.select('.component-wrapper')
			.boundingClientRect(data => {
				if (data && data.height) {
					cachedClientHeight = data.height;
					// console.log('uni.createSelectorQuery获取scroll-view高度:', cachedClientHeight);
				}
			})
			.exec();
	}
	
	// 通知组件状态变化
	const notifyComponentStatus = () => {
		// 获取所有组件引用
		const components = [
			indexComponent.value,
			communityComponent.value,
			toolComponent.value,
			phoneticComponent.value,
			myComponent.value
		];
		
		// 只处理当前激活的组件
		const currentComponent = components[activeTab.value];
		if (currentComponent && typeof currentComponent.onTabActivated === 'function') {
			// 不再需要检查是否是首次激活
			// 通知组件激活，每次激活都会加载数据
			currentComponent.onTabActivated();
		}
	};
	
	// 切换标签页
	const switchTab = (index) => {
		// 如果正在动画中或者点击当前激活的标签，不处理点击
		if (isAnimating.value || index === activeTab.value) return;
		
		// 保存上一个激活的标签
		prevActiveTab.value = activeTab.value;
		
		// 如果是首次加载此标签，将其添加到已加载列表
		if (!loadedTabs.value.includes(index)) {
			loadedTabs.value.push(index);
		}
		
		// 开始动画
		isAnimating.value = true;
		
		// 延迟后切换组件
		setTimeout(() => {
			// 切换标签
			activeTab.value = index;
			
			// 使用nextTick确保DOM更新
			nextTick(() => {
				// 结束动画状态
				isAnimating.value = false;
				
				// 通知组件状态变化
				notifyComponentStatus();
			});
		}, 150); // 等待淡出动画完成
	}
	
	// 组件挂载完成
	onMounted(() => {
		// 标记为已初始化
		isInitialized.value = true;
		
		nextTick(() => {
			updateScrollViewHeight();
			// 通知当前激活的组件（首页）
			notifyComponentStatus();
		});
	});
	
	// 页面显示时触发
	onShow(() => {
		// 如果已经初始化过，则通知当前激活的组件页面显示
		if (isInitialized.value) {
			const components = [
				indexComponent.value,
				communityComponent.value,
				toolComponent.value,
				phoneticComponent.value,
				myComponent.value
			];
			
			// 只处理当前激活的组件
			const currentComponent = components[activeTab.value];
			if (currentComponent && typeof currentComponent.onPageShow === 'function') {
				currentComponent.onPageShow();
			}
		}
	});
	
	// 页面隐藏时触发
	onHide(() => {
		// 通知当前激活的组件页面隐藏
		const components = [
			indexComponent.value,
			communityComponent.value,
			toolComponent.value,
			phoneticComponent.value,
			myComponent.value
		];
		
		// 只处理当前激活的组件
		const currentComponent = components[activeTab.value];
		if (currentComponent && typeof currentComponent.onPageHide === 'function') {
			currentComponent.onPageHide();
		}
	});
	
	const isScrolling = ref(false); // 添加这行代码
	const handleContainerScroll = (e) => {
	const detail = e.detail || {};
	const scrollTop = detail.scrollTop || 0;
	const scrollHeight = detail.scrollHeight || 0;
	let clientHeight = cachedClientHeight;
	if (!clientHeight) {
		clientHeight = detail.clientHeight || 0;
	}

	if (isScrolling.value) return; // 节流控制

	isScrolling.value = true;

	if (scrollTop + clientHeight >= scrollHeight - 40) {
		console.log("滚动到底部了");

		const components = [
			indexComponent.value,
			communityComponent.value,
			toolComponent.value,
			phoneticComponent.value,
			myComponent.value
		];
		const currentComponent = components[activeTab.value];
		if (currentComponent && typeof currentComponent.handleReachBottom === 'function') {
			currentComponent.handleReachBottom();
		}
	}

	// 重置节流标志
	setTimeout(() => {
		isScrolling.value = false;
	}, 300); // 调整这个时间以匹配你的动画或加载节奏
};
</script>

<style scoped>
	.content-container {
		padding-bottom: 150rpx; /* 为底部导航腾出空间 */
		position: relative;
		/* overflow: hidden; */
		height: calc(100vh - 158rpx);
	}
	
	/* 组件容器样式 */
	.component-wrapper {
		opacity: 1;
		transition: opacity 0.3s ease;
		position: relative;
		height: 100%;
		overflow: auto;
	}
	
	/* 淡出动画类 */
	.fade-out {
		opacity: 0;
	}
	
	.bottom-nav .tabbar-top-bg{
		position: absolute;
		width: 100vw;
		top: -60rpx;
	}

	.bottom-nav {
		position: fixed;
		bottom: 0;
		width: 100%;
		background-color: rgba(255, 255, 255, 0.98);
		display: flex;
		justify-content: space-around;
		padding: 24rpx 0 34rpx;
		backdrop-filter: blur(10px);
		z-index: 999;
	}

	.bottom-nav .nav-item {
		text-align: center;
		position: relative;
		width: 100rpx;
		height: 100rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		transition: all 0.3s ease;
		padding: 0;
	}
	
/* 	.bottom-nav .nav-item:active {
		transform: scale(0.9);
	} */

	.bottom-nav .nav-item .icon {
		position: relative;
		height: 48rpx;
		margin-bottom: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.bottom-nav .nav-item image {
		height: 48rpx;
		position: absolute;
		width: 48rpx;
		transition: all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
	}
	
	
	.bottom-nav .nav-item:nth-child(1) image {
	}
	
	.nav-item .normal {
		opacity: 1;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-item .active {
		opacity: 0;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-item.show-active .normal {
		opacity: 0;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-item.show-active .active {
		opacity: 1;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.nav-title {
		font-size: 24rpx;
		color: #666;
		line-height: 24rpx;
		height: 24rpx;
		margin-top: 2rpx;
		text-align: center;
		width: 100%;
		transition: all 0.3s ease;
		bottom: 10rpx;
	}
	

	/* 各导航项激活时的文本颜色 */
	.home-nav.show-active .nav-title {
		color: #106ee5;
	}
	
	.community-nav.show-active .nav-title {
		color: #ff6743;
	}
	
	.tool-nav.show-active .nav-title {
		color: #5394ff;
	}
	
	.phonetic-nav.show-active .nav-title {
		color: #8b6edf;
	}
	
	.my-nav.show-active .nav-title {
		color: #efaa4a;
	}
	
	/* 工具按钮特殊样式 */
	.tool-item {
		position: relative;
		height: 120rpx; /* 增加高度以容纳突出的圆形 */
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
	}
	
	.tool-circle-container {
		position: absolute;
		top: -50rpx;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 110rpx;
	}
	

	
	.tool-item .tool-circle {
		position: relative;
		width: 110rpx;
		height: 110rpx;
		background: linear-gradient(142deg, rgba(67,240,255,0.89), rgba(60,92,255,0.89));
		box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(39,115,255,0.61);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}
	
	.tool-item .tool-circle .tool-icon{
		width: 80rpx;
		height: 80rpx;
		/* 不需要绝对定位，因为父元素tool-circle已经有flex居中 */
		transform: none;
		left: auto;
		top: auto;
	}
	
	.tool-title {
		position: absolute;
		bottom: 10rpx; /* 调整底部位置，使其与其他按钮文本在同一水平线上 */
		left: 50%;
		transform: translateX(-50%);
		width: 100%;
	}
	
</style>