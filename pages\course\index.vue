<template>
  <view class="course-page">
    <!-- 顶部导航 -->
    <course-navbar 
      title="课程" 
      @search="onSearchConfirm"
      @clear="clearSearch"
    />
    
    <!-- 标签页 -->
    <view class="tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-item" 
        :class="{ active: activeTab === index }"
        @click="changeTab(index)"
      >
        <text>{{ tab.name }}</text>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <swiper class="swiper-container" :current="activeTab" @change="onSwiperChange">
      <swiper-item v-for="(tab, index) in tabs" :key="index">
        <scroll-view 
          scroll-y 
          refresher-enabled 
          :refresher-triggered="tab.refreshing"
          @refresherrefresh="onRefresh(index)"
          class="scroll-container"
        >
          <!-- 空数据 -->
          <s-empty v-if="!tab.list.length" text="暂无课程数据" />
          
          <!-- 课程列表 -->
          <view v-else class="course-list">
            <course-card
              v-for="(course, i) in tab.list"
              :key="i"
              :course="course"
              :iconUrl="course.coverImage || sheep.$url.cdn('/course/lesson.png')"
              @click="goToCourse(course)"
            />
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import sheep from '@/sheep';
import LessonApi from '@/sheep/api/course/lesson';
import CourseCard from '@/components/course-card/course-card.vue';

// 标签页定义
const tabs = reactive([
  { name: '我加入的', refreshing: false, list: [] },
  { name: '我的创建', refreshing: false, list: [] },
  { name: '我的收藏', refreshing: false, list: [] }
]);

// 搜索关键词
const searchKeyword = ref('');

// 清空搜索框
const clearSearch = () => {
  searchKeyword.value = '';
  // 清空搜索词时重新加载数据
  loadCourses(activeTab.value);
};

// 执行搜索
const onSearchConfirm = (keyword) => {
  searchKeyword.value = keyword;
  
  if (!searchKeyword.value.trim()) {
    // 如果搜索词为空，重新加载数据
    loadCourses(activeTab.value);
    return;
  }
  
  // 确定搜索类型 (3-我加入的，1-我创建的，2-我收藏的)
  let searchType;
  switch(activeTab.value) {
    case 0: // 我加入的
      searchType = 3;
      break;
    case 1: // 我的创建
      searchType = 1;
      break;
    case 2: // 我的收藏
      searchType = 2;
      break;
  }
  
  // 执行搜索API调用
  const searchParams = {
    type: searchType,
    keyword: searchKeyword.value.trim(),
    pageNo: 1,
    pageSize: 10
  };

  tabs[activeTab.value].list = [];
  
  // 调用搜索API
  LessonApi.searchLessons(searchParams)
    .then(res => {
      if (res.code === 0 && res.data) {
        // 处理搜索结果数据
        const listData = res.data.list || res.data.records || [];
        tabs[activeTab.value].list = formatCourseData(listData);
      }
    });
};

// 标签页相关
const activeTab = ref(0);

// 切换标签页
const changeTab = (index) => {
  // 如果点击的是当前标签，不做任何操作
  if (activeTab.value === index) return;
  
  activeTab.value = index;
  // 切换到新标签时加载数据
  loadCourses(index);
};

// 监听滑动切换
const onSwiperChange = (e) => {
  const index = e.detail.current;
  
  // 如果滑动到的是当前标签，不做任何操作
  if (activeTab.value === index) return;
  
  activeTab.value = index;
  // 切换到新标签时加载数据
  loadCourses(index);
};

// 下拉刷新
const onRefresh = async (index) => {
  tabs[index].refreshing = true;
  await loadCourses(index);
  tabs[index].refreshing = false;
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 格式化课程数据
const formatCourseData = (data = []) => {
  return data.map(item => ({
    id: item.id,
    title: item.title || '未命名课程',
    subtitle: item.description || '暂无课程描述',
    date: formatDate(item.createTime),
    nickname: item.nickname || item.creatorNickname || '',
    isStore: !!item.isStore,
    coverImage: item.coverImage,
    userId: item.userId // 创建者ID
  }));
};

// 加载课程数据
const loadCourses = async (tabIndex) => {
  let res;
  switch (tabIndex) {
    case 0: // 我加入的
      res = await LessonApi.getJoinedLessonList();
      break;
    case 1: // 我的创建
      res = await LessonApi.getCreatedLessonList();
      break;
    case 2: // 我的收藏
      res = await LessonApi.getFavoriteLessonPage({});
      break;
  }

  if (res.code === 0 && res.data) {
    let listData;
    // 针对不同接口返回的数据结构进行处理
    if (tabIndex === 2 && res.data.list) {
      // 处理"我收藏的"特殊数据结构
      listData = res.data.list;
    } else if (res.data.records) {
      // 处理分页数据结构
      listData = res.data.records;
    } else {
      // 处理直接返回数组的情况
      listData = Array.isArray(res.data) ? res.data : [];
    }

    tabs[tabIndex].list = formatCourseData(listData);
  } else {
    tabs[tabIndex].list = [];
    if (!tabs[tabIndex].refreshing) {
      sheep.$helper.toast(res.msg || '获取课程列表失败');
    }
  }
};

// 跳转到课程页面
const goToCourse = (course) => {
  // 获取当前登录用户ID
  const currentUserId = sheep.$store('user').userInfo?.id;
  
  // 判断是否为创建者
  const isCreator = currentUserId && course.userId && Number(currentUserId) === Number(course.userId);
  
  if (isCreator) {
    // 创建者跳转到课程详情页
    sheep.$router.go(`/pages/course/course-detail?id=${course.id}`);
  } else {
    // 非创建者跳转到访客模式的课程详情页
    sheep.$router.go(`/pages/group/detail/course/lesson-detail?id=${course.id}&isVisitor=true`);
  }
};

// 初始加载
onMounted(() => {
  // 只加载第一个标签页（我加入的）数据
  loadCourses(0);
  
  // 监听课程收藏状态变更事件
  uni.$on('refreshCourseList', (data) => {
    if (data && data.courseId) {
      // 只刷新当前正在查看的标签页
      if (activeTab.value === 2 && data.isStore !== undefined) {
        // 如果当前在收藏页面且收藏状态变更，刷新收藏列表
        loadCourses(2);
      } else if (tabs[activeTab.value].list.length > 0) {
        // 如果当前标签页已加载数据，则刷新当前标签页
        loadCourses(activeTab.value);
      }
    }
  });
});

// 在页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshCourseList');
});
</script>

<style scoped lang="scss">
.course-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background-color: #fff;
  padding: 15rpx 0;
  border-bottom: 1px solid #f0f0f0;
  z-index: 999;
}

.tab-item {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 0;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #777777;
  line-height: 71rpx;
}

.tab-item.active {
  font-weight: bold;
  color: #414141;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 112rpx;
  height: 14rpx;
  background: linear-gradient(91deg, rgba(97,246,255,0.89), rgba(18,157,237,0.89));
  border-radius: 7rpx;
  z-index: -1;
}

.swiper-container {
  flex: 1;
  height: 0;
}

.scroll-container {
  height: 100%;
}

.course-list {
  padding: 30rpx 20rpx;
}

</style>
