<template>
  <view class="container">
    <view class="bottom-box">
      <text class="top-title">音标训练</text>
    </view>

    <view class="scarf">
      <template v-for="item in state.pagination.list" :key="item.id">
        <view class="phonetic-list">
          <view class="phonetic-item" :class="getCardClass(item.type)">
            <view class="card-content">
              <view class="title niu-ellipsis-1">{{ item.name }}</view>
              <view class="abstract niu-ellipsis-1">{{ item.description }}</view>
              <view class="train-btn" @tap="goToDetail(item.id)">开始训练</view>
            </view>
          </view>
        </view>
      </template>

      <uni-load-more
        v-if="state.pagination.total > 0"
        :status="state.loadStatus"
        :content-text="{contentnomore: ' 没有更多数据了'}"
        @tap="loadMore"
      />
    </view>

    <!-- <bottom-nav :currentTab="3" /> -->
  </view>
</template>

<script setup>
  import { onShow, onReachBottom } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import { reactive } from 'vue';
  import _ from 'lodash-es';
  import { PhoneticApi } from '@/sheep/api/phonetic';

  const state = reactive({
    pagination: {
      list: [],
      total: 0,
      pageNo: 1,
      pageSize: 10,
    },
    loadStatus: '',
    isInitialized: false, // 标记是否已初始化
  });

  const goToDetail = (id) => {
    sheep.$router.go(`/pages/phonetic/detail?id=${id}`);
  };

  // 获取卡片样式类名
  const getCardClass = (type) => {
    switch(type) {
      case 1: return 'card-green'; // 音标
      case 2: return 'card-yellow'; // 单词
      case 3: return 'card-blue'; // 句子
      default: return 'card-green';
    }
  };

  // 加载列表
  async function getList() {
    state.loadStatus = 'loading';
    const { data } = await PhoneticApi.getPhoneticTrainProjectPage({
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
    });
    // 合并列表
    state.pagination.list = _.concat(state.pagination.list, data.list);
    state.pagination.total = data.total;
    state.loadStatus = state.pagination.list.length < state.pagination.total ? 'more' : 'noMore';
  }

  // 加载更多
  function loadMore() {
    if (state.loadStatus === 'noMore') {

      return;
    }
    state.pagination.pageNo++;
    getList();
  }

  // 处理页面滚动到底部的事件
  function handleReachBottom() {
    console.log('音标页面触发了滚动到底部事件');
    loadMore();
  }

  // 上拉加载更多 - 在作为独立页面时使用
  onReachBottom(() => {
    // 当作为独立页面使用时，此处会触发
    // console.log('音标页面作为独立页面触发了onReachBottom');
    // loadMore();
  });

  // 初始化数据
  function initData() {
    // 重置分页数据，避免数据重复
    state.pagination.pageNo = 1;
    state.pagination.list = [];
    state.pagination.total = 0;
    getList();
    state.isInitialized = true;
  }

  // 页面初始化 - 原生生命周期
  onShow(() => {
    // 检查是否是直接导航到此页面（非标签页切换）
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    // 如果当前页面路径是直接访问phonetic页面，而不是通过home页面的标签切换
    if (currentPage.route === 'pages/phonetic/index') {
      initData();
    }
  });

  // 暴露给父组件的方法 - 标签页被激活时调用
  defineExpose({
    // 标签页激活时调用
    onTabActivated() {
      // 每次激活都加载数据
      initData();
    },
    
    // 标签页停用时调用
    onTabDeactivated() {
      // 可以在这里暂停一些操作，如动画、计时器等
    },
    
    // 页面显示时调用（整个页面，不是标签页）
    onPageShow() {
      // 可以在这里恢复一些操作
    },
    
    // 页面隐藏时调用（整个页面，不是标签页）
    onPageHide() {
      // 可以在这里暂停一些操作
    },
    
    // 处理页面滚动到底部的事件 - 暴露给父组件调用
    handleReachBottom
  });

</script>

<style scoped lang="scss">
  .niu-ellipsis-1 {
    max-width: 80% !important;
  }

  .container {
    width: 100%;
    position: relative;
  }

  .bottom-box {
    width: 100%;
    height: 350rpx;
    background-image: url('#{$baseImgUrl}/live/live-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
  }

  .top-title {
    position: absolute;
    top: 126rpx;
    left: 40rpx;
    font-family: PingFang SC;
    font-size: 66rpx;
    font-weight: bold;
    color: #2D2D2D;
  }

  .scarf {
    padding: 32rpx;
    position: relative;
    top: -100rpx;
  }

  .phonetic-list {
    display: flex;
    flex-direction: column;
  }

  .phonetic-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;

    width: 691rpx;
    height: 243rpx;
    margin-bottom: 18rpx;
    padding: 30rpx;
    box-sizing: border-box;

    border-radius: 25rpx;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }

  .card-green {
    background-image: url('#{$baseImgUrl}/phonetic/green-back.png');

    .title {
      color: #0BD4C7;
    }

    .abstract {
      color: #72EDE4;
    }

    .train-btn {
      background: #11E4D6;
    }
  }

  .card-yellow {
    background-image: url('#{$baseImgUrl}/phonetic/yellow-back.png');

    .title {
      color: #FF8152;
    }

    .abstract {
      color: #FFAA81;
    }

    .train-btn {
      background: #FF8152;
    }
  }

  .card-blue {
    background-image: url('#{$baseImgUrl}/phonetic/blue-back.png');

    .title {
      color: #03ACDD;
    }

    .abstract {
      color: #3FB6DD;
    }

    .train-btn {
      background: #22C8FF;
    }
  }

  .card-content {
    margin-left: 24rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .title {
    font-family: PingFang SC;
    font-size: 33rpx;
  }

  .abstract {
    font-family: PingFang SC;
    font-size: 21rpx;
    margin-bottom: 28rpx;
  }

  .train-btn {
    display: flex;
    align-items: center;
    justify-content: center;

    background: linear-gradient(to right, #12D1ED, #46ADF0);
    border-radius: 28rpx;

    font-size: 25rpx;
    color: #ffffff;

    width: 160rpx;
    height: 55rpx;
  }

</style>

