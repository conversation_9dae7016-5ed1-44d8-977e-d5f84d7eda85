<!-- 绑定/更换手机号 changeMobile  -->
<template>
  <view>
    <!-- 标题栏 -->
    <view class="head-box ss-m-b-60">
      <view class="head-title ss-m-b-20">
        {{ userInfo.mobile ? '更换手机号' : '绑定手机号' }}
      </view>
      <view class="head-subtitle">为了您的账号安全，请使用本人手机号码</view>
    </view>

    <!-- 表单项 -->
    <uni-forms
      ref="changeMobileRef"
      v-model="state.model"
      :rules="state.rules"
      validateTrigger="bind"
      labelWidth="140"
      labelAlign="center"
    >
      <uni-forms-item name="mobile" label="手机号">
        <uni-easyinput
          placeholder="请输入手机号"
          v-model="state.model.mobile"
          :inputBorder="false"
          type="number"
        >
          <template v-slot:right>
            <button
              class="ss-reset-button code-btn-start"
              :disabled="state.isMobileEnd"
              :class="{ 'code-btn-end': state.isMobileEnd }"
              @click="handleSendSmsCode"
            >
              {{ getSmsTimer('changeMobile') }}
            </button>
          </template>
        </uni-easyinput>
      </uni-forms-item>

      <uni-forms-item name="code" label="验证码">
        <uni-easyinput
          placeholder="请输入验证码"
          v-model="state.model.code"
          :inputBorder="false"
          type="number"
          maxlength="4"
        >
          <template v-slot:right>
            <button class="ss-reset-button login-btn-start" @tap="changeMobileSubmit">
              确认
            </button>
          </template>
        </uni-easyinput>
      </uni-forms-item>
    </uni-forms>

    <!-- 微信独有：读取手机号 -->
    <button
      v-if="'WechatMiniProgram' === sheep.$platform.name"
      class="ss-reset-button type-btn w-400"
      open-type="getPhoneNumber"
      @getphonenumber="getPhoneNumber"
    >
      使用微信手机号
    </button>
  </view>
</template>

<script setup>
  import { computed, ref, reactive, unref } from 'vue';
  import sheep from '@/sheep';
  import { code, mobile } from '@/sheep/validate/form';
  import { closeAuthModal, getSmsCode, getSmsTimer } from '@/sheep/hooks/useModal';
  import UserApi from '@/sheep/api/member/user';

  const changeMobileRef = ref(null);
  const userInfo = computed(() => sheep.$store('user').userInfo);

  // 数据
  const state = reactive({
    isMobileEnd: false, // 手机号输入完毕
    model: {
      mobile: '', // 手机号
      code: '', // 验证码
    },
    rules: {
      code,
      mobile,
    },
  });

  // 处理发送验证码
  async function handleSendSmsCode() {
    if (!state.model.mobile) {
      uni.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }
    
    // 检查手机号是否已被使用
    const { code, data } = await UserApi.checkMobileExists(state.model.mobile);
    if (code === 0 && data === true) {
      // 显示确认对话框
      uni.showModal({
        title: '手机号已注册',
        content: '该手机号已被其他账号使用，继续绑定原账号数据将会被销毁',
        cancelText: '取消',
        confirmText: '继续发送',
        success: function (res) {
          if (res.confirm) {
            // 用户选择继续，发送验证码
            getSmsCode('changeMobile', state.model.mobile);
          }
        }
      });
      return;
    }
    
    // 手机号未被使用，直接发送验证码
    getSmsCode('changeMobile', state.model.mobile);
  }

  // 绑定手机号
  async function changeMobileSubmit() {
    const validate = await unref(changeMobileRef)
      .validate()
      .catch((error) => {
        console.log('error: ', error);
      });
    if (!validate) {
      return;
    }
    
    // 检查手机号是否已被使用
    const { code, data } = await UserApi.checkMobileExists(state.model.mobile);
    if (code === 0 && data === true) {
      // 显示确认对话框
      uni.showModal({
        title: '手机号已注册',
        content: '该手机号已被其他账号使用，继续绑定原账号数据将会被销毁',
        cancelText: '取消',
        confirmText: '继续绑定',
        success: async function (res) {
          if (res.confirm) {
            // 用户选择继续，执行强制绑定操作
            await performForceBind();
          }
        }
      });
      return;
    }
    
    // 手机号未被使用，直接绑定
    await performNormalBind();
  }
  
  // 执行普通绑定
  async function performNormalBind() {
    const { code } = await UserApi.updateUserMobile(state.model);
    if (code !== 0) {
      return;
    }
    sheep.$store('user').getInfo();
    closeAuthModal();
  }
  
  // 执行强制绑定（合并账号）
  async function performForceBind() {
    const { code } = await UserApi.forceBindMobile(state.model);
    if (code !== 0) {
      return;
    }
    sheep.$store('user').getInfo();
    closeAuthModal();
  }

  // 使用微信手机号
  async function getPhoneNumber(e) {
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      return;
    }
    const result = await sheep.$platform.useProvider().bindUserPhoneNumber(e.detail);
    if (result) {
      sheep.$store('user').getInfo();
      closeAuthModal();
    }
  }
</script>

<style lang="scss" scoped>
  @import '../index.scss';

  .w-400 {
    width: 400rpx;
  }
</style>
