<template>
  <easy-navbar :title="classInfo.name || '班级'" backPath='/pages/my/index' />
  <view class="detail-container">
    <view class="class-header">
      <image class="class-icon" :src="sheep.$url.cdn('/course/group.png')" mode="aspectFill" @click="goToEditPage"/>
      <view class="class-info"  @click="goToEditPage">
        <view class="class-title">
          {{ classInfo.name }}         
             <image class="class-edit-icon" :src="sheep.$url.cdn('/group/edit_image.png')"></image>         
        </view>        
        <view class="class-info-stats">
          <view class="stats-item stats-set">
            <image :src="sheep.$url.cdn('/group/set1.png')"></image>
            <span>{{ classInfo.studySetCount || 0 }}</span>
          </view>
          <view class="stats-item stats-course">
            <image :src="sheep.$url.cdn('/group/course2.png')"></image>
            <span>{{ classInfo.lessonCount || 0 }}</span>
          </view>
        </view>
        <view class="class-desc" v-if="classInfo.remark">{{ classInfo.remark }}</view>
      </view>
      <view class="class-switch" @click="goToSwitchClass">
        <view class="switch-link">切换</view><image class="come-icon" :src="sheep.$url.cdn('/my/come.png')"/>
      </view>
    </view>

    <view class="tabs-container">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: activeTabIndex === index }"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <view class="content-container">
      <!-- 课程组件 -->
      <CourseList v-if="activeTabIndex === 0" :class-id="classId" />

      <!-- 教案组件  -->
      <TeachingPlan v-if="activeTabIndex === 1" :class-id="classId" />

      <!-- 学习集组件  -->
      <WordSet v-if="activeTabIndex === 2" :class-id="classId" ref="wordSetRef" />

      <!-- 成员组件 -->
      <!-- <Member v-if="activeTabIndex === 3" :class-id="classId" /> -->
    </view>

  </view>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import GroupApi from '@/sheep/api/group';
  import { onLoad, onShow } from '@dcloudio/uni-app';
  import sheep from '@/sheep';

  // 引入组件
  import CourseList from '@/pages/group/detail/course/course.vue';
  import TeachingPlan from '@/pages/group/detail/teaching-plan/teaching-plan.vue';
  import WordSet from '@/pages/group/detail/set/word-set.vue';
  import Member from '@/pages/group/detail/member/member.vue';
  

  // 本地存储的班级ID的key
  const LAST_GROUP_ID_KEY = 'last_group_id';

  // 标记数据是否加载完成
  const dataLoaded = ref(false);
  // 班级ID
  const classId = ref();
  // 班级信息
  const classInfo = ref({
    name: '',
    remark: '',
    studySetCount: 0,
    courseCount: 0,
    memberCount: 0,
    isCreator: false, // 是否为创建者
  });

  // 是否为创建者
  const isCreator = ref(false);
  
  // 学习集组件引用
  const wordSetRef = ref(null);

  onLoad((options) => {
    // 重置所有数据状态
    resetPageData();
    
    classId.value = options.id;
    // 保存当前班级ID到本地存储
    if (classId && classId.value) {
      uni.setStorageSync(LAST_GROUP_ID_KEY, classId.value);
    }
    
    // 监听学习集更新事件
    uni.$on('refreshWordSets', handleRefreshWordSets);
  });
  
  // 处理学习集更新事件
  const handleRefreshWordSets = () => {
    loadClassInfo();
    
    // 如果当前是学习集标签页，刷新学习集数据
    if (activeTabIndex.value === 2 && wordSetRef.value) {
      wordSetRef.value.loadStudySets();
    }
  };
  
  // 组件卸载时移除事件监听
  onUnmounted(() => {
    uni.$off('refreshWordSets', handleRefreshWordSets);
  });
  
  // 每次页面显示时刷新数据
  onShow(() => {
    if (classId.value) {
      loadClassInfo();
      
      // 如果当前是学习集标签页，刷新学习集数据
      if (activeTabIndex.value === 2 && wordSetRef.value) {
        wordSetRef.value.loadStudySets();
      }
    }
  });

  // 选项卡数据
  const tabs = [
    { name: '课程', component: 'course' },
    { name: '教案', component: 'teaching-plan' },
    { name: '学习集', component: 'word-set-study' }
  ];

  // 当前活跃选项卡索引
  const activeTabIndex = ref(0);

  // 切换选项卡
  const switchTab = (index) => {
    activeTabIndex.value = index;
  };

  // 跳转到班级切换页面
  const goToSwitchClass = () => {
    sheep.$router.go('/pages/group/switch');
  };

  // 跳转到班级编辑页面
  const goToEditPage = () => {
    sheep.$router.go(`/pages/group/edit?id=${classId.value}`);
  };

  // 重置页面数据
  const resetPageData = () => {
    dataLoaded.value = false;
    activeTabIndex.value = 0;
    classInfo.value = {
      name: '',
      remark: '',
      studySetCount: 0,
      courseCount: 0,
      memberCount: 0,
      isCreator: false,
    };
    isCreator.value = false;
  };

  // 获取班级ID并加载班级详情
  const loadClassInfo = async () => {
    // 获取班级详情
    const { code, data } = await GroupApi.getMyGroup(classId.value);

    if (code !== 0) {
      // 班级不存在或无权限访问
      uni.showToast({
        title: '班级不存在或已被删除',
        icon: 'none',
        duration: 2000
      });
      // 清除缓存并跳转到班级切换页
      uni.removeStorageSync(LAST_GROUP_ID_KEY);
      sheep.$router.go('/pages/group/switch', {}, { redirect: true });
      return;
    }

    classInfo.value = data;
    isCreator.value = data.isCreator || false;
    dataLoaded.value = true;
  };

  // 页面加载时获取班级信息
  onMounted(() => {
    loadClassInfo();
  });
</script>

<style lang="scss" scoped>
  .detail-container {
    min-height: 100vh;
    background-color: #F8FCFF;
    position: relative;
  }

  .tabs-container {
    margin-bottom: 20rpx;
    display: flex;
    padding: 4rpx 40rpx;
    border-bottom: 1px solid #f0f0f0;
  }

  .tab-item {
    flex: 1;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30rpx;
    color: #666;
    position: relative;

    &.active {
      color: #2196f3;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 30%;
        width: 40%;
        height: 4rpx;
        background-color: #2196f3;
        border-radius: 2rpx;
      }
    }
  }

  .content-container {
    min-height: 80vh;
  }

  .class-header {
    display: flex;
    align-items: center;
    background: #fff;
    height: 178rpx;
    padding: 0 32rpx;
    border-radius: 0 0 32rpx 32rpx;
    position: relative;
    margin-bottom: 16rpx;
  }

  .class-badge {
    width: 36rpx;
    height: 36rpx;
    background: #2C2C2C;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    margin-right: 16rpx;
    padding-top: 4rpx;
    flex-shrink: 0;
  }

  .class-icon {
    width: 107rpx;
    height: 107rpx;
    border-radius: 16rpx;
    margin-right: 20rpx;
    background: #f2f6fa;
    flex-shrink: 0;
  }

  .class-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
  }

  .class-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #222;
    margin-bottom: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
  }
  
  .class-edit-icon {
    margin-left: 14rpx;
    width: 30rpx;
    height: 30rpx;   
  }

  .class-info-stats {
    display: flex;
    align-items: center;
    margin-top: 6rpx;
    gap: 12rpx;
  }

  .stats-item {
    width: 82rpx;
    height: 35rpx;
    border-radius: 7rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #666;
  }

  .stats-set {
    background-color: #F1F5FF;
    
  }

  .stats-course {
    background-color: #F3FCFF;
  }

  .stats-item image {
    width: 20rpx;
    height: 20rpx;
    margin-right: 4rpx;
  }

  .class-desc {
    font-size: 24rpx;
    color: #999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 4rpx;
  }

  .class-switch {
    width: 158rpx;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .switch-link {
    position: absolute;
    right: 32rpx;
    min-width: 80rpx;
    height: auto;
    font-weight: 500;
    font-size: 29rpx;
    color: #222222;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-family: 'PingFang SC', 'PingFang-SC-Medium', 'Microsoft YaHei', Arial, sans-serif;
    flex-direction: row;
    white-space: nowrap;
  }

  .switch-link .arrow {
    font-size: 32rpx;
    margin-left: 2rpx;
    font-weight: bold;
  }

  .come-icon {
    width: 14rpx;
    height: 24rpx;
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    flex-shrink: 0;
  }
</style>
