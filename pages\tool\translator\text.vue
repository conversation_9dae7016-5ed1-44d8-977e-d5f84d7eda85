<template>
    <view class="container">
        <easy-navbar title="文本翻译" />

        <scroll-view scroll-y class="main-content"  scroll-with-animation :scroll-top="scrollTop">
            <!-- 输入框区域 -->
            <view class="input-box">
                <textarea class="input-area" v-model="sourceText" placeholder="输入单词或句子开始翻译"
                    placeholder-class="placeholder-style" @input="detectLanguage" maxlength="2000" />
            </view>

            <!-- 语言切换和翻译按钮 -->
            <view class="language-bar">
                <view class="language-text" @click="toggleTranslationMode">
                    <text>{{ fromLanguage }}</text>
                    <text class="direction-icon">⇌</text>
                    <text>{{ toLanguage }}</text>
                </view>
                <button class="translate-btn" :disabled="isTranslating || typing" @click="translate">
                    {{ isTranslating ? '翻译中...' : '翻译' }}
                </button>
            </view>

            <!-- 翻译结果区域 -->
            <view class="translate-result" v-if="showTranslation" id="translate-result-section">
                <text class="result-title">翻译结果：</text>
                <view class="result-content">
                    <text selectable>{{ translatedText }}<text class="cursor" v-if="typing"></text></text>
                  <!-- <template v-if="typing">
                    <text selectable>{{ translatedText }}<text class="cursor"></text></text>
                  </template>
                  <template v-else>
                    <text selectable>{{ translatedText }}</text>
                  </template> -->
                </view>

                <!-- 复制和播放按钮 -->
                <view class="action-buttons">
                    <view class="action-group">
                        <button class="copy-btn" @click="handleCopyTranslation">
                            <image class="btn-icon" :src="sheep.$url.cdn(ImagePath.COPY_ICON)" mode="heightFix" />
                            <text class="btn-text">一键复制</text>
                        </button>
                        <button class="play-btn" @click="playAllTranslation">
                            <image class="btn-icon" :src="getPlayIconSource()" mode="heightFix" />
                            <text class="btn-text">{{ isPlaying ? '暂停播放' : '播放全文' }}</text>
                        </button>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script setup>
    import { ref, computed } from 'vue'
    import { onUnload, onHide, onLoad } from '@dcloudio/uni-app'
    import sheep from '@/sheep'
    import TranslateApi from '@/sheep/api/translate'
    import {
      getLanguageCodeByLabel,
      LanguageEnum,
      ImagePath,
      getTtsConfig,
      TtsTypeEnum, getTextLanguage,
    } from '@/sheep/util/language-detector';
    import { AiModelEnum } from '@/sheep/util/const'
    import ConversationApi from '@/sheep/api/text-optimizer/conversation'
    import NlsApi from '@/sheep/api/voice/nls'
    import { createStreamHandler } from '@/sheep/util/stream-parser'
    import { debounce } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common'


    const userInfo = computed(() => sheep.$store('user').userInfo)

    // 页面卸载时清理
    onUnload(() => {
        stopAudio() // 停止音频播放
        clearTypingEffect() // 清理打字机效果
    })
    onHide(() => {
        stopAudio()
        clearTypingEffect() // 清理打字机效果
    })

    // 状态管理
    const conversationId = ref('')
    const sourceText = ref('') // 输入的源文本
    const translatedText = ref('') // 翻译后的文本
    const pendingText = ref('') // 打字机待输出内容
    const typing = ref(false) // 是否正在打字机动画
    let typingTimer = null // 打字机定时器
    const showTranslation = ref(false) // 是否显示翻译结果
    const fromLanguage = ref(LanguageEnum.ZH.label) // 源语言
    const toLanguage = ref(LanguageEnum.TH.label) // 目标语言
    const isPlaying = ref(false) // 是否正在播放音频
    let audioContext = null // 音频上下文

    onLoad(async () => {

        // 读取缓存中的会话ID
        const cachedConversationId = uni.getStorageSync('translate_conversation_id')
        if (cachedConversationId) {
            conversationId.value = cachedConversationId
        } else {
            // 没有缓存的会话ID，先获取会话列表
            await loadLatestConversation()
        }
    })

    // 加载最新的会话
    const loadLatestConversation = async () => {
        const { code, data } = await ConversationApi.getConversationPageByModelName({
            pageNo: 1,
            pageSize: 10,
            userId: userInfo.value.id,
            modelName: AiModelEnum.TEXT_TRANSLATION.name,
        })

        const conversationData = data.list || []

        if (conversationData.length > 0) {
            const latestConversation = conversationData[0]
            conversationId.value = latestConversation.id.toString()
        } else {
            // 创建会话
            const res = await ConversationApi.createConversationByName(AiModelEnum.TEXT_TRANSLATION.name)
            if (res.code !== 0) {
                return
            }
            conversationId.value = res.data.toString()
        }

    }

    // 获取播放图标路径
    const getPlayIconSource = () => {
        return sheep.$url.cdn(isPlaying.value ? ImagePath.PLAY_ICON.WHITE_ANIMATED : ImagePath.PLAY_ICON
            .WHITE_STATIC) // 播放中显示gif，否则显示png
    }

    // 切换翻译语言方向
    const toggleTranslationMode = () => {
        [fromLanguage.value, toLanguage.value] = [toLanguage.value, fromLanguage.value] // 交换源语言和目标语言
    }

    // 翻译
    const isTranslating = ref(false) // 添加翻译状态标记

    const getTypingInterval = (len) => {
      if (len >= 100) return 10;
      if (len <= 20) return 80;
      return 80 - Math.floor((len - 20) * (70 / 80));
    }
    // 1. 优化打字机动画，每次加2个字，减少页面重绘
    function startTyping() {
      if (typingTimer) {
        clearInterval(typingTimer);
        typingTimer = null;
      }
      if (!pendingText.value || pendingText.value.length === 0) {
        typing.value = false;
        return;
      }
      typing.value = true;
      const interval = getTypingInterval(pendingText.value.length);
      typingTimer = setInterval(() => {
        if (pendingText.value && pendingText.value.length > 0) {
          // 每次加2个字
          const toAdd = pendingText.value.slice(0, 2);
          translatedText.value += toAdd;
          pendingText.value = pendingText.value.slice(toAdd.length);
          scrollToBottom(); // 新增：每次输出后滚动到底部
          // 判断是否是最后一批字符
          if (pendingText.value.length === 0) {
            typing.value = false;
            clearInterval(typingTimer);
            typingTimer = null;
          }
          // 动态调整速度
          const newInterval = getTypingInterval(pendingText.value.length);
          if (newInterval !== interval && pendingText.value.length > 0) {
            clearInterval(typingTimer);
            typingTimer = null;
            startTyping();
          }
        }
      }, interval);
    }

    // 2. 清理定时器和内容，防止残影
    function clearTypingEffect() {
      if (typingTimer) {
        clearInterval(typingTimer);
        typingTimer = null;
      }
      typing.value = false;
      translatedText.value = '';
      pendingText.value = '';
    }

    // 3. 在翻译前、页面卸载、重新翻译等场景调用 clearTypingEffect
    // 在 translate、onUnload、onHide、重新翻译前等地方加 clearTypingEffect()
    const translate = async () => {
        if (!sourceText.value.trim()) {
            sheep.$helper.toast('请输入要翻译的内容')
            return
        }
        clearTypingEffect() // 新增
        const params = {
            sourceText: sourceText.value,
            sourceLang: getLanguageCodeByLabel(fromLanguage.value),
            targetLang: getLanguageCodeByLabel(toLanguage.value),
            conversationId: conversationId.value,
        }

        // 调用翻译API
        showTranslation.value = true // 显示翻译区域
        isTranslating.value = true
        typing.value = false
        
        // 显示加载提示
        uni.showLoading({
            title: '翻译中...',
            mask: true
        });
        
        // 用于跟踪是否已收到第一块数据
        let firstChunkReceived = false;

        TranslateApi.textTranslateStream(params, {
            enableChunked: true,
            onChunkReceived: createStreamHandler((content, jsonData) => {
                if (jsonData.data.done) {
                    isTranslating.value = false // 翻译完成后重置状态
                }
                // 内容追加到pendingText，由打字机定时器逐步输出
                pendingText.value += content
                if (!typing.value) {
                  startTyping();
                }
                if (!firstChunkReceived) {
                    uni.hideLoading();
                    firstChunkReceived = true;
                }
            }, {
                // 配置选项
                silent: false, // 是否静默处理错误
                contentPath: 'data.receive.content', // 内容在JSON数据中的路径
                successCode: 0 // 成功状态码
            })
        }).catch(error => {
            console.error('翻译请求失败:', error);
            isTranslating.value = false;
            sheep.$helper.toast('翻译请求失败，请重试');
        });

        // uni.hideLoading();
    }

    // 复制翻译结果
    const handleCopyTranslation = () => {
        if (!translatedText.value) return
        sheep.$helper.copyText(translatedText.value) // 复制文本到剪贴板
    }

    // 播放全部文本
    const _playAllTranslation = async () => {
        if (!translatedText.value) return

        // 如果正在播放，暂停播放
        if (isPlaying.value && audioContext) {
            audioContext.pause()
            isPlaying.value = false
            return
        }

        // 如果有暂停的音频，继续播放
        if (audioContext && !isPlaying.value) {
            audioContext.play()
            isPlaying.value = true
            return
        }

        // 停止之前的音频（如果有）
        stopAudio()
        
        // 开始播放TTS语音，但不立即设置播放状态
        await commonPlayText(translatedText.value)
    }
    
    // 使用防抖包装播放全文函数
    const playAllTranslation = debounce(_playAllTranslation, 500, true)

    // 停止音频播放
    const stopAudio = () => {
        if (audioContext) {
            audioContext.stop()
            audioContext.destroy()
            audioContext = null
        }
        isPlaying.value = false // 重置播放状态
    }

    // 通用播放文本函数
    const commonPlayText = async text => {
        let ttsConfig = getTtsConfig(text, TtsTypeEnum.OPENAI)
        if (!ttsConfig) {
            stopAudio()
            return
        }

        const params = {
            text,
            speaker: ttsConfig.speaker,
            pitchRate: ttsConfig.pitchRate,
            displayCaptions: false,
        }

      const res = await NlsApi.ttsOpenAI(params); // 文本转语音

      if (res?.msg) {
          stopAudio()
          return
      }

      // 接口返回数据后才设置播放状态
      isPlaying.value = true

      const manager = uni.getFileSystemManager()
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.mp3`
      await new Promise((resolve, reject) => {
          manager.writeFile({
              filePath: tempFilePath,
              data: res,
              encoding: 'binary',
              success: resolve,
              fail: reject,
          })
      })
      await playFromUrl(tempFilePath) // 播放音频文件
    }

    // 从URL播放音频
    const playFromUrl = audioUrl => {
        return new Promise((resolve, reject) => {
            try {
                audioContext = uni.createInnerAudioContext()

                if (!audioContext) {
                    reject(new Error('无法创建音频上下文'))
                    return
                }

                audioContext.src = audioUrl

                audioContext.onEnded(() => {
                    stopAudio() // 播放结束
                    resolve()
                })

                audioContext.onError(() => {
                    stopAudio() // 播放错误
                    reject(new Error('音频播放失败'))
                })

                audioContext.play() // 开始播放
            } catch (error) {
                stopAudio()
                reject(error)
            }
        })
    }

    // 判断输入文本的语言
    const detectLanguage = () => {
        const detectedLabel = getTextLanguage(sourceText.value)
        if (!detectedLabel) return

        // 设置中译泰
        if (detectedLabel === LanguageEnum.ZH) {
            fromLanguage.value = LanguageEnum.ZH.label
            toLanguage.value = LanguageEnum.TH.label
        }
        // 设置泰译中
        else if (detectedLabel === LanguageEnum.TH) {
            fromLanguage.value = LanguageEnum.TH.label
            toLanguage.value = LanguageEnum.ZH.label
        }
    }

    const scrollTop = ref(0);
    function scrollToBottom() {
      // 设置一个足够大的值即可滚动到底部
      scrollTop.value = scrollTop.value+10;
    }
</script>

<style scoped>
    .container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        background-color: #ffffff;
    }

    .main-content {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 60rpx;
    }

    .input-box {
        position: relative;
        height: 280rpx;  /* 增加高度 */
        margin: 40rpx 20rpx;
        background: #ffffff;
        border: 3.8rpx solid #4184ff;
        border-radius: 16rpx;
    }

    .input-area {
        width: 100%;
        height: 260rpx;  /* 增加高度 */
        padding: 20rpx;
        box-sizing: border-box;
        font-size: 34rpx;  /* 增加字体大小 */
    }

    .placeholder-style {
        color: #999;
        font-size: 34rpx;  /* 增加字体大小 */
    }

    /* 语言切换和翻译按钮 */
    .language-bar {
        width: 100%;
        height: 48rpx;
        display: flex;
        gap: 30rpx;
        justify-content: center;
        align-items: center;
        margin: 20rpx 0 30rpx;
    }

    .language-bar .language-text {
        display: flex;
        align-items: center;
    }

    .language-bar .language-text .direction-icon {
        margin: 0 6rpx;
    }

    .language-bar .translate-btn {
        font-size: 32rpx;
        height: 62rpx;
        line-height: 62rpx;
        width: 122rpx;
        background-color: #46ADF0;
        color: #ffffff;
        margin: 0;
    }

    .translate-btn[disabled] {
        background-color: #ccc;
        opacity: 0.7;
    }

    /* 结果内容样式 */
    .translate-result {
        display: flex;
        flex-direction: column;
        padding: 20rpx;
        background-color: #F8F8F8;
        border: 1px solid #e0e0e0;
        border-radius: 10rpx;
        margin: 20rpx;
    }

    .translate-result {
        margin-top: 0;
        margin-bottom: 30rpx;
    }

    .result-title {
        font-size: 34rpx;  /* 增加字体大小 */
        font-weight: bold;
        color: #005Aff;
    }

    .result-content {
        font-size: 34rpx;  /* 增加字体大小 */
        margin-top: 22rpx;
        color: #000000;
        display: flex;
        align-items: center;
    }

    /* 按钮区域 */
    .action-buttons {
        width: 100%;
        margin: 46rpx 0 20rpx;
        display: flex;
        justify-content: center;
    }

    .action-buttons .action-group {
        display: flex;
        gap: 30rpx;
    }

    /* 按钮样式 */
    .copy-btn,
    .play-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 35rpx;
    }

    .copy-btn .btn-icon,
    .play-btn .btn-icon {
        height: 24rpx;
        margin-right: 8rpx;
    }

    .copy-btn .btn-text,
    .play-btn .btn-text {
        font-size: 30rpx;  /* 增加字体大小 */
    }

    .copy-btn {
        background-color: #ffffff;
        border: 1px solid #ddd;
        color: #000000;
    }

    .play-btn {
        background-color: #46ADF0;
        color: #ffffff;
    }
    .cursor {
      display: inline-block;
      width: 6rpx;
      height: 36rpx;
      background: skyblue;
      vertical-align: bottom;
      margin-left: 2px;
      animation: blink 1.3s infinite;
    }
    @keyframes blink {
      0%, 100% { background: skyblue; }
      50% { background: transparent; opacity: 0; }
    }
</style>
