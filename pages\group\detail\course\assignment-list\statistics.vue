<template>
  <view class="statistics-container">
    <!-- 顶部背景区域 -->
    <view class="header-bg" :style="{ backgroundImage: `url(${headerBgImage})` }">
      <!-- 顶部导航栏 -->
      <view
        class="navbar-area"
        :style="{ paddingTop: `${paddingTop}px`, paddingBottom: `${paddingBottom}px` }"
      >
        <!-- 左侧区域：返回按钮 -->
        <view class="left">
          <image
            class="back-icon"
            mode="widthFix"
            :src="sheep.$url.cdn('/common/back.png')"
            @click="goBack"
          />
        </view>

        <!-- 中间标题 -->
        <view class="title">
          <text>{{ assignmentInfo.title || '作业统计' }}</text>
        </view>

        <!-- 右侧区域 -->
        <view class="right">
          <view class="more-icon">
            <text class="iconfont icon-more-dot"></text>
          </view>
        </view>
      </view>

      <!-- 平均分展示 -->
      <view class="average-score">
        <view class="score-number">{{ averageScore }}<text class="score-unit">分</text></view>
        <view class="score-label">平均分</view>
      </view>
    </view>

    <!-- 客观题正确率区域 -->
    <view class="correctness-container">
      <view class="correctness-title-container">
        <image class="correctness-title-bg" :src="correctnessBgImage" mode="aspectFit"></image>
        <text class="correctness-title-text">答题正确率</text>
      </view>
      
      <view class="questions-container">
        <!-- 显示所有题目，不仅仅是客观题 -->
        <view 
          class="question-item" 
          v-for="(item, index) in filteredQuestions" 
          :key="index"
        >
          <view class="question-info">
            <view class="question-index">{{ index + 1 }}.</view>
            <view class="question-type" :style="getQuestionTypeStyle(item.questionType)">{{ getQuestionTypeText(item.questionType) }}</view>
            <text class="question-title">{{ formatStem(item.stem) }}</text>
          </view>
          <view class="question-rate">{{ formatRate(item.correctRate) }}</view>
        </view>

        <!-- 完全没有题目时显示空状态 -->
        <view v-if="questionsList.length === 0" class="empty-container">
          <s-empty text="暂无题目数据" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import sheep from '@/sheep';
import AssignmentApi from '@/sheep/api/course/assignment';

// 图片资源
const headerBgImage = sheep.$url.cdn('/course/job-statistics.png');
const correctnessBgImage = sheep.$url.cdn('/course/correctness.png');

// 导航栏相关参数
const paddingTop = ref(0);
const navbarHeight = ref(0);
const paddingBottom = ref(10);

// 作业ID和班级ID
const assignmentReleaseId = ref('');
const groupId = ref('');

// 作业信息
const assignmentInfo = reactive({
  id: '',
  title: '作业统计',
  totalScore: 100,
});

// 平均分
const averageScore = ref(0);

// 客观题列表
const questionsList = ref([]);

// 过滤后的题目列表（显示客观题）
const filteredQuestions = computed(() => {
  return questionsList.value.filter(q => isObjectiveQuestion(q.questionType));
});

// 根据题型判断是否为客观题（判断、单选、多选）
const isObjectiveQuestion = (type) => {
  return type === 0 || type === 1 || type === 2;
};

// 获取题型文本
const getQuestionTypeText = (type) => {
  const typeMap = {
    0: '判断题',
    1: '单选题',
    2: '多选题',
    3: '填空题',
    4: '简答题',
    5: '互译题'
  };
  return typeMap[type] || '未知题型';
};

// 获取题型样式
const getQuestionTypeStyle = (type) => {
  const styleMap = {
    0: { background: '#E7E9FF', color: '#9D65DB' }, // 判断题
    1: { background: '#E3F5FF', color: '#3DB3F2' }, // 单选题
    2: { background: '#E7FFF5', color: '#2DCF7D' }, // 多选题
    3: { background: '#FFF0FD', color: '#CF2DA3' }, // 填空题
    4: { background: '#FFFCF0', color: '#EEA931' }, // 简答题
    5: { background: '#FFF5F0', color: '#EE6231' }, // 互译题
  };
  return styleMap[type] || { background: '#E3F5FF', color: '#3DB3F2' }; // 默认样式
};

// 格式化正确率
const formatRate = (rate) => {
  if (rate === undefined || rate === null) return '0%';
  return `${Math.round(rate)}%`;
};

// 格式化题干文本（截取长度）
const formatStem = (stem) => {
  if (!stem) return '无题干';
  return stem.length > 15 ? stem.substring(0, 15) + '...' : stem;
};

// 获取URL参数
const getParams = () => {
  // @ts-ignore
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.options) {
    assignmentReleaseId.value = currentPage.options.assignmentReleaseId || '';
    groupId.value = currentPage.options.groupId || '';
  }
};

// 获取作业统计数据
const getStatisticsData = async () => {
  if (!assignmentReleaseId.value || !groupId.value) {
    return;
  }

  const params = {
    assignmentReleaseId: assignmentReleaseId.value,
    groupId: groupId.value
  };

  const res: any = await AssignmentApi.getAssignmentAnswerStatistics(params);

  if (res.code === 0 && res.data) {
    // 更新平均分
    averageScore.value = res.data.averageScore || 0;
    // 更新题目列表
    questionsList.value = res.data.questionList || [];
  } else {
    averageScore.value = 0;
    questionsList.value = [];
    sheep.$helper.toast('获取统计数据失败');
  }
};

// 返回上一页
const goBack = () => {
  sheep.$router.back();
};

onMounted(() => {
  // 获取URL参数
  getParams();
  
  // 获取作业统计数据
  getStatisticsData();
  
  // #ifdef MP-WEIXIN
  // 获取胶囊按钮信息
  try {
    // @ts-ignore
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    // 调整导航栏高度，使其能容纳胶囊按钮下方的标题
    paddingTop.value = menuButtonInfo.top;
    // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
    navbarHeight.value = paddingTop.value + 35;
  } catch (e) {
    // 获取状态栏高度
    const systemInfo = sheep.$helper.sys();
    paddingTop.value = systemInfo.marginTop || 20;
    navbarHeight.value = paddingTop.value + 35;
  }
  // #endif

  // #ifndef MP-WEIXIN
  // 获取状态栏高度
  const systemInfo = sheep.$helper.sys();
  paddingTop.value = systemInfo.marginTop;
  // 计算导航栏总高度 = 顶部内边距 + 导航栏自身高度(35px)
  navbarHeight.value = paddingTop.value + 35;
  // #endif
});
</script>

<style scoped lang="scss">
.statistics-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

.empty-container {
  padding: 50rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-bg {
  height: 500rpx;
  background-size: cover;
  background-position: center;
  position: relative;
}

.navbar-area {
  display: flex;
  align-items: center;
  height: 35px;
  width: 100%;
  position: relative;
  z-index: 100;
}

.left {
  position: absolute;
  left: 15px;
  display: flex;
  align-items: center;
  z-index: 101;

  .back-icon {
    width: 19rpx;
    padding: 10rpx;
    filter: brightness(10);
  }
}

.right {
  position: absolute;
  right: 15px;
  display: flex;
  align-items: center;
  z-index: 101;
}

.title {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}

.more-icon {
  width: 60rpx;
  text-align: right;
  font-size: 40rpx;
  color: #fff;
}

.average-score {
  position: absolute;
  top: 213rpx;
  left: 74rpx;
  color: #fff;
  z-index: 10;

  .score-number {
    font-weight: bold;
    line-height: 1;
    font-family: FZHanZhenGuangBiaoS-GB;
    font-size: 125rpx;
    color: #FEFEFE;
    text-shadow: 1rpx 1rpx 12rpx rgba(19,115,181,0.42);

    .score-unit {
      font-size: 70rpx;
      font-weight: normal;
    }
  }

  .score-label {
    font-size: 36rpx;
    margin-top: 10rpx;
  }
}

.correctness-container {
  margin: -60rpx 30rpx 40rpx;
  position: relative;
  z-index: 20;
}

.correctness-title-container {
  position: relative;
  height: 70rpx;
  margin-bottom: 22rpx;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.correctness-title-bg {
  width: 277rpx;
  height: 87rpx;
  position: absolute;
  left: 0;
  top: 0;
}

.correctness-title-text {
  position: absolute;
  left: 30rpx;
  top: 20rpx;
  z-index: 10;

  height: 32rpx;
  font-family: FZHZGBJW--GB1-0;
  font-weight: 400;
  font-size: 33rpx;
  color: #FFFFFF;
  text-shadow: 1rpx 2rpx 4rpx rgba(21,152,151,0.33);

  display: flex;
  justify-content: center;
  align-items: center;
}

.questions-container {
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 30rpx;
  margin-top: -40rpx;
  position: relative;
  z-index: 0;
}

.question-item {
  height: 109rpx;
  background: #F9F9F9;
  border-radius: 18rpx;

  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 23rpx;
  margin: 10rpx 29rpx;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.question-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.question-index {
  margin-right: 10rpx;

  width: 21rpx;
  height: 29rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #3D3D3D;

  display: flex;
  justify-content: center;
  align-items: center;
}

.question-type {
  width: 93rpx;
  height: 43rpx;
  border-radius: 6rpx;

  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;

  font-family: PingFang SC;
  font-weight: 500;
  font-size: 25rpx;
}

.question-title {
  height: 29rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #3D3D3D;
}

.question-rate {
  width: 77rpx;
  height: 29rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #FF6B6B;
}
</style> 