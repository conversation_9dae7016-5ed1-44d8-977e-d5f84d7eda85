import request from '@/sheep/request';

const SetCollectionApi = {
  // 创建学习集收藏
  createCollection: (data) => {
    return request({
      url: `/learning/word-set-collection/create`,
      method: 'POST',
      data,
	  custom: {
	    loading: false, // 不用加载中
	  },
    })
  },
  
  // 取消学习集收藏
  cancelCollection: (id) => {
    return request({
      url: `/learning/word-set-collection/cancel`,
      method: 'POST',
      params: { id },
	  custom: {
	    loading: false, // 不用加载中
	  },
    })
  },
  
  // 检查学习集是否已收藏
  checkCollectionStatus: (setId) => {
    return request({
      url: `/learning/word-set-collection/check-status`,
      method: 'GET',
      params: { setId }
    })
  }
};

export default SetCollectionApi; 