import request from '@/sheep/request';

// 智能扩写 API
const ExpandTextApi = {
  expandText: (data, config) => {
    return request({
      url: '/ai/text-optimizer/expand-text-stream',
      method: 'POST',
      data,
      custom: {
        showLoading: false
      },
      ...config
    });
  },

  // 获取历史记录列表
  getResultPage: (data) => {
    return request({
      url: '/ai/text-optimizer/expand-stream-history',
      method: 'POST',
      data,
      custom: {
        showLoading: false,
      },
    });
  },

    // 清空视频提炼历史记录
  clearHistory: () => {
      return request({
        url: '/ai/text-optimizer/clear-expand-history',
        method: 'DELETE',
        custom: {
          successMsg: '历史记录已清空',
        },
      });
  },
    
};

export default ExpandTextApi;
