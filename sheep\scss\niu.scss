@for $i from 1 through 10 {
    .niu-ellipsis-#{$i} {
      display: -webkit-box; /* 设置为box容器 */
      -webkit-box-orient: vertical; /* 规定框中的子元素排列方式 */
      -webkit-line-clamp: $i; /* 每个类生成对应的行数 */
      overflow: hidden; /* 超出的总体：隐藏 */
      text-overflow: ellipsis; /* 超出文字：省略号 */
      word-break: break-all; /* 英文单词换行，强制截断字符 */
      word-wrap: break-word; /* 英文换行，适应空间 */
      white-space: normal; /* 默认值，合并所有空白符 */
      line-height: 20px; /* 根据需要调整行高 */
    }
  }