<template>
  <view class="search-bar">
    <view class="search-input">
      <image :src="sheep.$url.cdn('/index/search-icon.png')" mode="widthFix" />
      <input type="text" placeholder="搜索学习集..." @confirm="handleSearch" v-model="keyword" />
      <button class="explore" @click="handleSearch">搜索</button>
    </view>
  </view>
</template>

<script setup>
  import { onShow } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import { ref } from 'vue';
  import { SearchApi } from '@/sheep/api/set/search';

  const keyword = ref('');

  const handleSearch = async () => {
    if (!keyword.value.trim()) {
      sheep.$helper.toast('请输入搜索内容');
      return;
    }

    const { code, data } = await SearchApi.learning({
      keyword: keyword.value,
      pageNo: 1,
      pageSize: 8,
    });

    if (code !== 0) {
      return;
    }

    if (!data.list.length) {
      sheep.$helper.toast('无搜索结果');
      return;
    }
    uni.setStorageSync('search_learning_result', data)
    sheep.$router.go(`/pages/index/search/list?keyword=${keyword.value}`);
  };

  onShow(() => {
    keyword.value = '';
  })
</script>

<style scoped lang="scss">
  .search-bar {
    padding: 24rpx 0;
    background-color: #fff;
    position: relative;
    z-index: 1;
  }

  .search-bar .search-input {
    background-color: #f8f9fa;
    border: 2rpx solid #eaecef;
    border-radius: 48rpx;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    position: relative;
  }

  .search-bar .search-input image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
    opacity: 0.6;
  }

  .search-bar .search-input input {
    border: none;
    background: none;
    font-size: 30rpx;
    color: #333;
    width: 100%;
    padding-right: 200rpx;
  }

  .search-bar .explore {
    background-color: #46adf0;
    color: #fff;
    padding: 0 40rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    border: none;
  }
</style>
