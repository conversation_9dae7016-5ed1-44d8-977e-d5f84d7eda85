<template>
  <view class="search-box">
    <view class="search-input">
      <image class="search-icon" :src="searchIcon" mode="aspectFit"></image>
      <input
        type="text"
        :placeholder="placeholder"
        placeholder-class="placeholder"
        v-model="inputValue"
        @confirm="emitSearch"
        @blur="emitSearch"
      />
    </view>
    <text class="search-btn" @tap="emitFilter">筛选</text>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';
import sheep from '@/sheep';

const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: '搜索'
  }
});
const emit = defineEmits(['update:modelValue', 'search', 'filter']);

const inputValue = ref(props.modelValue || '');

watch(() => props.modelValue, val => {
  inputValue.value = val;
});
watch(inputValue, val => {
  emit('update:modelValue', val);
});

const searchIcon = sheep.$url.cdn('/index/search-icon.png');

function emitSearch() {
  emit('search', inputValue.value);
}

function emitFilter() {
  emit('filter');
}
</script>

<style scoped lang="scss">
.search-box {
  display: flex;
  align-items: center;
  padding: 10rpx 30rpx 23rpx 30rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #f5f5f5;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #F5F5F5;
    border-radius: 40rpx;
    padding: 15rpx 20rpx;
    margin-right: 20rpx;

    .search-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }

    input {
      flex: 1;
      font-size: 28rpx;
    }

    .placeholder {
      color: #999;
    }
  }

  .search-btn {
    font-size: 28rpx;
    color: #3DB3F2;
  }
}
</style>