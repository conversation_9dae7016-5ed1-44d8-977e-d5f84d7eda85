/**
 * 计算两个时间之间的人性化距离描述
 * @param date 要计算的时间
 * @param baseDate 基准时间（默认为当前时间）
 * @returns 人性化的时间距离描述
 */
export function formatTimeDistance(date: Date | string, baseDate: Date = new Date()): string {
  const target = date instanceof Date ? date : new Date(date);
  const now = baseDate;

  const diff = Math.floor((now.getTime() - target.getTime()) / 1000); // 转换为秒

  // 小于1分钟
  if (diff < 60) {
    return '刚刚';
  }

  // 小于1小时
  if (diff < 3600) {
    const minutes = Math.floor(diff / 60);
    return `${minutes}分钟前`;
  }

  // 小于24小时
  if (diff < 86400) {
    const hours = Math.floor(diff / 3600);
    return `${hours}小时前`;
  }

  // 判断是否是昨天
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (
    target.getDate() === yesterday.getDate() &&
    target.getMonth() === yesterday.getMonth() &&
    target.getFullYear() === yesterday.getFullYear()
  ) {
    return '昨天';
  }

  // 小于7天
  if (diff < 604800) {
    const days = Math.floor(diff / 86400);
    return `${days}天前`;
  }

  // 小于30天
  if (diff < 2592000) {
    const weeks = Math.floor(diff / 604800);
    return `${weeks}周前`;
  }

  // 小于365天
  if (diff < 31536000) {
    const months = Math.floor(diff / 2592000);
    return `${months}个月前`;
  }

  // 大于等于365天
  const years = Math.floor(diff / 31536000);
  return `${years}年前`;
}

/**
 * 格式化时间
 * @param seconds 秒数
 * @returns 格式化后的时间字符串 (mm:ss)
 */
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}