<template>
  <view class="container">
    <easy-navbar title="" />

    <view class="content">
      <!-- 对话列表 -->
      <view class="chat-list">
        <view
          class="chat-item"
          v-for="chat in chatList"
          :key="chat.id"
          @click="openChat(chat)"
        >
          <view class="chat-card">
            <view class="chat-header">
              <view class="chat-title">{{ chat.title }}</view>
              <view class="chat-time">{{ chat.time }}</view>
            </view>
            <view class="chat-preview">{{ chat.lastMessage }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 弹出菜单 -->
    <view
      class="popup-overlay"
      v-if="showPopup"
      @tap="closePopup"
    >
      <view class="popup-menu" @tap.stop>
        <view class="menu-item" @tap="startChat">
          <text class="menu-text">发起对话</text>
        </view>
        <view class="menu-item" @tap="joinChat">
          <text class="menu-text">加入对话</text>
        </view>
      </view>
    </view>

    <!-- 底部添加按钮 -->
    <view class="add-chat-btn" @tap="togglePopup">
      <image
        class="add-icon"
        :src="addChatIcon"
        mode="aspectFit"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import sheep from '@/sheep'

// 添加对话图标
const addChatIcon = sheep.$url.cdn('/tool/addchat.png')

// 弹出菜单状态
const showPopup = ref(false)

// 选择的语言
const selectedLanguage = ref<any>(null)

// 对话数据接口
interface ChatItem {
  id: string
  title: string
  lastMessage: string
  time: string
}

// 模拟对话列表数据
const chatList = ref<ChatItem[]>([
  {
    id: '1',
    title: '用户18750',
    lastMessage: '那个江苏卫视做一个节目，成在中国的一个关...',
    time: '2025-07-21 21:21'
  },
  {
    id: '2',
    title: '用户18750',
    lastMessage: '那个江苏卫视做一个节目，成在中国的一个关...',
    time: '2025-07-21 21:21'
  },
  {
    id: '3',
    title: '用户18750',
    lastMessage: '那个江苏卫视做一个节目，成在中国的一个关...',
    time: '2025-07-21 21:21'
  },
  {
    id: '4',
    title: '用户18750',
    lastMessage: '那个江苏卫视做一个节目，成在中国的一个关...',
    time: '2025-07-21 21:21'
  },
  {
    id: '5',
    title: '用户18750',
    lastMessage: '那个江苏卫视做一个节目，成在中国的一个关...',
    time: '2025-07-21 21:21'
  }
])

// 切换弹出菜单
const togglePopup = () => {
  showPopup.value = !showPopup.value
}

// 关闭弹出菜单
const closePopup = () => {
  showPopup.value = false
}

// 发起对话
const startChat = () => {
  console.log('发起对话')
  showPopup.value = false
  sheep.$router.go('/pages/tool/interpreter/chat');
  // TODO: 实现发起对话逻辑
}

// 加入对话
const joinChat = () => {
  console.log('加入对话')
  showPopup.value = false
  sheep.$router.go('/pages/tool/interpreter/chat');
  // TODO: 实现加入对话逻辑
}

// 打开对话
const openChat = (chat: ChatItem) => {
  sheep.$router.go('/pages/tool/interpreter/chat');
}

// 组件挂载时读取选择的语言
onMounted(() => {
  // 从本地存储读取选择的语言
  const savedLanguage = uni.getStorageSync('interpreter_selected_language')
  if (savedLanguage) {
    selectedLanguage.value = savedLanguage
  }
})
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #F7FCFF;
  position: relative;
}

.content {
  padding: 30rpx;
  padding-bottom: 150rpx; // 为底部按钮留出空间
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx; // 与语言选择页保持一致的间距
}

.chat-item {
  width: 100%;
  display: flex;
  justify-content: center;
}

.chat-card {
  width: 690rpx;
  height: 171rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 25rpx;
  padding: 37rpx 28rpx 45rpx 28rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;

  &:active {
    transform: scale(0.98);
    transition: transform 0.2s ease;
  }
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chat-title {
  line-height: 1.2;
  flex: 1;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 29rpx;
  color: #494949;
}

.chat-time {
  white-space: nowrap;
  flex-shrink: 0;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 21rpx;
  color: #9F9F9F;
}

.chat-preview {
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  margin-top: 8rpx;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 25rpx;
  color: #919090;
}

// 弹出菜单样式
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
}

.popup-menu {
  width: 235rpx;
  height: 183rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 302rpx; // 距离底部按钮的距离
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  overflow: visible; // 确保尖角可见

  // 底部尖角
  &::after {
    content: '';
    position: absolute;
    bottom: -14rpx; // 调整位置让尖角紧贴矩形
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15rpx solid transparent;
    border-right: 15rpx solid transparent;
    border-top: 15rpx solid #FFFFFF;
    filter: drop-shadow(0 2rpx 4rpx rgba(211,223,230,0.3));
  }
}

.menu-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  // 分隔线，保留左右边距
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 30rpx;
    right: 30rpx;
    height: 1rpx;
    background-color: #F0F0F0;
  }

  &:active {
    background-color: #F8F8F8;
  }
}

.menu-text {
  text-align: center;
  font-family: PingFang SC;
  font-size: 25rpx;
  color: #444444;
}

// 底部添加按钮
.add-chat-btn {
  position: fixed;
  bottom: 153rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 140rpx;
  height: 140rpx;
  background: #00D4FF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 212, 255, 0.3);
  z-index: 999;

  &:active {
    transform: translateX(-50%) scale(0.95);
    transition: transform 0.2s ease;
  }
}
</style>
