<template>
  <view class="swipe-action-container">
    <view class="swipe-action-box">
      <!-- 内容区 -->
      <view
        class="swipe-action-content"
        :style="{ transform: 'translateX(' + offsetX + 'rpx)' }"
        @touchstart="onContentTouchStart"
        @touchmove="onContentTouchMove"
        @touchend="onContentTouchEnd"
      >
        <slot></slot>
      </view>

      <!-- 操作按钮区 -->
      <view class="swipe-action-buttons" :style="{ opacity: isTouching || isOpen ? 1 : 0 }">
        <view 
          v-for="(btn, index) in buttons" 
          :key="index" 
          class="swipe-action-btn"
          :style="[{
            backgroundColor: btn.backgroundColor || '#ff5252',
            width: btn.width + 'rpx'
          }]"
          @click="handleButtonClick(btn, index)"
        >
          <text :style="{color: btn.color || '#ffffff'}">{{ btn.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 定义组件名
defineOptions({
  name: 'swipe-action'
});

const props = defineProps({
  // 操作按钮配置
  buttons: {
    type: Array,
    default: () => [],
    // 每个按钮对象格式: {text: '按钮文本', width: 150, backgroundColor: '#ff5252', color: '#ffffff'}
  },
  // 是否禁用滑动
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否自动关闭（点击按钮后自动关闭）
  autoClose: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['button-click']);

// 滑动状态变量
const offsetX = ref(0);
const startX = ref(0);
const startY = ref(0);
const isOpen = ref(false);
const isTouching = ref(false);
const isHorizontalMove = ref(false);

// 计算按钮总宽度
const totalButtonsWidth = computed(() => {
  return props.buttons.reduce((total, btn) => total + (btn.width || 150), 0);
});

// 判断是否是输入元素
const isInputElement = (element) => {
  if (!element) return false;
  
  // 检查是否是input元素或其子元素
  const tagName = element.tagName && element.tagName.toLowerCase();
  if (tagName === 'input' || tagName === 'textarea') {
    return true;
  }
  
  // 如果有contentEditable属性也视为输入元素
  if (element.getAttribute && element.getAttribute('contenteditable') === 'true') {
    return true;
  }
  
  return false;
};

// 内容区域触摸开始
const onContentTouchStart = (event) => {
  if (props.disabled) return;
  
  // 检查是否点击了输入框
  const target = event.target;
  if (isInputElement(target)) {
    return;
  }
  
  // 记录初始触摸位置
  startX.value = event.touches[0].clientX;
  startY.value = event.touches[0].clientY;
  isTouching.value = true;
  isHorizontalMove.value = false;
};

// 内容区域触摸移动
const onContentTouchMove = (event) => {
  if (props.disabled || !isTouching.value) return;
  
  // 检查是否点击了输入框
  const target = event.target;
  if (isInputElement(target)) {
    return;
  }
  
  const currentX = event.touches[0].clientX;
  const currentY = event.touches[0].clientY;
  const deltaX = currentX - startX.value;
  const deltaY = currentY - startY.value;
  
  // 还未确定方向时，判断滑动方向
  if (!isHorizontalMove.value) {
    // 水平移动距离大于垂直移动距离，且水平移动超过10px时，认为是水平滑动
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
      isHorizontalMove.value = true;
    } 
    // 否则不处理，让原生滚动或者正常输入行为生效
    else {
      return;
    }
  }
  
  // 确定是水平滑动后，计算偏移量
  if (isHorizontalMove.value) {
    // 阻止默认滚动行为
    event.preventDefault && event.preventDefault();
    
    // 向左滑动（显示按钮）
    if (deltaX < 0) {
      // 限制最大滑动距离为按钮总宽度
      offsetX.value = Math.max(-totalButtonsWidth.value, deltaX);
    }
    // 向右滑动（隐藏按钮）
    else if (deltaX > 0 && offsetX.value < 0) {
      // 已经显示按钮的情况下，向右滑动恢复原位
      offsetX.value = Math.min(0, -totalButtonsWidth.value + deltaX);
    }
  }
};

// 内容区域触摸结束
const onContentTouchEnd = (event) => {
  if (props.disabled || !isTouching.value) return;
  
  if (isHorizontalMove.value) {
    // 如果滑动距离超过按钮总宽度的一半，则自动完全显示按钮
    if (offsetX.value < -totalButtonsWidth.value / 2) {
      offsetX.value = -totalButtonsWidth.value;
      isOpen.value = true;
    } else {
      // 否则恢复原位
      offsetX.value = 0;
      isOpen.value = false;
    }
  }
  
  // 重置状态
  startX.value = 0;
  startY.value = 0;
  isTouching.value = false;
  isHorizontalMove.value = false;
};

// 处理按钮点击
const handleButtonClick = (button, index) => {
  emit('button-click', { button, index });
  
  // 如果设置了自动关闭，点击后关闭
  if (props.autoClose) {
    offsetX.value = 0;
    isOpen.value = false;
  }
};

// 关闭滑动菜单（供父组件调用）
const close = () => {
  offsetX.value = 0;
  isOpen.value = false;
};

// 打开滑动菜单（供父组件调用）
const open = () => {
  offsetX.value = -totalButtonsWidth.value;
  isOpen.value = true;
};

// 导出方法供父组件调用
defineExpose({
  close,
  open,
  isOpen
});
</script>

<style lang="scss" scoped>
.swipe-action-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.swipe-action-box {
  position: relative;
  width: 100%;
}

.swipe-action-content {
  position: relative;
  width: 100%;
  z-index: 2;
  background-color: #fff;
  transition: transform 0.3s ease;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  will-change: transform; /* 优化性能 */
  /* 添加右侧边框，防止按钮露出 */
  border-right: 2px solid #fff;
}

.swipe-action-buttons {
  position: absolute;
  top: 0;
  right: -1px; /* 向右偏移1px，确保不会露出 */
  height: 100%;
  display: flex;
  z-index: 1;
  border-top-right-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
  overflow: hidden;
}

.swipe-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 26rpx;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.9;
    transform: scale(0.98);
  }
}
</style> 