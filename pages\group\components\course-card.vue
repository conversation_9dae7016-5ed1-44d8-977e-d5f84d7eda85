<template>
  <view class="course-card" @click="$emit('click')" @longpress="$emit('longpress')">
    <view class="card-content">
      <!-- 状态标签 -->
      <view v-if="item.status" class="status-tag">已学完</view>

      <!-- 课程封面 -->
      <view
        class="course-image"
        :style="coverStyle"
      />

      <!-- 课程信息 -->
      <view class="course-info">
        <view class="course-title">{{ item.title }}</view>
        <view class="teacher-info">
          <view class="teacher-avatar">
            <image :src="item.avatar" mode="aspectFit" />
          </view>
          <view class="teacher-name">
            <text>{{ item.nickname }}</text>
            <text v-if="false" class="teacher-tag">老师</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
  });

  const emits = defineEmits(['click']);

  // 计算属性：封面样式
  const coverStyle = computed(() => {
    return props.item.coverUrl
      ? { backgroundImage: `url(${props.item.coverUrl})`, backgroundSize: 'cover', backgroundPosition: 'center' }
      : { background: 'linear-gradient(to bottom, #BDC9FF, #D1EBFF)' };
  });
</script>

<style lang="scss" scoped>
  .course-card {
    background-color: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .card-content {
    position: relative;
  }

  .status-tag {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 2;
    background-color: #f0f0f0;
    color: #666;
    opacity: 0.8;
    padding: 6rpx 20rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
  }

  .course-image {
    width: 100%;
    height: 240rpx;
    border-radius: 12rpx 12rpx 0 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to bottom, #BDC9FF, #D1EBFF);
  }

  .course-info {
    padding: 16rpx;
  }

  .course-title {
    font-size: 20rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
    color: #39393A;
  }

  .teacher-info {
    display: flex;
    align-items: center;
  }

  .teacher-avatar {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12rpx;
    background-color: #4080ff;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .teacher-name {
    font-size: 20rpx;
    color: #2D2D2D;
  }

  .teacher-tag {
    font-size: 18rpx;
    color: #3B77FF;
    background-color: #CFE7FF;
    padding: 2rpx 10rpx;
    border-radius: 10rpx;
    margin-left: 8rpx;
  }
</style>
