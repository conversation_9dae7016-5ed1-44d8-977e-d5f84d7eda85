<template>
  <view class="container">
    <!-- 顶部导航 -->
    <easy-navbar title="创建学习集" />
    
    <!-- 表单区域 -->
    <view class="form-area">
      <!-- 学习集标题和封面 -->
      <view class="form-item title-input">
        <view class="title-cover-container">
          <input 
            type="text" 
            v-model="formData.title" 
            placeholder="请输入标题、说明等" 
            class="input-field"
          />
          <view class="cover-upload-inline" @click="uploadCover">
            <image v-if="!formData.coverUrl" :src="sheep.$url.cdn('/set/image.png')" class="upload-icon-img" mode="aspectFit"></image>
            <image v-else :src="formData.coverUrl" class="upload-image" mode="aspectFill"></image>
            <text v-if="!formData.coverUrl" class="upload-text">点击上传</text>
            <text v-else class="upload-text">更换封面</text>
          </view>
        </view>
      </view>
      
      <!-- 可见范围 -->
      <view class="form-item visibility">
        <text class="label">可见范围</text>
        <view class="radio-group">
          <view class="radio-item" @click="formData.visibility = 0">
            <view class="radio-circle" :class="{ active: formData.visibility === 0 }">
              <view class="radio-inner" v-if="formData.visibility === 0"></view>
            </view>
            <text>所有人</text>
          </view>
          <view class="radio-item" @click="formData.visibility = 2">
            <view class="radio-circle" :class="{ active: formData.visibility === 2 }">
              <view class="radio-inner" v-if="formData.visibility === 2"></view>
            </view>
            <text>分享班级</text>
          </view>
          <view class="radio-item" @click="formData.visibility = 1">
            <view class="radio-circle" :class="{ active: formData.visibility === 1 }">
              <view class="radio-inner" v-if="formData.visibility === 1"></view>
            </view>
            <text>仅自己</text>
          </view>
        </view>
      </view>
      
      <!-- 分享到班级 -->
      <view class="form-item share-class" v-if="formData.visibility !== 1" @click="goSelectClass">
        <text class="label">分享到班级</text>
        <view class="share-value">
          <text v-if="selectedClasses.length === 0">请选择班级</text>
          <text v-else>已选择{{ selectedClasses.length }}个班级</text>
          <uni-icons type="right" size="14" color="#999999"></uni-icons>
        </view>
      </view>
      
      <!-- 扫描文件区域 -->
      <view class="file-section">
        <view class="scan-btn" @click="scanFiles">
          <image :src="sheep.$url.cdn('/set/scan.png')" class="scan-icon-img" mode="aspectFit"></image>
          <text class="scan-text">扫描文件</text>
        </view>
      </view>
      
      <!-- 单词卡区域 -->
      <view class="word-cards-section">
        <swipe-action
          v-for="(card, cardIndex) in formData.wordCards" 
          :key="cardIndex"
          :buttons="[{ text: '删除', backgroundColor: '#ff5252', width: 150 }]"
          @button-click="handleCardDelete($event, cardIndex)"
          :disabled="formData.wordCards.length <= 1"
          :autoClose="true"
        >
          <view class="word-card">
            <view class="word-input">
              <input 
                type="text" 
                v-model="card.word" 
                placeholder="词语" 
                class="card-input"
              />
              <view class="word-image-upload" @click="uploadWordImage(cardIndex)">
                <image v-if="!card.imageUrl" :src="sheep.$url.cdn('/set/image.png')" class="upload-icon-img" mode="aspectFit"></image>
                <image v-else :src="card.imageUrl" class="upload-word-image" mode="aspectFill"></image>
                <text v-if="!card.imageUrl" class="upload-text">点击上传</text>
                <text v-else class="upload-text">更换图片</text>
              </view>
            </view>
            
            <!-- 音标输入区域 -->
            <view class="phonetic-input">
              <view class="input-container">
                <input 
                  type="text" 
                  v-model="card.phoneticSymbol" 
                  placeholder="音标（可选）" 
                  class="card-input"
                />
              </view>
            </view>
            
            <view v-for="(def, defIndex) in card.definitions" :key="defIndex" class="definition-input">
              <view class="input-with-delete">
                <input 
                  type="text" 
                  v-model="def.definition" 
                  placeholder="定义" 
                  class="card-input"
                />
                <view class="def-delete" v-if="card.definitions.length > 1" @click.stop.prevent="removeDefinition(cardIndex, defIndex)">
                  <text class="delete-icon">×</text>
                </view>
              </view>
            </view>
            
            <view class="add-def-btn" @click.stop="addDefinition(cardIndex)">
              <text>+ 添加更多定义</text>
            </view>
          </view>
        </swipe-action>
      </view>
      
      <!-- 添加按钮 -->
      <view class="add-btn-container">
        <view class="add-btn" @click="addWordCard">
          <image :src="sheep.$url.cdn('/group/blue-add.png')" class="add-icon" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    
    <!-- 底部确认按钮 -->
    <view class="confirm-btn" @click="submitForm">
      <text>确认</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import sheep from '@/sheep';
import SetApi from '@/sheep/api/set';
import FileApi from '@/sheep/api/infra/file';
import SwipeAction from '@/components/swipe-action/swipe-action.vue';

// 定义组件
defineOptions({
  components: {
    SwipeAction
  }
});

// 表单数据
const formData = reactive({
  title: '',
  coverUrl: '',
  visibility: 2, // 0: 所有人，1: 仅自己，2: 分享班级
  wordCards: [
    // 初始添加两张空的单词卡
    {
      word: '',
      phoneticSymbol: '',
      imageUrl: '',
      audioUrl: '',
      definitions: [{ definition: '' }]
    },
    {
      word: '',
      phoneticSymbol: '',
      imageUrl: '',
      audioUrl: '',
      definitions: [{ definition: '' }]
    }
  ]
});

// 加载状态
const loading = ref(false);

// 选中的班级
const selectedClasses = ref([]);
const selectedClassIds = ref([]);

// 跳转到班级选择页面
const goSelectClass = () => {
  // 传递已选择的班级ID
  const selectedIdsJson = JSON.stringify(selectedClassIds.value);
  sheep.$router.go(`/pages/set/select-group?isCreated=false&selectedIds=${encodeURIComponent(selectedIdsJson)}`);
};

// 监听班级选择结果
onMounted(() => {
  // 检查是否有预填充的表单数据
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  let options = {};
  
  if (currentPage && currentPage.options) {
    options = currentPage.options;
  } else if (currentPage && currentPage.$page && currentPage.$page.options) {
    options = currentPage.$page.options;
  }
  
  if (options.formData) {
    const decodedData = JSON.parse(decodeURIComponent(options.formData));

    // 使用传入的数据初始化表单
    // 使用解构赋值来更新 formData 的所有属性
    Object.assign(formData, decodedData);
  }
  
  // 监听班级选择事件
  uni.$on('classSelected', (data) => {
    selectedClasses.value = data.selectedClasses;
    selectedClassIds.value = data.selectedClasses.map(item => item.id);
  });
  
  return () => {
    // 组件卸载时移除监听
    uni.$off('classSelected');
  };
});

// 上传封面
const uploadCover = async () => {
  try {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'], // 压缩图片
      sourceType: ['album', 'camera'], // 从相册或相机
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 先使用临时图片路径更新预览，提供即时反馈
        formData.coverUrl = tempFilePath;
        
        // 显示上传中提示
        sheep.$helper.toast("上传中...");

          // 使用FileApi上传文件
          const uploadResult = await FileApi.uploadFile(tempFilePath);

          if (uploadResult.code !== 0){
            return;
          }
          // 检查data是否直接是URL字符串
          if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
            // 直接使用字符串URL
            formData.coverUrl = uploadResult.data;
            sheep.$helper.toast('上传成功');
          }
          // 检查data是否包含url属性的对象
          else if (uploadResult.data && uploadResult.data.url) {
            formData.coverUrl = uploadResult.data.url;
            sheep.$helper.toast('上传成功');
          }
      }
    });
  } catch (error) {
    sheep.$helper.toast('选择图片失败');
  }
};

// 上传单词卡图片
const uploadWordImage = async (cardIndex) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'], // 压缩图片
    sourceType: ['album', 'camera'], // 从相册或相机
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0];

      // 先使用临时图片路径更新预览，提供即时反馈
      formData.wordCards[cardIndex].imageUrl = tempFilePath;

      // 显示上传中提示
      sheep.$helper.toast("上传中...");

      // 使用FileApi上传文件
      const uploadResult = await FileApi.uploadFile(tempFilePath);

      if (uploadResult.code !== 0){
        return;
      }
      // 检查data是否直接是URL字符串
      if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
        // 直接使用字符串URL
        formData.wordCards[cardIndex].imageUrl = uploadResult.data;
        sheep.$helper.toast('上传成功');
      }
      // 检查data是否包含url属性的对象
      else if (uploadResult.data && uploadResult.data.url) {
        formData.wordCards[cardIndex].imageUrl = uploadResult.data.url;
        sheep.$helper.toast('上传成功');
      }
    }
  });
};

// 添加单词卡
const addWordCard = () => {
  // 新增一张空白单词卡
  formData.wordCards.push({
    word: '',
    phoneticSymbol: '',
    imageUrl: '',
    audioUrl: '',
    definitions: [{ definition: '' }]
  });
  
  // 滚动到底部以查看新添加的卡片
  setTimeout(() => {
    uni.pageScrollTo({
      scrollTop: 99999,
      duration: 300
    });
  }, 100);
};

// 处理卡片删除事件
const handleCardDelete = (event, index) => {
  // 如果只有一张卡片，不允许删除
  if (formData.wordCards.length <= 1) {
    sheep.$helper.toast('至少需要保留一张单词卡');
    return;
  }
  
  // 显示确认删除提示
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这张单词卡吗？',
    success: (res) => {
      if (res.confirm) {
        // 执行删除操作
        formData.wordCards.splice(index, 1);
        sheep.$helper.toast('已删除');
      }
    }
  });
};

// 删除单词卡 (保留旧方法以防其他地方调用)
const removeWordCard = (index) => {
  handleCardDelete(null, index);
};

// 添加定义
const addDefinition = (cardIndex) => {
  formData.wordCards[cardIndex].definitions.push({ definition: '' });
};

// 删除定义
const removeDefinition = (cardIndex, defIndex) => {
  const definitions = formData.wordCards[cardIndex].definitions;

  // 确保有至少两个定义才能删除
  if (definitions.length <= 1) {
    sheep.$helper.toast('至少需要保留一个定义');
    return;
  }

  // 执行删除操作
  definitions.splice(defIndex, 1);
};

// 添加扫描文件功能
const scanFiles = () => {
  sheep.$helper.inDevMsg();
};

// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!validateForm()) {
    return;
  }
  
  // 避免重复提交
  if (loading.value) {
    return;
  }
  
  // 显示加载中
  loading.value = true;
  sheep.$helper.toast('创建中...')

  // 检查图片URL是否是本地路径，如果是则可能需要再次上传
  if (formData.coverUrl && (formData.coverUrl.startsWith('file://') || formData.coverUrl.startsWith('/'))) {
    const uploadResult = await FileApi.uploadFile(formData.coverUrl);
    if (uploadResult && uploadResult.code === 0) {
      // 检查data是否直接是URL字符串
      if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
        formData.coverUrl = uploadResult.data;
      }
      // 检查data是否包含url属性的对象
      else if (uploadResult.data && uploadResult.data.url) {
        formData.coverUrl = uploadResult.data.url;
      }
    }
  }

  // 检查每个单词卡的图片URL是否是本地路径，如果是则需要上传
  for (let i = 0; i < formData.wordCards.length; i++) {
    const card = formData.wordCards[i];
    if (card.imageUrl && (card.imageUrl.startsWith('file://') || card.imageUrl.startsWith('/'))) {
      const uploadResult = await FileApi.uploadFile(card.imageUrl);
      if (uploadResult && uploadResult.code === 0) {
        // 检查data是否直接是URL字符串
        if (typeof uploadResult.data === 'string' && uploadResult.data.startsWith('http')) {
          formData.wordCards[i].imageUrl = uploadResult.data;
        }
        // 检查data是否包含url属性的对象
        else if (uploadResult.data && uploadResult.data.url) {
          formData.wordCards[i].imageUrl = uploadResult.data.url;
        }
      }
    }
  }

  // 根据接口文档格式化提交数据
  const submitData = {
    // id为0表示新建，如果是编辑模式应该传入实际id
    id: 0,
    title: formData.title.trim(),
    coverUrl: formData.coverUrl,
    visibility: formData.visibility,
    // 单词卡数据格式化
    wordCards: formData.wordCards
      .filter(card => card.word.trim() && card.definitions.some(def => def.definition.trim()))
      .map(card => ({
        word: card.word.trim(),
        phoneticSymbol: card.phoneticSymbol || '',
        imageUrl: card.imageUrl || '',
        // 不需要音频
        audioUrl: '',
        definitions: card.definitions
          .filter(def => def.definition.trim())
          .map(def => ({
            definition: def.definition.trim()
          }))
      }))
  };

  // 调用API创建学习集
  const res = await SetApi.createWordSet(submitData);

  if (res.code === 0) {
    sheep.$helper.toast('创建成功');
    
    // 如果选择了班级并且非仅自己可见，进行班级分享
    if (selectedClasses.value.length > 0 && formData.visibility !== 1) {
      const createdSetId = res.data.id || res.data;
      
      // 分享到选中的所有班级
      for (const classItem of selectedClasses.value) {
        await SetApi.shareToClass(createdSetId, classItem.id);
      }
      sheep.$helper.toast('分享到班级成功');
    }

    // 延迟返回上一页，以便用户看到成功提示
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
};

// 验证表单
const validateForm = () => {
  // 验证标题
  if (!formData.title.trim()) {
    sheep.$helper.toast('请输入学习集标题');
    return false;
  }
  
  // 验证至少有一张有效的单词卡
  let hasValidCard = false;
  
  for (let i = 0; i < formData.wordCards.length; i++) {
    const card = formData.wordCards[i];
    if (!card.word.trim()) {
      sheep.$helper.toast(`第${i + 1}张卡片的词语不能为空`);
      return false;
    }
    
    // 检查定义
    let hasValidDefinition = false;
    for (const def of card.definitions) {
      if (def.definition.trim()) {
        hasValidDefinition = true;
        break;
      }
    }
    
    if (!hasValidDefinition) {
      sheep.$helper.toast(`第${i + 1}张卡片至少需要一个有效定义`);
      return false;
    }
    
    hasValidCard = true;
  }
  
  if (!hasValidCard) {
    sheep.$helper.toast('请至少添加一张单词卡');
    return false;
  }
  
  return true;
};

onLoad((options) => {
  // 检查是否有预填充的表单数据
  if (options.formData) {
    const decodedData = JSON.parse(decodeURIComponent(options.formData));

    // 使用传入的数据初始化表单
    // 使用解构赋值来更新 formData 的所有属性
    Object.assign(formData, decodedData);

    sheep.$helper.toast('已导入词汇数据');
  }
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #F8FCFF ;
  display: flex;
  flex-direction: column;
  padding-bottom: 180rpx;
}

.header {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  
  .header-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    
    .back-icon {
      font-size: 40rpx;
      color: #333;
    }
  }
  
  .header-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    
    .menu-icon {
      font-size: 30rpx;
      margin-right: 20rpx;
      letter-spacing: 2rpx;
      color: #999;
    }
    
    .circle-icon {
      font-size: 36rpx;
      color: #666;
    }
  }
}

.form-area {
  flex: 1;
  padding: 0;
}

.form-item {
  background-color: #F8FCFF;
  padding: 30rpx 30rpx 10rpx 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  
  &.visibility, &.share-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.visibility {
  background-color: #F8FCFF;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

  &.visibility, &.share-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.share-class {
  background-color: #F8FCFF;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.share-value {
  display: flex;
  align-items: center;
  
  text {
    font-size: 26rpx;
    color: #666;
    margin-right: 10rpx;
  }
}

.label {
  font-size: 26rpx;
  color: #A5A5A5;
  font-family: 'SimHei', sans-serif;
}

.title-cover-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 通用输入框样式 */
input {
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
  color: #a5a5a5;
}

.input-field {
  flex: 1;
  height: 60rpx;
  font-size: 26rpx;
  font-family: 'SimHei', sans-serif;
  color: #171717;
  background-color: transparent;
}

.cover-upload-inline {
  width: 80rpx;
  height: 100rpx;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  
  .upload-icon-img {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 8rpx;
  }
  
  .upload-image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 4rpx;
    margin-bottom: 8rpx;
    object-fit: cover;
  }
  
  .upload-text {
    font-size: 18rpx;
    color: #999;
    text-align: center;
    line-height: 1;
  }
}

.radio-group {
  display: flex;
  align-items: center;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  margin-right: 10rpx;
  
  .radio-circle {
    width: 28rpx;
    height: 28rpx;
    border-radius: 50%;
    border: 2rpx solid #ccc;
    margin-right: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    
    &.active {
      border-color: #4CAF50;
    }
    
    .radio-inner {
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #4CAF50;
    }
  }
}

.file-section {
  display: flex;
  align-items: center;
  margin: 60rpx 0;
  padding: 0 30rpx;
  
  .scan-btn {
    display: flex;
    align-items: center;
    color: #2A7EEF;
    
    .scan-icon-img {
      width: 36rpx;
      height: 36rpx;
      margin-right: 8rpx;
    }
  }
}

.word-cards-section {
  padding: 10rpx 30rpx;
  margin-top: 10rpx;
  
  /* 为swipe-action添加样式 */
  :deep(.swipe-action-container) {
    margin-bottom: 30rpx;
    border-radius: 16rpx;
    overflow: hidden;
  }
  
  :deep(.swipe-action-content) {
    border-radius: 16rpx;
    /* 移除此样式以避免冲突 */
  }
  
  :deep(.swipe-action-buttons) {
    height: 100%;
    /* 确保按钮在默认状态下不可见 */
    right: -1px;
  }
  
  /* 增加内容区边框宽度，遮挡潜在的按钮 */
  :deep(.word-card) {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    /* 增加右侧边框 */
    border-right: 2px solid #fff;
  }
}

.word-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .card-number {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
    
    .card-delete {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .delete-icon {
        font-size: 36rpx;
        color: #999;
      }
    }
  }
  
  .word-input {
    margin-bottom: 30rpx;
    border-bottom: 1rpx solid #2A7EEF;
    padding-bottom: 10rpx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .card-input {
      width: calc(100% - 100rpx); /* 为上传图片按钮留出空间 */
      height: 48rpx;
      font-size: 26rpx;
      font-family: 'SimHei', sans-serif;
      color: #171717;
    }
    
    .word-image-upload {
      width: 80rpx;
      height: 100rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      
      .upload-icon-img {
        width: 60rpx;
        height: 60rpx;
        margin-bottom: 8rpx;
      }
      
      .upload-word-image {
        width: 60rpx;
        height: 60rpx;
        border-radius: 4rpx;
        margin-bottom: 8rpx;
        object-fit: cover;
      }
      
      .upload-text {
        font-size: 18rpx;
        color: #999;
        text-align: center;
        line-height: 1;
      }
    }
  }
  
  .phonetic-input {
    margin-bottom: 30rpx;
    
    .input-container {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      border-bottom: 1rpx solid #2A7EEF;
      padding-bottom: 10rpx;
    }
    
    .card-input {
      width: 100%; /* 音标只有一个，不需要为删除按钮预留空间 */
      height: 48rpx;
      font-size: 26rpx;
      font-family: 'SimHei', sans-serif;
      color: #171717;
    }
  }
  
  .definition-input {
    margin-bottom: 30rpx;
    
    .input-with-delete {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      border-bottom: 1rpx solid #2A7EEF;
      padding-bottom: 10rpx;
    }
    
    .card-input {
      width: calc(100% - 50rpx); /* 为删除按钮留出空间 */
      height: 48rpx;
      font-size: 26rpx;
      font-family: 'SimHei', sans-serif;
      color: #171717;
    }
    
    .def-delete {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 46rpx;
      height: 46rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 50%;
      padding: 0;
      box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      z-index: 10;
      
      .delete-icon {
        font-size: 32rpx;
        color: #999;
        font-weight: bold;
        line-height: 1;
      }
    }
  }
  
  .add-def-btn {
    height: 40rpx;
    padding: 8rpx 0;
    display: flex;
    justify-content: center;
    
    text {
      font-size: 24rpx;
      color: #2A7EEF;
      font-family: 'SimHei', sans-serif;
    }
  }
}

.add-btn-container {
  width: 100rpx;
  height: 100rpx;
  position: fixed;
  right: 40rpx;
  bottom: 200rpx;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-btn {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .add-icon {
    width: 100rpx;
    height: 100rpx;
  }
}

.confirm-btn {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  height: 90rpx;
  background-color: #239EED;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 16rpx rgba(57, 166, 242, 0.68);
  z-index: 10;
  
  text {
    color: #fff !important;
    font-size: 34rpx !important;
    font-family: 'SimHei', sans-serif;
  }
}

/* 添加更多定义按钮特殊样式 */
.add-def-btn {
  text {
    color: #2A7EEF !important;
    font-size: 24rpx !important;
  }
}

/* 页面文本和标签通用样式 */
text, .label {
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
  color: #A5A5A5;
}

/* 词语和定义输入框专用样式 */
.card-input {
  font-size: 26rpx !important;
  font-family: 'SimHei', sans-serif;
  color: #171717;
}

/* placeholder样式 */
::-webkit-input-placeholder {
  color: #A5A5A5;
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
}

input::placeholder {
  color: #A5A5A5;
  font-family: 'SimHei', sans-serif;
  font-size: 26rpx;
}

/* 扫描文件按钮特殊样式 */
.scan-btn {
  display: flex;
  align-items: center;
  color: #2A7EEF;
  
  .scan-icon-img {
    width: 36rpx;
    height: 36rpx;
    margin-right: 8rpx;
  }
  
  text {
    color: #2A7EEF !important;
  }
}
</style> 
