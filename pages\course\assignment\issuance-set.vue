<template>
  <view class="issuance-container">
    <!-- 顶部导航栏 -->
    <easy-navbar title="发放设置"></easy-navbar>
    
    <!-- 占位元素，防止内容被固定导航栏遮挡 -->
    <view class="navbar-placeholder" :style="{ height: `${navbarHeight + paddingBottom}px` }" />

    <!-- 盒子1：基本信息区域 -->
    <view class="setting-box basic-info">
      <!-- 标题 -->
      <view class="form-item">
        <view class="label">标题</view>
        <view class="input-wrap">
          <input 
            type="text" 
            v-model="formData.title" 
            placeholder="请输入发布作业标题" 
            placeholder-class="placeholder"
            :disabled="isEdit ? false : false"
          />
        </view>
      </view>

      <!-- 开始时间 -->
      <view class="form-item">
        <view class="label">开始时间</view>
        <uni-datetime-picker 
          type="datetime" 
          v-model="formData.startTime" 
          :border="false"
          return-type="string"
          format="yyyy-MM-dd HH:mm:ss"
          :disabled="isEdit ? false : false"
        >
          <view class="input-wrap select-wrap">
            <text class="select-text">{{ formData.startTime ? formData.startTime : '请选择' }}</text>
            <view class="arrow-icon"></view>
          </view>
        </uni-datetime-picker>
      </view>

      <!-- 结束时间 -->
      <view class="form-item">
        <view class="label">结束时间</view>
        <uni-datetime-picker 
          type="datetime" 
          v-model="formData.endTime" 
          :border="false"
          return-type="string"
          format="yyyy-MM-dd HH:mm:ss"
          :disabled="isEdit ? false : false"
        >
          <view class="input-wrap select-wrap">
            <text class="select-text">{{ formData.endTime ? formData.endTime : '请选择' }}</text>
            <view class="arrow-icon"></view>
          </view>
        </uni-datetime-picker>
      </view>
    </view>

    <view class="box-title">
      <view class="title-left">
        发放对象
        <view class="arrow-icon" :class="{ 'is-expanded': isExpanded }" @click="isEdit ? null : toggleExpand()"></view>
      </view>
      <text class="select-all" v-if="!isEdit" @click="toggleSelectAll">{{ isAllSelected ? '取消全选' : '全选' }}</text>
    </view>
    <!-- 盒子2：发放对象区域 -->
    <view class="setting-box released-obj">
      <!-- 班级列表 -->
      <view class="class-list">
        <template v-if="displayClassList.length === 0">
          <view class="empty-state">
            <text>暂无班级数据，请先创建班级</text>
          </view>
        </template>
        <template v-else>
          <view v-for="(item, index) in displayClassList" :key="index" class="class-item">
            <view class="checkbox-wrap" @click="isEdit ? null : toggleClass(index)">
              <view class="checkbox" :class="{ active: item.selected, disabled: isEdit }">
                <view v-if="item.selected" class="check-icon"></view>
              </view>
              <text class="class-name">{{ item.name }}</text>
            </view>
            <view class="student-count" @click="isEdit ? null : goToSelectStudents(index)">
              <text class="count-text" v-if="item.totalStudents">{{ item.selectedStudents }}/{{ item.totalStudents }}</text>
              <image class="arrow-right-icon" :src="arrowRightIcon" :class="{ disabled: isEdit }" mode="aspectFit"></image>
            </view>
          </view>
        </template>
      </view>
    </view>

    <view class="box-title">高级设置</view>
    <!-- 盒子3：高级设置区域 -->
    <view class="setting-box advanced-setting">
      <!-- 及格分数 -->
      <view class="form-item">
        <view class="label">及格分数</view>
        <view class="input-wrap">
          <input 
            type="number" 
            v-model="formData.passingScore" 
            placeholder="请输入" 
            placeholder-class="placeholder"
            :disabled="isEdit ? false : false"
          />
        </view>
      </view>

      <!-- 督促未交学生 -->
      <view class="form-item remind-item">
        <view class="remind-left">
          <view class="label">督促未交学生</view>
          <view v-if="formData.remindUncompleted" class="time-container">
            <text class="light-text">作业结束前</text>
            <input
              type="number"
              v-model="formData.remindHours"
              class="hours-input"
              :disabled="(isEdit ? false : false) || !formData.remindUncompleted"
            />
            <text class="light-text">小时发通知提醒未交学生</text>
          </view>
        </view>
        <switch 
          :checked="formData.remindUncompleted" 
          @change="handleRemindChange" 
          color="#2FBDF5"
          class="switch-control"
          :disabled="isEdit ? false : false"
        />
      </view>

      <!-- 随机抽题 -->
      <view class="form-item random-item">
        <view class="random-left">
          <view class="label">随机抽题</view>
          <view v-if="formData.randomQuestions" class="time-container">
            <text class="light-text">抽题数量</text>
            <input
              type="number"
              v-model="formData.randomCount"
              class="hours-input"
              :disabled="(isEdit ? true : false) || !formData.randomQuestions"
              @input="handleRandomCountChange"
            />
            <text class="light-text">/{{ formData.questionCount || 0 }}</text>
          </view>
        </view>
        <switch
          :checked="formData.randomQuestions"
          @change="handleRandomChange"
          color="#2FBDF5"
          class="switch-control"
          :disabled="isEdit ? true : false"
        />
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn" @click="startIssuance">
      <text>{{ isEdit ? '修改' : '开始' }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, computed } from 'vue';
  import sheep from '@/sheep';
  import UniDatetimePicker from '@/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue';
  import AssignmentApi from '@/sheep/api/course/assignment';
  import GroupApi from '@/sheep/api/group';

  // 导航栏相关参数
  const paddingTop = ref(0);
  const navbarHeight = ref(0);
  const paddingBottom = ref(10);

  // 图标资源
  const arrowRightIcon = sheep.$url.cdn('/course/come.png');

  // 课程ID和作业ID
  const courseId = ref('');
  const assignmentId = ref('');
  const isEdit = ref(false); // 是否是编辑模式

  // 班级列表数据
  const classList = ref([]);

  // 表单数据
  const formData = reactive({
    title: '',
    startTime: '',
    endTime: '',
    passingScore: '',
    remindUncompleted: true,
    remindHours: '24',
    randomQuestions: false,
    randomCount: '0',
    distributionTarget: 0, // 0-班级，1-个人
    questionCount: 0, // 作业库题目总数
  });

  const isExpanded = ref(false);
  
  // 展开/收起的班级列表
  const displayClassList = computed(() => {
    if (!classList.value || classList.value.length === 0) {
      return [];
    }
    // 在修改模式下始终显示所有班级
    if (isEdit.value) {
      return classList.value;
    }
    // 在发布模式下根据展开/收起状态决定显示
    return isExpanded.value ? classList.value : [classList.value[0]];
  });
  
  // 切换展开/收起状态
  const toggleExpand = () => {
	console.log(isExpanded.value)
    isExpanded.value = !isExpanded.value;
  };

  // 获取URL参数
  const getParams = () => {
    // @ts-ignore
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (currentPage && currentPage.options) {
      courseId.value = currentPage.options.courseId || '';
      assignmentId.value = currentPage.options.assignmentId || '';
      // 根据isUpdate参数判断是否是修改模式
      isEdit.value = currentPage.options.isUpdate === 'true';
      // 获取题目总数
      if (currentPage.options.questionCount) {
        formData.questionCount = Number(currentPage.options.questionCount);
      }
    }
  };
  
  // 获取班级列表数据
  const getClassList = async () => {
    // 使用sheep.api方式调用接口
    const res = await GroupApi.getMyGroupsWithUsers() as any;
    if (res.code === 0 && res.data) {
      // 转换数据格式
      classList.value = res.data.map(item => ({
        id: item.id,
        name: item.name,
        selected: false,
        totalStudents: item.users ? item.users.length : 0,
        selectedStudents: 0,
        selectedStudentIds: [], // 存储选中的学生ID
        users: item.users || []
      }));
    } else {
      sheep.$helper.toast('获取班级列表失败');
    }
  };

  // 切换班级选中状态
  const toggleClass = (index: number) => {
    classList.value[index].selected = !classList.value[index].selected;
    
    // 当选中班级时，自动将选中学生数设为总学生数
    // 当取消选中班级时，将选中学生数设为0
    if (classList.value[index].selected) {
      classList.value[index].selectedStudents = classList.value[index].totalStudents;
      // 生成所有学生的ID数组
      classList.value[index].selectedStudentIds = classList.value[index].users.map(user => user.id);
    } else {
      classList.value[index].selectedStudents = 0;
      classList.value[index].selectedStudentIds = [];
    }
  };

  // 处理督促开关变化
  const handleRemindChange = (e: any) => {
    formData.remindUncompleted = e.detail.value;
  };

  // 处理随机抽题开关变化
  const handleRandomChange = (e: any) => {
    formData.randomQuestions = e.detail.value;
    // 如果打开了随机抽题，设置默认的抽题数量为1（但不超过最大值）
    if (formData.randomQuestions && (!formData.randomCount || formData.randomCount === '0')) {
      formData.randomCount = formData.questionCount > 0 ? '1' : '0';
    }
  };

  // 监听抽题数量变化
  const handleRandomCountChange = (e: any) => {
    let count = Number(e.detail.value);
    // 确保抽题数量不超过题目总数
    if (count > formData.questionCount) {
      count = formData.questionCount;
      sheep.$helper.toast(`抽题数量不能超过最大值${formData.questionCount}`);
    }
    // 确保最小值为1
    if (count < 1) {
      count = 1;
    }
    formData.randomCount = count.toString();
  };

  // 跳转到选择学生页面
  const goToSelectStudents = (classIndex: number) => {
    const currentClass = classList.value[classIndex];
    // 将已选中的学生ID列表转换为字符串传递
    const selectedIds = currentClass.selectedStudentIds && currentClass.selectedStudentIds.length > 0 
      ? JSON.stringify(currentClass.selectedStudentIds) 
      : '';

    sheep.$router.go(`/pages/course/assignment/select-students?classId=${currentClass.id}&selectedIds=${encodeURIComponent(selectedIds)}`);
  };

  // 监听学生选择结果
  const listenStudentSelection = () => {
    // @ts-ignore
    uni.$on('studentSelected', (data) => {
      // 找到对应班级
      const classIndex = classList.value.findIndex(item => item.id == data.classId);
      if (classIndex !== -1) {
        // 更新班级的学生选择数据
        classList.value[classIndex].selectedStudents = data.selectedCount;
        classList.value[classIndex].selectedStudentIds = data.selectedStudentIds;
        
        // 如果有学生被选中，自动选中班级
        if (data.selectedCount > 0 && !classList.value[classIndex].selected) {
          classList.value[classIndex].selected = true;
          
          // 如果是个人发放模式，需要设置distributionTarget为1
          if (data.selectedCount < classList.value[classIndex].totalStudents) {
            formData.distributionTarget = 1; // 个人发放模式
          }
        }
      }
    });
  };

  // 获取作业详情
  const getAssignmentDetail = async () => {
    if (!assignmentId.value) return;
    
    try {
      const res = await AssignmentApi.getAssignmentReleaseById({
        id: assignmentId.value
      }) as any;
      if (res.code === 0 && res.data) {
        // 填充表单数据
        formData.title = res.data.title || '';
        formData.startTime = res.data.startTime ? formatLocalDateTime(res.data.startTime) : '';
        formData.endTime = res.data.endTime ? formatLocalDateTime(res.data.endTime) : '';
        formData.passingScore = res.data.passMark?.toString() || '';
        formData.remindUncompleted = res.data.isUrge || false;
        formData.remindHours = res.data.urgeDeadline?.toString() || '24';
        formData.randomQuestions = res.data.isRandom || false;
        formData.randomCount = res.data.randomCount?.toString() || '0';
        formData.distributionTarget = res.data.distributionTarget || 0;
        formData.questionCount = res.data.questionCount || 0;
        
        // 处理班级和学生数据
        if (res.data.groups && res.data.groups.length > 0) {
          // 创建班级ID映射
          const selectedGroupIds = res.data.groups.map(group => group.id);
          
          // 创建用户ID映射（如果是个人发放模式）
          const selectedUserIds = res.data.users ? res.data.users.map(user => user.id) : [];
          
          classList.value.forEach(classItem => {
            // 检查班级是否被选中
            if (selectedGroupIds.includes(Number(classItem.id))) {
              classItem.selected = true;
              
              // 如果是个人发放模式且有用户数据
              if (res.data.distributionTarget === 1 && selectedUserIds.length > 0) {
                // 计算该班级中被选中的学生
                const classSelectedUsers = classItem.users.filter(user => 
                  selectedUserIds.includes(user.id)
                );
                classItem.selectedStudents = classSelectedUsers.length;
                classItem.selectedStudentIds = classSelectedUsers.map(user => user.id);
              } else {
                // 班级模式，选中所有学生
                classItem.selectedStudents = classItem.totalStudents;
                classItem.selectedStudentIds = classItem.users.map(user => user.id);
              }
            } else {
              classItem.selected = false;
              classItem.selectedStudents = 0;
              classItem.selectedStudentIds = [];
            }
          });
        } else if (res.data.groupIds && res.data.groupIds.length > 0) {
          // 兼容旧的数据结构
          classList.value.forEach(classItem => {
            classItem.selected = res.data.groupIds.includes(Number(classItem.id));
            if (classItem.selected) {
              classItem.selectedStudents = classItem.totalStudents;
              classItem.selectedStudentIds = classItem.users.map(user => user.id);
            } else {
              classItem.selectedStudents = 0;
              classItem.selectedStudentIds = [];
            }
          });
        }
      }
    } catch (error) {
      console.error('获取作业详情失败:', error);
      sheep.$helper.toast('获取作业详情失败');
    }
  };
  
  // 获取作业库题目总数
  const getAssignmentInfo = async () => {
    if (!assignmentId.value || formData.questionCount > 0) return;

    // 获取作业库信息
    const res = await AssignmentApi.getAssignmentPage({
      id: assignmentId.value,
      page: 1,
      size: 1
    }) as any;

    if (res.code === 0 && res.data && res.data.list && res.data.list.length > 0) {
      const assignment = res.data.list.find(item => item.id === Number(assignmentId.value));
      if (assignment) {
        formData.questionCount = assignment.questionCount || 0;
      }
    }
  };

  // 开始发放
  const startIssuance = () => {
    // 表单验证
    if (!formData.title) {
      sheep.$helper.toast('请输入标题');
      return;
    }

    if (!formData.startTime) {
      sheep.$helper.toast('请选择开始时间');
      return;
    }

    if (!formData.endTime) {
      sheep.$helper.toast('请选择结束时间');
      return;
    }

    // 提交表单
    submitForm();
  };

  // 提交表单
  const submitForm = async () => {
    // 获取选中的班级
    const selectedClasses = classList.value.filter(item => item.selected);
    
    // 只在非编辑模式下检查是否选择了班级
    if (!isEdit.value && selectedClasses.length === 0) {
      sheep.$helper.toast('请选择至少一个班级');
      return;
    }
    
    // 检查抽题数量是否合法
    if (formData.randomQuestions) {
      const randomCount = Number(formData.randomCount);
      if (randomCount <= 0) {
        sheep.$helper.toast('抽题数量必须大于0');
        return;
      }
      if (randomCount > formData.questionCount) {
        sheep.$helper.toast(`抽题数量不能超过最大值${formData.questionCount}`);
        return;
      }
    }

    // 构建基础请求参数
    const params: any = {
      title: formData.title,
      startTime: formData.startTime ? parseLocalDateTime(formData.startTime) : '', // 使用统一的时间解析函数
      endTime: formData.endTime ? parseLocalDateTime(formData.endTime) : '', // 使用统一的时间解析函数
      passMark: formData.passingScore ? Number(formData.passingScore) : 0,
      isUrge: formData.remindUncompleted,
      urgeDeadline: formData.remindUncompleted ? Number(formData.remindHours) : 0
    };

    try {
      let res;
      
      if (isEdit.value) {
        // 编辑模式：调用updateAssignmentById接口
        params.id = Number(assignmentId.value);
        res = await AssignmentApi.updateAssignmentReleaseById(params) as any;
      } else {
        // 新发布模式：调用createAssignmentRelease接口
        params.id = 0;
        params.courseId = Number(courseId.value);
        params.assignmentId = Number(assignmentId.value);
        params.distributionTarget = formData.distributionTarget;
        params.isRandom = formData.randomQuestions;
        params.randomCount = formData.randomQuestions ? Number(formData.randomCount) : 0;
        params.questionCount = formData.questionCount; // 添加题目总数
        // 根据发放对象类型设置不同的目标ID
        if (formData.distributionTarget === 0) {
          // 班级模式：只传递班级ID数组
          params.groupIds = selectedClasses.map(item => Number(item.id));
        } else {
          // 个人模式：传递班级ID和学生ID数组
          params.groupIds = selectedClasses.map(item => Number(item.id));
          
          // 收集所有选中班级的选中学生
          const selectedStudents = [];
          selectedClasses.forEach(classItem => {
            if (classItem.selectedStudentIds && classItem.selectedStudentIds.length > 0) {
              selectedStudents.push(...classItem.selectedStudentIds);
            }
          });
          
          params.userIds = selectedStudents;
        }
        
        res = await AssignmentApi.createAssignmentRelease(params) as any;
      }

      if (res.code === 0) {
        sheep.$helper.toast(isEdit.value ? '修改成功' : '发放成功');
        setTimeout(() => {
          sheep.$router.back();
        }, 1500);
      } else {
        sheep.$helper.toast(res.msg || (isEdit.value ? '修改失败' : '发放失败'));
      }
    } catch (error) {
      console.error('提交失败:', error);
      sheep.$helper.toast(isEdit.value ? '修改失败' : '发放失败');
    }
  };

  const isAllSelected = computed(() => {
    return classList.value.every(item => item.selected);
  });

  // 切换全选/取消全选
  const toggleSelectAll = () => {
    const newState = !isAllSelected.value;
    classList.value.forEach(item => {
      item.selected = newState;
      if (newState) {
        // 选中时，设置选中学生数为总学生数
        item.selectedStudents = item.totalStudents;
        item.selectedStudentIds = item.users.map(user => user.id);
      } else {
        // 取消选中时，清空选中学生数据
        item.selectedStudents = 0;
        item.selectedStudentIds = [];
      }
    });
  };

  // 时间格式化函数 - 将时间戳转换为本地时间字符串
  const formatLocalDateTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // 时间解析函数 - 将本地时间字符串转换为时间戳
  const parseLocalDateTime = (dateTimeString: string): number => {
    // 直接使用Date构造函数解析本地时间字符串
    return new Date(dateTimeString.replace(/-/g, '/')).getTime();
  };

  onMounted(() => {
    // 获取URL参数
    getParams();
    
    // 获取班级列表数据
    getClassList();
    
    // 如果是编辑模式，获取作业详情
    if (isEdit.value) {
      getAssignmentDetail();
    } else {
      // 非编辑模式，获取作业库题目总数
      getAssignmentInfo();
    }
    
    // 监听学生选择结果
    listenStudentSelection();
  });

  // 组件卸载时移除事件监听
  onUnmounted(() => {
    // @ts-ignore
    uni.$off('studentSelected');
  });
</script>

<style scoped lang="scss">
  .issuance-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    position: relative;
    padding-bottom: 150rpx;
  }

  // 盒子通用样式
  .setting-box {
    background: #FFFFFF;
    box-shadow: 1rpx 1rpx 9rpx 0rpx rgba(211,223,230,0.27);
    border-radius: 25rpx;
    padding: 0 30rpx;
    margin: 0 30rpx 26rpx;
  }

  .basic-info {
    height: 324rpx;
  }

  .released-obj {
    height: auto;
    padding: 20rpx 30rpx;
  }

  .advanced-setting {
    padding: 0 30rpx;
    margin: 0 30rpx 26rpx;
    height: 404rpx;
    background: #FFFFFF;
    box-shadow: 1rpx 1rpx 9rpx 0rpx rgba(211,223,230,0.27);
    border-radius: 25rpx;
  }

  .box-title {
    font-weight: 480;
    margin-left: 30rpx;
    margin-right: 30rpx;
    margin-top: 20rpx;
    margin-bottom: 39rpx;
    height: 29rpx;
    font-family: PingFang SC;
    font-size: 30rpx;
    color: #3E3E3E;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 30rpx;

    .title-left {
      display: flex;
      align-items: center;
      gap: 8rpx;
    }

    .select-all {
      font-size: 26rpx;
      color: #3DB3F2;
    }

    .arrow-icon {
      width: 22rpx;
      height: 12rpx;
      position: relative;
      
      &:before,
      &:after {
        content: '';
        position: absolute;
        width: 12rpx;
        height: 2rpx;
        background-color: #3E3E3E;
        top: 50%;
        transition: transform 0.3s;
      }
      
      &:before {
        left: 0;
        transform: rotate(45deg);
      }
      
      &:after {
        right: 0;
        transform: rotate(-45deg);
      }
      
      &.is-expanded {
        &:before {
          transform: rotate(-45deg);
        }
        
        &:after {
          transform: rotate(45deg);
        }
      }
    }
  }

  .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 108rpx;
    border-bottom: 1px solid #f5f5f5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 29rpx;
      color: #3E3E3E;
    }
    
    .input-wrap {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      
      input {
        text-align: right;
        height: 25rpx;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 25rpx;
        color: #989898;
        
        &:disabled {
          opacity: 0.6;
          background-color: #f5f5f5;
        }
      }
      
      .placeholder {
        color: #989898;
        background-color: #f8f8f8;
        padding: 10rpx 20rpx;
        border-radius: 6rpx;
      }
    }
  }
  
  .select-wrap {
    display: flex;
    align-items: center;
    
    .select-text {
      margin-right: 10rpx;
      height: 25rpx;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 25rpx;
      color: #3DB3F2;
    }
    
    .arrow-icon {
      width: 22rpx;
      height: 12rpx;
      position: relative;
      margin-top: 6rpx;
      &:before,
      &:after {
        content: '';
        position: absolute;
        width: 12rpx;
        height: 2rpx;
        background-color: #3DB3F2;
        top: 50%;
      }
      &:before {
        left: 0;
        transform: rotate(45deg);
      }
      &:after {
        right: 0;
        transform: rotate(-45deg);
      }
    }
  }
  
  .class-list {
    width: 100%;
  }

  .class-item {
    padding: 30rpx 0;
    border-bottom: 1px solid #EBEEF5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:last-child {
      border-bottom: none;
    }
  }

  .checkbox-wrap {
    display: flex;
    align-items: center;
    flex: 1;
    
    .checkbox {
      width: 35rpx;
      height: 35rpx;
      border-radius: 50%;
      border: 1px solid #DCDFE6;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      &.active {
        border-color: #2FBDF5;
        background-color: #2FBDF5;
      }
      
      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        
        &:not(.active) {
          border-color: #DCDFE6;
          background-color: #F5F7FA;
        }
      }
      
      .check-icon {
        width: 16rpx;
        height: 10rpx;
        border-left: 2px solid #fff;
        border-bottom: 2px solid #fff;
        transform: rotate(-45deg);
        margin-bottom: 4rpx;
      }
    }
    
    .class-name {
      font-size: 29rpx;
      font-family: PingFang SC;
      color: #3E3E3E;
    }
  }

  .student-count {
    display: flex;
    align-items: center;
    padding-left: 20rpx;
    
    .count-text {
      font-size: 26rpx;
      color: #999;
      margin-right: 10rpx;
    }
    
    .arrow-right-icon {
      width: 12rpx;
      height: 22rpx;
      
      &.disabled {
        opacity: 0.6;
      }
    }
  }
  
  .remind-item, .random-item {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 20rpx 0;
    
    .remind-left, .random-left {
      flex: 1;
    }
    
    .switch-control {
      margin-top: 5rpx;
      
      &:disabled {
        opacity: 0.6;
      }
    }
  }
  
  .remind-item {
    .remind-left {
      .time-container {
        display: flex;
        align-items: center;
        margin-top: 10rpx;
      }
    }
  }
  
  .time-container {
    display: flex;
    align-items: center;
    margin-top: 10rpx;
    
    .light-text {
      font-size: 24rpx;
      font-family: PingFang SC;
      color: #989898;
    }
    
    .hours-input {
      width: 66rpx;
      height: 42rpx;
      border-radius: 6rpx;
      border: 1px solid #979696;
      background: #f8f8f8;
      text-align: center;
      margin: 0 10rpx;
      font-size: 24rpx;
      color: #3E3E3E;
    }
  }
  
  .random-item {
    .random-left {
      .random-count-container {
        display: flex;
        align-items: center;
        margin-top: 10rpx;
      }
    }
  }
  
  .random-setting, .random-count-container {
    display: flex;
    align-items: center;
    margin-top: 10rpx;
    
    .light-text {
      font-size: 24rpx;
      font-family: PingFang SC;
      color: #989898;
    }
    
    .count-input {
      width: 92rpx;
      height: 50rpx;
      background: #f8f8f8;
      text-align: center;
      margin: 0 10rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      color: #3E3E3E;
    }
  }
  
  .light-text-container {
    display: flex;
    align-items: center;
    padding-left: 30rpx;
    margin-top: -10rpx;
    margin-bottom: 20rpx;
    
    .light-text {
      font-size: 24rpx;
      font-family: PingFang SC;
      color: #989898;
    }
    
    .count-input {
      width: 92rpx;
      height: 50rpx;
      background: #f8f8f8;
      text-align: center;
      margin: 0 10rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      color: #3E3E3E;
    }
  }
  
  .bottom-btn {
    height: 82rpx;
    background: linear-gradient(270deg, rgba(0,222,255,0.89), rgba(70,173,240,0.89));
    border-radius: 41rpx;
	z-index: 2;
    position: fixed;
    bottom: 100rpx;
    left: 30rpx;
    right: 30rpx;
    background: #2FBDF5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 36rpx;
    font-weight: 500;
  }

  .empty-state {
    height: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 28rpx;
  }
</style>
