export const CertificationStatusEnum = {
  PENDING: { status: 0, description: '待审核' },
  APPROVED: { status: 1, description: '已通过' },
  REJECTED: { status: 2, description: '被拒绝' },
};

/**
 * 根据 status 获取对应的描述
 * @param {number} status - 状态值
 * @returns {string} 对应的描述
 */
export const getDescriptionByStatus = (status) => {
  const entry = Object.values(CertificationStatusEnum).find(
    (item) => item.status === status
  );
  return entry ? entry.description : '';
}
