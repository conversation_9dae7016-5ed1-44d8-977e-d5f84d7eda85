<template>
	<view class="container">
		<easy-navbar custom-class="title" :title="title" />

		<view class="form-container">
			<view class="form-card">
				<!-- 作业标题 -->
				<view class="form-item">
					<view class="form-row">
						<text class="label">作业标题</text>
						<uni-easyinput class="input" :inputBorder="false" :clearable="true" v-model="assignmentTitle"
							placeholder="请输入作业标题"></uni-easyinput>
					</view>
				</view>

				<view class="divider"></view>

				<!-- 评分机制 -->
				<view class="form-item">
					<view class="form-row">
						<text class="label">评分机制</text>
						<view class="score-type">
							<view class="type-wrapper">
								<view class="type-item" :class="{ active: scoreType === 'percentage' }"
									@tap="scoreType = 'percentage'">
									百分制
									<view v-if="scoreType === 'percentage'" class="check-icon">✓</view>
								</view>
								<text class="type-desc">平均分配每道题的分值</text>
							</view>
							<view class="type-wrapper">
								<view class="type-item" :class="{ active: scoreType === 'custom' }"
									@tap="scoreType = 'custom'">
									自定义
									<view v-if="scoreType === 'custom'" class="check-icon">✓</view>
								</view>
								<text class="type-desc">自行设置每道题的分值</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 下一步按钮 -->
			<button class="next-btn" @tap="handleNext">下一步</button>
		</view>
	</view>
</template>

<script setup lang="ts">
	import {
		ref,
		onMounted
	} from 'vue';
	import sheep from '@/sheep';
	import {
		onLoad
	} from '@dcloudio/uni-app';
	import AssignmentApi from '@/sheep/api/course/assignment.js';
	// 定义类型
	interface ApiResponse<T> {
		code : number;
		data : T;
		msg ?: string;
	}


	const title = ref('创建作业');
	const assignmentTitle = ref('');
	const scoreType = ref('percentage');
	const courseId = ref(null)

	// 初始化作业标题
	onMounted(() => {
		const today = new Date();
		const dateStr = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(
			today.getDate(),
		).padStart(2, '0')}`;
		assignmentTitle.value = `作业${dateStr}`;
	});

	// 处理下一步
	const handleNext = async () => {
		const type = scoreType.value == "percentage" ? 1 : 0
		try {
			const res = await AssignmentApi.createAssignment({
				courseId: courseId.value,
				name: assignmentTitle.value,
				scoringMechanism: type
			}) as unknown as ApiResponse<any>;
			if (res.code === 0 && res.data) {
				if (scoreType.value === "percentage") {  //百分比评分机制
					sheep.$router.go(`/pages/course/assignment/score-percentage?scoreType=${scoreType.value}&courseId=${courseId.value}&assignmentId=${res.data}`);
				} else {  //自定义评分
					sheep.$router.go(`/pages/course/assignment/score-custom?scoreType=${scoreType.value}&courseId=${courseId.value}&assignmentId=${res.data}`);
				}
			}
		} catch (error) {
			console.error('创建课程作业失败:', error);

		}

	};



	onLoad((options) => {
		courseId.value = options.courseId
	})
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #fff;
		position: relative;
	}

	.title {
		font-size: 18.75pt !important;
		color: #383838 !important;
		font-family: '黑体', 'SimHei', 'Heiti SC', sans-serif !important;
	}

	.form-container {
		padding: 42rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.form-card {
		width: 691rpx;
		background: #ffffff;
		box-shadow: 1rpx 1rpx 9rpx 0rpx rgba(211, 223, 230, 0.27);
		border-radius: 25rpx;
		border: 1px solid rgba(233, 247, 255, 0.9);
		padding: 40rpx;
		box-sizing: border-box;
	}

	.divider {
		width: 632rpx;
		height: 1rpx;
		background: #eeeeee;
		margin: 30rpx auto;
	}

	.form-item {
		margin-bottom: 30rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-row {
		display: flex;
		align-items: center;
		gap: 30rpx;
		position: relative;
	}

	.label {
		font-size: 28rpx;
		color: #333;
		white-space: nowrap;
		min-width: 120rpx;
	}

	.input {
		flex: 1;
		height: 80rpx;
		border: none;
		padding: 0 20rpx;
		font-size: 28rpx;
		background: transparent;
	}

	.score-type {
		flex: 1;
		display: flex;
		gap: 30rpx;
	}

	.type-wrapper {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 10rpx;
	}

	.type-item {
		width: 194rpx;
		height: 69rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 2px solid #bebebe;
		border-radius: 15rpx;
		font-size: 28rpx;
		color: #666;
		position: relative;
		overflow: hidden;

		&.active {
			border: 2px solid #46adf0;
			background-color: transparent;
			color: #46adf0;
		}
	}

	.check-icon {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 31rpx;
		height: 24rpx;
		background: #46adf0;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 17rpx;
		border-bottom-right-radius: 15rpx;
		border-top-left-radius: 50rpx;
	}

	.type-desc {
		width: 207rpx;
		margin-top: 16rpx;
		font-family: PingFang SC;
		font-weight: 400;
		font-size: 21rpx;
		color: #d0d0d0;
		text-align: center;
	}

	.next-btn {
		margin-top: 46rpx;
		width: 691rpx;
		height: 82rpx;
		line-height: 82rpx;
		background: linear-gradient(270deg,  rgba(70, 173, 240, 0.89),rgba(0, 222, 255, 0.89));
		border-radius: 41rpx;
		color: #fff;
		font-size: 32rpx;
		text-align: center;
	}
</style>