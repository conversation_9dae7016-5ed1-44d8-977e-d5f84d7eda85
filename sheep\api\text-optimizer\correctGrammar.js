import request from '@/sheep/request';

// 语法纠错API
const CorrectGrammarApi = {
  correctGrammar: (data, config) => {
    return request({
      url: '/ai/grammar-correction/correct-grammarStream',
      method: 'POST',
      data: data,
      custom: {
        showLoading: false
      },
      ...config
    });
  },

  // 获取消息分页接口
  getMessagePage: (pageNo, pageSize, conversationId, userId, content, createTime) => {
    return request({
      url: '/ai/grammar-correction/page',
      method: 'GET',
      params: {
        pageNo,       // 页码，从 1 开始
        pageSize,     // 每页条数，最大值为 100
        conversationId, // 对话编号（可选）
        userId,       // 用户编号（可选）
        content,      // 消息内容（可选）
        createTime,   // 创建时间（可选），数组格式 ['2023-01-01', '2023-12-31']
      },
      custom: {
        showLoading: false,
      },
    });
  },

  // 获得指定对话的消息列表接口
  getMessageListByConversationId: (conversationId) => {
    return request({
      url: '/ai/grammar-correction/list-by-conversation-id',
      method: 'GET',
      params: {
        conversationId, // 对话编号
      },
      custom: {
        showLoading: false,
      },
    });
  },
};

export default CorrectGrammarApi;
