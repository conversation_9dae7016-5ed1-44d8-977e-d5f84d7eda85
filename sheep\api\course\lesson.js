import request from '@/sheep/request';

const LessonApi = {
  
  // 获取课程详情
  getLesson: (id) => {
    return request({
      url: '/course/lesson/get',
      method: 'GET',
      params: { id },
    });
  },

  getQualityLessons: (params) => {
    return request({
      url: `/course/quality-lesson/page`,
      method: 'GET',
      params,
    });
  },
  
  // 获取课程附件列表
  getLessonAttachmentList: (courseId) => {
    return request({
      url: '/course/lesson/lesson-attachment/list-by-course-id',
      method: 'GET',
      params: { courseId },
    });
  },
  
  // 搜索课程附件
  searchLessonAttachments: (keyword, courseId) => {
    return request({
      url: '/course/lesson/lesson-attachment/search',
      method: 'GET',
      params: { keyword, courseId },
    });
  },
  
  // 获取班级关联的课程列表
  getLessonListByGroupId: (groupId) => {
    return request({
      url: '/course/lesson/list-by-group-id',
      method: 'GET',
      params: { groupId },
    });
  },
  
  // 获取班级下的课程分页
  getLessonPageByClass: (params) => {
    return request({
      url: '/course/lesson/page/class',
      method: 'GET',
      params,
      custom: {
        showLoading: false,
      }
    });
  },
  
  // 获取我创建的课程列表
  getCreatedLessonList: () => {
    return request({
      url: '/course/lesson/page/created',
      method: 'GET',
    });
  },
  
  // 获取我收藏的课程分页
  getFavoriteLessonPage: (params) => {
    return request({
      url: '/course/lesson/page/favourite',
      method: 'GET',
      params,
    });
  },
  
  // 获取我加入的班级关联的课程列表
  getJoinedLessonList: () => {
    return request({
      url: '/course/lesson/list/joined',
      method: 'GET',
    });
  },
  
  // 搜索课程
  searchLessons: (data) => {
    return request({
      url: '/course/lesson/search',
      method: 'POST',
      data,
    });
  },
  
  // 获取课程关联的班级列表
  getClassesByCourseId: (courseId) => {
    return request({
      url: '/course/member-group-lesson/course-classes',
      method: 'GET',
      params: { courseId },
    });
  },
  
  // 搜索课程关联的班级列表
  searchCourseClasses: (params) => {
    return request({
      url: '/course/member-group-lesson/search',
      method: 'GET',
      params,
    });
  },
  
  // 创建班级-课程关联
  createCourseClassRelation: (data) => {
    return request({
      url: '/course/member-group-lesson/create',
      method: 'POST',
      data,
    });
  },
  
  // 删除班级-课程关联
  deleteCourseClassRelation: (id) => {
    return request({
      url: '/course/member-group-lesson/delete',
      method: 'DELETE',
      params: { id },
    });
  },

  // 批量分享课程到班级
  batchShareClassCourses: (data) => {
    return request({
      url: '/course/member-group-lesson/batch-share',
      method: 'POST',
      data,
    });
  },

  // 批量删除班级中的课程
  batchDeleteClassCourses: (data) => {
    return request({
      url: '/course/member-group-lesson/batch-delete',
      method: 'POST',
      data,
    });
  },

  // 批量为课程添加班级
  batchCreateGroupsInCourse: (data) => {
    return request({
      url: '/course/member-group-lesson/batch-create-groups',
      method: 'POST',
      data,
    });
  },

  // 批量删除课程中的班级
  batchDeleteGroupsInCourse: (data) => {
    return request({
      url: '/course/member-group-lesson/batch-delete-groups',
      method: 'POST',
      data,
    });
  },
  
  // 更新课程附件名称
  updateAttachmentTitle: (id, title) => {
    return request({
      url: '/course/lesson/update-attachment',
      method: 'PUT',
      params: { id, title },
    });
  },
  
  // 删除课程附件
  deleteAttachment: (id) => {
    return request({
      url: '/course/lesson/delete-attachment',
      method: 'DELETE',
      params: { id },
    });
  },
};

export default LessonApi;
