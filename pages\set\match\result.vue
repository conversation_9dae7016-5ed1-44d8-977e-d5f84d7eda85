<template>
	<view class="complete-container">
		<!-- 顶部导航栏，传入非空title -->
		<easy-navbar :transparent="true"/>

		<!-- 背景波浪 -->
		<view class="wave-container">
			<image :src="sheep.$url.cdn('/set/cloud.png')" class="cloud-image" mode="widthFix" />
		</view>

		<!-- 主要内容区域 -->
		<view class="content">
			<!-- 中间图片 -->
			<image :src="sheep.$url.cdn('/set/homework.png')" class="homework-image" mode="widthFix" />

			<!-- 完成文本 -->
			<text class="complete-title">你的用时</text>
			<text class="complete-time">{{time}}秒</text>
			<text class="complete-record" v-if="bestTime">你的用时最少记录是{{bestTime}}秒</text>
		</view>

		<!-- 底部按钮区域 -->
		<view class="button-container">
			<view class="continue-button" @click="handlePlayAgain">再玩一次</view>
			<view class="back-button" @click="handleBack">返回</view>
		</view>
	</view>
</template>

<script setup>
	import sheep from '@/sheep';
	import EasyNavbar from '@/components/easy-navbar/easy-navbar.vue';
	import { ref } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';

	// 定义数据
	const time = ref('0.0');
	const bestTime = ref('');
	const setId = ref('');

	// 获取页面参数
	onLoad((options) => {
		if (options.time) {
			time.value = options.time;
		}
		if (options.setId) {
			setId.value = options.setId;
		}
		
		// 获取最佳时间记录
		getBestTime();
	});
	
	// 获取最佳记录
	const getBestTime = () => {
    const key = `match_best_time_${setId.value}`;
    const savedTime = uni.getStorageSync(key);

    // 如果有记录且不是首次玩
    if (savedTime) {
      // 如果当前成绩比历史最佳还好，更新记录
      if (parseFloat(time.value) < parseFloat(savedTime)) {
        uni.setStorageSync(key, time.value);
        bestTime.value = time.value;
      } else {
        bestTime.value = savedTime;
      }
    } else {
      // 首次玩，设置当前为最佳
      uni.setStorageSync(key, time.value);
    }
	};

	// 方法
	const handlePlayAgain = () => {
		// 重新开始匹配游戏
		sheep.$router.go('/pages/set/match/match', { setId: setId.value },{redirect: true});
	};

	const handleBack = () => {
		// 返回上一页
    	sheep.$router.back();
	};
</script>

<style scoped>
	.complete-container {
		width: 100%;
		height: 100vh;
		background: linear-gradient(180deg, #59c0fb 0%, #5dc7fb 50%, #46adf5 100%);
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	/* 云朵波浪背景 */
	.wave-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	.cloud-image {
		position: absolute;
		bottom: 0;
		width: 100%;
		z-index: 1;
	}

	/* 中间内容区域 */
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;
		z-index: 2;
	}

	.homework-image {
		width: 80%;
		max-width: 600rpx;
		margin-bottom: 60rpx;
	}

	.complete-title {
		font-size: 48rpx;
		color: #fff;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20rpx;
	}
	
	.complete-time {
		font-size: 80rpx;
		color: #fff;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20rpx;
	}
	
	.complete-record {
		font-size: 36rpx;
		color: #fff;
		text-align: center;
	}

	/* 底部按钮区域 */
	.button-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 80rpx;
		position: relative;
		z-index: 2;
	}

	.continue-button {
		width: 80%;
		height: 100rpx;
		background: linear-gradient(90deg, #0ccd57 0%, #0cde72 100%);
		border-radius: 50rpx;
		color: #fff;
		font-size: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
		box-shadow: 0 6rpx 12rpx rgba(12, 205, 87, 0.3);
	}

	.back-button {
		width: 80%;
		height: 100rpx;
		background: linear-gradient(90deg, #0ccd57 0%, #0cde72 100%);
		border-radius: 50rpx;
		color: #fff;
		font-size: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 6rpx 12rpx rgba(12, 205, 87, 0.3);
	}
</style>
