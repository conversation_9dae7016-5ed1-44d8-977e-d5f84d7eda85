<template>
  <view class="course-navbar">
    <!-- 导航栏 -->
    <view class="navbar-area" :style="{ paddingTop: `${paddingTop}px` }">
      <view class="left">
        <image
          v-if="showBack"
          class="back-icon"
          :src="sheep.$url.cdn('/common/back.png')"
          @click="goBack"
        />
        <text class="navbar-title">{{ title }}</text>
      </view>
      
      <view class="right">
        <!-- 搜索框 - 根据状态切换不同的样式 -->
        <view :class="['search-container', { 'search-active': isSearchActive }]">
          <!-- 搜索图标和框 -->
          <view class="search-icon-box" @click="expandSearch" v-if="!isSearchActive">
            <image class="search-icon" :src="sheep.$url.cdn('/course/search.png')" mode="aspectFit"></image>
          </view>
          
          <!-- 展开后的输入框 -->
          <template v-if="isSearchActive">
            <view class="search-input-wrapper">
              <image class="search-icon-small" :src="sheep.$url.cdn('/course/search.png')" mode="aspectFit"></image>
              <input 
                class="search-input"
                type="text"
                v-model="searchKeyword"
                :placeholder="placeholder"
                confirm-type="search"
                @confirm="onSearchConfirm"
                @blur="onInputBlur"
                ref="searchInputRef"
              />
              <text class="clear-icon" @click="clearSearch" v-if="searchKeyword">&#10005;</text>
            </view>
            <view class="search-btn" @click="doSearch">搜索</view>
          </template>
          
          <!-- 收起状态的文字 -->
          <text class="search-text" @click="expandSearch" v-if="!isSearchActive">{{ placeholder }}</text>
        </view>
        
        <!-- 右侧插槽 -->
        <slot name="right"></slot>
      </view>
    </view>

    <!-- 占位元素，防止内容被固定导航栏遮挡 -->
    <view class="navbar-placeholder" :style="{ height: `${navbarHeight}px` , background: `#ffffff`}"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import sheep from '@/sheep';

const props = defineProps({
  title: {
    type: String,
    default: '课程'
  },
  showBack: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: String,
    default: '搜课程'
  }
});

const emit = defineEmits(['search', 'clear']);

// 导航栏高度相关
const paddingTop = ref(0);
const navbarHeight = ref(0);

// 搜索相关
const isSearchActive = ref(false);
const searchKeyword = ref('');
const searchInputRef = ref(null);

// 展开搜索框并聚焦
const expandSearch = () => {
  isSearchActive.value = true;
  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    setTimeout(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus();
      }
    }, 100);
  });
};

// 失去焦点时关闭搜索框（如果没有搜索词）
const onInputBlur = () => {
  // 如果没有搜索词则关闭搜索框
  if (!searchKeyword.value) {
    isSearchActive.value = false;
    emit('clear');
  } else {
    // 有搜索词则触发搜索
    doSearch();
  }
};

// 清空搜索框
const clearSearch = () => {
  searchKeyword.value = '';
  if (searchInputRef.value) {
    searchInputRef.value.focus();
  }
  emit('clear');
};

// 执行搜索
const doSearch = () => {
  if (!searchKeyword.value.trim()) {
    // 如果搜索词为空，关闭搜索框
    isSearchActive.value = false;
    emit('clear');
    return;
  }
  
  emit('search', searchKeyword.value);
};

// 搜索框回车事件
const onSearchConfirm = () => {
  if (!searchKeyword.value.trim()) {
    // 如果搜索词为空，关闭搜索框
    isSearchActive.value = false;
    emit('clear');
    return;
  }
  doSearch();
};

// 返回上一页
const goBack = () => {
  sheep.$router.back();
};

// 设置导航栏高度
onMounted(() => {
  // #ifdef MP-WEIXIN
  // 获取胶囊按钮信息
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
  // 使用与easy-navbar相同的方式设置导航栏高度
  paddingTop.value = menuButtonInfo.top;
  navbarHeight.value = paddingTop.value + 35;
  // #endif

  // #ifndef MP-WEIXIN
  // 非微信环境获取状态栏高度
  const systemInfo = sheep.$helper.sys();
  paddingTop.value = systemInfo.marginTop;
  navbarHeight.value = paddingTop.value + 35;
  // #endif
});
</script>

<style scoped lang="scss">
.course-navbar {
  width: 100%;
}

.navbar-area {
  display: flex;
  align-items: center;
  height: 35px;
  width: 100%;
  z-index: 999;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #FFFFFF;
  transition: all 0.05s ease;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.left {
  position: absolute;
  left: 15px;
  display: flex;
  align-items: center;
  z-index: 101;
}

.back-icon {
  width: 19rpx;
  height: 32rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  padding: 10rpx;
}

.navbar-title {
  padding: 10rpx 0;
  font-size: 34rpx;
  color: #333;
  font-weight: 500;
  margin-left: 5rpx;
}

.right {
  position: absolute;
  right: 194rpx;
  display: flex;
  align-items: center;
  z-index: 101;
}

.search-container {
  display: flex;
  align-items: center;
  height: 60rpx;
  background-color: #f2f3f5;
  border-radius: 30rpx;
  padding-right: 20rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
  width: 180rpx;
}

.search-active {
  width: 380rpx !important;
  padding: 0 10rpx;
}

.search-icon-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #30BFF4;
}

.search-icon {
  width: 26rpx;
  height: 26rpx;
}

.search-icon-small {
  width: 32rpx;
  height: 32rpx;
  margin: 0 10rpx;
}

.search-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  height: 100%;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.clear-icon {
  font-size: 24rpx;
  color: #999;
  padding: 0 10rpx;
}

.search-btn {
  font-size: 28rpx;
  color: #30BFF4;
  padding: 0 10rpx;
}
</style> 