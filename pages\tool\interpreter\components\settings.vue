<template>
  <view class="settings-page">
    <!-- 导航栏 -->
    <easy-navbar title="对话信息">
      <template #right>
        <view class="nav-menu-btn" @click="showMenu">
          <view class="menu-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <view class="menu-circle">
            <text class="menu-icon">?</text>
          </view>
        </view>
      </template>
    </easy-navbar>

    <!-- 设置内容 -->
    <view class="settings-content">
      <!-- 对话成员 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">对话成员({{ dialogInfo.members.length }})</text>
        </view>
        <view class="members-container">
          <view
            class="member-item"
            v-for="member in dialogInfo.members"
            :key="member.id"
          >
            <text class="member-name">{{ member.name }}</text>
          </view>
        </view>
      </view>
      <!-- 对话名称 -->
      <view class="section">
        <view class="setting-item" @click="editDialogName">
          <text class="setting-label">对话名称</text>
          <view class="setting-value">
            <text class="value-text">{{ dialogInfo.name }}</text>
            <image class="arrow-icon" :src="arrowIcon" />
          </view>
        </view>
      </view>

      <!-- 字体大小 -->
      <view class="section">
        <view class="setting-item" @click="adjustFontSize">
          <text class="setting-label">字体大小</text>
          <image class="arrow-icon" :src="arrowIcon" />
        </view>
      </view>

      <!-- 消息免打扰 -->
      <view class="section">
        <view class="setting-item">
          <text class="setting-label">消息免打扰</text>
          <switch
            class="switch-control"
            :checked="isDoNotDisturb"
            @change="toggleDoNotDisturb"
            color="#2DC1FF"
          />
        </view>
      </view>

      <!-- 删除并退出 -->
      <view class="section delete-section">
        <view class="delete-button" @click="deleteAndExit">
          <text class="delete-text">删除并退出</text>
        </view>
      </view>
    </view>

    <!-- 字体设置弹窗 -->
    <view v-if="showFontPopup" class="font-popup-overlay" @click="closeFontPopup">
      <view class="font-popup" @click.stop>
        <!-- 完成按钮 -->
        <view class="popup-header">
          <view class="complete-btn" @click="completeFontSetting">
            <text class="complete-text">完成</text>
          </view>
        </view>

        <!-- 字体预览 -->
        <view class="font-preview">
          <text class="preview-text" :style="{ fontSize: currentFontSize + 'rpx' }">Aa</text>
        </view>

        <!-- 滑块控制 -->
        <view class="slider-container">
          <!-- 滑块轨道和节点 -->
          <view class="slider-track-wrapper">
            <!-- 滑块轨道 -->
            <view class="slider-track">
              <!-- 节点标记 -->
              <view class="track-node" style="left: 0"></view>
              <view class="track-node" style="left: 50%; transform: translateY(-50%) translateX(-50%);"></view>
              <view class="track-node" style="right: 0"></view>
            </view>
            <!-- 原生滑块 -->
            <slider
              class="font-slider"
              :value="fontSizeValue"
              :min="0"
              :max="2"
              :step="1"
              @change="onSliderChange"
              activeColor="transparent"
              backgroundColor="transparent"
              block-color="#FFFFFF"
              block-size="24"
            />
          </view>
        </view>

        <!-- 字体大小标签 -->
        <view class="size-labels">
          <text class="size-label small-label">小</text>
          <text class="size-label normal-label">标准</text>
          <text class="size-label large-label">大</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import sheep from '@/sheep'

// 图标资源
const arrowIcon = sheep.$url.cdn('/my/come2.png')

// 对话信息
const dialogInfo = ref({
  name: '用户codes',
  members: [
    { id: 1, name: '用户1857' },
    { id: 2, name: '用户1857' }
  ]
})

// 消息免打扰状态
const isDoNotDisturb = ref(true)

// 字体设置弹窗状态
const showFontPopup = ref(false)

// 字体大小设置 - 使用滑块控制
const fontSizeValue = ref(1) // 0=小, 1=标准, 2=大
const fontSizes = [20, 25, 30] // 对应的字体大小

// 当前字体大小
const currentFontSize = computed(() => fontSizes[fontSizeValue.value])

// 显示菜单
const showMenu = () => {
  console.log('显示菜单')
  // 这里可以添加菜单逻辑
}

// 编辑对话名称
const editDialogName = () => {
  console.log('编辑对话名称')
}

// 调整字体大小
const adjustFontSize = () => {
  showFontPopup.value = true
}

// 滑块值改变时的处理
const onSliderChange = (e) => {
  fontSizeValue.value = e.detail.value
  const fontSize = fontSizes[fontSizeValue.value]

  // 保存字体设置到本地存储
  uni.setStorageSync('chatFontSize', fontSize)

  // 发送事件通知chat页面更新字体大小
  uni.$emit('fontSizeChanged', fontSize)
}

// 完成字体设置
const completeFontSetting = () => {
  showFontPopup.value = false
}

// 关闭字体弹窗
const closeFontPopup = () => {
  showFontPopup.value = false
}

// 组件挂载时初始化字体大小
onMounted(() => {
  // 从本地存储读取当前字体大小设置
  const savedFontSize = uni.getStorageSync('chatFontSize')
  if (savedFontSize) {
    // 根据保存的字体大小找到对应的滑块位置
    const index = fontSizes.findIndex(size => size === savedFontSize)
    if (index !== -1) {
      fontSizeValue.value = index
    }
  } else {
    // 如果没有保存的设置，默认使用标准大小并保存
    const defaultFontSize = fontSizes[1] // 标准大小 25rpx
    fontSizeValue.value = 1
    uni.setStorageSync('chatFontSize', defaultFontSize)
    // 通知chat页面使用默认字体大小
    uni.$emit('fontSizeChanged', defaultFontSize)
  }
})

// 切换免打扰状态
const toggleDoNotDisturb = (e) => {
  isDoNotDisturb.value = e.detail.value
  console.log('免打扰状态:', isDoNotDisturb.value)
}

// 删除并退出
const deleteAndExit = () => {
  uni.showModal({
    title: '确认删除',
    content: '删除后将无法恢复，确定要删除并退出对话吗？',
    success: (res) => {
      if (res.confirm) {
        console.log('确认删除并退出')
        // 这里可以添加删除逻辑
      }
    }
  })
}
</script>

<style scoped lang="scss">
.settings-page {
  min-height: 100vh;
  background: #F8F8F8;
}

// 导航栏右侧菜单按钮
.nav-menu-btn {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 8rpx;
}

.menu-dots {
  display: flex;
  flex-direction: column;
  gap: 4rpx;

  .dot {
    width: 6rpx;
    height: 6rpx;
    background: #333333;
    border-radius: 50%;
  }
}

.menu-circle {
  width: 48rpx;
  height: 48rpx;
  background: #333333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .menu-icon {
    color: #FFFFFF;
    font-size: 24rpx;
    font-weight: bold;
  }
}

.settings-content {
  padding: 0 0 32rpx 0;
}

.section {
  margin-bottom: 19rpx;
  width: 100%;
  background: #FFFFFF;
  border-radius: 16rpx;
}

// 对话成员部分
.section-header {
  padding: 33rpx 30rpx;
}

.section-title {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.members-container {
  height: 87rpx;
  display: flex;
  align-items: center;
  //justify-content: center;
  gap: 24rpx;
  padding-bottom: 31rpx;
  padding-left: 30rpx;
}

.member-item {
  width: 197rpx;
  height: 56rpx;
  background: #F5F5F5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.member-name {
  font-family: PingFang SC;
  font-size: 28rpx;
  color: #666666;
}

// 设置项通用样式
.setting-item {
  height: 110rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.setting-label {
  font-family: PingFang SC;
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}

.setting-value {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.value-text {
  font-family: PingFang SC;
  font-size: 28rpx;
  color: #999999;
}

.arrow-icon {
  width: 9rpx;
  height: 17rpx;
  opacity: 0.6;
}

// 开关控件
.switch-control {
  transform: scale(0.9);
  width: 89rpx;
}

// 删除按钮部分
.delete-section {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-button {
  background: transparent;
  border: none;
  padding: 0;
}

.delete-text {
  font-family: PingFang SC;
  font-size: 32rpx;
  color: #FF4444;
  font-weight: 400;
}

// 字体设置弹窗样式
.font-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.font-popup {
  width: 636rpx;
  height: 445rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  margin: 0 32rpx;
}

.popup-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 38rpx;
  padding-right: 61rpx;
  padding-top: 39rpx;
}

.complete-btn {
  padding: 0;
  background: transparent;
  border: none;
}

.complete-text {
  font-family: PingFang SC;
  font-size: 32rpx;
  color: #2DC1FF;
  font-weight: 400;
}

.font-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  height: 120rpx; // 固定高度防止布局挤压
  min-height: 120rpx;
}

.preview-text {
  font-family: PingFang SC;
  color: #333333;
  font-weight: 400;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-container {
  // margin-bottom: 32rpx;
  padding: 0 48rpx;
}

.slider-track-wrapper {
  position: relative;
  width: 507rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  margin: 0 auto;
}

.slider-track {
  position: absolute;
  top: 50%;
  left: 24rpx;
  right: 24rpx;
  height: 4rpx;
  background: #E5E5E5;
  border-radius: 2rpx;
  transform: translateY(-50%);
  z-index: 1;
}

.track-node {
  position: absolute;
  top: 50%;
  width: 4rpx;
  height: 16rpx;
  background: #E5E5E5;
  border-radius: 2rpx;
  transform: translateY(-50%);
  z-index: 2;
}

.font-slider {
  position: relative;
  width: 100%;
  height: 40rpx;
  z-index: 3;
}

.size-labels {
  display: flex;
  justify-content: space-between;
  padding: 27rpx 80rpx;
}

.size-label {
  font-family: PingFang SC;
  color: #666666;
  font-weight: 400;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 不同大小的标签
.small-label {
  font-size: 20rpx;
}

.normal-label {
  font-size: 25rpx;
}

.large-label {
  font-size: 30rpx;
}
</style>
