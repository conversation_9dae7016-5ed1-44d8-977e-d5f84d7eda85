/**
 * 录音管理器单例模块
 * 全局管理录音状态和事件处理
 * 避免多次实例化导致的资源浪费和事件冲突
 * 通过设置 shouldEvaluate 控制是否执行发音评估
 */

import sheep from '.';

// 获取录音管理器单例
const recorderManager = uni.getRecorderManager();

// 全局状态
let shouldEvaluate = true;
let currentCallback = null;
let recordStartTime = 0;
let isRecording = false; // 录音状态标记
let hasCancelledRecently = false; // 最近是否触发了取消事件,用于解决快速点击时发生的事件错误
const MIN_RECORD_DURATION = 1000 * 0.5; // 最小录音时长
const CANCEL_RESET_TIME = 1000 * 1.5; // 取消状态重置时间
// 新增分贝回调数组
let dbCallbacks = [];

// 计算分贝
recorderManager.onFrameRecorded(res => {
  const arrayBuffer = res.frameBuffer; // 获取 PCM 数据帧
  if (!arrayBuffer) return 0;

    // 将ArrayBuffer转换为Int16Array
    const int16Array = new Int16Array(arrayBuffer);
    let sum = 0;

    // 计算平方和
    for (let i = 0; i < int16Array.length; i++) {
      sum += int16Array[i] * int16Array[i];
    }

    // 计算均方根(RMS)
    const rms = Math.sqrt(sum / int16Array.length);

    // 将RMS转换为分贝值
    const referenceValue = 32767;
    const rawDb = 20 * Math.log10(rms / referenceValue);

    // 设置噪声门限值（可根据实际环境调整）
    const noiseGateThreshold = -50;

    // 低于门限的声音视为0分贝
    let normalizedDb = 0;
    if (rawDb > noiseGateThreshold) {
      // 将超过门限的声音映射到0-100的范围
      normalizedDb = Math.min(100, (rawDb - noiseGateThreshold) * 2);
    }

    // 通知所有注册的回调函数
    dbCallbacks.forEach(callback => {
      callback(normalizedDb);
    });

    return normalizedDb;
});

// 初始化事件监听器
recorderManager.onStop((res) => {
  console.log('录音结束');

  // 更新录音状态
  isRecording = false;

  // 检查录音时长
  const recordDuration = Date.now() - recordStartTime;

  let toastShown = false;

  // 1. 录音时间过短优先
  if (recordDuration < MIN_RECORD_DURATION) {
    console.log('录音时间过短:', recordDuration, 'ms');
    sheep.$helper.toast('录音时间过短，不进行评估');
    toastShown = true;
    shouldEvaluate = false;
    hasCancelledRecently = false;
  }
  // 2. 取消录音
  else if (!shouldEvaluate || hasCancelledRecently) {
    console.log('录音被取消');
    sheep.$helper.toast('已取消录音');
    toastShown = true;
    hasCancelledRecently = false; // 重置
  }

  // 3. 其它情况可按需添加

  if (shouldEvaluate && currentCallback) {
    // 检查临时文件是否存在且有效
    uni.getFileInfo({
      filePath: res.tempFilePath,
      success: (fileInfo) => {
        // 文件过小可能无效，小于1KB
        if (fileInfo.size < 1024) {
          console.log('录音文件过小:', fileInfo.size, 'bytes');
          if (!toastShown) sheep.$helper.toast('音时间过短，不进行评估');
          return;
        }

        console.log('录音处理完成');
        currentCallback(res.tempFilePath);
      },
      fail: (err) => {
        console.error('获取文件信息失败:', err);
        if (!toastShown) sheep.$helper.toast('录音文件异常，请重试');
      }
    });
  }

  // 重置状态
  setTimeout(() => {
    shouldEvaluate = true;
    currentCallback = null;
    recordStartTime = 0;
  }, 100);
});

// 录音错误处理
recorderManager.onError((error) => {
  console.error('录音错误:', error);
  // 更新录音状态
  isRecording = false;

  sheep.$helper.toast('录音发生错误')
});

// 录音开始事件
recorderManager.onStart(() => {
  console.log('录音开始');
  isRecording = true;
  recordStartTime = Date.now();

  // 检查是否最近触发了取消事件，如果是，则终止录音
  if (hasCancelledRecently) {
    console.log('检测到取消状态，停止录音');
    // 立即停止录音，不进行评估
    shouldEvaluate = false;
    recorderManager.stop();
  }
});

export default {
  // 开始录音
  start(options) {
    // 如果已经在录音，不要重复启动
    if (isRecording) {
      console.log('录音已在进行中');
      return;
    }

    shouldEvaluate = true;

    // 默认选项
    const defaultOptions = {
      format: 'wav',           // 使用wav格式
      sampleRate: 16000,       // 采样率
      numberOfChannels: 1,     // 单声道
      encodeBitRate: 96000,    // 编码码率
      frameSize: 50            // 指定帧大小，提高稳定性
    };


    recorderManager.start(options ? options : defaultOptions);
  },

  // 停止录音
  stop(callback, isCancel = false) {

    // 如果是取消操作，则不应该进行评估
    if (isCancel) {
      shouldEvaluate = false;
    }

    // 如果没有在录音，忽略停止操作
    if (!isRecording) {
      hasCancelledRecently = true;// 不要删除防止事件执行顺序不对
      return;
    }


    currentCallback = callback;
    recorderManager.stop();
  },

  // 取消录音 (用于意外中断如来电等情况)
  cancel() {
    console.log('取消录音');

    // 如果没有在录音，忽略取消操作
    if (!isRecording) {
      return;
    }

    // 设置最近取消标记
    hasCancelledRecently = true;
    shouldEvaluate = false;

    // 一段时间后自动清除取消标记
    setTimeout(() => {
      hasCancelledRecently = false;
    }, CANCEL_RESET_TIME);

    recorderManager.stop();
  },

  // 检查是否正在录音
  isRecording() {
    return isRecording;
  },

  // 检查是否最近取消过录音
  hasCancelledRecently() {
    return hasCancelledRecently;
  },

  // 新增注册分贝值回调函数的方法
  onDbChange(callback) {
    if (typeof callback === 'function' && !dbCallbacks.includes(callback)) {
      dbCallbacks.push(callback);
    }
    return () => this.offDbChange(callback); // 返回注销函数
  },

  // 取消注册分贝回调
  offDbChange(callback) {
    const index = dbCallbacks.indexOf(callback);
    if (index !== -1) {
      dbCallbacks.splice(index, 1);
    }
  },

  // 清除所有分贝回调
  clearDbCallbacks() {
    dbCallbacks = [];
  },

  // 手动重置取消状态（如果需要）
  resetCancelledState() {
    hasCancelledRecently = false;
  }
};
