<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <easy-navbar title="班级"/>

    <!-- 切换选项卡 -->
    <view class="tabs">
      <view
        :class="['tab-item', activeTab === 'joined' ? 'active' : '']"
        @click="switchTab('joined')"
      >
        <text>我加入的</text>
      </view>
      <view
        :class="['tab-item', activeTab === 'created' ? 'active' : '']"
        @click="switchTab('created')"
      >
        <text>我创建的</text>
      </view>
    </view>

    <!-- 班级列表 - 我加入的 -->
    <view class="class-list" v-if="activeTab === 'joined'">
      <view v-if="joinedClasses.length === 0" class="empty-state">
        <text>暂无加入的班级</text>
      </view>
      <view class="class-item" v-for="(item, index) in joinedClasses" :key="index" @click="selectClass(item)">
        <view class="class-content">
          <view class="class-avatar">
            <image :src="sheep.$url.cdn('/group/faculty.png')" />
          </view>
          <view class="class-info">
            <view class="class-name">{{ item.name }}</view>
            <view class="count-boxes">
              <view class="count-box study-set">
                <view class="icon-placeholder"><image :src="sheep.$url.cdn('/group/set1.png')" /></view>
                <text>:{{ item.studySetCount || 0 }}</text>
              </view>
              <view class="count-box lesson">
                <view class="icon-placeholder"><image :src="sheep.$url.cdn('/group/course2.png')" /></view>
                <text>:{{ item.lessonCount || 0 }}</text>
              </view>
              <view class="count-box homework">
                <view class="icon-placeholder"><image :src="sheep.$url.cdn('/group/homework2.png')" /></view>
                <text>:{{ item.assignmentCount || 0 }}</text>
              </view>
            </view>
            <view class="class-remark">{{ item.remark || '暂无班级说明' }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 班级列表 - 我创建的 -->
    <view class="class-list" v-if="activeTab === 'created'">
      <view v-if="createdClasses.length === 0" class="empty-state">
        <text>暂无创建的班级</text>
      </view>
      <view class="class-item" v-for="(item, index) in createdClasses" :key="index" @click="selectClass(item)">
        <view class="class-content">
          <view class="class-avatar">
            <image :src="sheep.$url.cdn('/group/faculty.png')" />
          </view>
          <view class="class-info">
            <view class="class-name">{{ item.name }}</view>
            <view class="count-boxes">
              <view class="count-box study-set">
                <view class="icon-placeholder"><image :src="sheep.$url.cdn('/group/set1.png')" /></view>
                <text>:{{ item.studySetCount || 0 }}</text>
              </view>
              <view class="count-box lesson">
                <view class="icon-placeholder"><image :src="sheep.$url.cdn('/group/course2.png')" /></view>
                <text>:{{ item.lessonCount || 0 }}</text>
              </view>
              <view class="count-box homework">
                <view class="icon-placeholder"><image :src="sheep.$url.cdn('/group/homework2.png')" /></view>
                <text>:{{ item.assignmentCount || 0 }}</text>
              </view>
            </view>
            <view class="class-remark">{{ item.remark || '暂无班级说明' }}</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 创建班级按钮 -->
    <view class="create-button" @click="createClass">
      <text class="create-text">创建班级</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import GroupApi from '@/sheep/api/group/index';
import sheep from '@/sheep';
import { onShow } from '@dcloudio/uni-app';

// 响应式数据
const activeTab = ref('joined'); // 默认选中"我加入的"
const joinedClasses = ref([]);
const createdClasses = ref([]);

// 本地存储的班级ID的key
const LAST_GROUP_ID_KEY = 'last_group_id';

// 加载所有班级
const loadAllClasses = () => {
  if (activeTab.value === 'joined') {
    loadJoined();
  } else if (activeTab.value === 'created') {
    loadCreated();
  }
};

// 加载我加入的班级
const loadJoined = async () => {
  const res = await GroupApi.getMyJoinedGroups();
  if (res.code !== 0) {
    return;
  }
  joinedClasses.value = res.data;
};

// 加载我创建的班级
const loadCreated = async () => {
  const res = await GroupApi.getMyCreatedGroups();
  if (res.code !== 0) {
    return;
  }
  createdClasses.value = res.data;
};

// 切换选项卡
const switchTab = (tab) => {
  activeTab.value = tab;
  loadAllClasses();
};

// 选择班级
const selectClass = (classItem) => {
  // 保存选择的班级ID到本地存储
  if (classItem && classItem.id) {
    uni.setStorageSync(LAST_GROUP_ID_KEY, classItem.id);
  }
  
  // 跳转到班级详情页
  sheep.$router.go(`/pages/group/index?id=${classItem.id}`,{},{redirect: true});
};

// 返回上一页
const goBack = () => {
  sheep.$router.back();
};

// 创建班级
const createClass = () => {
  sheep.$router.go('/pages/group/add');
};

// 页面加载和显示时获取班级信息
onMounted(() => {
  loadAllClasses();
});

onShow(() => {
  loadAllClasses();
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #F8FCFF;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx;
}

/* 自定义关闭按钮 */
.close-btn {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  padding: 10rpx;
}

/* 选项卡样式 */
.tabs {
  display: flex;
  background-color: #ffffff;
  padding: 15rpx 30rpx;
  border-radius: 50rpx;
  margin: 40rpx 40rpx 20rpx 40rpx;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  margin: 0 10rpx;
  border-radius: 40rpx;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }

  &.active {
    width: 329rpx;
    height: 80rpx;
    background: linear-gradient(-2deg,rgba(70,173,240,0.89),rgba(0,222,255,0.89));
    border-radius: 40rpx;
    color: #fff;
  }
}

/* 班级列表样式 */
.class-list {
  padding: 20rpx;
}

.empty-state {
  display: flex;
  justify-content: center;
  padding-top: 200rpx;

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.class-item {
  height: 182rpx;
  background: #FFFFFF;
  box-shadow: 1rpx 2rpx 12rpx 0rpx rgba(211,223,230,0.52);
  border-radius: 30rpx;
  margin: 0 20rpx 28rpx 20rpx;
  padding: 0 26rpx;
}

.class-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.class-avatar {
  width: 121rpx;
  height: 121rpx;
  background: #EFF9FF;
  border-radius: 61rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  image {
    width: 100%;
    height: 100%;
  }
}

.class-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 121rpx;
  padding: 5rpx 0;
}

.class-name {
  font-size: 32rpx;
  color: #202020;
  font-weight: 500;
  margin-bottom: 10rpx;
  line-height: 1.2;
}

.count-boxes {
  display: flex;
  margin-bottom: 10rpx;
}

.count-box {
  width: 82rpx;
  height: 35rpx;
  border-radius: 7rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 21rpx;
  line-height: 35rpx;
}

.icon-placeholder {
  width: 22rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 7rpx;
}

.icon-placeholder image {
  width: 100%;
  height: 100%;
}

.study-set {
  background: #F1F5FF;
  color: #5683FF;
}

.lesson {
  background: #F3FCFF;
  color: #16B6FF;
}

.homework {
  background: #E6FFF9;
  color: #12E287;
}

.class-remark {
	width: 489rpx;
  font-size: 24rpx;
  color: #ACACAC;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 创建班级按钮 */
.create-button {
  height: 82rpx;
  background: linear-gradient(270deg,  rgba(70,173,240,0.89),rgba(0,222,255,0.89));
  border-radius: 41rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 80%;
  padding: 3rpx 40rpx;
  margin-left: 40rpx;
  margin-right: 40rpx;
  margin-bottom: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
}

.create-text {
  width: 132rpx;
  height: 35rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
}
</style> 