<template>
  <view class="certification-container">
    <easy-navbar title="教师认证" />

    <view class="content-wrapper">
      <view class="status-card" v-if="status !== null && status !== -1">
        <view class="status-card-inner" :class="{
          'status-approved': status === CertificationStatusEnum.APPROVED.status,
          'status-rejected': status === CertificationStatusEnum.REJECTED.status,
          'status-pending': status === CertificationStatusEnum.PENDING.status
        }">
          <view class="status-icon">
            <image v-if="status === CertificationStatusEnum.APPROVED.status"
              :src="sheep.$url.cdn('/user/approved.png')"
              mode="aspectFit" class="status-image"/>
            <image v-else-if="status === CertificationStatusEnum.REJECTED.status"
              :src="sheep.$url.cdn('/user/rejected.png')"
              mode="aspectFit" class="status-image"/>
            <image v-else-if="status === CertificationStatusEnum.PENDING.status"
              :src="sheep.$url.cdn('/user/pending.png')"
              mode="aspectFit" class="status-image"/>
          </view>
          <view class="status-content">
            <view class="status-title">{{ getDescriptionByStatus(status) }}</view>
            <view class="status-desc">
              <template v-if="status === CertificationStatusEnum.PENDING.status">
                您的认证信息正在审核中，请耐心等待
              </template>
              <template v-else-if="status === CertificationStatusEnum.REJECTED.status">
                请根据审核备注补充信息后重新提交
                <view v-if="auditOpinion" class="audit-opinion">审核备注：{{ auditOpinion }}</view>
              </template>
              <template v-else-if="status === CertificationStatusEnum.APPROVED.status">
                您的教师认证已通过审核
                <view v-if="auditOpinion" class="audit-opinion">审核备注：{{ auditOpinion }}</view>
              </template>
            </view>
          </view>
        </view>
      </view>

      <view class="form-wrapper">
        <view class="section-title">请完善您的个人信息</view>
        <uni-forms :model="formData" validate-trigger="submit" label-width="0">
          <uni-forms-item name="name" :label="''">
            <view class="form-item">
              <view class="form-label">姓名</view>
              <uni-easyinput
                v-model="formData.name"
                placeholder="请输入姓名"
                :inputBorder="false"
                class="form-input"
                :disabled="isCertified"
              />
            </view>
          </uni-forms-item>

          <uni-forms-item name="idCardNumber" :label="''">
            <view class="form-item">
              <view class="form-label">身份证号</view>
              <uni-easyinput
                v-model="formData.idCardNumber"
                placeholder="请输入身份证号"
                :inputBorder="false"
                type="idcard"
                class="form-input"
                :disabled="isCertified"
              />
            </view>
          </uni-forms-item>
        </uni-forms>
      </view>

      <view class="form-wrapper">
        <view class="section-title">备注信息（可选）</view>
        <view class="form-item">
          <uni-easyinput
            v-model="formData.userRemark"
            placeholder="请输入备注信息"
            :inputBorder="false"
            type="textarea"
            class="remark-textarea"
            :disabled="isCertified"
            maxlength="500"
          />
        </view>
      </view>

      <view class="upload-section">
        <view class="section-title">请上传证件照（可选）</view>

        <view class="upload-cards">
          <view class="upload-card">
            <view class="card-left">
              <view class="card-title">头像面</view>
              <view class="card-subtitle">请上传头像面</view>
            </view>
            <view class="card-right">
              <s-uploader
                v-model="formData.idCardFrontUrl"
                fileMediatype="image"
                limit="1"
                :imageStyles="{width: '368rpx', height: '240rpx'}"
                @success="(res) => onUploadSuccess(res, 'idCardFrontUrl')"
                :disabled="isCertified"
              >
                <image class="id-card-preview" :src="previewUrl.idCardFrontUrl" mode="widthFix" />
              </s-uploader>
            </view>
          </view>

          <view class="upload-card">
            <view class="card-left">
              <view class="card-title">国徽面</view>
              <view class="card-subtitle">请上传国徽面</view>
            </view>
            <view class="card-right">
              <s-uploader
                v-model="formData.idCardBackUrl"
                fileMediatype="image"
                limit="1"
                :imageStyles="{width: '368rpx', height: '240rpx'}"
                @success="(res) => onUploadSuccess(res, 'idCardBackUrl')"
                :disabled="isCertified"
              >
                <image class="id-card-preview" :src="previewUrl.idCardBackUrl" mode="widthFix" />
              </s-uploader>
            </view>
          </view>
        </view>
      </view>

      <view class="submit-section">
        <button class="submit-btn" @click="submitForm">{{ submitText }}</button>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import sheep from '@/sheep';
  import CertificationApi from '@/sheep/api/member/certification';
  import { CertificationStatusEnum, getDescriptionByStatus } from '@/pages/user/util/constants';

  const previewUrl = ref({
    idCardFrontUrl: sheep.$url.cdn('/user/face.png'),
    idCardBackUrl: sheep.$url.cdn('/user/emblem.png'),
  });

  const formData = ref({
    id: undefined,
    name: '',
    idCardNumber: '',
    idCardFrontUrl: '',
    idCardBackUrl: '',
    userRemark: '',
  });

  const status = ref(-1);
  const auditOpinion = ref('');
  const isCertified = ref(false)
  const submitText = ref('提交')

  const onUploadSuccess = async (res, fieldName) => {
    formData.value[fieldName] = res.tempFilePaths[0];
    previewUrl.value[fieldName] = res.tempFilePaths[0];
  };

  // 提交表单
  const submitForm = async () => {
    const { code } = await CertificationApi.submitCertification(formData.value);

    if (code !== 0) {
      return;
    }

    sheep.$helper.toast('提交成功，请等待审核，期间可修改信息', 2200);
    setTimeout(() => {
      sheep.$router.back();
    }, 2000);
  };

  const getMy = async () => {
    const { code, data } = await CertificationApi.getMyCertification();
    if (code !== 0 || data === null) {
      return;
    }

    formData.value = data;
    previewUrl.value = data;

    status.value = data?.status;
    auditOpinion.value = data?.auditOpinion;

    switch(status.value) {
      case CertificationStatusEnum.APPROVED.status:
        isCertified.value = true;
        submitText.value = '已认证';
        break;
      case CertificationStatusEnum.REJECTED.status:
        isCertified.value = false;
        submitText.value = '再次提交';
        break;
      case CertificationStatusEnum.PENDING.status:
        isCertified.value = false;
        submitText.value = '修改提交';
        break;
    }
  };

  onMounted(() => {
    getMy();
  })
</script>

<style lang="scss" scoped>
  .certification-container {
    min-height: 100vh;
    background-color: #F6F6F6;
  }

  .content-wrapper {
    padding: 20rpx 30rpx 40rpx;
  }

  /* 状态卡片样式 */
  .status-card {
    margin-bottom: 30rpx;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .status-card-inner {
    display: flex;
    padding: 30rpx;
    border-radius: 12rpx;
  }

  .status-approved {
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.2));
    border-left: 8rpx solid #52c41a;
  }

  .status-rejected {
    background: linear-gradient(135deg, rgba(245, 34, 45, 0.1), rgba(245, 34, 45, 0.2));
    border-left: 8rpx solid #f5222d;
  }

  .status-pending {
    background: linear-gradient(135deg, rgba(250, 173, 20, 0.1), rgba(250, 173, 20, 0.2));
    border-left: 8rpx solid #faad14;
  }

  .status-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .status-image {
    width: 60rpx;
    height: 60rpx;
  }

  .status-content {
    flex: 1;
  }

  .status-title {
    font-size: 32rpx;
    font-weight: 600;
    margin-bottom: 10rpx;
  }

  .status-approved .status-title {
    color: #52c41a;
  }

  .status-rejected .status-title {
    color: #f5222d;
  }

  .status-pending .status-title {
    color: #faad14;
  }

  .status-desc {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }

  .audit-opinion {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #757575;
  }

  .form-wrapper {
    background-color: #FFFFFF;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    border: 1px solid #F2F2F2;
    overflow: hidden;
  }

  .form-item {
    padding: 20rpx 30rpx;
  }

  :deep(.uni-forms-item__inner) {
    padding: 0;
  }

  :deep(.uni-forms-item) {
    border-bottom: 1px solid #F2F2F2;
  }

  :deep(.uni-forms-item:last-child) {
    border-bottom: none;
  }

  :deep(.uni-forms-item__error) {
    padding-left: 30rpx;
  }

  .form-label {
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 10rpx;
    font-weight: 500;
  }

  .form-input {
    font-size: 28rpx;
  }

  .upload-section {
    background-color: #FFFFFF;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    border: 1px solid #F2F2F2;
    overflow: hidden;
  }

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #F2F2F2;
  }

  .upload-cards {
    display: flex;
    flex-direction: column;
  }

  .upload-card {
    padding: 30rpx;
    border-bottom: 1px solid #F2F2F2;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:last-child {
      border-bottom: none;
    }
  }

  .card-left {
    flex: 1;
    padding-left: 10px;
  }

  .card-right {
    width: 380rpx;
  }

  .card-title {
    font-size: 30rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 8rpx;
  }

  .card-subtitle {
    font-size: 26rpx;
    color: #999999;
  }

  .id-card-preview {
    width: 100%;
    height: 100%;
    border: 0 !important;
  }

  .submit-section {
    padding: 40rpx 0
  }

  .remark-textarea {
    width: 100%;
    height: 160rpx;
    font-size: 28rpx;
    padding: 10rpx 0;
    box-sizing: border-box;
  }

  .textarea-counter {
    text-align: right;
    font-size: 24rpx;
    color: #999999;
    padding-top: 10rpx;
  }

  .submit-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    background: #46ADF0;
    color: #FFFFFF;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 45rpx;
    text-align: center;
    border: none;
    box-shadow: 0 4rpx 8rpx rgba(70, 173, 240, 0.2);
  }
</style>
