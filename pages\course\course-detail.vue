<template>
  <view class="course-detail-page">
    <!-- 顶部导航 -->
    <view class="navbar-area" :style="{ paddingTop: `${paddingTop}px` }">
      <view class="left">
        <image
          class="back-icon"
          :src="sheep.$url.cdn('/common/back.png')"
          @click="handleBack"
        />
        <text class="navbar-title">{{ lessonDetail?.title || '课程详情' }}</text>
      </view>
    </view>

    <!-- 占位元素，防止内容被固定导航栏遮挡 -->
    <view class="navbar-placeholder" :style="{ height: `${navbarHeight}px` , background: `#ffffff`}"></view>
    
    <!-- 课程内容区域 -->
    <scroll-view scroll-y class="content-scroll">
      <!-- 搜索框 -->
      <view class="search-box">
        <view class="search-input-wrapper">
          <image class="search-icon" :src="sheep.$url.cdn('/course/search.png')" mode="aspectFit"></image>
          <input 
            class="search-input" 
            type="text" 
            v-model="searchKeyword" 
            placeholder="搜索班级" 
            confirm-type="search"
            @confirm="searchClasses"
            @blur="searchClasses"
          />
        </view>
        <view class="collection-btn" @click="toggleCollection">
          <image 
            class="collection-icon" 
            :src="isStore === true ? sheep.$url.cdn('/course/shoucangxuanzhong.png') : sheep.$url.cdn('/course/shoucang.png')" 
            mode="aspectFit">
          </image>
          <text class="collection-text">收藏</text>
        </view>
      </view>
      
      <!-- 功能菜单 -->
      <view class="function-menu">
        <view class="function-item" @click="navigateToChapter">
          <view class="function-icon-container" :style="getIconStyle('/course/chapter.png', '#FFC077', '#FF9F50')">
            <text class="function-text">章节</text>
          </view>
        </view>
        
        <view class="function-item" @click="navigateToAssignment">
          <view class="function-icon-container" :style="getIconStyle('/course/assignment.png', '#92A1FD', '#5870FD')">
            <text class="function-text">作业</text>
          </view>
        </view>
        
        <view class="function-item" @click="navigateToDocument">
          <view class="function-icon-container" :style="getIconStyle('/course/doument.png', '#5EE09B', '#00DD83')">
            <text class="function-text">资料</text>
          </view>
        </view>
      </view>
      
      <!-- 班级列表 -->
      <view class="class-list-section">
        <s-empty v-if="classList.length === 0" text="暂无班级数据" />
        
        <view v-else class="class-list">
          <view 
            v-for="(item, index) in classList" 
            :key="index" 
            class="class-item"
            @click="navigateToClass(item)"
          >
            <image class="class-icon" :src="sheep.$url.cdn('/group/new-class.png')" mode="aspectFit"></image>
            <view class="class-info">
              <view class="class-title">{{ item.groupName || '默认班级' }}</view>
              <view class="class-desc">{{ item.groupRemark || '学生2' }}</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 右下角分享按钮 -->
    <view class="share-button" @click="handleShare">
      <image class="share-icon" :src="sheep.$url.cdn('/course/blue_share.png')" mode="aspectFit"></image>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import sheep from '@/sheep';
import LessonApi from '@/sheep/api/course/lesson';
import LessonCollectionApi from '@/sheep/api/course/lesson-collection';

// 定义接口和类型
interface LessonDetail {
  id?: number | string;
  title?: string;
  description?: string;
  coverImage?: string;
  [key: string]: any;
}

interface ClassItem {
  id?: number | string;
  groupId?: number | string;
  groupName?: string;
  groupRemark?: string;
  groupUserId?: number | string;
  [key: string]: any;
}

interface ApiResponse {
  code: number;
  data: any;
  msg?: string;
}

// uni-app 全局类型
declare const getCurrentPages: () => any[];
declare const uni: any;

// 课程ID
const courseId = ref<string | null>(null);
// 课程详情
const lessonDetail = ref<LessonDetail>({});
// 班级列表
const classList = ref<ClassItem[]>([]);
// 搜索关键词
const searchKeyword = ref('');
// 收藏状态
const isStore = ref<boolean | null>(null);

// 导航栏高度相关
const paddingTop = ref(0);
const navbarHeight = ref(0);

// 获取URL参数
const getParams = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.options) {
    courseId.value = currentPage.options.id;
  }
};

// 获取课程详情
const getLessonDetail = async () => {
  if (!courseId.value) return;
  const res = await LessonApi.getLesson(courseId.value) as unknown as ApiResponse;
  if (res.code === 0 && res.data) {
    lessonDetail.value = res.data;
    // 获取收藏状态
    getCollectionStatus();
  }
};

// 获取收藏状态
const getCollectionStatus = async () => {
  if (!courseId.value) return;
  try {
    if (lessonDetail.value && lessonDetail.value.isStore !== undefined) {
      isStore.value = lessonDetail.value.isStore;
    } else {
      isStore.value = false; // 默认未收藏
    }
  } catch (error) {
    isStore.value = false;
  }
};

// 切换收藏状态
const toggleCollection = async () => {
  if (!courseId.value) return;
  let res;
  if (isStore.value) {
    // 取消收藏
    res = await LessonCollectionApi.uncollectLesson(courseId.value) as unknown as ApiResponse;
  } else {
    // 收藏课程
    res = await LessonCollectionApi.collectLesson(courseId.value) as unknown as ApiResponse;
  }

  if (res && res.code === 0) {
    // 成功后更新状态
    isStore.value = !isStore.value;
    // 显示提示信息
    sheep.$helper.toast(isStore.value ? '收藏成功' : '已取消收藏');
  } else {
    // API调用失败
    sheep.$helper.toast('操作失败，请重试');
  }
};

// 获取班级列表
const getClassList = async () => {
  if (!courseId.value) return;
  const res = await LessonApi.getClassesByCourseId(courseId.value) as unknown as ApiResponse;
  if (res.code === 0 && res.data) {
    classList.value = res.data || [];
  }
};

// 搜索班级
const searchClasses = async () => {
  if (!courseId.value) return;
  const params = {
    courseId: courseId.value,
    keyword: searchKeyword.value.trim()
  };

  const res = await LessonApi.searchCourseClasses(params) as unknown as ApiResponse;
  if (res.code === 0 && res.data) {
    classList.value = res.data || [];
  }
};

// 导航到章节页面
const navigateToChapter = () => {
  sheep.$helper.inDevMsg();
};

// 导航到作业页面
const navigateToAssignment = () => {
  sheep.$router.go('/pages/course/assignment/index', { courseId: courseId.value });
};

// 导航到资料页面
const navigateToDocument = () => {
  sheep.$helper.inDevMsg();
}

// 导航到班级详情页面
const navigateToClass = (classItem: ClassItem) => {
  sheep.$router.go(`/pages/group/index?id=${classItem.groupId}`);
};

// 处理分享
const handleShare = () => {
  // 跳转到选择班级页面
  sheep.$router.go('/pages/set/select-group', {
    shareType: 'course',
    courseId: courseId.value
  });
};

// 自定义返回处理
const handleBack = () => {
    // 发送刷新事件通知上一页刷新数据
    uni.$emit('refreshCourseList', { courseId: courseId.value, isStore: isStore.value });
    sheep.$router.back();
};

// 设置导航栏高度
const setupNavbar = () => {
  // #ifdef MP-WEIXIN
  // 获取胶囊按钮信息
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
  // 使用与easy-navbar相同的方式设置导航栏高度
  paddingTop.value = menuButtonInfo.top;
  navbarHeight.value = paddingTop.value + 35;
  // #endif

  // #ifndef MP-WEIXIN
  // 非微信环境获取状态栏高度
  const systemInfo = sheep.$helper.sys();
  paddingTop.value = systemInfo.marginTop;
  navbarHeight.value = paddingTop.value + 35;
  // #endif
};

// 获取图标样式
const getIconStyle = (iconPath, startColor, endColor) => {
  const cdnUrl = sheep.$url.cdn(iconPath);
  return {
    background: `linear-gradient(135deg, ${startColor}, ${endColor})`,
    backgroundImage: `url(${cdnUrl})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center'
  };
};

// 页面加载
onMounted(() => {
  setupNavbar();
  getParams();
  getLessonDetail();
  getClassList();
  
  // 监听刷新班级列表事件
  uni.$on('refreshCourseClasses', (data) => {
    if (data && data.courseId && data.courseId == courseId.value) {
      getClassList();
    }
  });
});

// 在页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshCourseClasses');
});
</script>

<style scoped lang="scss">
.course-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #FFFFFF;
  position: relative;
}

// 导航栏样式 - 参考index页面
.navbar-area {
  display: flex;
  align-items: center;
  height: 35px;
  width: 100%;
  z-index: 999;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #FFFFFF;
  transition: all 0.05s ease;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.left {
  position: absolute;
  left: 15px;
  display: flex;
  align-items: center;
  z-index: 101;
}

.back-icon {
  width: 19rpx;
  height: 32rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  padding: 10rpx;
}

.navbar-title {
  padding: 10rpx 0;
  font-size: 34rpx;
  color: #333;
  font-weight: 500;
  margin-left: 5rpx;
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.right {
  position: absolute;
  right: 30rpx;
  display: flex;
  align-items: center;
  z-index: 101;
}

// 内容区域
.content-scroll {
  flex: 1;
  box-sizing: border-box;
}

// 搜索框
.search-box {
  padding: 20rpx 30rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
}

.search-input-wrapper {
  width: 530rpx;
  height: 65rpx;
  background-color: #F5F5F5;
  border-radius: 33rpx;
  opacity: 0.8;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  margin-right: 14rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 65rpx;
  font-size: 28rpx;
  color: #333;
}

.collection-btn {
  width: 155rpx;
  height: 65rpx;
  background: #F5F5F5;
  border-radius: 33rpx;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collection-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.collection-text {
  font-size: 28rpx;
  color: #333;
}

// 功能菜单
.function-menu {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  background-color: #fff;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.function-icon-container {
  position: relative;
  width: 214rpx;
  height: 134rpx;
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.function-text {
  width: auto;
  height: 28rpx;
  position: absolute;
  top: 53rpx;
  left: 30rpx;
  font-family: 'PingFang SC',serif;
  font-weight: bold;
  font-size: 29rpx;
  color: #FFFFFF;
  line-height: 28rpx;
  text-shadow: 1rpx 2rpx 6rpx rgba(197,112,8,0.38);
  z-index: 1;
  white-space: nowrap;
}

// 班级列表
.class-list-section {
  background-color: #fff;
  min-height: 300rpx;
}

.class-list {
  padding: 0 30rpx;
}

.class-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #F1F1F1;
}

.class-item:last-child {
  border-bottom: none;
}

.class-icon {
  width: 104rpx;
  height: 85rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.class-info {
  flex: 1;
}

.class-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.class-desc {
  font-size: 26rpx;
  color: #999;
}

// 右下角分享按钮
.share-button {
  position: fixed;
  right: 30rpx;
  bottom: 323rpx;
  width: 95rpx;
  height: 93rpx;
  background: #FFFFFF;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

</style>
