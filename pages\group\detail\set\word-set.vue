<template>
  <view class="word-set-container">
    <s-empty v-if="!toLearnSets?.length" />

    <view v-else>
      <!-- 待学习部分 -->
      <view class="section">
        <view class="course-grid">
          <CourseCard
            v-for="(item, index) in toLearnSets"
            :key="index"
            :item="item"
            @click="goToWordDetail(item.id)"
            @longpress="showActionSheet(item.id, $event)"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import sheep from '@/sheep';
  import CourseCard from '@/pages/group/components/course-card.vue';
  import SetApi from '@/sheep/api/set';
  import GroupApi from '@/sheep/api/group';

  // 接收班级ID参数
  const props = defineProps({
    classId: {
      type: String,
      default: '',
    },
  });

  // 学习集
  const toLearnSets = ref([]);

  // 班级信息
  const classInfo = ref(null);

  // 是否为班级创建者
  const isClassCreator = ref(false);

  // 加载学习集数据
  const loadStudySets = async () => {
    const params = {
      classId: props.classId
    }
    const { code, data } = await SetApi.getWordSetPageByClass(params);
    if (code !== 0) {
      return;
    }
    toLearnSets.value = data.list;
  };

  // 加载班级信息并判断是否为创建者
  const loadClassInfo = async () => {
    if (!props.classId) return;
    // 获取班级详情
    const { code, data } = await GroupApi.getGroup(props.classId);
    if (code === 0) {
      classInfo.value = data;

      // 获取当前用户信息
      const userStore = sheep.$store('user');
      let currentUserInfo = userStore.userInfo;
      if (!currentUserInfo || !currentUserInfo.id) {
        currentUserInfo = await userStore.updateUserData();
      }

      // 判断是否为班级创建者
      const currentUserId = currentUserInfo?.id;
      const classCreatorId = data.userId;
      isClassCreator.value = currentUserId && classCreatorId && Number(currentUserId) === Number(classCreatorId);
    }
  };

  // 长按显示操作菜单
  const showActionSheet = async (setId, e) => {
    if (e) e.stopPropagation();
    
    // 只有班级创建者才能操作
    if (!isClassCreator.value) {
      sheep.$helper.toast('只有班级创建者可以移除学习集');
      return;
    }

    // 显示操作菜单
    uni.showActionSheet({
      itemList: ['移除'],
      itemColor: '#000000',
      success: (res) => {
        if (res.tapIndex === 0) {
          // 移除学习集
          confirmRemoveSet(setId);
        }
      },
    });
  };

  // 确认移除学习集
  const confirmRemoveSet = (setId) => {
    uni.showModal({
      title: '确认移除',
      content: '确定要将此学习集从班级中移除吗？移除后班级成员将无法访问此学习集。',
      confirmText: '移除',
      confirmColor: '#ff0000',
      success: async (res) => {
        if (res.confirm) {
          try {
            uni.showLoading({
              title: '移除中...',
              mask: true,
            });
            
            // 调用批量删除接口
            const response = await SetApi.batchDeleteFromClass({
              setIds: [setId],
              classId: props.classId
            });
            
            uni.hideLoading();
            
            if (response.code === 0) {
              sheep.$helper.toast('移除成功');
              // 刷新学习集列表
              loadStudySets();
            } else {
              sheep.$helper.toast(response.msg || '移除失败');
            }
          } catch (error) {
            uni.hideLoading();
            sheep.$helper.toast('网络异常，请稍后再试');
            console.error('移除学习集失败:', error);
          }
        }
      },
    });
  };

  // 跳转到单词详情页
  const goToWordDetail = (id) => {
    sheep.$router.go(`/pages/set/word-card?id=${id}`);
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadClassInfo();
    loadStudySets();
  });
  
  // 向父组件暴露方法
  defineExpose({
    loadStudySets
  });
</script>

<style lang="scss" scoped>
.word-set-container {
  padding: 20rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #272727;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.divider {
  height: 1rpx;
  background-color: #E1E1E1;
  margin: 20rpx 0 50rpx;
  width: 100%;
}
</style>
