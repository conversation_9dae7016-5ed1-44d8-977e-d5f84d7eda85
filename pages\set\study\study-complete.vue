<template>
  <view class="complete-container">
    <!-- 顶部导航栏，传入非空title -->
    <easy-navbar :transparent="true"/>

    <!-- 背景波浪 -->
    <view class="wave-container">
      <image :src="sheep.$url.cdn('/set/cloud.png')" class="cloud-image" mode="widthFix" />
    </view>

    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 中间图片 -->
      <image :src="sheep.$url.cdn('/set/homework.png')" class="homework-image" mode="widthFix" />

      <!-- 完成文本 -->
      <text class="complete-text">太棒啦！已完成学习模式</text>
      <text v-if="allMastered" class="mastered-text">您已掌握全部题目！</text>
    </view>

    <!-- 底部按钮区域 -->
    <view class="button-container">
      <view class="button-row" v-if="hasIncorrectQuestions">
        <view class="review-button" @click="handleReview">复习模式</view>
        <view class="continue-button" @click="handleContinue">进入测试</view>
      </view>
      <view class="button-row" v-else>
        <view class="restart-button" @click="handleRestart">重新学习</view>
        <view class="continue-button" @click="handleContinue">进入测试</view>
      </view>
      <view class="back-button" @click="handleBack">返回</view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onBeforeUnmount } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import sheep from '@/sheep';
  import EasyNavbar from '@/components/easy-navbar/easy-navbar.vue';
  import SessionApi from '@/sheep/api/set/session';
  
  // 路由参数
  const setId = ref(0);
  const hasIncorrectQuestions = ref(true); // 默认显示复习按钮，直到确认没有错题
  const allMastered = ref(false); // 是否全部掌握
  const sessionId = ref(null); // 当前会话ID
  
  // 在onLoad中获取参数
  onLoad((options) => {
    setId.value = Number(options.setId) || 0;
    if (setId.value) {
      checkIncorrectQuestions();
    }
  });
  
  // 检查是否有需要复习的题目
  const checkIncorrectQuestions = async () => {
    // 获取当前学习模式的会话
    const ongoingRes = await SessionApi.getOngoingSession({
      setId: setId.value,
      mode: 1, // 学习模式
    });

    if (ongoingRes.data && ongoingRes.data.id) {
      sessionId.value = ongoingRes.data.id;
      // 直接使用getOngoingSession返回的学习记录
      let studyRecords = ongoingRes.data.studyRecords || [];
      
      // 根据题目ID对学习记录进行分组，取每个题目的最新答题记录
      const latestRecords = {};
      studyRecords.forEach(record => {
        const questionId = record.questionId;
        if (!latestRecords[questionId] || 
            new Date(record.createTime) > new Date(latestRecords[questionId].createTime)) {
          latestRecords[questionId] = record;
        }
      });

      // 检查是否有错误的题目
      const hasIncorrect = Object.values(latestRecords).some(record => record.isCorrect === 0);
      
      // 明确将hasIncorrectQuestions设置为false，如果没有错误的题目或全部已掌握
      hasIncorrectQuestions.value = hasIncorrect && Object.keys(latestRecords).length > 0;
      allMastered.value = !hasIncorrect && Object.keys(latestRecords).length > 0;

      if (!hasIncorrect && Object.keys(latestRecords).length > 0) {
        // 如果全部掌握，结束当前会话
        await SessionApi.completeSession(sessionId.value);
      }
    } else {
      // 如果没有获取到会话数据，默认不显示复习按钮
      hasIncorrectQuestions.value = false;
      console.log('未获取到会话数据，不显示复习按钮');
    }
  };

  // 在会话完成后结束会话
  const endSessionOnExit = async () => {
    if (sessionId.value) {
      await SessionApi.completeSession(sessionId.value);
    }
  };

  // 方法
  const handleContinue = async () => {
    // 进入测试前结束当前会话
    await endSessionOnExit();
    // 继续进入下一个模式的逻辑，传递setId参数
    sheep.$router.go('/pages/set/test/test', { setId: setId.value },{redirect: true});
  };

  const handleReview = async () => {
    // 不结束会话，直接进入复习模式
    sheep.$router.go('/pages/set/study/study', { 
      setId: setId.value,
      mode: 'review' 
    },{redirect: true});
  };

  const handleRestart = async () => {
    // 如果有进行中的会话，先结束它
    if (sessionId.value) {
      await SessionApi.completeSession(sessionId.value);
      sheep.$helper.toast('正在准备新的学习会话...');

      // 创建新会话并进入学习模式
      sheep.$router.go('/pages/set/study/study', {
        setId: setId.value,
        mode: 'restart' // 标记为重新开始
      },{redirect: true});
    } else {
      // 如果没有会话ID，直接进入学习模式
      sheep.$router.go('/pages/set/study/study', { 
        setId: setId.value,
        mode: 'restart'
      },{redirect: true});
    }
  };

  const handleBack = async () => {
    // 返回上一页前结束会话
    await endSessionOnExit();
    // 返回上一页
    sheep.$router.back();
  };
</script>

<style scoped>
  .complete-container {
    width: 100%;
    height: 100vh;
    background: linear-gradient(180deg, #59c0fb 0%, #5dc7fb 50%, #46adf5 100%);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* 云朵波浪背景 */
  .wave-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .cloud-image {
    position: absolute;
    bottom: 0;
    width: 100%;
    z-index: 1;
  }

  /* 中间内容区域 */
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
  }

  .homework-image {
    width: 80%;
    max-width: 600rpx;
    margin-bottom: 60rpx;
  }

  .complete-text {
    font-size: 48rpx;
    color: #fff;
    font-weight: bold;
    text-align: center;
  }
  
  .mastered-text {
    font-size: 36rpx;
    color: #ffff00;
    margin-top: 20rpx;
    text-align: center;
  }

  /* 底部按钮区域 */
  .button-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 80rpx;
    position: relative;
    z-index: 2;
  }

  .button-row {
    display: flex;
    width: 80%;
    justify-content: space-between;
    margin-bottom: 30rpx;
  }

  .continue-button, .review-button, .restart-button {
    width: 48%;
    height: 100rpx;
    background: linear-gradient(90deg, #0ccd57 0%, #0cde72 100%);
    border-radius: 50rpx;
    color: #fff;
    font-size: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 12rpx rgba(12, 205, 87, 0.3);
  }

  .review-button {
    background: linear-gradient(90deg, #ff9500 0%, #ffaa33 100%);
    box-shadow: 0 6rpx 12rpx rgba(255, 149, 0, 0.3);
  }
  
  .restart-button {
    background: linear-gradient(90deg, #1890ff 0%, #3ba0ff 100%);
    box-shadow: 0 6rpx 12rpx rgba(24, 144, 255, 0.3);
  }

  .back-button {
    width: 80%;
    height: 100rpx;
    background: linear-gradient(90deg, #0ccd57 0%, #0cde72 100%);
    border-radius: 50rpx;
    color: #fff;
    font-size: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 12rpx rgba(12, 205, 87, 0.3);
  }
</style>