# AI代码编写注意事项
这是一个vue3版本的uniapp项目，为了你的代码编写质量，请遵循以下规范：

## 请求数据的api默认放在onShow
```示例
  import { onShow } from '@dcloudio/uni-app';
  onShow(() => {
    if (bottomNavRef.value) {
      bottomNavRef.value.updateActiveTab();
    }
    // 每次显示页面时刷新数据
    fetchLearningSetData();
  });
```


## 样式使用预处理语言sass
```示例
<style scoped lang="scss">
  .container {
    width: 100%;
    margin: 0 auto;
    background-color: #fff;
    min-height: 100vh;
    position: relative;
  }
</style>
```

## 图片url使用sheep中方法
```vue
     <image
          class="award-icon"
          mode="widthFix"
          :src="sheep.$url.static('/uniapp/my/word-award.png')"
        />
```

```js
import sheep from '@/sheep'
const cardBg = sheep.$url.css('/uniapp/my/award.png')
```

```sass
.header {
  background: v-bind(headerBg) center/cover no-repeat;
}
```